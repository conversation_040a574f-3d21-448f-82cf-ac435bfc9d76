input TestDriveFixedPeriodInput {
    """
    Start date of the fixed period
    """
    startDate: DateTime!

    """
    End date of the fixed period
    """
    endDate: DateTime!

    """
    Minimum number of days in advance required for booking (default: 0 means no advance booking limit)
    """
    advancedBookingLimit: Int!

    """
    Available booking time slots for this fixed period
    """
    bookingTimeSlot: [AppointmentTimeSlotInput!]!
}

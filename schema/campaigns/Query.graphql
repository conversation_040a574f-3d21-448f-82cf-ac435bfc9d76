extend type Query {
    """
    Get a campaign by ID
    """
    getCampaign(id: ObjectID!): Campaign

    """
    Get campaigns listing with pagination, sorting and filtering
    """
    listCampaigns(
        pagination: Pagination
        sort: CampaignSortingRule
        filter: CampaignFilteringRule
    ): PaginatedCampaign!

    """
    Get all active campaigns for a company
    """
    getActiveCampaigns(companyId: ObjectID!): [Campaign!]!
}

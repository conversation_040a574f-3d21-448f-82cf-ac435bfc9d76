"""
Company export result
"""
type CompanyExportResult {
    """
    Success status
    """
    success: Boolean!

    """
    Download URL for the exported file
    """
    downloadUrl: String

    """
    Export filename
    """
    filename: String

    """
    Export statistics
    """
    statistics: CompanyExportStatistics

    """
    Error message if export failed
    """
    error: String
}

"""
Export statistics
"""
type CompanyExportStatistics {
    """
    Number of modules exported
    """
    modulesCount: Int!

    """
    Number of files exported
    """
    filesCount: Int!

    """
    Total export size in bytes
    """
    totalSize: Int!

    """
    Number of related collections exported
    """
    collectionsCount: Int!
}

type CompanyImportResult {
    """
    Success status
    """
    success: Boolean!

    """
    Imported company ID
    """
    companyId: ObjectID

    """
    Import message
    """
    message: String!

    """
    Conflicts detected during import
    """
    conflicts: [CompanyImportConflict!]

    """
    Import statistics
    """
    statistics: CompanyImportStatistics

    """
    Errors during import
    """
    errors: [String!]
}

type CompanyImportStatistics {
    """
    Number of modules imported
    """
    modulesCount: Int!

    """
    Number of files imported
    """
    filesCount: Int!

    """
    Number of related collections imported
    """
    collectionsCount: Int!

    """
    Total import size in bytes
    """
    totalSize: Int!
}

type CompanyImportConflict {
    """
    Collection where conflict occurred
    """
    collection: String!

    """
    Document ID causing conflict
    """
    documentId: String!

    """
    Field causing conflict (if applicable)
    """
    field: String

    """
    Conflict description
    """
    message: String!
}

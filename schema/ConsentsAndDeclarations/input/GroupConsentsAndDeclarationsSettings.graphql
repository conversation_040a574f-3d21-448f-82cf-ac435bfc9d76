input GroupConsentsAndDeclarationsSettings {
    """
    Display Name
    """
    displayName: String!

    """
    Title
    """
    title: TranslatedStringInput

    """
    Description
    """
    description: TranslatedStringInput!

    """
    Order Number
    """
    orderNumber: Int!

    """
    Is active
    """
    isActive: Boolean!

    """
    Purpose
    """
    purpose: [ConsentsAndDeclarationsPurpose!]!

    """
    Data field
    """
    dataField: DataField!

    """
    Child consents to create/update (all group children must be checkbox type)
    """
    children: [CheckboxConsentsAndDeclarationsSettings!]!
}

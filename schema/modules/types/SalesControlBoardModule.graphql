type SalesControlBoardModule implements Module {
    """
    UID
    """
    id: ObjectID!

    """
    Company ID
    """
    companyId: ObjectID!

    """
    Company
    """
    company: Company!

    """
    Display name
    """
    displayName: String!

    """
    Permissions
    """
    permissions: [String]!

    """
    Versioning
    """
    versioning: SimpleVersioning!

    """
    Test Drive Monthly Target
    """
    testDriveMonthlyTarget: DealerInt!

    """
    Order Intakes Monthly Target
    """
    orderIntakesMonthlyTarget: DealerInt!

    """
    Retail Monthly Target
    """
    retailsMonthlyTarget: DealerInt!

    """
    Finance Commission Monthly Target
    Value should be in percentage (1-100)
    If the value is outside this range, it will be rounded to the closest integer.
    """
    financeCommissionMonthlyTarget: DealerFloat!

    """
    Insurance Commission Monthly Target
    Value should be in percentage (1-100)
    If the value is outside this range, it will be rounded to the closest integer.
    """
    insuranceCommissionMonthlyTarget: DealerFloat!

    """
    Sales Consultants Assignments
    """
    salesConsultantsAssignments: DealerAssignmentObjectId!
}

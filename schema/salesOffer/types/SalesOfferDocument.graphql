type SalesOfferDocument implements UploadFileInterface {
    """
    UID
    """
    id: ObjectID!

    """
    Original filename
    """
    filename: String!

    """
    E-Tag
    """
    etag: String!

    """
    Filesize
    """
    size: Int!

    """
    Upload date
    """
    uploadedAt: DateTime!

    """
    Signed URL

    Private image will return a signed URL with a 1H validity
    """
    url: String

    """
    Preview image
    """
    preview: UploadedFile

    """
    Kind of sales offer document
    """
    kind: SalesOfferDocumentKind!

    """
    Status of sales offer document
    """
    status: SalesOfferDocumentStatus!

    """
    Last updated time of sales offer document
    """
    lastUpdatedAt: DateTime!

    """
    Created time of sales offer document
    """
    createdAt: DateTime!

}

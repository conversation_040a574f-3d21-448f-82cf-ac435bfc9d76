type FICommission {
    salesConsultantName: String!
    inHouseFinanceTarget: Float!
    inHouseFinanceMtd: Float!
    inHouseFinanceYtd: Float!
    inHouseFinance3MAvg: Float!
    inHouseInsuranceTarget: Float!
    inHouseInsuranceMtd: Float!
    inHouseInsuranceYtd: Float!
    inHouseInsurance3MAvg: Float!
}

type WeekFunnel {
    start: DateTime!
    end: DateTime!
    leadsCreated: Int!
    testDrives: Int!
    salesOffers: Int!
    orderIntakes: Int!
    retails: Int!
    leadToTestDriveRate: Float!
    testDriveToSalesOfferRate: Float!
    salesOfferToOrderIntakeRate: Float!
    orderIntakeToRetailRate: Float!
}

type ProgressGoal {
    retailTargetMonth: Int!
    retailActualMonth: Int!
    retailMonthRate: Float!
    retailMonthDev: Int!
    retailTargetYtd: Int!
    retailActualYtd: Int!
    retailYtdRate: Float!
    retailYtdDev: Int!
    financeTarget: Float!
    financeActualRate: Float!
    insuranceTarget: Float!
    insuranceActualRate: Float!
}

type SalesPerformanceOverview {
    leadsCreated: Int!
    testDrives: Int!
    salesOffers: Int!
    orderIntakes: Int!
    retails: Int!
}

type SalesControlBoard {
    lostDataType: [SalesControlBoardDataType!]!
    fiCommissions: [FICommission!]!
    weekFunnels: [WeekFunnel!]!
    progressGoal: ProgressGoal
    performance: SalesControlBoardPerformance
    monthlyAverageTarget: ModelAverageTarget
}

type ModelAverageTarget {
    orderIntakesMonthlyModelAverageTarget: Float!
    retailsMonthlyModelAverageTarget: Float!
    testDriveMonthlyModelAverageTarget: Float!
    salesOfferMonthlyModelAverageTarget: Float!
    leadsMonthlyModelAverageTarget: Float!
}

type SalesControlBoardItemPerformance {
    salesConsultantName: String!
    modelName: String!
    retail: BasePerformance!
    orderIntake: BasePerformance!
    lead: SalesControlBoardLeadPerformance!
    salesOffer: BasePerformance!
    testDrive: BasePerformance!
}

type BasePerformance {
    actual: Float!
    target: Float!
    dev: Float!
}

type SalesControlBoardLeadPerformance {
    target: Float!
    actual: Float!
    dev: Float!
    lio: Float!
    ulr: Float!
    olr: Float!
    pi: Float!
}

type SalesControlBoardPerformance {
    total: [SalesControlBoardTotalPerformance!]!
    model: [SalesControlBoardModelTotalPerformance!]!
    items: [SalesControlBoardItemPerformance!]!
}

type SalesControlBoardModelTotalPerformance {
    modelName: String!
    totalLeadTarget: Float!
    totalLeadActual: Float!
    totalLeadDev: Float!
    totalOrderIntakeTarget: Float!
    totalOrderIntakeActual: Float!
    totalOrderIntakeDev: Float!
    totalRetailTarget: Float!
    totalRetailActual: Float!
    totalRetailDev: Float!
    totalSalesOfferTarget: Float!
    totalSalesOfferActual: Float!
    totalSalesOfferDev: Float!
    totalTestDriveTarget: Float!
    totalTestDriveActual: Float!
    totalTestDriveDev: Float!
    totalLio: Float!
    totalPi: Float!
    totalUlr: Float!
    totalOlr: Float!
}

type SalesControlBoardTotalPerformance {
    salesConsultantName: String!
    totalLeadTarget: Float!
    totalLeadActual: Float!
    totalLeadDev: Float!
    totalOrderIntakeTarget: Float!
    totalOrderIntakeActual: Float!
    totalOrderIntakeDev: Float!
    totalRetailTarget: Float!
    totalRetailActual: Float!
    totalRetailDev: Float!
    totalSalesOfferTarget: Float!
    totalSalesOfferActual: Float!
    totalSalesOfferDev: Float!
    totalTestDriveTarget: Float!
    totalTestDriveActual: Float!
    totalTestDriveDev: Float!
    totalLio: Float!
    totalPi: Float!
    totalUlr: Float!
    totalOlr: Float!
}

type LeadRatio {
    numerator: Int!
    denominator: Int!
}

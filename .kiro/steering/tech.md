# Technology Stack

## Core Technologies
- **Runtime**: Node.js 20.2+ 
- **Language**: TypeScript with strict configuration
- **Package Manager**: Yarn 4.2.1 (<PERSON>)
- **Frontend**: React 18.3+ with TypeScript
- **Backend**: Express.js with Apollo Server
- **Database**: MongoDB 6.0+ with encryption support (CSFLE)
- **Cache/Queue**: Redis 7.0+ with Bull queues
- **GraphQL**: Apollo Server/Client with code generation

## Build System
- **Bundler**: Custom Webpack 5 configuration
- **Transpiler**: SWC (preferred) or Babel
- **CSS**: Less with CSS Modules
- **Development**: Hot reload with webpack-dev-middleware

## Key Libraries
- **UI Framework**: Ant Design 4.24+ with Porsche Design System
- **Styling**: styled-components 5.3+
- **State Management**: Apollo Client cache
- **Forms**: Formik with validation
- **Date/Time**: Day.js (moment.js replacement)
- **File Processing**: Sharp, ExcelJS, PDF-lib
- **Authentication**: JWT with OIDC support
- **Monitoring**: Sentry, Prometheus metrics

## Development Tools
- **Testing**: Jest with SWC, Cypress for E2E
- **Linting**: ESLint with Airbnb config + TypeScript
- **Formatting**: Prettier
- **Git Hooks**: Husky with commitlint
- **CI/CD**: CircleCI with Docker deployment

## Common Commands

### Development
```bash
yarn dev                    # Start development server
yarn dev:fs                 # Development with filesystem cache
yarn dev:istanbul           # Development with coverage
```

### Building
```bash
yarn build                  # Production build
yarn build:istanbul         # Build with coverage instrumentation
```

### Code Generation
```bash
yarn generate:schema        # Generate GraphQL types
yarn generate:indexes       # Generate API indexes
yarn generate:dataSets      # Generate test datasets
```

### Quality Assurance
```bash
yarn lint                   # Run ESLint
yarn lint:fix               # Fix linting issues
tsc                         # TypeScript compilation check
yarn test                   # Run Jest tests
```

## Environment Setup
- Uses Docker Compose for local services (MongoDB, Redis, MinIO, MailPit)
- Environment variables loaded from `.env.development` files
- Supports multiple deployment environments via Terraform
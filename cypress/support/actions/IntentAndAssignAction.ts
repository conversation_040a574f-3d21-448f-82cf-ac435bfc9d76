import dayjs from 'dayjs';
import urljoin from 'url-join';
import { DropdownValue } from './shared';

class IntentAndAssignAction {
    accessLeadPage(url: string, leadSuiteId: string) {
        cy.visit(urljoin(url, 'leads', leadSuiteId));
        cy.waitForAssets();
        cy.waitForGraphQL();
    }

    validateDropdown(name: string, expectedOptions: DropdownValue[]) {
        cy.get(`select[name='${name}']`)
            .invoke('val')
            .then(options => {
                expect(expectedOptions.flatMap(option => option.value)).includes(options);
            });
    }

    getDropdownValue(name: string, expectedOptions: DropdownValue[]) {
        cy.get(`select[name='${name}']`)
            .invoke('val')
            .then(options => {
                expect(expectedOptions.flatMap(option => option.value)).includes(options);
            });
    }

    selectDropdown(name: string, input: number) {
        cy.get(`select[name='${name}']`).select(input, { force: true });
    }

    checkValidOptions(name: string, expectedOptions: DropdownValue[]) {
        cy.get(`select[name='${name}'] option`).each($option => {
            if ($option.val() === '') {
                return; // Skip the empty option
            }

            const optionValue = $option.val();
            expect(expectedOptions.flatMap(option => option.value)).include(optionValue);
        });
    }

    selectDropdownByText(name: string, optionText: string) {
        cy.get(`select[name='${name}']`).select(optionText, { force: true });
    }

    validateDateValue(dateCy: string, expectedValue: string) {
        cy.get(`input[data-cy="${dateCy}"]`)
            .invoke('val')
            .then(value => {
                expect(value).to.equal(expectedValue);
            });
    }

    fillDatePicker(dateCy: string, value: string) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        // Step 1: Parse the input date into a standardized format (YYYY-MM-DD)
        const parsedDate = dayjs(value);
        if (!parsedDate.isValid()) {
            throw new Error(`Invalid date format: ${value}`);
        }
        const targetYear = parsedDate.year();
        const targetMonth = parsedDate.month();
        const targetDate = parsedDate.format('YYYY-MM-DD');

        // Open the date picker
        cy.get(`[data-cy="${dateCy}"]`).click();

        // Step 2: Navigate to the correct year
        cy.get('.ant-picker-header-view .ant-picker-year-btn').then($yearBtn => {
            const currentYear = parseInt($yearBtn.text(), 10);

            if (currentYear > targetYear) {
                const steps = currentYear - targetYear;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-super-prev-btn').click(); // Navigate to previous year
                }
            } else if (currentYear < targetYear) {
                const steps = targetYear - currentYear;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-super-next-btn').click(); // Navigate to next year
                }
            }
        });

        // Step 3: Navigate to the correct month
        cy.get('.ant-picker-header-view .ant-picker-month-btn').then($monthBtn => {
            const currentMonth = $monthBtn.text();
            const currentMonthIndex = months.indexOf(currentMonth);

            if (currentMonthIndex > targetMonth) {
                const steps = currentMonthIndex - targetMonth;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-prev-btn').click(); // Navigate to previous month
                }
            } else if (currentMonthIndex < targetMonth) {
                const steps = targetMonth - currentMonthIndex;
                for (let i = 0; i < steps; i++) {
                    cy.get('.ant-picker-header-next-btn').click(); // Navigate to next month
                }
            }
        });

        // Step 4: Select the target date
        cy.get('.ant-picker-date-panel')
            .find(`td[title="${targetDate}"]`) // Find the specific date cell
            .click(); // Select the date
    }

    checkSelectFieldsErrorMessage(name: string, errorMessage: string) {
        cy.get(`select[name='${name}']`)
            .parent()
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }

    checkDateFieldsErrorMessage(dateCy: string, errorMessage: string) {
        cy.get(`[data-cy="${dateCy}"]`)
            .parents('div.ant-form-item-control')
            .should(error => {
                expect(error).contain(errorMessage);
            });
    }

    isInContactDetailPage() {
        cy.get('[data-cy="lead-detail-header"]').should('contain.text', 'Contact Details').and('be.visible');
    }

    openIntentAndAssignModal() {
        cy.get('[data-cy="intent-and-assign-action"]').click();
    }

    intentAndAssignModalShouldShow() {
        cy.get('[data-cy="intent-and-assign-modal"]').shadow().find('dialog').and('have.attr', 'open');
    }

    intentAndAssignModalShouldHide() {
        cy.get('[data-cy="intent-and-assign-modal"]').shadow().find('dialog').and('have.attr', 'inert');
    }

    submitIntentAndAssignForm(isWaitForGraphQL = true) {
        cy.get('[data-cy="intent-and-assign-submit-button"] button').click({ force: true });
        isWaitForGraphQL && cy.waitForGraphQL();
    }

    cancelIntentAndAssignForm() {
        cy.get('[data-cy="intent-and-assign-cancel-button"]').shadow().find('button').click({ force: true });
    }

    verifySelectedVehicleDetail(modelName: string, variantName: string, variantId: string) {
        cy.get('div[class*="ContentItem__Container-sc-"]  h3[class*="ContentItem__ContentItemTitle-sc-"]')
            .contains('Vehicle of Interest')
            .should('be.visible')
            .click();

        cy.get('div[data-cy="vehicle-model"]')
            .find('input[type="text"]')
            .then($inputs => {
                const values = Array.from($inputs).map(input => (input as HTMLInputElement).value);
                expect(values).to.include(modelName);
            });

        cy.get('div[data-cy="vehicle-name"]')
            .find('input[type="text"]')
            .then($inputs => {
                const values = Array.from($inputs).map(input => (input as HTMLInputElement).value);
                expect(values).to.include(variantName);
            });

        cy.get('div[data-cy="vehicle-identifier"]')
            .find('input[type="text"]')
            .then($inputs => {
                const values = Array.from($inputs).map(input => (input as HTMLInputElement).value);
                expect(values).to.include(variantId);
            });
    }
}

export default IntentAndAssignAction;

class BaseActions {
    checkIfElementExists(dataCy: string) {
        cy.get(`[data-cy="${dataCy}"]`).should('exist');
    }

    clickElement(dataCy: string) {
        cy.get(`[data-cy="${dataCy}"]`).click();
    }

    checkIfInputExists(name: string) {
        cy.get(`input[name="${name}"]`).should('exist');
    }

    selectDropdown(name: string, input: string | number) {
        cy.get(`select[name='${name}']`).select(input, { force: true });
    }

    fillAndValidateInputByName(name: string, input: string) {
        const inputName = `input[name='${name}']`;
        cy.get(inputName).clear().type(input).should('be.visible').should('have.value', input);
    }
}

export default BaseActions;

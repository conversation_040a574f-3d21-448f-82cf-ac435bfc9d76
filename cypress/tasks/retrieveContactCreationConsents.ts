import { ConsentFeatureType } from '../../src/server/database/documents/ConsentsAndDeclarations';
import { ModuleType } from '../../src/server/database/documents/modules';
import { E2EConsents } from '../snapshots/datasets/consentsAndDeclarations/shared';
import { getCurrentSnapshots } from './loadSnapshots';

const task = async ({ agreementModuleE2eId }: { agreementModuleE2eId: string }): Promise<E2EConsents[]> => {
    const agreementModule = await getCurrentSnapshots().modules.getModule(
        agreementModuleE2eId,
        ModuleType.ConsentsAndDeclarations
    );

    const consents = await getCurrentSnapshots().consentsAndDeclarations.getAllConsents();

    return consents.filter(
        consent =>
            consent.moduleId.equals(agreementModule._id) &&
            consent.featurePurpose.featureId.equals(agreementModule._id) &&
            consent.featurePurpose.type === ConsentFeatureType.Module &&
            consent.isActive &&
            !consent.isDeleted
    );
};

export default task;

import { ObjectId } from 'mongodb';
import urljoin from 'url-join';
import { FinderVehicleCondition } from '../../src/app/api/types';
import config from '../../src/server/core/config';
import { IntentType, PurposeOfVisit } from '../../src/server/database/documents/Lead/shared';
import { EndpointType } from '../../src/server/database/documents/Router';
import { ModuleType } from '../../src/server/database/documents/modules';
import { E2ELead } from '../snapshots/datasets/baseLeads';
import { E2ELaunchPadModule } from '../snapshots/datasets/modules/baseLaunchPadModule';
import { E2EVariant } from '../snapshots/datasets/vehicles/baseVariant';
import { getCurrentSnapshots } from './loadSnapshots';

const task = async ({
    routerE2eId,
    moduleE2eId,
    leadE2eId,
    rootUserGroupE2eId,
}: {
    routerE2eId: string;
    moduleE2eId: string;
    leadE2eId: string;
    rootUserGroupE2eId: string;
}): Promise<{
    url: string;
    lead: E2ELead;
    module: E2ELaunchPadModule;
    intentTypeOptions: { value: IntentType }[];
    purposeOfVisitOptions: { value: PurposeOfVisit }[];
    salesConsultantOptions: { value: ObjectId; name: string }[];
    vehicleOptions: { value: ObjectId; name: string; id: string }[];
    variantOptions: { value: ObjectId; name: string; id: string; price: number; parentId: ObjectId }[];
    vehicleConditionOptions: { value: FinderVehicleCondition }[];
}> => {
    const router = await getCurrentSnapshots().routers.getRouter(routerE2eId);
    const module = (await getCurrentSnapshots().modules.getModule(
        moduleE2eId,
        ModuleType.LaunchPadModule
    )) as E2ELaunchPadModule;
    const lead = await getCurrentSnapshots().leads.getLead(leadE2eId);

    const usergroup = await getCurrentSnapshots().userGroups.getUserGroup(rootUserGroupE2eId);
    const users = await getCurrentSnapshots().users.getAll();

    const salesConsultantOptions = usergroup.userIds.map(userId => {
        const user = users.find(user => user._id === userId);

        return {
            value: userId,
            name: user?.displayName || '',
        };
    });

    const intentTypeOptions = [{ value: IntentType.ScheduledAppointment }, { value: IntentType.WalkIn }];
    const purposeOfVisitOptions = [
        {
            value: PurposeOfVisit.NewVehicleEnquiryPurchase,
        },
        {
            value: PurposeOfVisit.PreOwnedVehicleEnquiryPurchase,
        },
        {
            value: PurposeOfVisit.VehicleConfigurationConsultation,
        },
        {
            value: PurposeOfVisit.InterestInTestDrive,
        },
        {
            value: PurposeOfVisit.VehicleCollectionDelivery,
        },
        {
            value: PurposeOfVisit.BrandExperienceEventAttendance,
        },
        {
            value: PurposeOfVisit.AccessoriesMerchandiseShopping,
        },
        { value: PurposeOfVisit.TradeInEvaluation },
    ];

    const variantSuiteIds = module.dealerVehicles.flatMap(vehicle => vehicle.vehicleSuiteIds);
    const vehicles = await getCurrentSnapshots().vehicles.getAll();
    const variants = vehicles.filter(vehicle => variantSuiteIds.some(id => id === vehicle._versioning.suiteId));

    const vehicleOptions = variants
        .map(variant => {
            const model = vehicles.find(vehicle => vehicle._id.equals((variant as E2EVariant).modelId));
            if (!model) {
                return null;
            }

            return {
                value: model._id,
                name: model.name.defaultValue,
                id: model.identifier,
            };
        })
        .filter(v => v !== null);

    const variantOptions =
        variants.map(variant => ({
            parentId: (variant as E2EVariant).modelId,
            value: variant._id,
            name: variant.name.defaultValue,
            id: variant.identifier,
            price: (variant as E2EVariant).vehiclePrice,
        })) ?? [];

    const vehicleConditionOptions = [{ value: FinderVehicleCondition.New }, { value: FinderVehicleCondition.Preowned }];

    const endpoint = router.endpoints.find(
        endpoint =>
            endpoint._type === EndpointType.LaunchPadApplicationEntrypoint &&
            endpoint.launchPadApplicationModuleId.equals(module._id)
    );

    if (!endpoint) {
        throw new Error('E2EEndpoint not found');
    }

    return {
        url: urljoin(`${config.protocol}://${router.hostname}:3000`, router.pathname, endpoint?.pathname),
        lead,
        module,
        intentTypeOptions,
        purposeOfVisitOptions,
        salesConsultantOptions,
        vehicleOptions,
        variantOptions,
        vehicleConditionOptions,
    };
};

export default task;

import urljoin from 'url-join';
import config from '../../src/server/core/config';
import { EndpointType } from '../../src/server/database/documents/Router';
import { ModuleType } from '../../src/server/database/documents/modules';
import { E2ELead } from '../snapshots/datasets/baseLeads';
import type { E2EUser } from '../snapshots/datasets/baseUsers';
import { E2ELaunchPadModule } from '../snapshots/datasets/modules/baseLaunchPadModule';
import { getCurrentSnapshots } from './loadSnapshots';

const task = async ({
    routerE2eId,
    moduleE2eId,
    leadE2eId,
    rootUserE2eId,
}: {
    routerE2eId: string;
    moduleE2eId: string;
    leadE2eId: string;
    rootUserE2eId: string;
}): Promise<{
    url: string;
    lead: E2ELead;
    module: E2ELaunchPadModule;
    rootUser: E2EUser;
}> => {
    const router = await getCurrentSnapshots().routers.getRouter(routerE2eId);
    const module = (await getCurrentSnapshots().modules.getModule(
        moduleE2eId,
        ModuleType.LaunchPadModule
    )) as E2ELaunchPadModule;
    const lead = await getCurrentSnapshots().leads.getLead(leadE2eId);

    const rootUser = await getCurrentSnapshots().users.getUser(rootUserE2eId);

    const endpoint = router.endpoints.find(
        endpoint =>
            endpoint._type === EndpointType.LaunchPadApplicationEntrypoint &&
            endpoint.launchPadApplicationModuleId.equals(module._id)
    );

    if (!endpoint) {
        throw new Error('E2EEndpoint not found');
    }

    return {
        url: urljoin(`${config.protocol}://${router.hostname}:3000`, router.pathname, endpoint?.pathname),
        lead,
        module,
        rootUser,
    };
};

export default task;

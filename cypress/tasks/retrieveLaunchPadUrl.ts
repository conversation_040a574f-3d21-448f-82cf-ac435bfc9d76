import urljoin from 'url-join';
import config from '../../src/server/core/config';
import { EndpointType } from '../../src/server/database/documents/Router';
import { ModuleType } from '../../src/server/database/documents/modules';
import { E2ELaunchPadModule } from '../snapshots/datasets/modules/baseLaunchPadModule';
import { getCurrentSnapshots } from './loadSnapshots';

const task = async ({
    routerE2eId,
    moduleE2eId,
}: {
    routerE2eId: string;
    moduleE2eId: string;
}): Promise<{
    url: string;
}> => {
    const router = await getCurrentSnapshots().routers.getRouter(routerE2eId);
    const module = (await getCurrentSnapshots().modules.getModule(
        moduleE2eId,
        ModuleType.LaunchPadModule
    )) as E2ELaunchPadModule;

    const endpoint = router.endpoints.find(
        endpoint =>
            endpoint._type === EndpointType.LaunchPadApplicationEntrypoint &&
            endpoint.launchPadApplicationModuleId.equals(module._id)
    );

    if (!endpoint) {
        throw new Error('E2EEndpoint not found');
    }

    return {
        url: urljoin(`${config.protocol}://${router.hostname}:3000`, router.pathname, endpoint?.pathname),
    };
};

export default task;

import { ObjectId } from 'mongodb';
import urljoin from 'url-join';
import config from '../../src/server/core/config';
import { EndpointType } from '../../src/server/database/documents/Router';
import { ModuleType } from '../../src/server/database/documents/modules';
import { E2ELead } from '../snapshots/datasets/baseLeads';
import type { E2EUser } from '../snapshots/datasets/baseUsers';
import { E2ELaunchPadModule } from '../snapshots/datasets/modules/baseLaunchPadModule';
import { E2EVariant } from '../snapshots/datasets/vehicles/baseVariant';
import { getCurrentSnapshots } from './loadSnapshots';

const task = async ({
    routerE2eId,
    moduleE2eId,
    leadE2eId,
    rootUserE2eId,
}: {
    routerE2eId: string;
    moduleE2eId: string;
    leadE2eId: string;
    rootUserE2eId: string;
}): Promise<{
    url: string;
    lead: E2ELead;
    module: E2ELaunchPadModule;
    variantOptions: { value: ObjectId; name: string; id: string; price: number; parentId: ObjectId }[];
    rootUser: E2EUser;
}> => {
    const router = await getCurrentSnapshots().routers.getRouter(routerE2eId);
    const module = (await getCurrentSnapshots().modules.getModule(
        moduleE2eId,
        ModuleType.LaunchPadModule
    )) as E2ELaunchPadModule;
    const lead = await getCurrentSnapshots().leads.getLead(leadE2eId);

    const rootUser = await getCurrentSnapshots().users.getUser(rootUserE2eId);

    const variantSuiteIds = module.dealerVehicles.flatMap(vehicle => vehicle.vehicleSuiteIds);
    const vehicles = await getCurrentSnapshots().vehicles.getAll();
    const variants = vehicles.filter(vehicle => variantSuiteIds.some(id => id === vehicle._versioning.suiteId));

    const variantOptions =
        variants.map(variant => ({
            parentId: (variant as E2EVariant).modelId,
            value: variant._id,
            name: variant.name.defaultValue,
            id: variant.identifier,
            price: (variant as E2EVariant).vehiclePrice,
        })) ?? [];

    const endpoint = router.endpoints.find(
        endpoint =>
            endpoint._type === EndpointType.LaunchPadApplicationEntrypoint &&
            endpoint.launchPadApplicationModuleId.equals(module._id)
    );

    if (!endpoint) {
        throw new Error('E2EEndpoint not found');
    }

    return {
        url: urljoin(`${config.protocol}://${router.hostname}:3000`, router.pathname, endpoint?.pathname),
        lead,
        module,
        variantOptions,
        rootUser,
    };
};

export default task;

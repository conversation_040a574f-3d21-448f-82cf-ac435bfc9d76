import dayjs from 'dayjs';
import type { E2ELanguage } from '../../../snapshots/datasets/baseLanguagePacks';
import ConsentsActions from '../../../support/actions/ConsentsActions';
import ShowroomVisitAction from '../../../support/actions/ShowroomVisitAction';
import { importSnapshot, retrieveFormCreationValueTranslation } from './shared';

describe('Showroom Visit Booking', () => {
    beforeEach(() => {
        cy.task('apv:loadSnapshots', importSnapshot);
        cy.readyForNetworkIdling();
        cy.signIn('root');
    });

    const action = new ShowroomVisitAction();

    const now = dayjs();
    const appointmentDate = action.getAppointmentDate(now);

    it('Scenario: Successfully create a showroom visit appointment', () => {
        let baseLanguage: E2ELanguage;

        cy.task('apv:getLanguage', { languagePackE2eId: 'rootLanguage' }).then(async (language: any) => {
            baseLanguage = language;
        });

        cy.task('apv:retrieveShowroomVisitCreationData', {
            moduleE2eId: 'showroomVisitLaunchPadModule',
            routerE2eId: 'showroomVisitLaunchpadRouter',
            leadE2eId: 'leadForShowroomVisit',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('showroom-visit-action');

            // Click the Showroom Visit button
            action.clickElement('showroom-visit-action');

            // Check if model appears
            action.modalShouldShow();

            // Check if the required fields exists
            action.checkIfElementExists('form-date-picker-showroomVisitDate');
            action.checkIfSelectExists('showroomVisitTime');

            // Check if the Create/Cancel buttons exists
            action.checkIfElementExists('create-showroom-visit-button');
            action.checkIfElementExists('cancel-showroom-visit-creation-button');

            // Fill in the form
            action.fillDatePicker('form-date-picker-showroomVisitDate', appointmentDate);
            action.selectDropdown('showroomVisitTime', 1);

            cy.task('apv:retrieveShowroomVisitCreationConsents', {
                agreementModuleE2eId: 'rootAgreementModule',
            }).then(async (consents: any) => {
                const consentActions = new ConsentsActions();
                for (let x = 0; x < consents.length; x++) {
                    if (consents[x].__e2e__.id === 'displayInShowroomVisitCreationConsent1') {
                        consentActions.fillCheckbox(consents[x], true, x, baseLanguage);
                    }
                }
            });

            // submit the form
            action.submitModalForm();

            // Verify that the showroom visit detail page is displayed
            action.isInDetailPage();

            // Verify the lead detail
            const identifierPrefix = `P${now.format('YY')}${(now.month() + 1).toString().padStart(2, '0')}`;

            const createdAt = now.format('DD MMM YYYY');

            action.verifyFieldContainValue('appointment-identifier', identifierPrefix);
            action.verifyField('cap-lead-id', '0000654858');
            action.verifyField('appointment-status', 'Booking Confirmed');
            action.verifyField('appointment-type', 'Showroom Visit');
            action.validateDateValue('form-date-picker-appointmentDate', appointmentDate);

            action.verifyField('appointment-created-at', createdAt);
            action.verifyField('appointment-dealer', 'Root Dealer A');
            action.verifyField('appointment-created-by', rootUser.displayName);

            action.validateDropdown('visitAppointmentStage.assigneeId', rootUser._id.toString());
            action.verifyField('appointment-module', module.displayName);
            action.verifyFieldContainValue('appointment-updated-at', createdAt);

            // Validate buttons
            action.checkIfElementExists('complete-showroom-visit-action');
            action.checkIfElementExists('void-showroom-visit-action');

            // Validate tabs
            action.validateTabs();
        });
    });

    it('Scenario: Cancel showroom visit creation', () => {
        cy.task('apv:retrieveShowroomVisitCreationData', {
            moduleE2eId: 'showroomVisitLaunchPadModule',
            routerE2eId: 'showroomVisitLaunchpadRouter',
            leadE2eId: 'leadForShowroomVisit',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('showroom-visit-action');

            // Click the Showroom Visit button
            action.clickElement('showroom-visit-action');

            // Check if model appears
            action.modalShouldShow();

            // Click the Cancel button
            action.cancelModalForm();

            // Check if model hides
            action.modalShouldHide();
        });
    });

    it('Scenario: Validate required fields in showroom visit modal', () => {
        const formValue = retrieveFormCreationValueTranslation();

        cy.task('apv:retrieveShowroomVisitCreationData', {
            moduleE2eId: 'showroomVisitLaunchPadModule',
            routerE2eId: 'showroomVisitLaunchpadRouter',
            leadE2eId: 'leadForShowroomVisit',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('showroom-visit-action');

            // Click the Showroom Visit button
            action.clickElement('showroom-visit-action');

            // Check if model appears
            action.modalShouldShow();

            // Check if the required fields exists
            action.checkIfElementExists('form-date-picker-showroomVisitDate');
            action.checkIfSelectExists('showroomVisitTime');

            // Check if the Create/Cancel buttons exists
            action.checkIfElementExists('create-showroom-visit-button');
            action.checkIfElementExists('cancel-showroom-visit-creation-button');

            // Submit the form without filling any fields
            action.submitModalForm(false);

            action.checkDateFieldErrorMessage('form-date-picker-showroomVisitDate', formValue.formErrors.required);

            // Check if model still remains open
            action.modalShouldShow();

            // Fill in appointment date
            action.fillDatePicker('form-date-picker-showroomVisitDate', appointmentDate);

            action.submitModalForm(false);
            action.checkSelectFieldErrorMessage('showroomVisitTime', formValue.formErrors.required);

            // Check if model still remains open
            action.modalShouldShow();
        });
    });
});

import dayjs from 'dayjs';
import TestDriveAction from '../../../support/actions/TestDriveAction';
import { importSnapshot, retrieveTestDriveCreationValueTranslation } from './shared';

describe('Scenario: Successfully create a test drive appointment', () => {
    beforeEach(() => {
        cy.task('apv:loadSnapshots', importSnapshot);
        cy.readyForNetworkIdling();
        cy.signIn('root');
    });

    const action = new TestDriveAction();

    const now = dayjs();
    const appointmentDate = action.getAppointmentDate(now);

    it('Successfully create a new test drive appointment with all required fields', () => {
        const customerValue = retrieveTestDriveCreationValueTranslation();

        cy.task('apv:retrieveTestDriveCreationData', {
            moduleE2eId: 'testDriveCreationLaunchPadModule',
            routerE2eId: 'testDriveCreationLaunchpadRouter',
            leadE2eId: 'leadForTestDrive',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, variantOptions, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('test-drive-action');

            // Click the Test Drivebutton
            action.clickElement('test-drive-action');

            // Check if model appears
            action.testDriveModalShouldShow();

            // Check if the required fields exists
            action.checkIfElementExists('form-date-picker-testDriveDate');
            action.checkIfSelectExists('testDriveTime');
            action.checkIfSelectExists('preferredVehicle');

            // check "Additional Customer Details" section
            action.checkIfSelectExists('customer.fields.Title.value');
            action.checkIfInputExists('customer.fields.FirstName.value');
            action.checkIfInputExists('customer.fields.LastName.value');
            action.checkIfInputExists('customer.fields.FullName.value');
            action.checkIfInputExists('customer.fields.Email.value');
            action.checkIfInputExists('customer.fields.Phone.value.value');
            action.checkIfInputExists('customer.fields.IdentityNumber.value');
            action.checkIfInputExists('customer.fields.Address.value');
            action.checkIfInputExists('customer.fields.UnitNumber.value');
            action.checkIfInputExists('customer.fields.PostalCode.value');
            action.checkIfSelectExists('customer.fields.Country.value');
            action.checkIfSelectExists('customer.fields.DrivingLicense.value.[0].type');

            // Check if the Create/Cancel buttons exists
            action.checkIfElementExists('create-test-drive-button');
            action.checkIfElementExists('cancel-test-drive-creation-button');

            // Fill in the test drive creation form
            const preferredVehicle = variantOptions[0];
            action.fillDatePicker('form-date-picker-testDriveDate', appointmentDate);
            action.selectDropdown('testDriveTime', 1);
            action.selectDropdown('preferredVehicle', preferredVehicle.value);

            action.selectDropdown('customer.fields.Title.value', customerValue.title);
            action.fillAndValidateInputByName('customer.fields.FirstName.value', customerValue.firstName);
            action.fillAndValidateInputByName('customer.fields.LastName.value', customerValue.lastName);
            action.fillAndValidateInputByName('customer.fields.FullName.value', customerValue.fullName);
            action.fillAndValidateInputByName('customer.fields.Email.value', customerValue.email);
            action.fillAndValidatePhoneField('Phone', customerValue.phone.value, customerValue.countryCode);
            action.fillAndValidateInputByName('customer.fields.IdentityNumber.value', customerValue.identityNumber);
            action.fillAndValidateInputByName('customer.fields.Address.value', customerValue.address);
            action.fillAndValidateInputByName('customer.fields.UnitNumber.value', customerValue.unitNumber);
            action.fillAndValidateInputByName('customer.fields.PostalCode.value', customerValue.postalCode);
            action.selectDropdown('customer.fields.Country.value', customerValue.countryValue);
            action.selectDropdown('customer.fields.DrivingLicense.value.[0].type', 'Not Applicable');

            // submit the form
            action.submitTestDriveModalForm();

            // Verify that the test drive detail page is displayed
            action.isInTestDriveDetailPage();

            // Verify the lead detail
            const identifierPrefix = `P${now.format('YY')}${(now.month() + 1).toString().padStart(2, '0')}`;

            const createdAt = now.format('DD MMM YYYY');

            action.verifyTestDriveFieldContainValue('appointment-identifier', identifierPrefix);
            action.verifyTestDriveField('cap-lead-id', '**********');
            action.verifyTestDriveField('appointment-status', 'Booking Confirmed');
            action.verifyTestDriveField('appointment-type', 'Test Drive');
            action.validateDateValue('form-date-picker-appointmentDate', appointmentDate);

            action.verifyTestDriveField('appointment-created-at', createdAt);
            action.verifyTestDriveField('appointment-dealer', 'Root Dealer A');
            action.verifyTestDriveField('appointment-created-by', rootUser.displayName);

            action.validateDropdown('appointmentStage.assigneeId', rootUser._id);
            action.verifyTestDriveField('appointment-module', module.displayName);
            action.verifyTestDriveFieldContainValue('appointment-updated-at', createdAt);

            action.verifySelectedVehicleDetail(preferredVehicle.name);

            // Validate buttons
            action.checkIfElementExists('start-test-drive-action');
            action.checkIfElementExists('void-appointment-action');

            // Validate tabs
            action.validateTabs();
        });
    });

    it('Scenario: Cancel test drive creation', () => {
        cy.task('apv:retrieveTestDriveCreationData', {
            moduleE2eId: 'testDriveCreationLaunchPadModule',
            routerE2eId: 'testDriveCreationLaunchpadRouter',
            leadE2eId: 'leadForTestDrive',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, variantOptions, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('test-drive-action');

            // Click the Test Drivebutton
            action.clickElement('test-drive-action');

            // Check if model appears
            action.testDriveModalShouldShow();

            // Click the Cancel button
            action.cancelTestDriveModalForm();

            // Check if model hides
            action.testDriveModalShouldHide();
        });
    });

    it('Scenario: Validate required fields in test drive modal', () => {
        const customerValue = retrieveTestDriveCreationValueTranslation();

        cy.task('apv:retrieveTestDriveCreationData', {
            moduleE2eId: 'testDriveCreationLaunchPadModule',
            routerE2eId: 'testDriveCreationLaunchpadRouter',
            leadE2eId: 'leadForTestDrive',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, variantOptions, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('test-drive-action');

            // Click the Test Drivebutton
            action.clickElement('test-drive-action');

            // Check if model appears
            action.testDriveModalShouldShow();

            // Check if the required fields exists
            action.checkIfElementExists('form-date-picker-testDriveDate');
            action.checkIfSelectExists('testDriveTime');
            action.checkIfSelectExists('preferredVehicle');

            // check "Additional Customer Details" section
            action.checkIfSelectExists('customer.fields.Title.value');
            action.checkIfInputExists('customer.fields.FirstName.value');
            action.checkIfInputExists('customer.fields.LastName.value');
            action.checkIfInputExists('customer.fields.FullName.value');
            action.checkIfInputExists('customer.fields.Email.value');
            action.checkIfInputExists('customer.fields.Phone.value.value');
            action.checkIfInputExists('customer.fields.IdentityNumber.value');
            action.checkIfInputExists('customer.fields.Address.value');
            action.checkIfInputExists('customer.fields.UnitNumber.value');
            action.checkIfInputExists('customer.fields.PostalCode.value');
            action.checkIfSelectExists('customer.fields.Country.value');
            action.checkIfSelectExists('customer.fields.DrivingLicense.value.[0].type');

            // Check if the Create/Cancel buttons exists
            action.checkIfElementExists('create-test-drive-button');
            action.checkIfElementExists('cancel-test-drive-creation-button');

            action.clearDateFieldValue('form-date-picker-testDriveDate');

            // Submit the form without filling any fields
            action.submitTestDriveModalForm(false);

            action.checkDateFieldErrorMessage('form-date-picker-testDriveDate', customerValue.formErrors.required);

            // Check if model still remains open
            action.testDriveModalShouldShow();

            // Fill in appointment date
            action.fillDatePicker('form-date-picker-testDriveDate', appointmentDate);

            action.submitTestDriveModalForm(false);
            action.checkSelectFieldErrorMessage('testDriveTime', customerValue.formErrors.required);

            // Check if model still remains open
            action.testDriveModalShouldShow();

            // Fill in appointment time
            action.selectDropdown('testDriveTime', 1);
            action.submitTestDriveModalForm(false);

            action.checkSelectFieldErrorMessage('preferredVehicle', customerValue.formErrors.required);

            // Check if model still remains open
            action.testDriveModalShouldShow();
        });
    });

    it('Scenario: Validate customer details required fields', () => {
        const customerValue = retrieveTestDriveCreationValueTranslation();

        cy.task('apv:retrieveTestDriveCreationData', {
            moduleE2eId: 'testDriveCreationLaunchPadModule',
            routerE2eId: 'testDriveCreationLaunchpadRouter',
            leadE2eId: 'leadForTestDrive',
            rootUserE2eId: 'root',
        }).then(async ({ url, lead, module, variantOptions, rootUser }: any) => {
            // Visit the lead page
            action.accessLeadPage(url, lead._versioning.suiteId.toString());

            // Should see the action button
            action.checkIfElementExists('test-drive-action');

            // Click the Test Drivebutton
            action.clickElement('test-drive-action');

            // Check if model appears
            action.testDriveModalShouldShow();

            // Check if the required fields exists
            action.checkIfElementExists('form-date-picker-testDriveDate');
            action.checkIfSelectExists('testDriveTime');
            action.checkIfSelectExists('preferredVehicle');

            // check "Additional Customer Details" section
            action.checkIfSelectExists('customer.fields.Title.value');
            action.checkIfInputExists('customer.fields.FirstName.value');
            action.checkIfInputExists('customer.fields.LastName.value');
            action.checkIfInputExists('customer.fields.FullName.value');
            action.checkIfInputExists('customer.fields.Email.value');
            action.checkIfInputExists('customer.fields.Phone.value.value');
            action.checkIfInputExists('customer.fields.IdentityNumber.value');
            action.checkIfInputExists('customer.fields.Address.value');
            action.checkIfInputExists('customer.fields.UnitNumber.value');
            action.checkIfInputExists('customer.fields.PostalCode.value');
            action.checkIfSelectExists('customer.fields.Country.value');
            action.checkIfSelectExists('customer.fields.DrivingLicense.value.[0].type');

            // Check if the Create/Cancel buttons exists
            action.checkIfElementExists('create-test-drive-button');
            action.checkIfElementExists('cancel-test-drive-creation-button');

            // Fill in the test drive creation form
            const preferredVehicle = variantOptions[0];
            action.fillDatePicker('form-date-picker-testDriveDate', appointmentDate);
            action.selectDropdown('testDriveTime', 1);
            action.selectDropdown('preferredVehicle', preferredVehicle.value);

            // Submit the form without filling any fields
            action.submitTestDriveModalForm(false);

            action.checkInputFieldErrorMessage('customer.fields.Email.value', customerValue.formErrors.required);

            // Check if model still remains open
            action.testDriveModalShouldShow();

            action.selectDropdown('customer.fields.Title.value', customerValue.title);
            action.fillAndValidateInputByName('customer.fields.FirstName.value', customerValue.firstName);
            action.fillAndValidateInputByName('customer.fields.LastName.value', customerValue.lastName);
            action.fillAndValidateInputByName('customer.fields.FullName.value', customerValue.fullName);
            action.fillAndValidateInputByName('customer.fields.Email.value', customerValue.email);
            action.fillAndValidatePhoneField('Phone', customerValue.phone.value, customerValue.countryCode);
            action.fillAndValidateInputByName('customer.fields.IdentityNumber.value', customerValue.identityNumber);
            action.fillAndValidateInputByName('customer.fields.Address.value', customerValue.address);
            action.fillAndValidateInputByName('customer.fields.UnitNumber.value', customerValue.unitNumber);
            action.fillAndValidateInputByName('customer.fields.PostalCode.value', customerValue.postalCode);
            action.selectDropdown('customer.fields.Country.value', customerValue.countryValue);
            action.selectDropdown('customer.fields.DrivingLicense.value.[0].type', 'Not Applicable');

            // submit the form
            action.submitTestDriveModalForm();

            // Verify that the test drive detail page is displayed
            action.isInTestDriveDetailPage();
        });
    });
});

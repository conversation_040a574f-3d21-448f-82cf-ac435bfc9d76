import { E2ELanguage } from '../../../snapshots/datasets/baseLanguagePacks';
import { importBaseDealers } from '../../../snapshots/datasets/dealers/shared';
import { importBaseLanguage } from '../../../snapshots/datasets/languagePacks/shared';
import { importBaseModule } from '../../../snapshots/datasets/modules/shared';
import { importBaseSetting } from '../../../snapshots/datasets/settings/shared';
import { importBaseVehicle } from '../../../snapshots/datasets/vehicles/shared';

export const testDriveCreationValues = {
    title: 'Ms',
    firstName: 'John',
    lastName: 'Doe',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    wrongEmail: '3ejb@email',
    countryCode: 'SG',
    phone: {
        value: '81234567',
    },
    wrongPhone: 'abcd',
    identityNumber: '*********',
    wrongIdentityNumber: 'Z1234567Z',
    address: 'test',
    unitNumber: '12-12',
    postalCode: '123123',
    invalidPostalCode: 'abcd',
    countryValue: 'Singapore',
    formErrors: {
        required: 'This field is required',
        invalidEmail: 'Invalid Email Address',
        invalidPhone: 'Invalid Number',
        invalidPostalCode: 'Invalid Postal Code',
        invalidIdentityNumber: 'Invalid Identity Number',
    },
};

export const importSnapshot = [
    ...importBaseSetting(['baseRuntimeSettings', 'baseEmailContext']),
    'baseLanguagePacks',
    ...importBaseLanguage(['japaneseLanguagePack']),
    'basePermissions',
    'baseUsers',
    'baseCompanies',
    ...importBaseDealers(['rootDealerB']),
    'baseDealers',
    ...importBaseModule([
        'baseAgreementModule',
        'baseSigningModule',
        'baseAppointmentModule',
        'testDriveCreation/testDriveCreationAppointmentModule',
        'baseCustomerModule',
        'baseVehicleModule',
        'baseVisitAppointmentModule',
        'testDriveCreation/testDriveCreationCustomerModule',
    ]),
    ...importBaseVehicle([
        'baseMake',
        'baseModel',
        'baseModelB',
        'baseModel718',
        'baseModel911',
        'baseModelTaycan',
        'baseVariant',
        'baseVariantB',
        'baseVariant718Boxster',
        'baseVariant911Carrera',
        'baseVariant911ST',
        'baseVariantTaycan4S',
    ]),
    'modules/testDriveCreation/testDriveCreationLaunchPadModule',
    'routers/testDriveCreationLaunchpadRouter',
    'baseCustomers',
    'leads/testDriveLead',
];

export const retrieveTestDriveCreationValueTranslation = (language?: E2ELanguage) => ({
    ...testDriveCreationValues,
    formErrors: {
        required: language?.translations?.common?.formErrors?.required ?? testDriveCreationValues.formErrors.required,
        invalidEmail:
            language?.translations?.common?.formErrors?.invalidEmail ?? testDriveCreationValues.formErrors.invalidEmail,
        invalidPhone:
            language?.translations?.common?.formErrors?.invalidPhone ?? testDriveCreationValues.formErrors.invalidPhone,
        invalidPostalCode:
            language?.translations?.common?.formErrors?.invalidPostalCode ??
            testDriveCreationValues.formErrors.invalidPostalCode,
        invalidIdentityNumber:
            language?.translations?.common?.formErrors?.invalidIdentityNumber ??
            testDriveCreationValues.formErrors.invalidIdentityNumber,
    },
});

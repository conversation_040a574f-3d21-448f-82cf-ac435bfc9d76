import ConsentsActions from '../../../support/actions/ConsentsActions';
import ContactCreationAction from '../../../support/actions/ContactCreationAction';
import { importSnapshot, retrieveContactCreationValueTranslation } from './shared';

describe('Complete Launchpad Contact Creation Feature', () => {
    beforeEach(() => {
        cy.task('apv:loadSnapshots', importSnapshot);
        cy.readyForNetworkIdling();
        cy.signIn('root');
    });

    it('Successfully create a new contact with all required fields', () => {
        const action = new ContactCreationAction();
        const customerValue = retrieveContactCreationValueTranslation();

        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Visit the launchpad page
            action.accessLaunchPadPage(url);

            // Should see the Create Contact button
            action.checkIfElementExists('create-contact-action');

            // Click the Create Contact button
            action.clickElement('create-contact-action');

            // Check if model appears
            action.checkIfElementExists('bp-search-modal');
            action.shouldSeeCreateContactModal();

            // Check if the search modal is shown
            action.checkIfElementExists('search-cap-customer-form');

            // check if search modal is shown
            action.checkModalHeader('Create/Search Contact');

            // Check if the query input exists
            action.checkIfInputExists('query');

            // Check if the select button exists
            action.checkIfElementExists('select-business-partner-button');

            // Check if the Create New button exists
            action.checkIfElementExists('create-new-button');

            // Click the Create New button
            action.clickElement('create-new-button');

            // Check if the modal header is correct
            action.checkModalHeader('Create New Contact');

            // Fill in the contact creation form
            action.selectDropdown('customer.fields.Title.value', customerValue.title);

            action.fillAndValidateInput('FirstName', customerValue.firstName);
            action.fillAndValidateInput('LastName', customerValue.lastName);

            action.fillAndValidateInput('Email', customerValue.email);

            action.fillAndValidatePhoneField('Phone', customerValue.phone.value, customerValue.countryCode);
        });

        // Fill in the consents checkboxes
        cy.task('apv:retrieveContactCreationConsents', {
            agreementModuleE2eId: 'rootAgreementModule',
        }).then(async (consents: any) => {
            const consentActions = new ConsentsActions();
            for (let x = 0; x < consents.length; x++) {
                consentActions.fillCheckbox(consents[x], true, x);
            }
        });

        // Submit the contact creation form
        action.submitCreateContact(false);

        // Verify that the contact detail page is displayed
        action.isInContactDetailPage();

        // Verify the notification message
        action.verifyNotificationMessage('New contact successfully created');

        // Verify the lead detail
        action.verifyLeadDetail('Pending Qualify', `${customerValue.firstName} ${customerValue.lastName}`);
    });

    it('Attempt to create contact without required fields', () => {
        const action = new ContactCreationAction();
        const customerValue = retrieveContactCreationValueTranslation();

        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Visit the launchpad page
            action.accessLaunchPadPage(url);

            // Click the Create Contact button
            action.clickElement('create-contact-action');

            // Click the Create New button
            action.clickElement('create-new-button');

            // Check if the modal header is correct
            action.checkModalHeader('Create New Contact');

            // wait for form to load
            action.checkIfElementExists('newContactId');

            // Submit the form without filling required fields
            action.submitCreateContact(false);

            // Check if the error messages are displayed for required fields
            action.checkFieldsErrorMessage('FirstName', customerValue.formErrors.required);
            action.checkFieldsErrorMessage('LastName', customerValue.formErrors.required);
            action.checkFieldsErrorMessage('Email', customerValue.formErrors.required);
            action.checkPhoneFieldsErrorMessage('Phone', customerValue.formErrors.invalidPhone);

            // Check if model still appears
            action.shouldSeeCreateContactModal();

            // Check if the modal header is correct
            action.checkModalHeader('Create New Contact');
        });
    });

    it('Create contact with all optional fields filled', () => {
        const action = new ContactCreationAction();
        const customerValue = retrieveContactCreationValueTranslation();

        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Visit the launchpad page
            action.accessLaunchPadPage(url);

            // Should see the Create Contact button
            action.checkIfElementExists('create-contact-action');

            // Click the Create Contact button
            action.clickElement('create-contact-action');

            // Check if model appears
            action.shouldSeeCreateContactModal();

            // Check if the search modal is shown
            action.checkIfElementExists('search-cap-customer-form');

            // Check if the dialog appears
            action.shouldSeeCreateContactModal();

            // Check if the query input exists
            action.checkIfInputExists('query');

            // Check if the select button exists
            action.checkIfElementExists('select-business-partner-button');

            // Check if the Create New button exists
            action.checkIfElementExists('create-new-button');

            // Click the Create New button
            action.clickElement('create-new-button');

            // Check if the modal header is correct
            action.checkModalHeader('Create New Contact');

            // Fill in the contact creation form
            action.selectDropdown('customer.fields.Title.value', customerValue.title);

            action.fillAndValidateInput('FirstName', customerValue.firstName);
            action.fillAndValidateInput('LastName', customerValue.lastName);

            action.fillAndValidateInput('Email', customerValue.email);

            action.fillAndValidatePhoneField('Phone', customerValue.phone.value, customerValue.countryCode);

            action.fillBirthday('Birthday', customerValue.birthday);
            action.fillAndValidateInput('Address', customerValue.address);
            action.fillAndValidateInput('UnitNumber', customerValue.unitNumber);

            action.fillAndValidateInput('PostalCode', customerValue.postalCode);
        });

        // Fill in the consents checkboxes
        cy.task('apv:retrieveContactCreationConsents', {
            agreementModuleE2eId: 'rootAgreementModule',
        }).then(async (consents: any) => {
            const consentActions = new ConsentsActions();
            for (let x = 0; x < consents.length; x++) {
                consentActions.fillCheckbox(consents[x], true, x);
            }
        });

        // Submit the contact creation form
        action.submitCreateContact(false);

        // Verify that the contact detail page is displayed
        action.isInContactDetailPage();

        // Verify the notification message
        action.verifyNotificationMessage('New contact successfully created');

        // Verify the lead detail
        action.verifyLeadDetail('Pending Qualify', `${customerValue.firstName} ${customerValue.lastName}`);
    });

    it('Cancel contact creation from initial modal', () => {
        const action = new ContactCreationAction();

        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Visit the launchpad page
            action.accessLaunchPadPage(url);

            // Should see the Create Contact button
            action.checkIfElementExists('create-contact-action');

            // Click the Create Contact button
            action.clickElement('create-contact-action');

            // Check if model appears
            action.shouldSeeCreateContactModal();

            // Check if the search modal is show
            action.checkIfElementExists('search-cap-customer-form');

            // Click the close button on the modal
            action.closeModal();

            // Check if model appears
            action.modalShouldBeHidden();
        });
    });

    it('Cancel contact creation from form', () => {
        const action = new ContactCreationAction();

        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Visit the launchpad page
            action.accessLaunchPadPage(url);

            // Should see the Create Contact button
            action.checkIfElementExists('create-contact-action');

            // Click the Create Contact button
            action.clickElement('create-contact-action');

            // Check if model appears
            action.shouldSeeCreateContactModal();

            // Check if the search modal is shown
            action.checkIfElementExists('search-cap-customer-form');

            // Check if the dialog appears
            action.shouldSeeCreateContactModal();

            // Check if the query input exists
            action.checkIfInputExists('query');

            // Check if the select button exists
            action.checkIfElementExists('select-business-partner-button');

            // Check if the Create New button exists
            action.checkIfElementExists('create-new-button');

            // Click the Create New button
            action.clickElement('create-new-button');

            // Check if the modal header is correct
            action.checkModalHeader('Create New Contact');

            // wait for form to load
            action.checkIfElementExists('newContactId');

            // Click the Cancel button
            action.clickElement('cancel-kyc-button');

            // Check if model appears
            action.modalShouldBeHidden();
        });
    });

    it('Verify contact appears in the contacts list after creation', () => {
        const action = new ContactCreationAction();
        const customerValue = retrieveContactCreationValueTranslation();
        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Visit the launchpad page
            action.accessLaunchPadPage(url);

            // Should see the Create Contact button
            action.checkIfElementExists('create-contact-action');

            // Click the Create Contact button
            action.clickElement('create-contact-action');

            // Check if model appears
            action.shouldSeeCreateContactModal();

            // Check if the search modal is shown
            action.checkIfElementExists('search-cap-customer-form');

            // Check if the dialog appears
            action.shouldSeeCreateContactModal();

            // Check if the query input exists
            action.checkIfInputExists('query');

            // Check if the select button exists
            action.checkIfElementExists('select-business-partner-button');

            // Check if the Create New button exists
            action.checkIfElementExists('create-new-button');

            // Click the Create New button
            action.clickElement('create-new-button');

            // Check if the modal header is correct
            action.checkModalHeader('Create New Contact');

            // Fill in the contact creation form
            action.selectDropdown('customer.fields.Title.value', customerValue.title);

            action.fillAndValidateInput('FirstName', customerValue.firstName);
            action.fillAndValidateInput('LastName', customerValue.lastName);

            action.fillAndValidateInput('Email', customerValue.email);

            action.fillAndValidatePhoneField('Phone', customerValue.phone.value, customerValue.countryCode);
        });

        // Fill in the consents checkboxes
        cy.task('apv:retrieveContactCreationConsents', {
            agreementModuleE2eId: 'rootAgreementModule',
        }).then(async (consents: any) => {
            const consentActions = new ConsentsActions();
            for (let x = 0; x < consents.length; x++) {
                consentActions.fillCheckbox(consents[x], true, x);
            }
        });

        // Submit the contact creation form
        action.submitCreateContact(false);

        // Verify that the contact detail page is displayed
        action.isInContactDetailPage();

        cy.task('apv:retrieveLaunchPadUrl', {
            moduleE2eId: 'contactCreationLaunchPadModule',
            routerE2eId: 'contactCreationLaunchpadRouters',
        }).then(async ({ url }: any) => {
            // Back to the launchpad page
            action.accessLaunchPadPage(url);

            // Should see the created contact in the list
            action.findAndAccessLeadDetailByCustomerName(`${customerValue.firstName} ${customerValue.lastName}`);

            // Verify the lead detail
            action.verifyLeadDetail('Pending Qualify', `${customerValue.firstName} ${customerValue.lastName}`);
        });

        cy.task('apv:getUser', { userE2eId: 'root' }).then(async ({ _id }: any) => {
            // Verify the assigned user
            action.verifyAssignedUser(_id.toString());
        });
    });
});

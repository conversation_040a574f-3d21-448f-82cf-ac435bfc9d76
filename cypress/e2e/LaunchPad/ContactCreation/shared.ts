import dayjs from 'dayjs';
import { E2ELanguage } from '../../../snapshots/datasets/baseLanguagePacks';
import { importBaseConsents } from '../../../snapshots/datasets/consentsAndDeclarations/shared';
import { importBaseDealers } from '../../../snapshots/datasets/dealers/shared';
import { importBaseLanguage } from '../../../snapshots/datasets/languagePacks/shared';
import { importBaseModule } from '../../../snapshots/datasets/modules/shared';
import { importBaseSetting } from '../../../snapshots/datasets/settings/shared';
import { importBaseVehicle } from '../../../snapshots/datasets/vehicles/shared';

export const contactCreationValues = {
    title: 'MR',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    wrongEmail: '3ejb@email',
    countryCode: 'SG',
    phone: {
        value: '91234567',
    },
    wrongPhone: 'abcd',
    address: 'ct hub',
    unitNumber: '100',
    postalCode: '311120',
    invalidPostalCode: 'abcd',
    countryValue: 'Singapore',
    birthday: dayjs('01 01 1970', 'DD MM YYYY').toString(),
    road: 'ct hub',
    city: 'lavendar',
    todayDate: dayjs().toString(),
    customizedFieldValues: ['Via social media', 'EU 38', 'In 1 year'],
    formErrors: {
        required: 'This field is required',
        invalidEmail: 'Invalid Email Address',
        invalidPhone: 'Invalid Number',
        invalidPostalCode: 'Invalid Postal Code',
        invalidDateOfBirth: 'Not eligible as minimum age is 18',
    },
};

export const importSnapshot = [
    ...importBaseSetting(['baseRuntimeSettings', 'baseEmailContext']),
    'baseLanguagePacks',
    ...importBaseLanguage(['japaneseLanguagePack']),
    'basePermissions',
    'baseUsers',
    'baseCompanies',
    ...importBaseDealers(['rootDealerB']),
    'baseDealers',
    ...importBaseModule([
        'baseAgreementModule',
        'baseSigningModule',
        'baseAppointmentModule',
        'contactCreation/contactCreationCustomerModule',
        'baseVehicleModule',
        'baseVisitAppointmentModule',
    ]),
    ...importBaseVehicle([
        'baseMake',
        'baseModel',
        'baseModelB',
        'baseModel718',
        'baseModel911',
        'baseModelTaycan',
        'baseVariant',
        'baseVariantB',
        'baseVariant718Boxster',
        'baseVariant911Carrera',
        'baseVariant911ST',
        'baseVariantTaycan4S',
    ]),
    'modules/contactCreation/contactCreationLaunchPadModule',
    ...importBaseConsents(['displayInContactCreationConsent']),
    'routers/contactCreationLaunchpadRouters',
];

export const retrieveContactCreationValueTranslation = (language?: E2ELanguage) => ({
    ...contactCreationValues,
    formErrors: {
        required: language?.translations?.common?.formErrors?.required ?? contactCreationValues.formErrors.required,
        invalidEmail:
            language?.translations?.common?.formErrors?.invalidEmail ?? contactCreationValues.formErrors.invalidEmail,
        invalidPhone:
            language?.translations?.common?.formErrors?.invalidPhone ?? contactCreationValues.formErrors.invalidPhone,
        invalidPostalCode:
            language?.translations?.common?.formErrors?.invalidPostalCode ??
            contactCreationValues.formErrors.invalidPostalCode,
        invalidDateOfBirth:
            language?.translations?.common?.formErrors?.invalidDateOfBirth ??
            contactCreationValues.formErrors.invalidDateOfBirth,
    },
});

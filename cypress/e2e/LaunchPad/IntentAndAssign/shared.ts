import dayjs from 'dayjs';
import { E2ELanguage } from '../../../snapshots/datasets/baseLanguagePacks';
import { importBaseConsents } from '../../../snapshots/datasets/consentsAndDeclarations/shared';
import { importBaseDealers } from '../../../snapshots/datasets/dealers/shared';
import { importBaseLanguage } from '../../../snapshots/datasets/languagePacks/shared';
import { importBaseModule } from '../../../snapshots/datasets/modules/shared';
import { importBaseSetting } from '../../../snapshots/datasets/settings/shared';
import { importBaseVehicle } from '../../../snapshots/datasets/vehicles/shared';

export const intentAndAssignErrors = {
    required: 'This field is required',
};

export const importSnapshot = [
    ...importBaseSetting(['baseRuntimeSettings', 'baseEmailContext']),
    'baseLanguagePacks',
    ...importBaseLanguage(['japaneseLanguagePack']),
    'basePermissions',
    'baseUsers',
    'baseCompanies',
    ...importBaseDealers(['rootDealerB']),
    'baseDealers',
    ...importBaseModule([
        'baseAgreementModule',
        'baseSigningModule',
        'baseAppointmentModule',
        'baseCustomerModule',
        'baseVehicleModule',
        'baseVisitAppointmentModule',
    ]),
    ...importBaseVehicle([
        'baseMake',
        'baseModel',
        'baseModelB',
        'baseModel718',
        'baseModel911',
        'baseModelTaycan',
        'baseVariant',
        'baseVariantB',
        'baseVariant718Boxster',
        'baseVariant911Carrera',
        'baseVariant911ST',
        'baseVariantTaycan4S',
    ]),
    'modules/baseLaunchPadModule',
    'routers/launchpadRouters',
    'baseCustomers',
    'leads/intentAndAssignLead',
    'users/intentAndAssignUser',
    'baseUserGroups',
];

export const retrieveIntentAndAssignTranslation = (language?: E2ELanguage) => ({
    ...intentAndAssignErrors,
    required: language?.translations?.common?.formErrors?.required ?? intentAndAssignErrors.required,
});

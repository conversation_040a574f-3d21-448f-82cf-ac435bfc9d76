import { genSalt, hash } from 'bcryptjs';
import { ObjectId } from 'mongodb';
import PermissionHelpers from '../../helpers/PermissionHelpers';
import type { DataSetGenerator } from '../../helpers/datasets';
import { E2EUser } from '../baseUsers';
import { defaultSystemSimpleVersioning } from '../shared';

export const dependencies = ['basePermissions'];

const generator: DataSetGenerator = async directory => {
    const permissions = new PermissionHelpers(directory);
    const permission = await permissions.getPermission('root');

    const lyndaAdminUser: E2EUser = {
        _id: new ObjectId(),
        email: '<EMAIL>',
        displayName: 'Lynda Admin',
        password: await hash('rootPassword', await genSalt(10)),
        passwordFrom: new Date(),
        previousPasswords: [],
        otpSetup: null,
        mobile: { prefix: 65, value: '' },
        permissionIds: [permission._id],
        sentActivationEmail: true,
        isActive: true,
        isDeleted: false,
        singleSessionMode: true,

        _versioning: defaultSystemSimpleVersioning,
        additionalCalendarEmails: [],

        __e2e__: {
            id: 'lyndaAdmin',
            password: 'rootPassword',
        },
    };

    return {
        users: [lyndaAdminUser],
    };
};

export default generator;

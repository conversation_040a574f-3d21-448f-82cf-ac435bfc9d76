import { ObjectId } from 'mongodb';
import { ApplicationKind } from '../../../../src/server/database/documents/Applications/core';
import { LocalCustomerFieldSource } from '../../../../src/server/database/documents/Customer';
import { LeadStatus } from '../../../../src/server/database/documents/Lead/shared';
import { EndpointType } from '../../../../src/server/database/documents/Router';
import { ModuleType } from '../../../../src/server/database/documents/modules';
import CustomerHelpers from '../../helpers/CustomerHelpers';
import DealerHelpers from '../../helpers/DealerHelpers';
import LanguagePackHelpers from '../../helpers/LanguagePackHelpers';
import ModuleHelpers from '../../helpers/ModuleHelpers';
import RouterHelpers from '../../helpers/RouterHelpers';
import UserHelpers from '../../helpers/UserHelpers';
import VehicleHelpers from '../../helpers/VehicleHelpers';
import { DataSetGenerator } from '../../helpers/datasets';
import { E2ELead } from '../baseLeads';
import { generateDefaultSystemAdvancedVersioning } from '../shared';

const generator: DataSetGenerator = async directory => {
    const userHelpers = new UserHelpers(directory);
    const dealerHelpers = new DealerHelpers(directory);
    const moduleHelpers = new ModuleHelpers(directory);
    const routerHelpers = new RouterHelpers(directory);
    const languageHelpers = new LanguagePackHelpers(directory);
    const customerHelpers = new CustomerHelpers(directory);
    const vehicleHelpers = new VehicleHelpers(directory);

    const [rootUser, rootDealer, launchPadApplicationModule, customerModule, router, language, customer, rootVariant] =
        await Promise.all([
            userHelpers.getUser('root'),
            dealerHelpers.getDealer('rootDealer'),
            moduleHelpers.getModule('showroomVisitLaunchPadModule', ModuleType.LaunchPadModule),
            moduleHelpers.getModule('rootCustomerModule', ModuleType.LocalCustomerManagement),
            routerHelpers.getRouter('showroomVisitLaunchpadRouter'),
            languageHelpers.getLanguagePack('rootLanguage'),
            customerHelpers.getCustomer('sharedCustomer'),
            vehicleHelpers.getVehicle('rootVariant'),
        ]);

    const leadForShowroomVisit: E2ELead = {
        _id: new ObjectId(),
        kind: ApplicationKind.Launchpad,
        identifier: 'L250700027',
        isLead: true,
        customerId: customer._id,
        assigneeId: rootUser._id,
        dealerId: rootDealer._id,
        moduleId: launchPadApplicationModule._id,
        status: LeadStatus.SubmittedToCap,
        tradeInVehicle: [
            {
                isSelected: true,
                source: LocalCustomerFieldSource.UserInput,
            },
        ],
        customerAgreements: {
            moduleId: customerModule._id,
            agreements: [],
        },
        isDraft: false,
        capValues: {
            leadId: '0000654858',
        },
        documents: [],
        _versioning: generateDefaultSystemAdvancedVersioning(),
        routerId: router._id,
        endpointId: router.endpoints.find(
            endpoint =>
                endpoint._type === EndpointType.LaunchPadApplicationEntrypoint &&
                endpoint.launchPadApplicationModuleId === launchPadApplicationModule._id
        )?._id,
        languageId: language._id,
        __e2e__: {
            id: 'leadForShowroomVisit',
        },
        companyId: launchPadApplicationModule.companyId,
        vehicleId: rootVariant._id,
    };

    return {
        leads: [leadForShowroomVisit],
    };
};

export default generator;

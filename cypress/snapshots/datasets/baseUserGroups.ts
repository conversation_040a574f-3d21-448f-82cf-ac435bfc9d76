import { ObjectId } from 'mongodb';
import type { UserGroup } from '../../../src/server/database/documents/UserGroup';
import CompanyHelpers from '../helpers/CompanyHelpers';
import DealerHelpers from '../helpers/DealerHelpers';
import UserHelpers from '../helpers/UserHelpers';
import type { DataSetGenerator } from '../helpers/datasets';
import { defaultSystemSimpleVersioning } from './shared';

export type E2EUserGroup = UserGroup & {
    __e2e__: {
        id: string;
    };
};

const generator: DataSetGenerator = async directory => {
    const userHelper = new UserHelpers(directory);
    const companyHelper = new CompanyHelpers(directory);
    const dealerHelpers = new DealerHelpers(directory);

    const [company, rootUser, lyndaAdmin, rootDealer, rootDealerB] = await Promise.all([
        companyHelper.getCompany('rootCompany'),
        userHelper.getUser('root'),
        userHelper.getUser('lyndaAdmin'),
        dealerHelpers.getDealer('rootDealer'),
        dealerHelpers.getDealer('rootDealerB'),
    ]);

    const rootUserGroup: E2EUserGroup = {
        _id: new ObjectId(),

        companyId: company._id,
        userIds: [rootUser._id, lyndaAdmin._id],
        dealerIds: [rootDealer._id, rootDealerB._id],
        _versioning: defaultSystemSimpleVersioning,
        description: 'PSG Admin Group',
        displayName: 'PSG Admin Group',
        isActive: true,
        isParallel: true,
        superiorUserGroupIds: [],

        __e2e__: {
            id: 'rootUserGroup',
        },
    };

    return {
        userGroups: [rootUserGroup],
    };
};

export default generator;

import { ObjectId } from 'bson';
import { ApplicationStage } from '../../../../src/server/database/documents/Applications/core';
import { EndpointType, LayoutType, type Router } from '../../../../src/server/database/documents/Router';
import { ModuleType } from '../../../../src/server/database/documents/modules';
import CompanyHelpers from '../../helpers/CompanyHelpers';
import LanguagePackHelpers from '../../helpers/LanguagePackHelpers';
import ModuleHelpers from '../../helpers/ModuleHelpers';
import type { DataSetGenerator } from '../../helpers/datasets';
import { E2ERouter } from '../baseRouters';
import { importBaseModule } from '../modules/shared';
import { defaultSystemSimpleVersioning } from '../shared';

export const dependencies = [
    'baseCompanies',
    ...importBaseModule(['testDriveCreation/testDriveCreationLaunchPadModule']),
];

const generator: DataSetGenerator = async directory => {
    const moduleHelpers = new ModuleHelpers(directory);
    const companyHelpers = new CompanyHelpers(directory);
    const languageHelpers = new LanguagePackHelpers(directory);
    const [launchPadApplicationModule, company, language] = await Promise.all([
        moduleHelpers.getModule('testDriveCreationLaunchPadModule', ModuleType.LaunchPadModule),
        companyHelpers.getCompany('rootCompany'),
        languageHelpers.getLanguagePack('rootLanguage'),
    ]);

    const router: E2ERouter = {
        _id: new ObjectId(),
        _versioning: defaultSystemSimpleVersioning,
        companyId: company._id,
        endpoints: [
            {
                _id: new ObjectId(),
                _type: EndpointType.LaunchPadApplicationEntrypoint,
                displayName: 'Launch Pad Test Drive',
                launchPadApplicationModuleId: launchPadApplicationModule._id,
                pathname: '/launchpad-test-drive',
            },
            {
                _id: new ObjectId(),
                _type: EndpointType.ApplicationList,
                applicationModuleIds: [launchPadApplicationModule._id],
                title: 'Test Drives',
                applicationStage: ApplicationStage.Appointment,
                pathname: '/testdrives',
            },
        ],
        hostname: 'localhost',
        languages: [language._id],
        layout: {
            _type: LayoutType.BasicPro,
            layoutMode: 'mix',
            navigationTheme: 'dark',
            withSignInButton: false,
        },
        pathScripts: [],
        menuItems: [],
        pathname: '',
        withAdmin: true,
        googleTagManagerId: null,
        pixelId: null,
        __e2e__: {
            id: 'testDriveCreationLaunchpadRouter',
        },
    };

    return {
        routers: [router],
    };
};

export default generator;

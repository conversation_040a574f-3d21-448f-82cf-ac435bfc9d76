import { ObjectId } from 'bson';
import { BodyType, VehicleKind, type LocalVariant } from '../../../../src/server/database/documents/Vehicle';
import { ModuleType } from '../../../../src/server/database/documents/modules';
import ModuleHelpers from '../../helpers/ModuleHelpers';
import VehicleHelpers from '../../helpers/VehicleHelpers';
import type { DataSetGenerator } from '../../helpers/datasets';
import { baseTranslatedString, defaultSystemSimpleVersioning } from '../shared';
import { E2EVariant } from './baseVariant';

enum EngineType {
    Petrol = 'petrol',
    Diesel = 'diesel',
    Hybrid = 'hybrid',
    Electric = 'Electric',
}

export const dependencies = ['modules/baseVehicleModule', 'vehicles/baseModel911'];

const generator: DataSetGenerator = async directory => {
    const moduleHelpers = new ModuleHelpers(directory);
    const vehicleHelpers = new VehicleHelpers(directory);

    const [module, model] = await Promise.all([
        moduleHelpers.getModule('rootVehicleModule', ModuleType.SimpleVehicleManagement),
        vehicleHelpers.getVehicle('rootModel911'),
    ]);

    const rootVariant: E2EVariant = {
        _id: new ObjectId(),
        _versioning: {
            ...defaultSystemSimpleVersioning,
            isLatest: true,
            suiteId: new ObjectId(),
        },
        _kind: VehicleKind.LocalVariant,
        identifier: 'variant911ST',
        isActive: true,
        isDeleted: false,
        moduleId: module._id,
        name: {
            defaultValue: '911 S/T',
            overrides: [],
        },
        bodyType: BodyType.Coupe,
        engine: baseTranslatedString,
        description: baseTranslatedString,
        engineType: EngineType.Petrol,
        modelId: model._id,
        features: baseTranslatedString,
        vehiclePrice: 100000,
        appleCarPlay: false,
        order: 5,
        __e2e__: {
            id: 'rootVariant911ST',
        },
    };

    return {
        vehicles: [rootVariant],
    };
};

export default generator;

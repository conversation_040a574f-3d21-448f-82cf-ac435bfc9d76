import { ObjectId } from 'bson';
import { BodyType, VehicleKind, type LocalModel } from '../../../../src/server/database/documents/Vehicle';
import { ModuleType } from '../../../../src/server/database/documents/modules';
import ModuleHelpers from '../../helpers/ModuleHelpers';
import VehicleHelpers from '../../helpers/VehicleHelpers';
import type { DataSetGenerator } from '../../helpers/datasets';
import { defaultSystemSimpleVersioning } from '../shared';
import { E2EModel } from './baseModel';

export const dependencies = ['modules/baseVehicleModule', 'vehicles/baseMake'];

const generator: DataSetGenerator = async directory => {
    const moduleHelpers = new ModuleHelpers(directory);
    const vehicleHelpers = new VehicleHelpers(directory);
    const [module, make] = await Promise.all([
        moduleHelpers.getModule('rootVehicleModule', ModuleType.SimpleVehicleManagement),
        vehicleHelpers.getVehicle('rootMake'),
    ]);

    const rootModel: E2EModel = {
        _id: new ObjectId(),
        _versioning: {
            ...defaultSystemSimpleVersioning,
            isLatest: true,
            suiteId: new ObjectId(),
        },
        _kind: VehicleKind.LocalModel,
        identifier: 'modelTaycan',
        isActive: true,
        isDeleted: false,
        moduleId: module._id,
        name: {
            defaultValue: 'Taycan',
            overrides: [],
        },
        bodyType: BodyType.Coupe,
        makeId: make._id,
        parentModelId: undefined,
        order: 1,
        __e2e__: {
            id: 'rootModelTaycan',
        },
    };

    return {
        vehicles: [rootModel],
    };
};

export default generator;

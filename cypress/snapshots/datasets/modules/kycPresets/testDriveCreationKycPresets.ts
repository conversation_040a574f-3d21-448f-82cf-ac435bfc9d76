import { ObjectId } from 'mongodb';
import { ConditionType } from '../../../../../src/server/database/documents/Conditions';
import { LocalCustomerFieldKey } from '../../../../../src/server/database/documents/Customer';
import { defaultConditionForApplicant } from '../../shared';
import { type E2EKYCPreset } from './shared';

export enum KycFieldPurpose {
    KYC = 'kyc',
    Share = 'share',
}

export const testDriveCreationKycPresets: E2EKYCPreset[] = [
    {
        _id: new ObjectId(),
        displayName: 'Test Drive Creation KycPresets',
        conditions: [
            {
                type: ConditionType.IsTestDrive,
            },
        ],
        isActive: true,
        __e2e__: {
            id: 'testDriveCreationKycPreset',
        },
        fields: [
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Title,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.FirstName,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.LastName,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.FullName,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Email,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Phone,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.IdentityNumber,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Address,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.UnitNumber,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.PostalCode,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Country,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.DrivingLicense,
                purpose: [KycFieldPurpose.KYC],
            },
        ],
    },
];

import { ObjectId } from 'mongodb';
import { LocalCustomerFieldKey } from '../../../../../src/server/database/documents/Customer';
import { defaultConditionForApplicant } from '../../shared';
import { type E2EKYCPreset } from './shared';

export enum KycFieldPurpose {
    KYC = 'kyc',
    Share = 'share',
}

export const contactCreationKycPresets: E2EKYCPreset[] = [
    {
        _id: new ObjectId(),
        displayName: 'Root Lead C@Pture Form KycPresets',
        conditions: [defaultConditionForApplicant],
        isActive: true,
        __e2e__: {
            id: 'rootLeadGenFormKycPreset',
        },
        fields: [
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Title,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.FirstName,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.LastName,
                purpose: [KycFieldPurpose.KYC],
            },

            {
                isRequired: true,
                key: LocalCustomerFieldKey.Email,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: true,
                key: LocalCustomerFieldKey.Phone,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: false,
                key: LocalCustomerFieldKey.Birthday,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: false,
                key: LocalCustomerFieldKey.Address,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: false,
                key: LocalCustomerFieldKey.UnitNumber,
                purpose: [KycFieldPurpose.KYC],
            },
            {
                isRequired: false,
                key: LocalCustomerFieldKey.PostalCode,
                purpose: [KycFieldPurpose.KYC],
            },
        ],
    },
];

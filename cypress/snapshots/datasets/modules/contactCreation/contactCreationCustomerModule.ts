import { ObjectId } from 'bson';
import { AgeCalculationMethod, ModuleType } from '../../../../../src/server/database/documents/modules';
import CompanyHelpers from '../../../helpers/CompanyHelpers';
import type { DataSetGenerator } from '../../../helpers/datasets';
import { defaultSystemSimpleVersioning } from '../../shared';
import { E2ECustomerModule } from '../baseCustomerModule';
import { contactCreationKycFields } from '../kycFields/contactCreationKycFields';
import { contactCreationKycPresets } from '../kycPresets/contactCreationKycPresets';

export const dependencies = ['baseCompanies'];
const generator: DataSetGenerator = async directory => {
    const companyHelpers = new CompanyHelpers(directory);
    const company = await companyHelpers.getCompany('rootCompany');

    const customerModule: E2ECustomerModule = {
        _id: new ObjectId(),
        _versioning: defaultSystemSimpleVersioning,
        companyId: company._id,
        _type: ModuleType.LocalCustomerManagement,
        displayName: 'Root Customer Module',
        kycFields: contactCreationKycFields,
        kycPresets: contactCreationKycPresets,
        extraSettings: {
            ageCalculationMethod: AgeCalculationMethod.BirthdayBased,
            minimumAge: 18,
            mobileVerification: false,
        },
        __e2e__: {
            id: 'contactCreationCustomerModule',
        },
    };

    return {
        modules: [customerModule],
    };
};

export default generator;

import { LocalCustomerFieldKey } from '../../../../../src/server/database/documents/Customer';
import type { LocalCustomerManagementModuleKycField } from '../../../../../src/server/database/documents/modules';

// eslint-disable-next-line import/prefer-default-export
export const testDriveCreationKycFields: LocalCustomerManagementModuleKycField[] = [
    {
        field: LocalCustomerFieldKey.Title,
        order: 1,
    },
    {
        field: LocalCustomerFieldKey.FirstName,
        order: 2,
    },
    {
        field: LocalCustomerFieldKey.LastName,
        order: 3,
    },
    {
        field: LocalCustomerFieldKey.FullName,
        order: 4,
    },
    {
        field: LocalCustomerFieldKey.Email,
        order: 5,
    },
    {
        field: LocalCustomerFieldKey.Phone,
        order: 6,
    },
    {
        field: LocalCustomerFieldKey.IdentityNumber,
        order: 7,
    },
    {
        field: LocalCustomerFieldKey.Address,
        order: 8,
    },
    {
        field: LocalCustomerFieldKey.UnitNumber,
        order: 9,
    },
    {
        field: LocalCustomerFieldKey.PostalCode,
        order: 10,
    },
    {
        field: LocalCustomerFieldKey.Country,
        order: 11,
    },
    {
        field: LocalCustomerFieldKey.DrivingLicense,
        order: 12,
    },
];

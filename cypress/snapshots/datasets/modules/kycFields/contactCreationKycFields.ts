import { LocalCustomerFieldKey } from '../../../../../src/server/database/documents/Customer';
import type { LocalCustomerManagementModuleKycField } from '../../../../../src/server/database/documents/modules';

export const contactCreationKycFields: LocalCustomerManagementModuleKycField[] = [
    {
        field: LocalCustomerFieldKey.Title,
        order: 1,
    },
    {
        field: LocalCustomerFieldKey.FirstName,
        order: 2,
    },
    {
        field: LocalCustomerFieldKey.LastName,
        order: 3,
    },
    {
        field: LocalCustomerFieldKey.Email,
        order: 4,
    },
    {
        field: LocalCustomerFieldKey.Phone,
        order: 5,
    },
    {
        field: LocalCustomerFieldKey.Address,
        order: 6,
    },
    {
        field: LocalCustomerFieldKey.UnitNumber,
        order: 7,
    },
    {
        field: LocalCustomerFieldKey.PostalCode,
        order: 8,
    },
    {
        field: LocalCustomerFieldKey.Country,
        order: 9,
    },
];

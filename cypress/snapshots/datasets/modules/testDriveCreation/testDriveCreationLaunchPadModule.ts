import { ObjectId } from 'bson';
import { CounterMethod } from '../../../../../src/server/database/documents';
import { LaunchPadModule, ModuleType } from '../../../../../src/server/database/documents/modules';
import CompanyHelpers from '../../../helpers/CompanyHelpers';
import DealerHelpers from '../../../helpers/DealerHelpers';
import ModuleHelpers from '../../../helpers/ModuleHelpers';
import VehicleHelpers from '../../../helpers/VehicleHelpers';
import type { DataSetGenerator } from '../../../helpers/datasets';
import { defaultSystemSimpleVersioning } from '../../shared';
import { importBaseVehicle } from '../../vehicles/shared';
import { E2ELaunchPadModule } from '../baseLaunchPadModule';
import { importBaseModule } from '../shared';

export const dependencies = [
    'baseLanguagePacks',
    'baseCompanies',
    'baseDealers',
    ...importBaseModule([
        'baseAgreementModule',
        'baseSigningModule',
        'baseAppointmentModule',
        'baseCustomerModule',
        'baseVehicleModule',
        'baseVisitAppointmentModule',
        'testDriveCreation/testDriveCreationCustomerModule',
        'testDriveCreation/testDriveCreationAppointmentModule',
    ]),
    ...importBaseVehicle(['baseVariant911ST', 'baseVariantTaycan4S']),
];

const generator: DataSetGenerator = async directory => {
    const companyHelpers = new CompanyHelpers(directory);
    const moduleHelpers = new ModuleHelpers(directory);
    const vehicleHelpers = new VehicleHelpers(directory);
    const dealerHelpers = new DealerHelpers(directory);

    const [
        vehicleModule,
        consentModule,
        appointmentModule,
        visitAppointmentModule,
        rootCustomerModule,
        company,
        variant911,
        variantTaycan4S,
        dealer,
        dealerB,
    ] = await Promise.all([
        moduleHelpers.getModule('rootVehicleModule', ModuleType.EventApplicationModule),
        moduleHelpers.getModule('rootAgreementModule', ModuleType.ConsentsAndDeclarations),
        moduleHelpers.getModule('testDriveCreationAppointmentModule', ModuleType.AppointmentModule),
        moduleHelpers.getModule('rootVisitAppointmentModule', ModuleType.VisitAppointmentModule),
        moduleHelpers.getModule('testDriveCreationCustomerModule', ModuleType.LocalCustomerManagement),
        companyHelpers.getCompany('rootCompany'),

        vehicleHelpers.getVehicle('rootVariant911ST'),
        vehicleHelpers.getVehicle('rootVariantTaycan4S'),

        dealerHelpers.getDealer('rootDealer'),
        dealerHelpers.getDealer('rootDealerB'),
    ]);

    const launchPadModule: E2ELaunchPadModule = {
        _id: new ObjectId(),
        companyId: company._id,
        _type: ModuleType.LaunchPadModule,

        agreementsModuleId: consentModule._id,
        customerModuleId: rootCustomerModule._id,
        appointmentModuleId: appointmentModule._id,
        visitAppointmentModuleId: visitAppointmentModule._id,

        displayName: 'Test Drive Launch Pad',
        dealerVehicles: [
            {
                dealerId: dealer._id,
                vehicleSuiteIds: [variantTaycan4S._versioning.suiteId, variant911._versioning.suiteId],
            },
            {
                dealerId: dealerB._id,
                vehicleSuiteIds: [variantTaycan4S._versioning.suiteId, variant911._versioning.suiteId],
            },
        ],
        leadCounter: {
            method: CounterMethod.Global,
            padding: 5,
            prefix: 'LEAD-{YY}-{MM}',
        },
        appointmentCounter: {
            method: CounterMethod.Global,
            padding: 5,
            prefix: 'P{YY}{MM}',
        },
        vehicleModuleId: vehicleModule._id,
        capModuleId: new ObjectId(),
        _versioning: defaultSystemSimpleVersioning,
        __e2e__: {
            id: 'testDriveCreationLaunchPadModule',
        },
        hasTradeInRequest: false,
    };

    return {
        modules: [launchPadModule],
    };
};

export default generator;

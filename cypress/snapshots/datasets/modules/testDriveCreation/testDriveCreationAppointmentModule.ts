import { ObjectId } from 'bson';
import dayjs from 'dayjs';
import { TimeSlotEnum } from '../../../../../src/server/database/documents/moduleShared';
import { ModuleType } from '../../../../../src/server/database/documents/modules';
import CompanyHelpers from '../../../helpers/CompanyHelpers';
import ModuleHelpers from '../../../helpers/ModuleHelpers';
import type { DataSetGenerator } from '../../../helpers/datasets';
import { baseEmailContents, baseTranslatedString, defaultSystemSimpleVersioning } from '../../shared';
import type { E2EAppointmentModule } from '../baseAppointmentModule';

export const dependencies = ['baseCompanies'];

const generator: DataSetGenerator = async directory => {
    const companyHelpers = new CompanyHelpers(directory);
    const moduleHelpers = new ModuleHelpers(directory);

    const [company, signingModule] = await Promise.all([
        companyHelpers.getCompany('rootCompany'),
        moduleHelpers.getModule('rootSigningModule', ModuleType.BasicSigningModule),
    ]);

    const appointmentModule: E2EAppointmentModule = {
        _id: new ObjectId(),
        _versioning: defaultSystemSimpleVersioning,
        companyId: company._id,
        _type: ModuleType.AppointmentModule,
        displayName: 'Test Drive Appointment Module',
        advancedBookingLimit: 0,
        bookingTimeSlot: [
            {
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 5,
                slot: dayjs().set('hour', 10).set('minute', 0).set('second', 0).toDate(),
            },
            {
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 5,
                slot: dayjs().set('hour', 12).set('minute', 0).set('second', 0).toDate(),
            },
            {
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 5,
                slot: dayjs().set('hour', 14).set('minute', 0).set('second', 0).toDate(),
            },
        ],
        emailContents: {
            customer: {
                bookingConfirmation: baseEmailContents,
                submitConfirmation: baseEmailContents,
                bookingAmendment: baseEmailContents,
                completeTestDriveWithoutProcess: baseEmailContents,
                endTestDriveWithProcess: baseEmailContents,
                bookingCancellation: baseEmailContents,
            },
            salesPerson: {
                finderReservation: baseEmailContents,
                submitConfirmation: baseEmailContents,
                endTestDriveReminder: baseEmailContents,
                bookingAmendment: baseEmailContents,
                bookingCancellation: baseEmailContents,
                bookingConfirmation: baseEmailContents,
            },
        },
        hasTestDriveProcess: true,
        unavailableDayOfWeek: [],
        bookingInformation: baseTranslatedString,
        hasTestDriveSigning: false,
        maxAdvancedBookingLimit: 10,
        signingModuleId: signingModule._id,
        showRemoteFlowButtonInKYCPage: true,
        __e2e__: {
            id: 'testDriveCreationAppointmentModule',
        },
    };

    return {
        modules: [appointmentModule],
    };
};

export default generator;

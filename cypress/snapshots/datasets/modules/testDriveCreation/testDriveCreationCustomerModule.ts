import { ObjectId } from 'bson';
import { AgeCalculationMethod, ModuleType } from '../../../../../src/server/database/documents/modules';
import CompanyHelpers from '../../../helpers/CompanyHelpers';
import type { DataSetGenerator } from '../../../helpers/datasets';
import { defaultSystemSimpleVersioning } from '../../shared';
import { E2ECustomerModule } from '../baseCustomerModule';
import { testDriveCreationKycFields } from '../kycFields/testDriveCreationKycFields';
import { testDriveCreationKycPresets } from '../kycPresets/testDriveCreationKycPresets';

export const dependencies = ['baseCompanies'];
const generator: DataSetGenerator = async directory => {
    const companyHelpers = new CompanyHelpers(directory);
    const company = await companyHelpers.getCompany('rootCompany');

    const customerModule: E2ECustomerModule = {
        _id: new ObjectId(),
        _versioning: defaultSystemSimpleVersioning,
        companyId: company._id,
        _type: ModuleType.LocalCustomerManagement,
        displayName: 'Test Drive Customer Module',
        kycFields: testDriveCreationKycFields,
        kycPresets: testDriveCreationKycPresets,
        extraSettings: {
            ageCalculationMethod: AgeCalculationMethod.BirthdayBased,
            minimumAge: 18,
            mobileVerification: false,
        },
        __e2e__: {
            id: 'testDriveCreationCustomerModule',
        },
    };

    return {
        modules: [customerModule],
    };
};

export default generator;

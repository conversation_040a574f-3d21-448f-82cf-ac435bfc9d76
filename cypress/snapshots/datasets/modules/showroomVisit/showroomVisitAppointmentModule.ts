import { ObjectId } from 'bson';
import dayjs from 'dayjs';
import { TimeSlotEnum } from '../../../../../src/server/database/documents/moduleShared';
import { ModuleType } from '../../../../../src/server/database/documents/modules';
import CompanyHelpers from '../../../helpers/CompanyHelpers';
import ModuleHelpers from '../../../helpers/ModuleHelpers';
import type { DataSetGenerator } from '../../../helpers/datasets';
import { baseEmailContents, baseTranslatedString, defaultSystemSimpleVersioning } from '../../shared';
import type { E2EVisitAppointmentModule } from '../baseVisitAppointmentModule';

export const dependencies = ['baseCompanies'];

const generator: DataSetGenerator = async directory => {
    const companyHelpers = new CompanyHelpers(directory);
    const moduleHelpers = new ModuleHelpers(directory);

    const [company] = await Promise.all([
        companyHelpers.getCompany('rootCompany'),
        moduleHelpers.getModule('rootSigningModule', ModuleType.BasicSigningModule),
    ]);

    const appointmentModule: E2EVisitAppointmentModule = {
        _id: new ObjectId(),
        _versioning: defaultSystemSimpleVersioning,
        companyId: company._id,
        _type: ModuleType.VisitAppointmentModule,
        displayName: 'Showroom Visit Appointment Module',
        advancedBookingLimit: 0,
        bookingTimeSlot: [
            {
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 5,
                slot: dayjs().set('hour', 10).set('minute', 0).set('second', 0).toDate(),
            },
            {
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 5,
                slot: dayjs().set('hour', 12).set('minute', 0).set('second', 0).toDate(),
            },
            {
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 5,
                slot: dayjs().set('hour', 14).set('minute', 0).set('second', 0).toDate(),
            },
        ],
        emailContents: {
            customer: {
                bookingConfirmation: baseEmailContents,
                submitConfirmation: baseEmailContents,
                bookingAmendment: baseEmailContents,
                bookingCancellation: baseEmailContents,
                bookingComplete: baseEmailContents,
            },
            salesPerson: {
                submitConfirmation: baseEmailContents,
                bookingAmendment: baseEmailContents,
                bookingCancellation: baseEmailContents,
                bookingConfirmation: baseEmailContents,
            },
        },
        unavailableDayOfWeek: [],
        bookingInformation: baseTranslatedString,
        maxAdvancedBookingLimit: 10,
        __e2e__: {
            id: 'showroomVisitAppointmentModule',
        },
    };

    return {
        modules: [appointmentModule],
    };
};

export default generator;

import { ObjectId } from 'mongodb';
import { ConditionType } from '../../../../src/server/database/documents/Conditions';
import {
    ConsentFeatureType,
    ConsentsAndDeclarationsPurpose,
    ConsentsAndDeclarationsType,
    DataField,
    type CheckboxConsentsAndDeclarations,
} from '../../../../src/server/database/documents/ConsentsAndDeclarations';
import { ModuleType } from '../../../../src/server/database/documents/modules';
import ModuleHelpers from '../../helpers/ModuleHelpers';

import { DataSetGenerator } from '../../helpers/datasets';
import { importBaseModule } from '../modules/shared';
import { defaultSystemSimpleVersioning } from '../shared';
import { LegalTextPosition } from './shared';

export type E2ECheckboxConsent = CheckboxConsentsAndDeclarations & {
    __e2e__: {
        id: string;
    };
};

export const dependencies = [...importBaseModule(['baseAgreementModule'])];

const generator: DataSetGenerator = async directory => {
    const agremeentModuleHelpers = new ModuleHelpers(directory);

    const agreementModule = await agremeentModuleHelpers.getModule(
        'rootAgreementModule',
        ModuleType.ConsentsAndDeclarations
    );

    const consent1: E2ECheckboxConsent = {
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Checkbox,
        _versioning: {
            ...defaultSystemSimpleVersioning,
            suiteId: new ObjectId(),
            isLatest: true,
        },
        conditions: [
            {
                type: ConditionType.IsShowroomVisit,
            },
        ],
        dataField: DataField.DataProcessing,
        displayName: 'Showroom Visit only',
        featurePurpose: {
            featureId: agreementModule._id,
            type: ConsentFeatureType.Module,
        },
        isActive: true,
        isDeleted: false,
        moduleId: agreementModule._id,
        isMandatory: true,
        legalTextPosition: LegalTextPosition.Modal,
        legalMarkup: {
            defaultValue: '',
            overrides: [],
        },
        orderNumber: 5,
        purpose: [ConsentsAndDeclarationsPurpose.KYC],
        description: {
            defaultValue: '002',
            overrides: [],
        },
        title: {
            defaultValue: 'Showroom Visit only',
            overrides: [],
        },
        __e2e__: {
            id: 'displayInShowroomVisitCreationConsent1',
        },
    };

    const consent2: E2ECheckboxConsent = {
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Checkbox,
        _versioning: {
            ...defaultSystemSimpleVersioning,
            suiteId: new ObjectId(),
            isLatest: true,
        },
        conditions: [
            {
                type: ConditionType.IsShowroomVisit,
            },
        ],
        dataField: DataField.DataProcessing,
        displayName: 'Showroom Visit Agreement',
        featurePurpose: {
            featureId: agreementModule._id,
            type: ConsentFeatureType.Module,
        },
        isActive: true,
        isDeleted: false,
        moduleId: agreementModule._id,
        isMandatory: false,
        legalTextPosition: LegalTextPosition.After,
        legalMarkup: {
            defaultValue: '',
            overrides: [],
        },
        orderNumber: 6,
        purpose: [ConsentsAndDeclarationsPurpose.KYC],
        description: {
            defaultValue: 'Showroom Visit Agreement',
            overrides: [],
        },
        title: {
            defaultValue: '',
            overrides: [],
        },
        __e2e__: {
            id: 'displayInShowroomVisitCreationConsent2',
        },
    };

    return {
        consentsAndDeclarations: [consent1, consent2],
    };
};

export default generator;

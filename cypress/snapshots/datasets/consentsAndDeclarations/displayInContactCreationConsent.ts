import { ObjectId } from 'mongodb';
import {
    ConsentFeatureType,
    ConsentsAndDeclarationsPurpose,
    ConsentsAndDeclarationsType,
    DataField,
    type CheckboxConsentsAndDeclarations,
} from '../../../../src/server/database/documents/ConsentsAndDeclarations';
import { ModuleType } from '../../../../src/server/database/documents/modules';
import ModuleHelpers from '../../helpers/ModuleHelpers';

import { DataSetGenerator } from '../../helpers/datasets';
import { importBaseModule } from '../modules/shared';
import { baseTranslatedString, defaultConditionForApplicant, defaultSystemSimpleVersioning } from '../shared';
import { LegalTextPosition } from './shared';

export type E2ECheckboxConsent = CheckboxConsentsAndDeclarations & {
    __e2e__: {
        id: 'displayInContactCreationConsent';
    };
};

export const dependencies = [...importBaseModule(['baseAgreementModule'])];

const generator: DataSetGenerator = async directory => {
    const agremeentModuleHelpers = new ModuleHelpers(directory);

    const agreementModule = await agremeentModuleHelpers.getModule(
        'rootAgreementModule',
        ModuleType.ConsentsAndDeclarations
    );

    const consent: E2ECheckboxConsent = {
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Checkbox,
        _versioning: {
            ...defaultSystemSimpleVersioning,
            suiteId: new ObjectId(),
            isLatest: true,
        },
        conditions: [defaultConditionForApplicant],
        dataField: DataField.DataProcessing,
        displayName: 'Privacy Policy',
        featurePurpose: {
            featureId: agreementModule._id,
            type: ConsentFeatureType.Module,
        },
        isActive: true,
        isDeleted: false,
        moduleId: agreementModule._id,
        isMandatory: true,
        hasLegalMarkup: true,
        legalMarkup: {
            defaultValue: '1234356789',
            overrides: [],
        },
        legalTextPosition: LegalTextPosition.Modal,
        orderNumber: 5,
        purpose: [ConsentsAndDeclarationsPurpose.KYC],
        description: baseTranslatedString,
        title: {
            defaultValue: 'Privacy Policy',
            overrides: [],
        },
        __e2e__: {
            id: 'displayInContactCreationConsent',
        },
    };

    return {
        consentsAndDeclarations: [consent],
    };
};

export default generator;

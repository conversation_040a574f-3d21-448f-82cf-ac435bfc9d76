import { E2EUserGroup } from '../datasets/baseUserGroups';
import { SnapshotDirectory } from './datasets';

class UserGroupHelpers {
    private userGroups: Array<E2EUserGroup>;

    constructor(directory: SnapshotDirectory) {
        this.userGroups = Object.keys(directory).reduce<Array<E2EUserGroup>>(
            (acc, datasetName) => [...acc, ...(directory[datasetName].userGroups || [])],
            []
        );
    }

    public async getUserGroup(userGroupE2eId): Promise<E2EUserGroup> {
        const userGroup = this.userGroups.find(userGroup => userGroup.__e2e__.id === userGroupE2eId);

        if (!userGroup) {
            throw new Error('E2EUserGroup not found');
        }

        return userGroup;
    }
}

export default UserGroupHelpers;

import http from 'http';
import express from 'express';
import httpProxy from 'http-proxy';
import { choosePort } from 'react-dev-utils/WebpackDevServerUtils';
import type { Compiler } from 'webpack';
import devMiddleware from 'webpack-dev-middleware';
import hotMiddleware from 'webpack-hot-middleware';

const createServerUrlSingleton = () => {
    let serverUrl: null | string = null;
    let promiseResolve: ((url: string) => void) | null = null;
    let promise: Promise<string> | null = null;

    const initiate = () => {
        if (!promise) {
            promise = new Promise<string>(resolve => {
                promiseResolve = resolve;
            });
        }
    };

    initiate();

    return {
        initiate,
        set: (url: string) => {
            promiseResolve!(url);
            serverUrl = url;
            promise = null;
        },
        get: () => promise || Promise.resolve(serverUrl!),
    };
};

export type ServerUrl = ReturnType<typeof createServerUrlSingleton>;

const getDevelopmentServer = async (appCompiler: Compiler) => {
    // identify on which port to server the application
    const port = await choosePort('0.0.0.0', 3000);

    // create an express server to serve the application as a whole
    const app = express();

    // create a proxy but do not define a target yet
    const proxy = httpProxy.createProxyServer({ xfwd: true });

    // print error when there's any
    proxy.on('error', () => {
        // simply ignore the error
        // as it's not critical to us
    });

    app.use(
        // use the dev middleware to provide hot reload on the frontend
        devMiddleware(appCompiler, {
            stats: 'errors-only',
            serverSideRender: true,
            writeToDisk: true,
            publicPath: '/public/',
        })
    );

    // propagate hot reload
    app.use(hotMiddleware(appCompiler));

    const serverUrl = createServerUrlSingleton();
    app.use(async (req, res, next) => {
        try {
            const target = await serverUrl.get();
            proxy.web(req, res, { target });
        } catch (error) {
            next(error);
        }
    });

    // create the http server
    const httpServer = http.createServer(app);

    httpServer.on('upgrade', async (req, socket, head) => {
        try {
            const target = await serverUrl.get();
            proxy.ws(req, socket, head, { target });
        } catch (error) {
            // do nothing about it
        }
    });

    httpServer.listen(port);

    return { port, serverUrl };
};

export default getDevelopmentServer;

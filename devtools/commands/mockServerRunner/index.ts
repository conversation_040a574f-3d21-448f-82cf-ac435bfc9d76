import chalk from 'chalk';
import { rimraf } from 'rimraf';
import webpack from 'webpack';
import createDevBuild from '../../backend/createDevBuild';
import loadEnvConfig from '../../env';
import webpackConfig from '../../frontend/webpack';
import { buildDirname, rootDirname } from '../../variables';
import getDevelopmentServer from './getDevelopmentServer';
import getStartBackend from './getStartBackend';

const run = async () => {
    // empty build directory
    rimraf.sync(buildDirname);

    // retrieve webpack compiler instances
    const appCompiler = webpack(webpackConfig);

    // load environment
    loadEnvConfig(rootDirname, true);

    // start the server
    const { serverUrl, port } = await getDevelopmentServer(appCompiler);
    const startBackend = await getStartBackend(serverUrl);

    // watch the server compiler
    await createDevBuild(async entrypoint => {
        serverUrl.initiate();
        await startBackend(entrypoint);
    });

    console.warn(chalk.cyan(`Server will be listening on ${port}`));
};

export default run;

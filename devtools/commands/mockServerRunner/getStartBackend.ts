import { ChildProcess, fork } from 'child_process';
import chalk from 'chalk';
import type { ServerUrl } from './getDevelopmentServer';

const getStartBackend = async (serverUrl: ServerUrl) => {
    let currentChildProcess: ChildProcess | null = null;
    let currentChildClosePromise: Promise<void> | null = null;

    const startServerInChild = async (serverEntry: string) => {
        if (currentChildProcess) {
            // kill the current child
            console.info(chalk.cyan(`⌛  Killing current child..`));
            currentChildProcess.kill(currentChildProcess.killed ? 'SIGKILL' : 'SIGTERM');
            await currentChildClosePromise;
        }

        // create a new promise to wait for the child process to close
        let closeResolver: (() => void) | null = null;
        currentChildClosePromise = new Promise<void>(resolve => {
            closeResolver = resolve;
        });

        // start a new child
        console.info(chalk.cyan(`⌛  Forking..`));
        currentChildProcess = fork(serverEntry, {
            env: process.env,
            execArgv: ['--enable-source-maps'],
        });

        currentChildProcess.on('message', message => {
            // @ts-ignore
            if (message.serverUrl) {
                // @ts-ignore
                serverUrl.set(message.serverUrl);
                // @ts-ignore
                console.info(chalk.cyan(`⌛  Backend server internally served on ${message.serverUrl}..`));
            }
        });

        currentChildProcess.on('spawn', () => {
            console.info(chalk.cyan(`⌛  Spawning backend..`));
        });

        currentChildProcess.on('close', code => {
            if (code) {
                console.warn(chalk.yellow(`⚠️ child process exited with ${code}`));
                console.warn(chalk.yellow('⚠️ setup stopped to avoid infinite forks, make changes to proceed'));
            } else {
                console.warn(chalk.yellow(`⚠️ child process exited successfully`));
            }

            if (closeResolver) {
                // resolve the promise
                closeResolver();
            }

            // reset child variables to null
            currentChildProcess = null;
            closeResolver = null;
            currentChildClosePromise = null;
        });
    };

    return startServerInChild;
};

export default getStartBackend;

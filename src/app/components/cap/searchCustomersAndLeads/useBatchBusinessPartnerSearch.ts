import { delay } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useGetCapBusinessPartnersFromLeadQuery } from '../../../api/queries/getCapBusinessPartnersFromLead';
import { BusinessPartnerSearchField } from '../../../api/types';
import isKYCPresetHasCurrentVehicleFields from '../../../utilities/kycPresets/isKYCPresetHasCurrentVehicleFields';
import type { Action, State } from './useReducer';

type UseBatchSearchProps = {
    state: State;
    dispatch: React.Dispatch<Action>;
    onLeadDetails: boolean;
    pagination: { limit: number; offset: number };
};

const useBatchBusinessPartnerSearch = ({ state, dispatch, onLeadDetails, pagination }: UseBatchSearchProps) => {
    const { lead, applicationModuleId, capModuleId, eventId, batchSearch } = state;

    if (!lead) {
        return { startBatchSearch: () => {}, totalBatches: 0, SEARCH_BATCHES: [], shouldUseBatchSearch: false };
    }

    const searchBatches = useMemo(() => {
        const hasPorscheId = (() => {
            if (lead.customer.__typename === 'LocalCustomer') {
                return Boolean(lead.customer.customerCiamId);
            }

            return false;
        })();

        const hasCurrentVehicleFields = isKYCPresetHasCurrentVehicleFields(lead.customerKYC);

        const isJapan = lead.company?.countryCode === 'JP';

        const firstLastNameBatch = isJapan
            ? [BusinessPartnerSearchField.FirstAndLastName, BusinessPartnerSearchField.FirstAndLastNameJapan]
            : [BusinessPartnerSearchField.FirstAndLastName];

        return [
            [BusinessPartnerSearchField.Email, BusinessPartnerSearchField.Phone],
            firstLastNameBatch,
            ...(hasPorscheId || hasCurrentVehicleFields
                ? [[BusinessPartnerSearchField.Vin, BusinessPartnerSearchField.PorscheId]]
                : []),
        ];
    }, [lead]);

    const searchFromLead = useMemo(
        () => Boolean(onLeadDetails ? lead?.id : lead?.id && lead?.capValues),
        [onLeadDetails, lead]
    );

    const eventIdForBpSearch = useMemo(
        () => (lead?.__typename === 'EventLead' && lead?.eventId ? lead.eventId : eventId),
        [lead, eventId]
    );

    const leadSearchQuery = useGetCapBusinessPartnersFromLeadQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            applicationModuleId,
            eventId: eventIdForBpSearch,
            capModuleId,
            leadId: lead?.id,
            searchFields: searchBatches[batchSearch.currentBatch] || [],
            pagination,
        },
        skip: true,
    });

    const executeBatchSearch = useCallback(
        async (batchIndex: number) => {
            if (!searchFromLead) {
                console.warn('Batch search should only be used for lead-based searches');

                return;
            }

            if (batchIndex >= searchBatches.length) {
                // All batches completed
                dispatch({
                    type: 'setBatchSearch',
                    batchSearch: { searchInProgress: false },
                });

                return;
            }

            const currentBatch = searchBatches[batchIndex];

            dispatch({
                type: 'setBatchSearch',
                batchSearch: {
                    currentBatch: batchIndex,
                    searchInProgress: true,
                },
            });

            try {
                const result = await leadSearchQuery.refetch({
                    searchFields: currentBatch,
                });

                const businessPartners = result.data?.businessPartners?.items || [];

                if (businessPartners.length > 0) {
                    // Found results - stop searching
                    dispatch({
                        type: 'setBatchSearch',
                        batchSearch: {
                            hasResults: true,
                            searchInProgress: false,
                            batchResults: businessPartners,
                            batchesCompleted: searchBatches.slice(0, batchIndex + 1),
                        },
                    });

                    dispatch({
                        type: 'setStateValues',
                        values: {
                            businessPartners,
                            firstLoad: false,
                        },
                    });
                } else {
                    // No results - continue to next batch
                    dispatch({
                        type: 'setBatchSearch',
                        batchSearch: {
                            batchesCompleted: searchBatches.slice(0, batchIndex + 1),
                        },
                    });

                    // Continue to next batch after a brief delay
                    setTimeout(() => {
                        executeBatchSearch(batchIndex + 1);
                    }, 500);
                }
            } catch (error) {
                console.error(`Batch ${batchIndex + 1} search failed:`, error);

                // Continue to next batch even if current one fails
                dispatch({
                    type: 'setBatchSearch',
                    batchSearch: {
                        batchesCompleted: searchBatches.slice(0, batchIndex + 1),
                    },
                });

                // Continue to next batch after a brief delay
                delay(500, () => executeBatchSearch(batchIndex + 1));
            }
        },
        [searchFromLead, searchBatches, dispatch, leadSearchQuery]
    );

    const startBatchSearch = useCallback(() => {
        if (!searchFromLead) {
            console.warn('Batch search should only be used for lead-based searches');

            return;
        }

        dispatch({ type: 'resetBatchSearch' });

        dispatch({
            type: 'setBatchSearch',
            batchSearch: { wasStarted: true },
        });

        dispatch({
            type: 'setStateValues',
            values: {
                businessPartners: [],
                searchBPError: false,
            },
        });

        executeBatchSearch(0);
    }, [dispatch, executeBatchSearch, searchFromLead]);

    return {
        startBatchSearch,
        totalBatches: searchBatches.length,
        SEARCH_BATCHES: searchBatches,
        shouldUseBatchSearch: searchFromLead,
    };
};

export default useBatchBusinessPartnerSearch;

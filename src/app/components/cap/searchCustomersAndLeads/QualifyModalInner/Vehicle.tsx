import { GetLocalVariantsOptionsQuery, LaunchPadModuleSpecsFragment, LeadDataFragment } from '../../../../api';
import LeadFinderVehicle from './LeadFinderVehicle';
import LeadGenericVehicle from './LeadGenericVehicle';

interface VehicleProps {
    lead: LeadDataFragment;
    dealerId: string;
    availableLocalVariantsForModule: GetLocalVariantsOptionsQuery;
    dealerVehicles: LaunchPadModuleSpecsFragment['dealerVehicles'];
}

const Vehicle = ({ lead, dealerId, availableLocalVariantsForModule, dealerVehicles }: VehicleProps) => {
    if (lead?.vehicle?.__typename === 'FinderVehicle') {
        return <LeadFinderVehicle lead={lead} />;
    }

    return (
        <LeadGenericVehicle
            availableLocalVariantsForModule={availableLocalVariantsForModule}
            dealerId={dealerId}
            dealerVehicles={dealerVehicles}
        />
    );
};

export default Vehicle;

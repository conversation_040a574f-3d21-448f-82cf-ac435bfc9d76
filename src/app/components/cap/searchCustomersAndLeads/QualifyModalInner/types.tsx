import type { FinderVehicleCondition, TradeInVehiclePayload } from '../../../../api/types';
import type { KYCPresetFormFields } from '../../../../utilities/kycPresets';

export type QualifyContactValues = {
    // in Finder vehicle, we dont have model attach it.
    vehicleModel?: string;
    vehicleId: string;
    assigneeId: string;
    vehicleCondition: FinderVehicleCondition;
    campaignId: string;
    purchaseIntention: Date;
    customer: { fields: KYCPresetFormFields };
    tradeInVehicle: TradeInVehiclePayload[];
};

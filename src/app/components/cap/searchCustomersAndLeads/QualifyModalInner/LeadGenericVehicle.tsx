import { Col } from 'antd'; // Assuming Ant Design is used
import { useFormikContext } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { GetLocalVariantsOptionsQuery, LaunchPadModuleSpecsFragment } from '../../../../api';
import { useThemeComponents } from '../../../../themes/hooks';
import { defaultFilterOption } from '../../../fields/SelectField';
import { defaultColSpan } from '../../../leads/shared';
import useModelVariantOptions from '../../../leads/shared/useModelVariantOptions';
import { QualifyContactValues } from './types';

interface LeadGenericVehicleProps {
    dealerId: string;
    availableLocalVariantsForModule: GetLocalVariantsOptionsQuery;
    dealerVehicles: LaunchPadModuleSpecsFragment['dealerVehicles'];
}

const LeadGenericVehicle = ({ availableLocalVariantsForModule, dealerVehicles, dealerId }: LeadGenericVehicleProps) => {
    const {
        FormFields: { SelectField },
    } = useThemeComponents();
    const { t } = useTranslation();

    const { values, setFieldValue } = useFormikContext<QualifyContactValues>();

    const [modelOptions, variantOptions] = useModelVariantOptions({
        dealerId,
        modelId: values.vehicleModel,
        availableLocalVariantsForModule,
        dealerVehicles,
    });

    const onVehicleModelChange = useCallback(() => setFieldValue('vehicleId', null, false), [setFieldValue]);

    return (
        <>
            <Col key="vehicleModel" {...defaultColSpan}>
                <SelectField
                    key="vehicleModel"
                    filterOption={defaultFilterOption}
                    label={t('launchpadLeadDetails:qualifyModal.fields.vehicleModel')}
                    name="vehicleModel"
                    onChange={onVehicleModelChange}
                    options={modelOptions}
                    required
                    showSearch
                />
            </Col>
            <Col key="vehicleId" {...defaultColSpan}>
                <SelectField
                    key="vehicleId"
                    disabled={!values.vehicleModel}
                    filterOption={defaultFilterOption}
                    label={t('launchpadLeadDetails:qualifyModal.fields.variant')}
                    name="vehicleId"
                    options={variantOptions}
                    required
                    showSearch
                />
            </Col>
        </>
    );
};

export default LeadGenericVehicle;

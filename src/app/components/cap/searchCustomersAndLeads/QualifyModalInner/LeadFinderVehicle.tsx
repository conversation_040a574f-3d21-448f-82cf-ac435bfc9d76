import { Col } from 'antd'; // Assuming Ant Design is used
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { LeadDataFragment } from '../../../../api';
import { useThemeComponents } from '../../../../themes/hooks';
import { defaultColSpan } from '../../../leads/shared';
import retrieveLeadVehicleDetails from '../../../leads/shared/retrieveLeadVehicleDetails';

interface LeadFinderVehicleProps {
    lead: LeadDataFragment;
}

const LeadFinderVehicle = ({ lead }: LeadFinderVehicleProps) => {
    const {
        FormFields: { DisplayField },
    } = useThemeComponents();
    const { t } = useTranslation();

    const { vehicleModelName, vehicleVariantName } = useMemo(() => retrieveLeadVehicleDetails(lead), [lead]);

    return (
        <>
            <Col key="vehicleModel" {...defaultColSpan}>
                <DisplayField
                    key="vehicleModel"
                    label={t('launchpadLeadDetails:qualifyModal.fields.vehicleModel')}
                    value={vehicleModelName}
                />
            </Col>
            <Col key="vehicleId" {...defaultColSpan}>
                <DisplayField
                    key="vehicleId"
                    label={t('launchpadLeadDetails:qualifyModal.fields.variant')}
                    value={vehicleVariantName}
                />
            </Col>
        </>
    );
};

export default LeadFinderVehicle;

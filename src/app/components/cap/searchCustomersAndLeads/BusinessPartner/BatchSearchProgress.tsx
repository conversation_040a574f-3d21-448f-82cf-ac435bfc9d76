import { PSpinner } from '@porsche-design-system/components-react';
import {
    spacingStaticSmall,
    spacingStaticMedium,
    textSmallStyle,
    themeLightContrastHigh,
    themeLightPrimary,
} from '@porsche-design-system/components-react/styles';
import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { BusinessPartnerSearchField } from '../../../../api/types';
import type { State } from '../useReducer';

const ProgressContainer = styled.div`
    width: 100%;
    gap: ${spacingStaticMedium};
    display: flex;
    flex-direction: column;
`;

const BatchItem = styled.div`
    display: flex;
    gap: ${spacingStaticSmall};
`;

const BatchLabel = styled.span({
    color: themeLightPrimary,
    ...textSmallStyle,
});

const ResultCount = styled.span({
    color: themeLightContrastHigh,
    ...textSmallStyle,
});

const IconContainer = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
`;

type BatchSearchProgressProps = {
    resultCount?: number;
    searchBatches: BusinessPartnerSearchField[][];
    state: State;
};

const BatchSearchProgress: React.FC<BatchSearchProgressProps> = ({ state, searchBatches, resultCount = 0 }) => {
    const { t } = useTranslation(['capApplication']);

    const { currentBatch, searchInProgress, hasResults, batchesCompleted } = state.batchSearch;

    const getBatchLabel = useCallback(
        (batch: BusinessPartnerSearchField[]): string => {
            const fieldLabels = {
                [BusinessPartnerSearchField.Email]: t('capApplication:batchSearchProgress.searchField.email'),
                [BusinessPartnerSearchField.Phone]: t('capApplication:batchSearchProgress.searchField.phone'),
                [BusinessPartnerSearchField.Vin]: t('capApplication:batchSearchProgress.searchField.vin'),
                [BusinessPartnerSearchField.PorscheId]: t('capApplication:batchSearchProgress.searchField.porscheId'),
                [BusinessPartnerSearchField.FirstAndLastName]: t(
                    'capApplication:batchSearchProgress.searchField.firstAndLastName'
                ),
                [BusinessPartnerSearchField.FirstAndLastNameJapan]: t(
                    'capApplication:batchSearchProgress.searchField.firstAndLastName'
                ),
            };

            if (
                batch.includes(BusinessPartnerSearchField.FirstAndLastName) &&
                batch.includes(BusinessPartnerSearchField.FirstAndLastNameJapan)
            ) {
                return fieldLabels[BusinessPartnerSearchField.FirstAndLastName];
            }

            return batch
                .map(field => fieldLabels[field])
                .join(` ${t('capApplication:batchSearchProgress.searchField.or')} `);
        },
        [t]
    );

    const getResultCountForBatch = useCallback(
        (batchIndex: number): number => {
            if (hasResults && currentBatch === batchIndex) {
                return resultCount;
            }

            return 0;
        },
        [hasResults, currentBatch, resultCount]
    );

    const batchItems = useMemo(
        () =>
            searchBatches
                .filter(
                    batch =>
                        batchesCompleted.some(completed => completed === batch) ||
                        currentBatch === searchBatches.indexOf(batch)
                )
                .map((batch, index) => {
                    const batchResultCount = getResultCountForBatch(index);
                    const isActive = currentBatch === index && searchInProgress;

                    return (
                        <BatchItem key={batch[0]}>
                            <BatchLabel>
                                {t('capApplication:batchSearchProgress.searchWith')} {getBatchLabel(batch)}:{' '}
                            </BatchLabel>
                            {isActive && (
                                <IconContainer>
                                    <PSpinner size="inherit" />
                                </IconContainer>
                            )}
                            {!isActive && (
                                <ResultCount>
                                    {t(
                                        `capApplication:batchSearchProgress.${batchResultCount > 1 ? 'multipleResultCount' : 'resultCount'}`,
                                        { count: batchResultCount }
                                    )}
                                </ResultCount>
                            )}
                        </BatchItem>
                    );
                }),
        [searchBatches, batchesCompleted, currentBatch, getResultCountForBatch, searchInProgress, t, getBatchLabel]
    );

    return <ProgressContainer>{batchItems}</ProgressContainer>;
};

export default BatchSearchProgress;

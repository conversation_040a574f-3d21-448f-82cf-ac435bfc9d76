import { LeadDataFragment } from '../../../api/fragments/LeadData';
import { FinderVehicleCondition } from '../../../api/types';

export const retrieveLeadVehicleDetails = (lead: LeadDataFragment) => {
    switch (lead.__typename) {
        case 'FinderLead': {
            const vehicle = lead.vehicle.__typename === 'FinderVehicle' ? lead.vehicle : null;
            if (!vehicle) {
                throw new Error('Vehicle data is not available for FinderLead');
            }

            return {
                vehicleModelName: vehicle.listing.vehicle.modelSeries.localize,
                vehicleVariantName: vehicle.name.defaultValue,
                vehicleCondition:
                    vehicle.listing.vehicle.condition.value === 'preowned'
                        ? FinderVehicleCondition.Preowned
                        : FinderVehicleCondition.New,
                purchaseIntention: lead.purchaseIntention ? new Date(lead.purchaseIntention) : undefined,
                campaignId: lead.campaignValues?.capCampaignId,
            };
        }

        case 'StandardLead':
        case 'ConfiguratorLead': {
            return {
                vehicleModelName: null,
                vehicleVariantName: null,
                vehicleCondition: FinderVehicleCondition.New,
                campaignId: lead.campaignValues?.capCampaignId,
                purchaseIntention: lead.purchaseIntention ? new Date(lead.purchaseIntention) : undefined,
            };
        }

        default: {
            return {
                vehicleModelName: null,
                vehicleVariantName: null,
                vehicleCondition: lead.vehicleCondition,
                campaignId: lead.campaignValues?.capCampaignId,
                purchaseIntention: lead.purchaseIntention ? new Date(lead.purchaseIntention) : undefined,
            };
        }
    }
};

export default retrieveLeadVehicleDetails;

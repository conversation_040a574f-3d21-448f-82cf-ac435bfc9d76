import { ApolloError } from '@apollo/client';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useCancelApplicationMutation } from '../../../api/mutations/cancelApplication';
import { ApplicationStage } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import Button from '../../../themes/porscheV3/Button';
import Modal from '../../../themes/porscheV3/Modal';

const Container = styled.div`
    margin-bottom: 32px;
`;

type UseVoidAppointmentModalProps = {
    applicationId: string;
    appointmenStage: ApplicationStage.Appointment | ApplicationStage.VisitAppointment;
    refetchApplication?: () => void;
};

type VoidAppointmentModalProps = UseVoidAppointmentModalProps & {
    visible: boolean;
    onClose?: (isSaved: boolean) => void;
};

const VoidAppointmentModal = ({
    applicationId,
    appointmenStage,
    onClose,
    visible,
    refetchApplication,
}: VoidAppointmentModalProps) => {
    const translationKey = useMemo(
        () =>
            appointmenStage === ApplicationStage.Appointment
                ? 'launchpadAppointmentDetails'
                : 'launchpadShowroomVisitDetails',
        [appointmenStage]
    );

    const { t } = useTranslation([translationKey]);
    const { notification, Typography } = useThemeComponents();

    const [cancelApplicationMutation, { loading: submissionLoading }] = useCancelApplicationMutation();

    const onCancel = useCallback(() => {
        onClose?.(false);
    }, [onClose]);

    const voidAppointmentAction = useCallback(async () => {
        try {
            notification.loading({
                content: t(`${translationKey}:voidModal.updating`),
                duration: 0,
                key: 'primary',
            });

            const { errors } = await cancelApplicationMutation({
                variables: {
                    applicationId,
                    stage: appointmenStage,
                },
            });

            if (!errors) {
                notification.success({
                    content: t(`${translationKey}:voidModal.submissionCompleteMessage`),
                    key: 'primary',
                });

                onClose?.(true);
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error({
                    content: error.graphQLErrors[0]?.message || error.message,
                    key: 'primary',
                });
            } else {
                console.error(error);
                notification.error({
                    content: t(`${translationKey}:voidModal.errorMessage`),
                    key: 'primary',
                });
            }
        } finally {
            notification.destroy('primary');
        }
    }, [notification, t, translationKey, cancelApplicationMutation, applicationId, appointmenStage, onClose]);

    const footerButtons = useMemo(
        () => [
            <Button
                key="submit-void"
                loading={submissionLoading}
                onClick={e => {
                    e.preventDefault();
                    voidAppointmentAction();
                }}
                type="primary"
                block
            >
                {t(`${translationKey}:voidModal.buttons.confirm`)}
            </Button>,
            <Button key="cancel" disabled={submissionLoading} onClick={onCancel} type="tertiary" block>
                {t(`${translationKey}:voidModal.buttons.cancel`)}
            </Button>,
        ],
        [submissionLoading, t, translationKey, onCancel, voidAppointmentAction]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButtons}
            maskClosable={!submissionLoading}
            onCancel={onCancel}
            open={visible}
            title={t(`${translationKey}:voidModal.title`)}
            centered
            destroyOnClose
        >
            <Container>
                <Typography.Text level={4}>{t(`${translationKey}:voidModal.confirmationMessage`)}</Typography.Text>
            </Container>
        </Modal>
    );
};

export default VoidAppointmentModal;

export const useVoidAppointmentModal = ({ refetchApplication, ...props }: UseVoidAppointmentModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: (isSaved: boolean) => {
                setVisible(false);
                if (isSaved) {
                    refetchApplication?.();
                }
            },
        }),
        [refetchApplication]
    );

    return {
        ...actions,
        render: () => <VoidAppointmentModal onClose={actions.close} visible={visible} {...props} />,
    };
};

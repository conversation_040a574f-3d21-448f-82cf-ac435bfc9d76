import { Divider as AntdDivider } from 'antd';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { ApplicationAgreementDataFragment } from '../../../api/fragments/ApplicationAgreementData';
import AgreementsGrid from '../../../pages/shared/CIPage/ConsentAndDeclarations/AgreementsGrid';

const Container = styled.div`
    display: flex;
    flex-direction: column;

    & .ant-form-item-label > label {
        font-size: 16px;
    }
`;

const Title = styled.h4<{ size?: 'default' | 'large' }>`
    font-size: 20px;
    margin-bottom: ${({ size = 'default' }) => (size === 'default' ? '18px' : '24px')};
    font-weight: 900;
`;

const Divider = styled(AntdDivider)`
    &.ant-divider-horizontal {
        margin-bottom: 50px;
        margin-top: 50px;
    }
`;

export type ConsentsAndDeclarationsProps = {
    applicationAgreements: ApplicationAgreementDataFragment[];
};

const ConsentsAndDeclarations = ({ applicationAgreements }: ConsentsAndDeclarationsProps) => {
    const { t } = useTranslation('eventApplicantForm');

    if (!applicationAgreements || applicationAgreements.length === 0) {
        return null;
    }

    return (
        <Container>
            <Title>{t('eventApplicantForm:consentsAndDeclarations.title')}</Title>
            <AgreementsGrid agreements={applicationAgreements} prefix="agreements" />
        </Container>
    );
};

export default ConsentsAndDeclarations;

import { useMemo } from 'react';
import type { ApplicationAgreementDataFragment } from '../../../api/fragments/ApplicationAgreementData';
import type { KycFieldSpecsFragment } from '../../../api/fragments/KYCFieldSpecs';
import type { LocalCustomerManagementModule } from '../../../api/types';
import useAgreementsValidator from '../../../pages/shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useKYCFormValidator from '../../../utilities/kycPresets/useKYCValidators';
import validators from '../../../utilities/validators';

const useNewContactValidator = ({
    applicationAgreements,
    applicationKycPresets,
    extraSettings,
    countryCode,
}: {
    applicationAgreements: ApplicationAgreementDataFragment[];
    applicationKycPresets: KycFieldSpecsFragment[];
    extraSettings: LocalCustomerManagementModule['extraSettings'];
    countryCode: string;
}) => {
    const agreementValidators = useAgreementsValidator(applicationAgreements, 'agreements');
    const applicantsValidator = useKYCFormValidator({
        field: applicationKycPresets,
        extraSettings,
        moduleCountryCode: countryCode,
        prefix: 'customer.fields',
        saveDraft: false,
    });

    return useMemo(
        () => validators.compose(applicantsValidator, agreementValidators),
        [agreementValidators, applicantsValidator]
    );
};

export default useNewContactValidator;

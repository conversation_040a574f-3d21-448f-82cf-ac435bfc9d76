import { themeLightBackgroundSurface } from '@porsche-design-system/components-react/styles';
import { useMemo } from 'react';

export interface ProgressBarProps {
    value: number;
}

const ProgressBar = ({ value }: ProgressBarProps) => {
    const [activeColorA, activeColorB] = useMemo(() => {
        if (value >= 100) {
            return ['#9AD09D', '#71B375'];
        }

        if (value >= 80 && value <= 99) {
            return ['#9AB2E7', '#5885E9'];
        }

        if (value >= 50 && value <= 79) {
            return ['#FFD572', '#FEBD38'];
        }

        return ['#D78B87', '#D5615B'];
    }, [value]);

    return (
        <div
            style={{
                width: '100%',
                overflow: 'hidden',
                borderRadius: '10px',
                backgroundColor: themeLightBackgroundSurface,
            }}
        >
            <div
                style={{
                    width: `${value > 100 ? 100 : value}%`,
                    height: '20px',
                    borderRadius: '10px',
                    background: `linear-gradient(${activeColorA}, ${activeColorB})`,
                    transitionProperty: 'all',
                    transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    transitionDuration: '500ms',
                }}
            />
        </div>
    );
};

export default ProgressBar;

import { TreeSelect } from 'antd';
import { useField } from 'formik';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import FormItem from './FormItem';
import { TreeSelectFieldProps } from './shared';

const TreeSelectField = ({
    name,
    required,
    label,
    itemProps,
    tooltip,
    onChange,
    enhanceTreeData,
    treeData,
    titleSelectable = false,
    ...props
}: TreeSelectFieldProps) => {
    const { t } = useTranslation('common');
    const [treeSelectedValue, setTreeSelectedValue] = useState<string>(null);
    const [field, meta, { setValue }] = useField({ name });
    const enhancedOnChange = useCallback(
        value => {
            const selectedValue = value.split('.')[1];
            setValue(selectedValue);

            if (onChange) {
                onChange(selectedValue);
            }
        },
        [setValue, onChange]
    );

    const adjustedTreeData = useMemo(() => {
        // This for making the parent group can't be selected
        const adjustedData = treeData.map(tree => ({
            ...tree,
            selectable: titleSelectable,
            children: tree.children.map(children => ({
                ...children,
                isLeaf: true,
                key: `${tree.value}.${children.value}`,
                value: `${tree.value}.${children.value}`,
            })),
        }));

        return enhanceTreeData ? enhanceTreeData(adjustedData) : adjustedData;
    }, [enhanceTreeData, titleSelectable, treeData]);

    useEffect(() => {
        const firstParentOfValue = adjustedTreeData.find(tree =>
            tree.children.find(children => children.value.includes(field.value))
        );

        if (firstParentOfValue) {
            setTreeSelectedValue(`${firstParentOfValue.value}.${field.value}`);
        } else {
            setTreeSelectedValue(null);
        }
    }, [adjustedTreeData, field.value]);

    return (
        <FormItem {...itemProps} label={label} meta={meta} required={required} tooltip={tooltip}>
            <TreeSelect
                // spread props
                {...props}
                // then spread the field properties itself
                {...field}
                onChange={enhancedOnChange}
                placeholder={t('common:pleaseSelect')}
                treeData={adjustedTreeData}
                value={treeSelectedValue}
                virtual={false}
                showArrow
            />
        </FormItem>
    );
};

export default memo(TreeSelectField);

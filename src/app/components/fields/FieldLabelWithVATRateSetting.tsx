import { CalendarOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Button, ButtonProps, FormItemProps, Tooltip } from 'antd';
import { LabelTooltipType, WrapperTooltipProps } from 'antd/es/form/FormItemLabel';
import { isValidElement, useCallback } from 'react';
import styled from 'styled-components';

const FieldLabelWithVATRateSettingContainer = styled.div`
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 22px;
`;

const LabelContainer = styled.span`
    display: flex;
    align-items: center;
`;

const StyledTooltip = styled(Tooltip)`
    cursor: help;
    margin-left: 4px;
    color: rgba(0, 0, 0, 0.45);
`;

type Props = {
    label: JSX.Element | string | React.ReactNode;
    onSettingButtonClicked?: ButtonProps['onClick'];
    tooltip?: FormItemProps['tooltip'];
};

const convertToTooltipProps = (tooltip: LabelTooltipType): WrapperTooltipProps | null => {
    if (!tooltip) {
        return {};
    }

    if (typeof tooltip === 'object' && !isValidElement(tooltip)) {
        return {
            ...tooltip,
            placement: 'top',
        } as WrapperTooltipProps;
    }

    return {
        title: tooltip,
        placement: 'top',
    };
};

const useRenderTooltip = (tooltip: LabelTooltipType) => {
    const renderTooltip = useCallback(() => {
        const tooltipProps = convertToTooltipProps(tooltip);
        const icon = tooltipProps?.icon;

        return tooltip ? <StyledTooltip {...tooltipProps}>{icon || <InfoCircleOutlined />}</StyledTooltip> : null;
    }, [tooltip]);

    return renderTooltip;
};

const FieldLabelWithVATRateSetting = ({ label, onSettingButtonClicked, tooltip }: Props) => {
    const renderTooltip = useRenderTooltip(tooltip);

    if (!label) {
        return null;
    }

    return (
        <FieldLabelWithVATRateSettingContainer>
            <LabelContainer>
                {label}
                {renderTooltip()}
            </LabelContainer>
            <Button
                icon={<CalendarOutlined style={{ color: 'var(--ant-primary-color)' }} />}
                onClick={onSettingButtonClicked}
                size="small"
                type="text"
            />
        </FieldLabelWithVATRateSettingContainer>
    );
};

export default FieldLabelWithVATRateSetting;

import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Button, Form, Result, Space, Spin } from 'antd';
import { useField } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetDealersOptionsQuery } from '../../../../api/queries/getDealersOptions';
import type { ListDealerOption } from '../../../../api/types';
import DrawerWithAutoWidth from '../../../DrawerWithAutoWidth';
import type { DrawerWithAutoWidthProps } from '../../../DrawerWithAutoWidthProps';
import { TreeSelectFieldProps } from '../../shared';
import { useAddDealershipModal } from '../AddDealershipModal';
import { type DealershipDrawerProps, renderSelectField } from '../DealershipSelectField/DealershipDrawer';

export type DealershipTreeSelectDrawerProps = Pick<DrawerWithAutoWidthProps, 'title' | 'visible' | 'onClose'> &
    Pick<TreeSelectFieldProps, 'treeData'> &
    Pick<DealershipDrawerProps, 'name' | 'dealers' | 'renderField' | 'companyId'> & {
        ableToAddDealer?: boolean;
        info?: JSX.Element;
    };

const DealershipTreeSelectDrawer = ({
    onClose,
    title,
    visible,
    name,
    dealers: dealersFromProps,
    renderField = renderSelectField,
    treeData,
    companyId,
    ableToAddDealer = false,
    info,
}: DealershipTreeSelectDrawerProps) => {
    const { t } = useTranslation(['dealershipField']);
    const [{ value: dealerships }, , { setValue }] = useField<{ dealerId: string; relatedId: string }[]>({
        name,
    });

    const { data, loading } = useGetDealersOptionsQuery({
        fetchPolicy: 'cache-and-network',
        skip: !!dealersFromProps,
        variables: { filter: companyId ? { companyId } : null },
    });

    const dealersFromQuery = data?.dealers;

    const allDealers = useMemo(() => dealersFromProps || dealersFromQuery || [], [dealersFromProps, dealersFromQuery]);

    const remainingDealers = useMemo(() => {
        if (!dealerships) {
            return allDealers;
        }

        // exclude dealers already filled
        return allDealers.filter(dealer => !dealerships.some(dealership => dealership.dealerId === dealer.id));
    }, [allDealers, dealerships]);

    const actions = useMemo(() => {
        const currentDealers = dealerships || [];

        return {
            add: (dealerId: string) => setValue([...currentDealers, { dealerId, relatedId: null }]),
            delete: (index: number) => {
                const newValue = [...currentDealers];
                newValue.splice(index, 1);
                setValue(newValue);
            },
        };
    }, [setValue, dealerships]);

    const addModal = useAddDealershipModal(remainingDealers, actions.add);

    const cleanedDealerships = useMemo((): DealershipItem[] => {
        if (!dealerships) {
            return [];
        }

        return dealerships
            .map((dealership, index) => {
                const dealer = allDealers.find(dealer => dealer.id === dealership.dealerId);

                return { ...dealership, dealer, name: `${name}[${index}].relatedId`, index, treeData };
            })
            .filter(dealership => !!dealership.dealer)
            .sort((a, b) => a.dealer.displayName.localeCompare(b.dealer.displayName));
    }, [dealerships, allDealers, name, treeData]);

    const content = (
        <Space direction="vertical" style={{ width: '100%' }}>
            {cleanedDealerships.length > 0 && (
                <Form layout="vertical">
                    {cleanedDealerships.map(dealership =>
                        renderField({
                            dealership,
                            onDelete: () => actions.delete(dealership.index),
                            options: (treeData.find(tree => tree.value === dealership.dealerId)?.children ?? []).map(
                                ({ title, value }) => ({ label: title, value })
                            ),
                            allowClear: true,
                            showSearch: true,
                            hideDeleteDealer: true,
                        })
                    )}
                </Form>
            )}
            {ableToAddDealer && remainingDealers.length > 0 && (
                <>
                    <Button icon={<PlusOutlined />} onClick={addModal.open} type="dashed" block>
                        {t('dealershipField:default.addDealer')}
                    </Button>
                    {addModal.render()}
                </>
            )}

            {!cleanedDealerships.length && !remainingDealers.length && (
                <Result status="info" subTitle="No dealer registered in the system yet" />
            )}
        </Space>
    );

    return (
        <DrawerWithAutoWidth
            maxWidth={30}
            onClose={onClose}
            placement="right"
            title={title}
            visible={visible}
            destroyOnClose
        >
            <Space direction="vertical" size={24}>
                {info}
                <Spin spinning={loading}>{content}</Spin>
            </Space>
        </DrawerWithAutoWidth>
    );
};

export type DealershipItem = {
    dealerId: string;
    index: number;
    dealer: ListDealerOption;
    name: string;
};

export default DealershipTreeSelectDrawer;

export const useDealershipTreeSelectDrawer = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (
            props: Pick<
                DealershipTreeSelectDrawerProps,
                'name' | 'title' | 'renderField' | 'dealers' | 'treeData' | 'companyId' | 'ableToAddDealer' | 'info'
            >
        ) => (
            <DealershipTreeSelectDrawer
                {...props}
                name={`${props.name}.overrides`}
                onClose={actions.close}
                visible={visible}
            />
        ),
    };
};

import { ShopOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useThemeComponents } from '../../../../themes/hooks';
import { TreeSelectFieldProps } from '../../shared';
import { DealershipTreeSelectDrawerProps, useDealershipTreeSelectDrawer } from './DealershipTreeSelectDrawer';
import Down from '../../../../icons/down.svg';

export type DealershipTreeSelectFieldProps = TreeSelectFieldProps &
    Pick<DealershipTreeSelectDrawerProps, 'dealers' | 'companyId' | 'info'> & {
        // if not provided, fallback to options
        dealerTreeData?: TreeSelectFieldProps['treeData'];
    };

const DealershipTreeSelectField = ({
    name,
    dealers,
    disabled,
    dealerTreeData,
    treeData,
    companyId,
    info,
    ...props
}: DealershipTreeSelectFieldProps) => {
    const drawer = useDealershipTreeSelectDrawer();
    const { FormFields } = useThemeComponents();

    // hide button completely when disable
    const button = disabled ? null : <Button icon={<ShopOutlined />} onClick={drawer.open} size="small" type="link" />;

    return (
        <>
            <FormFields.TreeSelectField
                disabled={disabled}
                name={`${name}.defaultId`}
                {...props}
                suffixIcon={
                    <>
                        {button}
                        <Down fill="var(--ant-primary-color)" />
                    </>
                }
                treeData={treeData}
            />
            {drawer.render({
                name,
                title: props.label,
                dealers,
                treeData: dealerTreeData || treeData,
                companyId,
                info,
            })}
        </>
    );
};

export default DealershipTreeSelectField;

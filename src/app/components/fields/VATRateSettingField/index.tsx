import { Col, Row } from 'antd';
import dayjs from 'dayjs';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import DatePickerField from '../../../themes/admin/Fields/DatePickerField';
import InputNumberField from '../../../themes/admin/Fields/InputNumberField';
import FieldLabelWithRemove from '../FieldLabelWithRemove';
import FieldLabelWithVATRateSetting from '../FieldLabelWithVATRateSetting';
import { VATRateSettingFieldProps } from './VATRateSettingFieldProps';
import { useVATRateTableDrawer } from './VATRateTableDrawer';
import type { VATRateTableDrawerProps } from './VATRateTableDrawerProps';

const VATRateSettingField = ({
    name,
    title,
    applyDefaultStyles = true,
    disabled = false,
    maxVATTableLength = 10,
    ...props
}: VATRateSettingFieldProps) => {
    const drawer = useVATRateTableDrawer();
    const { label, tooltip, ...inputFieldProps } = props;
    const { t } = useTranslation(['vatRateSettingField']);

    const renderInputField: VATRateTableDrawerProps['renderField'] = (vatRateSettingItem, onDelete, disabled) => (
        <Row key={vatRateSettingItem.index} gutter={[10, 2]} style={{ display: 'flex' }}>
            <Col xs={12}>
                {/* eslint-disable-next-line react/jsx-no-undef */}
                <DatePickerField
                    key={`${vatRateSettingItem.name}.startDate`}
                    defaultValue={dayjs(vatRateSettingItem.startDate)}
                    disabled={disabled}
                    label={t('drawer.startDateTitle')}
                    name={`${vatRateSettingItem.name}.startDate`}
                    required
                />
            </Col>
            <Col xs={12}>
                <InputNumberField
                    key={`${vatRateSettingItem.name}.value`}
                    addonAfter="%"
                    disabled={disabled}
                    label={
                        !disabled ? (
                            <FieldLabelWithRemove label={t('drawer.valueTitle')} onRemoveButtonClicked={onDelete} />
                        ) : (
                            t('drawer.valueTitle')
                        )
                    }
                    name={`${vatRateSettingItem.name}.value`}
                    required
                />
            </Col>
        </Row>
    );

    return (
        <>
            <InputNumberField
                name={`${name}.appliedVATRate`}
                {...inputFieldProps}
                addonAfter="%"
                applyDefaultStyles={applyDefaultStyles}
                defaultValue=""
                label={
                    <FieldLabelWithVATRateSetting
                        label={label}
                        onSettingButtonClicked={drawer.open}
                        tooltip={tooltip}
                    />
                }
                tooltip={label ? null : tooltip}
                disabled
            />
            {drawer.render({
                name,
                title: t('drawer.title'),
                maxVATTableLength,
                renderField: renderInputField,
            })}
        </>
    );
};

export default memo(VATRateSettingField);

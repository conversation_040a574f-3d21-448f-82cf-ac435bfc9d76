import { PlusOutlined } from '@ant-design/icons';
import { Button, Form, Space, Spin } from 'antd';
import { useField } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DrawerWithAutoWidth from '../../DrawerWithAutoWidth';
import { useGenericStateOnPageContext } from '../../contexts/shared';
import { VATRateSettingItem, type VATRateTableDrawerProps } from './VATRateTableDrawerProps';

const VATRateTableDrawer = ({
    onClose,
    title,
    name,
    renderField,
    maxVATTableLength,
    disabled,
    visible,
}: VATRateTableDrawerProps) => {
    const { t } = useTranslation(['vatRateSettingField']);
    const [{ value: vatRateTable }, , { setValue }] = useField<{ startDate: Date; value: number }[]>({
        name,
    });

    const actions = useMemo(() => {
        const currentvatRateTable = vatRateTable || [];

        return {
            add: () => setValue([...currentvatRateTable, { startDate: new Date(), value: null }]),
            delete: (index: number) => {
                const newValue = [...currentvatRateTable];
                newValue.splice(index, 1);
                setValue(newValue);
            },
        };
    }, [setValue, vatRateTable]);

    const mappedTable = useMemo((): VATRateSettingItem[] => {
        if (!vatRateTable) {
            return [];
        }

        return vatRateTable.map((item, index) => ({ ...item, name: `${name}[${index}]`, index }));
    }, [vatRateTable, name]);

    const content = (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Form layout="vertical">
                {mappedTable?.map(vatRateSettingItem =>
                    renderField(vatRateSettingItem, () => actions.delete(vatRateSettingItem.index), disabled)
                )}
            </Form>

            {!disabled && mappedTable?.length < maxVATTableLength && (
                <Button icon={<PlusOutlined />} onClick={() => actions.add()} type="dashed" block>
                    {t('addButton')}
                </Button>
            )}
        </Space>
    );

    return (
        <DrawerWithAutoWidth onClose={onClose} placement="right" title={title} visible={visible} destroyOnClose>
            <Spin spinning={false}>{content}</Spin>
        </DrawerWithAutoWidth>
    );
};

export default VATRateTableDrawer;

export const useVATRateTableDrawer = () => {
    const [visible, setVisible] = useState(false);
    const genericStateOnPageContext = useGenericStateOnPageContext();

    const withContentRefinement = useMemo(() => {
        if (!genericStateOnPageContext) {
            return null;
        }

        const {
            actions: { setIsContentRefinementGloballyDisabled },
        } = genericStateOnPageContext;

        return { disableContentRefinementGlobally: setIsContentRefinementGloballyDisabled };
    }, [genericStateOnPageContext]);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => {
                if (withContentRefinement) {
                    const { disableContentRefinementGlobally } = withContentRefinement;
                    if (disableContentRefinementGlobally) {
                        disableContentRefinementGlobally(false);
                    }
                }
                setVisible(false);
            },
        }),
        [withContentRefinement]
    );

    return {
        ...actions,
        render: (
            props: Pick<VATRateTableDrawerProps, 'name' | 'title' | 'renderField' | 'maxVATTableLength' | 'disabled'>
        ) => (
            <VATRateTableDrawer
                {...props}
                name={`${props.name}.vatRateTable`}
                onClose={actions.close}
                visible={visible}
            />
        ),
    };
};

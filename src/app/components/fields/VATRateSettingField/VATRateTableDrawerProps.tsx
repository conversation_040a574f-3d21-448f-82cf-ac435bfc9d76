import { DrawerProps } from 'antd';

export type VATRateTableDrawerProps = Pick<DrawerProps, 'title' | 'visible' | 'onClose'> & {
    name: string;
    vatRateSettings?: VATRateSettingItem[];
    renderField?: (item: VATRateSettingItem, onDelete: () => void, disabled?: boolean) => JSX.Element;
    maxVATTableLength?: number;
    disabled?: boolean;
    visible?: boolean;
};

export type VATRateSettingItem = {
    value: number;
    startDate: Date;
    index?: number;
    name?: string;
};

import { Form, Typography } from 'antd';
import { memo } from 'react';
import styled, { css } from 'styled-components';

export const StyledFormItem = styled(Form.Item)<{ applyDefaultStyles?: boolean; isError?: boolean }>`
    & .ant-form-item-label {
        & > label {
            color: rgba(0, 0, 0, 0.9);
        }
    }
    & .ant-form-item-control {
        font-size: var(--input-font-size, 16px);

        & > .ant-form-item-control-input {
            width: 100%;
            min-height: unset;

            background-color: #f5f5f5;
            border-bottom: 1px solid #d3d3d3;
            padding: 0px 11px 0 11px;

            min-height: var(--input-height, 34px);
            height: fit-content;

            ${props =>
                !props.applyDefaultStyles &&
                css`
                    border: 1px solid #d9d9d9;
                    border-radius: 2px;
                `}

            ${props =>
                props.isError &&
                css`
                    border-color: #ff4d4f;
                    background-color: #ffe2e4;
                `}
        }

        ${props =>
            props.isError &&
            css`
                .ant-form-item-explain {
                    color: #cc1922;
                    margin-top: 4px;
                    margin-bottom: 24px;
                }
            `}
    }
`;

export const StyledValue = styled(Typography)<{ disabled: boolean }>`
    white-space: pre-line;
    padding-bottom: 4px;
    padding-top: 4px;

    ${({ disabled }) =>
        disabled &&
        `
            color: rgba(0, 0, 0, 0.25);
            cursor: not-allowed;
        `}
`;

export type DisplayFieldWithInfoMessageProps = {
    label?: string;
    value: string | number | JSX.Element;
    disabled?: boolean;
    applyDefaultStyles?: boolean;
    message?: string;
    isError?: boolean;
};

const DisplayFieldWithInfoMessage = ({
    label,
    value,
    disabled = false,
    applyDefaultStyles = true,
    message,
    isError = false,
}: DisplayFieldWithInfoMessageProps) => (
    <StyledFormItem
        applyDefaultStyles={applyDefaultStyles}
        label={label}
        {...(message ? { help: message, isError } : {})}
    >
        <StyledValue disabled={disabled}>{value}</StyledValue>
    </StyledFormItem>
);

export default memo(DisplayFieldWithInfoMessage);

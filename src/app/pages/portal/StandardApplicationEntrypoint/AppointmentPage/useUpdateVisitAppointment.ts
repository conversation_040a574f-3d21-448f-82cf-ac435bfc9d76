import { useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { VisitAppointmentModuleSpecsFragment } from '../../../../api/fragments/VisitAppointmentModuleSpecs';
import { SubmitApplicantAppointmentMutationVariables } from '../../../../api/mutations/submitApplicantAppointment';
import { UpdateApplicantVisitAppointmentDocument } from '../../../../api/mutations/updateApplicantVisitAppointment';
import type {
    UpdateApplicantVisitAppointmentMutation,
    UpdateApplicantVisitAppointmentMutationVariables,
} from '../../../../api/mutations/updateApplicantVisitAppointment';

import type { AppointmentValues } from './appointmentValues';

export const getSlotFromVisitAppointmentDateTime = (
    appointmentModule: Pick<VisitAppointmentModuleSpecsFragment, 'bookingTimeSlot'>,
    datePickerEnabled: boolean,
    values?: AppointmentValues,
    timeZone?: string
): SubmitApplicantAppointmentMutationVariables['bookingTimeSlot'] | undefined => {
    if (!values.time || !values.date) {
        return undefined;
    }

    const { date, time } = values;
    const bookingDate = dayjs(date).format('YYYY-MM-DD');
    const bookingTimeSlot = dayjs(`${bookingDate} ${values.time}`).tz(timeZone, true);
    const systemBookingTimeSlot = appointmentModule.bookingTimeSlot.find(timeSlot => {
        const slot = dayjs(timeSlot.slot).tz(timeZone).format('HH:mm');

        return slot === time;
    });

    return {
        bookingLimit: datePickerEnabled && systemBookingTimeSlot?.bookingLimit ? systemBookingTimeSlot.bookingLimit : 0,
        slot: datePickerEnabled ? bookingTimeSlot.toDate() : null,
        useCurrentDateTime: values?.useCurrentDateTime ?? false,
    };
};

export const useUpdateVisitAppointment = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (
            token: string,
            values: AppointmentValues,
            appointmentModule: Pick<VisitAppointmentModuleSpecsFragment, 'bookingTimeSlot'>,
            datePickerEnabled: boolean,
            timeZone?: string
        ) => {
            const bookingTimeSlot = getSlotFromVisitAppointmentDateTime(
                appointmentModule,
                datePickerEnabled,
                values,
                timeZone
            );

            const { data } = await apolloClient.mutate<
                UpdateApplicantVisitAppointmentMutation,
                UpdateApplicantVisitAppointmentMutationVariables
            >({
                mutation: UpdateApplicantVisitAppointmentDocument,
                variables: {
                    bookingTimeSlot,
                    token,
                },
            });

            return data;
        },
        [apolloClient]
    );
};

export default useUpdateVisitAppointment;

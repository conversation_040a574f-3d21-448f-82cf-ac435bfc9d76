import { useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { AppointmentModuleSpecsFragment } from '../../../../api/fragments/AppointmentModuleSpecs';
import { JourneyEventDataFragment } from '../../../../api/fragments/JourneyEventData';
import { UpdateApplicantAppointmentDocument } from '../../../../api/mutations/updateApplicantAppointment';
import type {
    UpdateApplicantAppointmentMutation,
    UpdateApplicantAppointmentMutationVariables,
} from '../../../../api/mutations/updateApplicantAppointment';

import { BookingPeriodType } from '../../../../api/types';

import type { AppointmentValues } from './appointmentValues';
import { CleanedTimeSlot } from './useAppointmentAvailability';

export const getSlotFromAppointmentDateTime = (
    timeSlots: CleanedTimeSlot,
    datePickerEnabled: boolean,
    values?: AppointmentValues,
    timeZone?: string
): UpdateApplicantAppointmentMutationVariables['bookingTimeSlot'] | undefined => {
    if (!values.time || !values.date) {
        return undefined;
    }

    const { date, time } = values;
    const bookingDate = dayjs(date).format('YYYY-MM-DD');
    const bookingTimeSlot = dayjs(`${bookingDate} ${values.time}`).tz(timeZone, true);

    const systemBookingTimeSlot = timeSlots.find(timeSlot => {
        const slot = dayjs(timeSlot.slot).tz(timeZone).format('HH:mm');

        return slot === time;
    });

    return {
        bookingLimit: datePickerEnabled && systemBookingTimeSlot?.bookingLimit ? systemBookingTimeSlot.bookingLimit : 0,
        slot: datePickerEnabled ? bookingTimeSlot.toDate() : null,
        useCurrentDateTime: values?.useCurrentDateTime ?? false,
    };
};

export const useUpdateAppointment = () => {
    const apolloClient = useApolloClient();

    return useCallback(
        async (
            token: string,
            values: AppointmentValues,
            appointmentModule: Pick<AppointmentModuleSpecsFragment, 'bookingTimeSlot'>,
            datePickerEnabled: boolean,
            timeZone?: string,
            event?: JourneyEventDataFragment
        ) => {
            let bookingTimeSlots: CleanedTimeSlot = appointmentModule?.bookingTimeSlot ?? [];

            if (datePickerEnabled && event?.customTestDriveBookingSlots?.isEnabled) {
                bookingTimeSlots =
                    event.customTestDriveBookingSlots.bookingPeriodType === BookingPeriodType.BookingWindow
                        ? (event.customTestDriveBookingSlots.bookingWindowSettings?.bookingTimeSlot ?? [])
                        : event.customTestDriveBookingSlots.fixedPeriods.flatMap(
                              period => period.bookingTimeSlot || []
                          );
            }

            const bookingTimeSlot = getSlotFromAppointmentDateTime(
                bookingTimeSlots,
                datePickerEnabled,
                values,
                timeZone
            );

            const { data } = await apolloClient.mutate<
                UpdateApplicantAppointmentMutation,
                UpdateApplicantAppointmentMutationVariables
            >({
                mutation: UpdateApplicantAppointmentDocument,
                variables: {
                    bookingTimeSlot,
                    token,
                },
            });

            return data;
        },
        [apolloClient]
    );
};

export default useUpdateAppointment;

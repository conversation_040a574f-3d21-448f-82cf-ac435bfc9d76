import { PInlineNotification } from '@porsche-design-system/components-react';
import { Alert } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../../components/contexts/shared';

interface ResetToAllVariantsAlertProps {
    visible: boolean;
}

const ResetToAllVariantsAlert: React.FC<ResetToAllVariantsAlertProps> = ({ visible }) => {
    const { t } = useTranslation('carList');
    const { layout } = useRouter();
    const [dismissed, setDismissed] = useState(false);

    if (!visible || dismissed) {
        return null;
    }

    const isPorscheV3Layout = layout?.__typename === 'PorscheV3Layout';

    const handleDismiss = () => {
        setDismissed(true);
    };

    if (isPorscheV3Layout) {
        return (
            <PInlineNotification onDismiss={handleDismiss} state="warning" style={{ margin: '16px 0' }}>
                <span slot="heading">{t('alerts.resetToAllVariants.title')}</span>
                {t('alerts.resetToAllVariants.description')}
            </PInlineNotification>
        );
    }

    return (
        <Alert
            description={t('alerts.resetToAllVariants.description')}
            message={t('alerts.resetToAllVariants.title')}
            onClose={handleDismiss}
            style={{ margin: '16px 0' }}
            type="warning"
            closable
            showIcon
        />
    );
};

export default ResetToAllVariantsAlert;

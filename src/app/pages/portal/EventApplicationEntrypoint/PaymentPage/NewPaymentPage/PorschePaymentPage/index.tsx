import { Formik } from 'formik';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import notification from '../../../../../../themes/porscheV3/notification';
import { ensureApplicationForPayment } from '../../../../../../utilities/journeys/payment';
import useHandleError from '../../../../../../utilities/useHandleError';
import useValidator from '../../../../../../utilities/useValidator';
import useAgreementsValidator from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { TransactionStatus } from '../../../../../shared/PaymentPage/PorschePaymentPage/PorschePaymentWidget';
import {
    type PorschePaymentFormValues,
    SubmittedState,
    type PorschePaymentInnerProps,
} from '../../../../../shared/PaymentPage/PorschePaymentPage/types';
import usePorscheDepositSubmission, {
    submitPayment,
} from '../../../../../shared/PaymentPage/PorschePaymentPage/usePorscheDepositSubmission';
import { useSkipForDepositButton } from '../../../../../shared/PaymentPage/shared/SkipForDepositButton';
import usePaymentAgreements from '../../../../../shared/PaymentPage/shared/usePaymentAgreements';
import Inner from './Inner';

const PorschePaymentPage = (
    props: Omit<PorschePaymentInnerProps, 'SkipDepositButton' | 'canSkipDeposit' | 'isSuccessful'>
) => {
    const { state, dispatch } = props;
    const { token } = state;
    const { t } = useTranslation(['paymentDetails']);

    const paymentAgreements = usePaymentAgreements(state.application);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const [transactionStatus, setTransactionStatus] = useState<TransactionStatus | null>(null);

    const isSuccessful =
        state.application.draftFlow.isDepositCompleted ||
        (transactionStatus && SubmittedState.includes(transactionStatus));

    const submitPorscheDeposit = usePorscheDepositSubmission();
    const { skipDeposit, render: renderSkipForDepositButon, canSkipDeposit } = useSkipForDepositButton(state);

    const onSubmit = useHandleError(
        async (values: PorschePaymentFormValues, helpers) => {
            try {
                notification.loading({
                    content: t('paymentDetails:messages.paymentSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                const { paymentData, promoCodeId, ...agreements } = values;

                if (!skipDeposit && state.application.deposit.amount !== 0) {
                    // call payment submission api
                    const paymentResult = await submitPayment(paymentData, agreements, token, promoCodeId);

                    const { redirectUrl, status } = paymentResult;

                    switch (status) {
                        case TransactionStatus.FAILED: {
                            notification.warn(t('paymentDetails:resultCode.error'));

                            return;
                        }

                        case TransactionStatus.ERROR: {
                            notification.warn(t('paymentDetails:resultCode.refused'));

                            return;
                        }
                    }

                    setTransactionStatus(status);

                    if (redirectUrl) {
                        // redirect to 3DS
                        window.location.replace(redirectUrl);

                        return;
                    }
                }

                // calls graphql submit if there is no redirection
                // otherwise callback will call journey later
                const result = await submitPorscheDeposit(token, agreements, skipDeposit);

                const { application: newApplication } = result;

                const ensuredApplication = ensureApplicationForPayment(newApplication);

                if (
                    ensuredApplication.draftFlow.isDepositCompleted ||
                    (skipDeposit && ensuredApplication.draftFlow.isDepositSkipped)
                ) {
                    dispatch({ type: 'next', token: result.token, application: ensuredApplication });
                } else {
                    // there's an error
                    notification.warn(t('paymentDetails:resultCode.error'));
                }
            } finally {
                notification.destroy('primary');
            }
        },
        [dispatch, skipDeposit, state.application.deposit.amount, submitPorscheDeposit, t, token],
        {}
    );

    const innerProps: PorschePaymentInnerProps = useMemo(
        () => ({ ...props, SkipDepositButton: renderSkipForDepositButon, canSkipDeposit, isSuccessful }),
        [canSkipDeposit, isSuccessful, props, renderSkipForDepositButon]
    );

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.application.deposit.amount > 0 ? validation : null}
        >
            <Inner {...innerProps} />
        </Formik>
    );
};

export default PorschePaymentPage;

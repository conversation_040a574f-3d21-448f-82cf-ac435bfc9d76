import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getPaymentAgreements } from '../../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import PaymentSuccess from '../../../../../shared/PaymentPage/PorschePaymentPage/PaymentSuccess';
import PorschePaymentWidget from '../../../../../shared/PaymentPage/PorschePaymentPage/PorschePaymentWidget';
import type {
    PorschePaymentFormValues,
    PorschePaymentInnerProps,
} from '../../../../../shared/PaymentPage/PorschePaymentPage/types';
import usePorschePaymentMethods from '../../../../../shared/PaymentPage/PorschePaymentPage/usePorschePaymentMethods';
import getMandatory from '../../../../../shared/PaymentPage/shared/getMandatoryAgreement';
import type { EventApplicationState } from '../../../../../shared/PaymentPage/shared/types';
import NextButton from '../../../shared/NextButton';
import useBackAndSkipButtons from '../../shared/useBackAndSkipButtons';
import PaymentPageInnerWrapper from '../shared/PaymentPageInnerWrapper';

const Inner = ({
    state,
    dispatch,
    SkipDepositButton,
    isSuccessful,
    canSkipDeposit = false,
}: PorschePaymentInnerProps) => {
    const { t } = useTranslation(['paymentDetails']);

    const { application, stage, stages, token } = state;

    const eventApplication = useMemo(() => application as EventApplicationState, [application]);

    const { deposit } = eventApplication;

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(eventApplication.applicantAgreements),
        [eventApplication.applicantAgreements]
    );

    const { values, isSubmitting, handleSubmit, validateForm, submitForm } =
        useFormikContext<PorschePaymentFormValues>();

    const onSkipDeposit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [validateForm, submitForm]);

    const { hasSinglePaymentMethod, loading } = usePorschePaymentMethods(token);

    const skipAndBackButtons = useBackAndSkipButtons({
        canSkipDeposit,
        dispatch,
        isSubmitting,
        onSkipDeposit,
        SkipDepositButton,
    });

    const toolbarButtons = useMemo(
        () =>
            [
                ...skipAndBackButtons,
                <NextButton
                    key="nextButton"
                    disabled={isSubmitting}
                    form="completePorschePaymentForm"
                    htmlType="submit"
                >
                    {t('paymentDetails:actions.submit')}
                </NextButton>,
            ].filter(Boolean),
        [isSubmitting, skipAndBackButtons, t]
    );

    if (deposit.__typename !== 'ApplicationPorscheDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    return (
        <PaymentPageInnerWrapper
            application={eventApplication}
            depositAmount={deposit.amount}
            formName="completePorschePaymentForm"
            handleSubmit={handleSubmit}
            paymentAgreements={paymentAgreements}
            stage={stage}
            stages={stages}
            toolbarButtons={toolbarButtons}
        >
            <div
                style={
                    paymentAgreements.some(agreement => !values[agreement.id].isAgreed && getMandatory(agreement))
                        ? {
                              opacity: '30%',
                              pointerEvents: 'none',
                          }
                        : {}
                }
            >
                {isSuccessful ? (
                    <PaymentSuccess />
                ) : (
                    !loading && (
                        <PorschePaymentWidget deposit={deposit} hasSinglePaymentMethod={hasSinglePaymentMethod} />
                    )
                )}
            </div>
        </PaymentPageInnerWrapper>
    );
};

export default Inner;

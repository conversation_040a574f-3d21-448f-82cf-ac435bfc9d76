import { PText } from '@porsche-design-system/components-react';
import { isFinite } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import useCompanyFormats from '../../../../../../utilities/useCompanyFormats';

type BookingDepositProps = {
    depositAmount: number;
};

const DepositContainer = styled.div`
    width: 100%;
    background-color: #eeeff2;
    border-radius: var(--card-border-radius, 4px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 12px 16px;
`;

const BookingDeposit = ({ depositAmount }: BookingDepositProps) => {
    const { t } = useTranslation('paymentDetails');
    const formats = useCompanyFormats();

    if (!isFinite(depositAmount)) {
        return null;
    }

    return (
        <DepositContainer>
            <PText size="medium" weight="bold">
                {t('paymentDetails:labels.bookingDeposit.label')}
            </PText>
            <PText size="medium" weight="bold">
                {formats.formatAmountWithCurrency(depositAmount)}
            </PText>
        </DepositContainer>
    );
};

export default BookingDeposit;

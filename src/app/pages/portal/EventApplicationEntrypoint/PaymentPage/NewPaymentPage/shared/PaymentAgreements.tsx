import { PText } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import type { ApplicationAgreementDataFragment } from '../../../../../../api/fragments/ApplicationAgreementData';
import AgreementField from '../../../../../shared/CIPage/ConsentAndDeclarations/AgreementField';

type PaymentAgreementsType = {
    depositAmount: number;
    paymentAgreements: ApplicationAgreementDataFragment[];
};

const PaymentAgreements = ({ depositAmount, paymentAgreements }: PaymentAgreementsType) => {
    const { t } = useTranslation(['paymentDetails']);

    return (
        depositAmount > 0 &&
        paymentAgreements.length > 0 && (
            <Col span={24}>
                <Row gutter={[16, 16]}>
                    <Col span={24}>
                        <PText size="medium" weight="bold">
                            {t(`paymentDetails:messages.checkAgreements`)}
                        </PText>
                    </Col>
                    <Col span={24}>
                        <Row gutter={[16, 16]}>
                            {paymentAgreements.map(agreement => (
                                <Col key={agreement.id} lg={12} xs={24}>
                                    <AgreementField agreement={agreement} bordered />
                                </Col>
                            ))}
                        </Row>
                    </Col>
                </Row>
            </Col>
        )
    );
};

export default PaymentAgreements;

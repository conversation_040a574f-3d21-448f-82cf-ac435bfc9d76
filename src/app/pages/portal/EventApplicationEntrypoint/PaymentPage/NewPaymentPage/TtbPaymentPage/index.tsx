import { Formik } from 'formik';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PaymentStatus } from '../../../../../../api/types';
import notification from '../../../../../../themes/porscheV3/notification';
import { ensureApplicationForPayment } from '../../../../../../utilities/journeys/payment';
import useHandleError from '../../../../../../utilities/useHandleError';
import useValidator from '../../../../../../utilities/useValidator';
import useAgreementsValidator from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type { TtbPaymentFormValues, TtbPaymentPageProps } from '../../../../../shared/PaymentPage/TtbPaymentPage/types';
import useTtbDepositSubmission, {
    submitPayment,
} from '../../../../../shared/PaymentPage/TtbPaymentPage/useTtbDepositSubmission';
import { useSkipForDepositButton } from '../../../../../shared/PaymentPage/shared/SkipForDepositButton';
import usePaymentAgreements from '../../../../../shared/PaymentPage/shared/usePaymentAgreements';
import Inner from './Inner';

const TtbPaymentPage = ({ state, dispatch, CustomLayout, ...props }: TtbPaymentPageProps) => {
    const { token, application } = state;
    const { t } = useTranslation(['paymentDetails']);

    const paymentAgreements = usePaymentAgreements(state.application);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitTtbDeposit = useTtbDepositSubmission();
    const { skipDeposit, render, canSkipDeposit } = useSkipForDepositButton(state);

    useEffect(() => {
        if ([PaymentStatus.Failed, PaymentStatus.Error].includes(application.deposit.status)) {
            notification.error(t('paymentDetails:resultCode.error'));
        }
    }, [application.deposit.status, t]);

    const onSubmit = useHandleError(
        async (values: TtbPaymentFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentRedirecting'),
                duration: 0,
                key: 'primary',
            });
            const { promoCodeId, ...agreementValues } = values;

            if (!skipDeposit && application.deposit.amount !== 0) {
                try {
                    // call payment submission api
                    const paymentResult = await submitPayment(agreementValues, token, promoCodeId);
                    const { redirectUrl } = paymentResult;

                    if (redirectUrl) {
                        // redirect to 3DS
                        window.location.replace(redirectUrl);

                        return;
                    }

                    // there's an error
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                } catch {
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                }
            }

            const result = await submitTtbDeposit(token, agreementValues, skipDeposit).finally(() => {
                notification.destroy('primary');
            });

            const { application: newApplication } = result;

            const ensuredApplication = ensureApplicationForPayment(newApplication);

            if (ensuredApplication.draftFlow.isDepositCompleted) {
                dispatch({
                    type: 'next',
                    token: result.token,
                    application: ensuredApplication,
                });
            } else {
                // there's an error
                notification.warn(t('paymentDetails:resultCode.error'));
            }
        },
        [t, skipDeposit, application, submitTtbDeposit, token, dispatch],
        {}
    );

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.application.deposit.amount > 0 ? validation : null}
        >
            <Inner
                CustomLayout={CustomLayout}
                SkipDepositButton={render}
                canSkipDeposit={canSkipDeposit}
                dispatch={dispatch}
                state={state}
                {...props}
            />
        </Formik>
    );
};

export default TtbPaymentPage;

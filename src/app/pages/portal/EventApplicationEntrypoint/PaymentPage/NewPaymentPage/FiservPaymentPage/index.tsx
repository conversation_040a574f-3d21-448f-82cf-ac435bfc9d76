import { Formik } from 'formik';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { PaymentStatus } from '../../../../../../api/types';
import notification from '../../../../../../themes/porscheV3/notification';
import { ensureApplicationForPayment } from '../../../../../../utilities/journeys/payment';
import useHandleError from '../../../../../../utilities/useHandleError';
import useValidator from '../../../../../../utilities/useValidator';
import useAgreementsValidator from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type {
    FiservPaymentFormValues,
    FiservPaymentPageProps,
} from '../../../../../shared/PaymentPage/FiservPaymentPage/types';
import useFiservDepositSubmission, {
    submitPayment,
} from '../../../../../shared/PaymentPage/FiservPaymentPage/useFiservDepositSubmission';
import { useSkipForDepositButton } from '../../../../../shared/PaymentPage/shared/SkipForDepositButton';
import usePaymentAgreements from '../../../../../shared/PaymentPage/shared/usePaymentAgreements';
import Inner from './Inner';

const FiservPaymentPage = ({ state, dispatch, CustomLayout, ...props }: FiservPaymentPageProps) => {
    const { token, application } = state;
    const { t } = useTranslation(['paymentDetails']);

    const paymentAgreements = usePaymentAgreements(state.application);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitFiservDeposit = useFiservDepositSubmission();
    const { skipDeposit, render, canSkipDeposit } = useSkipForDepositButton(state);

    useEffect(() => {
        if ([PaymentStatus.Failed, PaymentStatus.Error].includes(application.deposit.status)) {
            notification.error(t('paymentDetails:resultCode.error'));
        }
    }, [application.deposit.status, t]);

    const onSubmit = useHandleError(
        async (values: FiservPaymentFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentRedirecting'),
                duration: 0,
                key: 'primary',
            });
            const { promoCodeId, ...agreementValues } = values;

            if (!skipDeposit && application.deposit.amount !== 0) {
                try {
                    // call payment submission api
                    const paymentResult = await submitPayment(agreementValues, token, promoCodeId);
                    const { redirectUrl } = paymentResult;

                    if (redirectUrl) {
                        // redirect to 3DS
                        window.location.replace(redirectUrl);

                        return;
                    }

                    // there's an error
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                } catch {
                    notification.error(t('paymentDetails:resultCode.error'));

                    return;
                }
            }

            const result = await submitFiservDeposit(token, agreementValues, skipDeposit).finally(() => {
                notification.destroy('primary');
            });

            const { application: newApplication } = result;

            const ensuredApplication = ensureApplicationForPayment(newApplication);

            if (ensuredApplication.draftFlow.isDepositCompleted) {
                dispatch({
                    type: 'next',
                    token: result.token,
                    application: ensuredApplication,
                });
            } else {
                // there's an error
                notification.warn(t('paymentDetails:resultCode.error'));
            }
        },
        [t, skipDeposit, application, submitFiservDeposit, token, dispatch],
        {}
    );

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.application.deposit.amount > 0 ? validation : null}
        >
            <Inner
                CustomLayout={CustomLayout}
                SkipDepositButton={render}
                canSkipDeposit={canSkipDeposit}
                dispatch={dispatch}
                state={state}
                {...props}
            />
        </Formik>
    );
};

export default FiservPaymentPage;

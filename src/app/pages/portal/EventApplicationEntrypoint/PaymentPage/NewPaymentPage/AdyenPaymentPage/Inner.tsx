import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getPaymentAgreements } from '../../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import AdyenPayment from '../../../../../shared/PaymentPage/AdyenPaymentPage/AdyenPayment';
import type {
    AdyenFormValues,
    AdyenPaymentPageInnerProps,
} from '../../../../../shared/PaymentPage/AdyenPaymentPage/types';
import type { AdyenResult } from '../../../../../shared/PaymentPage/AdyenPaymentPage/useAdyenPayment';
import getMandatory from '../../../../../shared/PaymentPage/shared/getMandatoryAgreement';
import type { EventApplicationState } from '../../../../../shared/PaymentPage/shared/types';
import NextButton from '../../../shared/NextButton';
import useBackAndSkipButtons from '../../shared/useBackAndSkipButtons';
import PaymentPageInnerWrapper from '../shared/PaymentPageInnerWrapper';

const Inner = ({
    state,
    dispatch,
    SkipDepositButton,
    canSkipDeposit = false,
    adyenSessionResult,
    onChangeSessionResult,
}: AdyenPaymentPageInnerProps) => {
    const { t } = useTranslation(['paymentDetails']);

    const { application, stage, stages } = state;

    const eventApplication = useMemo(() => application as EventApplicationState, [application]);

    const { deposit, draftFlow, dealerId, event } = eventApplication;

    const [allowToSubmit, setAllowToSubmit] = useState(false);

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(eventApplication.applicantAgreements),
        [eventApplication.applicantAgreements]
    );

    const { values, isSubmitting, handleSubmit, validateForm, submitForm } = useFormikContext<AdyenFormValues>();

    const onPaymentCompleted = useCallback(
        (result: AdyenResult) => {
            // Result code already authorised from the front-end
            const canSubmit = draftFlow.isDepositCompleted || result.resultCode === 'Authorised';

            if (canSubmit) {
                onChangeSessionResult(result.sessionResult);
                setAllowToSubmit(true);
            }
        },
        [draftFlow.isDepositCompleted, onChangeSessionResult]
    );

    useEffect(() => {
        // Why using useEffect?
        // There is race condition when setting up new session data
        // and submitting the form. We need to wait for both
        if (adyenSessionResult && allowToSubmit) {
            submitForm();
        }
    }, [adyenSessionResult, allowToSubmit, submitForm]);

    const onSkipDeposit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [validateForm, submitForm]);

    if (deposit.__typename !== 'ApplicationAdyenDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    const depositAmount = useMemo(() => {
        const { defaultValue, overrides } = event.depositAmount;
        if (event.depositAmount.overrides.length) {
            const dealerSpecific = overrides.find(override => override.dealerId === dealerId);

            const value = !dealerSpecific ? defaultValue : dealerSpecific.value;

            return value;
        }

        return defaultValue;
    }, [dealerId, event.depositAmount]);

    const skipAndBackButtons = useBackAndSkipButtons({
        canSkipDeposit,
        dispatch,
        isSubmitting,
        onSkipDeposit,
        SkipDepositButton,
    });

    const toolbarButtons = useMemo(
        () =>
            [
                ...skipAndBackButtons,
                deposit?.amount === 0 && (
                    <NextButton key="nextButton" disabled={isSubmitting} form="completeAdyenForm" htmlType="submit">
                        {t('paymentDetails:actions.submit')}
                    </NextButton>
                ),
            ].filter(Boolean),
        [deposit?.amount, isSubmitting, skipAndBackButtons, t]
    );

    return (
        <PaymentPageInnerWrapper
            application={eventApplication}
            depositAmount={depositAmount}
            formName="completeAdyenForm"
            handleSubmit={handleSubmit}
            paymentAgreements={paymentAgreements}
            stage={stage}
            stages={stages}
            toolbarButtons={toolbarButtons}
        >
            <div
                style={
                    deposit?.sessionId &&
                    paymentAgreements.some(agreement => !values[agreement.id].isAgreed && getMandatory(agreement))
                        ? {
                              opacity: '30%',
                              pointerEvents: 'none',
                          }
                        : {}
                }
            >
                <AdyenPayment deposit={deposit} onPaymentCompleted={onPaymentCompleted} />
            </div>
        </PaymentPageInnerWrapper>
    );
};

export default Inner;

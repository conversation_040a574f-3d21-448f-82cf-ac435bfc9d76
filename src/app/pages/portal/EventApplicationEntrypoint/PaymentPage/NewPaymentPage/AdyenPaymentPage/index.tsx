import { Formik } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import notification from '../../../../../../themes/porscheV3/notification';
import { ensureApplicationForPayment } from '../../../../../../utilities/journeys/payment';
import useHandleError from '../../../../../../utilities/useHandleError';
import useValidator from '../../../../../../utilities/useValidator';
import useAgreementsValidator from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import useAgreementsValues from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import {
    type AdyenFormValues,
    type AdyenPaymentPageInnerProps,
    type AdyenPaymentPageProps,
} from '../../../../../shared/PaymentPage/AdyenPaymentPage/types';
import useAdyenDepositSubmission from '../../../../../shared/PaymentPage/AdyenPaymentPage/useAdyenDepositSubmission';
import { useSkipForDepositButton } from '../../../../../shared/PaymentPage/shared/SkipForDepositButton';
import usePaymentAgreements from '../../../../../shared/PaymentPage/shared/usePaymentAgreements';
import Inner from './Inner';

const AdyenPaymentPage = ({ state, dispatch, CustomLayout, ...props }: AdyenPaymentPageProps) => {
    const { token } = state;
    const { t } = useTranslation(['paymentDetails']);
    const paymentAgreements = usePaymentAgreements(state.application);
    const initialValues = useAgreementsValues(paymentAgreements);
    const validator = useAgreementsValidator(paymentAgreements);
    const validation = useValidator(validator);

    const submitAdyenDeposit = useAdyenDepositSubmission();
    const { skipDeposit, render: renderSkipForDepositButon, canSkipDeposit } = useSkipForDepositButton(state);
    const [adyenSessionResult, setSessionResult] = useState(null);

    const onSubmit = useHandleError(
        async (values: AdyenFormValues) => {
            notification.loading({
                content: t('paymentDetails:messages.paymentSubmitting'),
                duration: 0,
                key: 'primary',
            });

            const { promoCodeId, ...agreementValues } = values;
            const result = await submitAdyenDeposit(token, agreementValues, skipDeposit, adyenSessionResult).finally(
                () => {
                    notification.destroy('primary');
                }
            );

            const { application: newApplication } = result;

            dispatch({
                type: 'next',
                token: result.token,
                application: ensureApplicationForPayment(newApplication),
            });
        },
        [t, submitAdyenDeposit, token, skipDeposit, adyenSessionResult, dispatch],
        {}
    );

    const onChangeSessionResult = useCallback((newSessionData: string) => {
        setSessionResult(newSessionData);
    }, []);

    const innerProps: AdyenPaymentPageInnerProps = useMemo(
        () => ({
            ...props,
            CustomLayout,
            SkipDepositButton: renderSkipForDepositButon,
            adyenSessionResult,
            canSkipDeposit,
            dispatch,
            onChangeSessionResult,
            state,
        }),
        [
            CustomLayout,
            adyenSessionResult,
            canSkipDeposit,
            dispatch,
            onChangeSessionResult,
            props,
            renderSkipForDepositButon,
            state,
        ]
    );

    return (
        <Formik
            initialValues={initialValues}
            onSubmit={onSubmit}
            validate={state.application.deposit.amount > 0 ? validation : null}
        >
            <Inner {...innerProps} />
        </Formik>
    );
};

export default AdyenPaymentPage;

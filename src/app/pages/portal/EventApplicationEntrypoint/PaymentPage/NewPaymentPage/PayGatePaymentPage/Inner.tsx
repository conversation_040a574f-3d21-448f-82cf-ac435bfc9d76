import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { getPaymentAgreements } from '../../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import type { AgreementValues } from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import type { PayGatePaymentPageInnerProps } from '../../../../../shared/PaymentPage/PaygatePaymentPage/types';
import type { EventApplicationState } from '../../../../../shared/PaymentPage/shared/types';
import NextButton from '../../../shared/NextButton';
import useBackAndSkipButtons from '../../shared/useBackAndSkipButtons';
import PaymentPageInnerWrapper from '../shared/PaymentPageInnerWrapper';

const Inner = ({ state, dispatch, SkipDepositButton, canSkipDeposit = false }: PayGatePaymentPageInnerProps) => {
    const { t } = useTranslation(['paymentDetails']);

    const { application, stage, stages } = state;

    const eventApplication = useMemo(() => application as EventApplicationState, [application]);

    const { deposit } = eventApplication;

    const paymentAgreements = useMemo(
        () => getPaymentAgreements(state.application.applicantAgreements),
        [state.application.applicantAgreements]
    );

    const { isSubmitting, handleSubmit, validateForm, submitForm } = useFormikContext<AgreementValues>();

    const onSkipDeposit = useCallback(async () => {
        await validateForm();
        await submitForm();
    }, [validateForm, submitForm]);

    const skipAndBackButtons = useBackAndSkipButtons({
        canSkipDeposit,
        dispatch,
        isSubmitting,
        onSkipDeposit,
        SkipDepositButton,
    });

    const toolbarButtons = useMemo(
        () =>
            [
                ...skipAndBackButtons,
                <NextButton
                    key="nextButton"
                    disabled={isSubmitting}
                    form="completePayGatePaymentForm"
                    htmlType="submit"
                >
                    {deposit.amount === 0 ? t('paymentDetails:actions.submit') : t('paymentDetails:actions.pay')}
                </NextButton>,
            ].filter(Boolean),
        [deposit?.amount, isSubmitting, skipAndBackButtons, t]
    );

    if (deposit.__typename !== 'ApplicationPayGateDeposit') {
        throw new Error('Deposit type is unexpected');
    }

    return (
        <PaymentPageInnerWrapper
            application={eventApplication}
            depositAmount={deposit.amount}
            formName="completePayGatePaymentForm"
            handleSubmit={handleSubmit}
            paymentAgreements={paymentAgreements}
            stage={stage}
            stages={stages}
            toolbarButtons={toolbarButtons}
        />
    );
};

export default Inner;

import dayjs from 'dayjs';
import { isEqual, omit } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import { usePersistentData, usePersistentDataListener } from '../../../../utilities/usePersistData';
import { ApplicantFormValues } from '../ApplicantForm/shared';

export const EVENT_JOURNEY_TEMPORARY_KEY = 'eventJourneyTemporaryKey';

export type EventJourneyTemporaryValue = { expiresAt: Date | string | null } & Partial<ApplicantFormValues>;

export const usePersistEventJourneyValues = () => {
    const { sessionTimeout } = useCompany();
    const { setItem, deleteItem } = usePersistentData();

    const expireAt = useMemo(() => sessionTimeout * 60, [sessionTimeout]);

    const persistedValue = usePersistentDataListener<EventJourneyTemporaryValue>(EVENT_JOURNEY_TEMPORARY_KEY);

    const save = useCallback(
        (newValue?: EventJourneyTemporaryValue) => {
            const updated: EventJourneyTemporaryValue = {
                ...newValue,
                expiresAt: dayjs().add(expireAt, 'seconds').toDate(),
            };

            if (!isEqual(omit('expireAt', persistedValue), omit('expireAt', updated))) {
                setItem(EVENT_JOURNEY_TEMPORARY_KEY, updated, expireAt, '');
            }
        },
        [expireAt, persistedValue, setItem]
    );

    const remove = useCallback(() => {
        deleteItem(EVENT_JOURNEY_TEMPORARY_KEY);
    }, [deleteItem]);

    return useMemo(() => ({ save, remove, persistedValue }), [persistedValue, remove, save]);
};

import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { JourneySectionWrapperProps } from '../../../../shared/JourneyPage/JourneySectionWrapper';
import { useJourneyStepsContext } from '../../../../shared/JourneyPage/JourneyStepsContext';
import { getCurrentStageIndex } from '../../../../shared/JourneyPage/mapJourneySteps';
import { JourneyStage } from '../../../StandardApplicationEntrypoint/Journey/shared';
import JourneySectionTitle from './JourneySectionTitle';
import PriorStage from './PriorStage';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 32px;
`;

const MainContentCard = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 16px;
`;

const PriorStagesWrapper = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 32px;
    width: 100%;
`;

type EventJourneySectionWrapperProps = JourneySectionWrapperProps & {
    consentSection?: JSX.Element;
};

const JourneySectionWrapper = ({
    stage: initialStage,
    stages: initialStages,
    application,
    children,
    isPorscheIdLoginMandatory = false,
    consentSection,
}: EventJourneySectionWrapperProps) => {
    const { t } = useTranslation('journey');

    const { onBack, shouldAllowBack } = useJourneyStepsContext();

    const { currentStage, stages } = useMemo(() => {
        /*
            On VF-1502, the Login or Register and Customer Details step would be combined into Required Details
        */
        const adjustedStages = initialStages.filter(
            stage => ![JourneyStage.ApplicantKYC, JourneyStage.PorscheIdLoginRegister].includes(stage)
        );

        if (
            [JourneyStage.ApplicantKYC, JourneyStage.PorscheIdLoginRegister, JourneyStage.RequiredDetails].includes(
                initialStage
            )
        ) {
            return { currentStage: JourneyStage.RequiredDetails, stages: adjustedStages };
        }

        return { currentStage: initialStage, stages: adjustedStages };
    }, [initialStage, initialStages]);

    const currentStageIndex = useMemo(() => getCurrentStageIndex(stages || [], currentStage), [stages, currentStage]);

    const [priorStages, laterStages] = useMemo(() => {
        const prior = stages?.slice(0, currentStageIndex);
        const later = stages?.slice(currentStageIndex + 1);

        return [prior, later];
    }, [stages, currentStageIndex]);

    const hasCompleteMandatoryPorscheIdLogin = useMemo(
        () => isPorscheIdLoginMandatory && initialStage !== JourneyStage.PorscheIdLoginRegister,
        [initialStage, isPorscheIdLoginMandatory]
    );

    return (
        <Container>
            {priorStages.length > 0 && (
                <PriorStagesWrapper>
                    {priorStages.map((priorStage, index) => (
                        <PriorStage
                            key={priorStage}
                            active={index === currentStageIndex}
                            allowClick={
                                !(
                                    hasCompleteMandatoryPorscheIdLogin &&
                                    priorStage === JourneyStage.PorscheIdLoginRegister
                                ) &&
                                shouldAllowBack &&
                                index < currentStageIndex &&
                                currentStageIndex - index === 1
                            }
                            application={application}
                            isPrior={priorStage !== currentStage}
                            onBack={onBack}
                            stage={priorStage}
                            title={t(
                                `journey:steps.${priorStage === JourneyStage.ApplicantKYC ? JourneyStage.RequiredDetails : priorStage}`
                            )}
                        />
                    ))}
                </PriorStagesWrapper>
            )}

            <MainContentCard className="v3-layout-card">
                <JourneySectionTitle
                    key={initialStage}
                    allowClick={false}
                    isPrior={false}
                    onClick={onBack}
                    title={t(`journey:steps.${initialStage}`)}
                    active
                />
                {children}
            </MainContentCard>

            {/* Note: Since VF-1592, the consent is separated section from KYC, hence need to define a new active section for consent
            The consentSection should only being passed on KYC page */}
            {initialStage === JourneyStage.ApplicantKYC && consentSection && (
                <div className="v3-layout-card">{consentSection}</div>
            )}

            {laterStages && laterStages.length > 0 && (
                <>
                    {laterStages.map(laterStage => (
                        <div key={laterStage} className="v3-layout-card">
                            <JourneySectionTitle
                                active={false}
                                isPrior={false}
                                title={t(`journey:steps.${laterStage}`)}
                            />
                        </div>
                    ))}
                </>
            )}
        </Container>
    );
};

export default JourneySectionWrapper;

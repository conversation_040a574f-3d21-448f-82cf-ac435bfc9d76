import { Col } from 'antd';
import { type PropsWithChildren } from 'react';
import styled from 'styled-components';
import { Applications } from '../../../../shared/JourneyPage/JourneySectionWrapper/types';
import { JourneyStage } from '../../../StandardApplicationEntrypoint/Journey/shared';
import JourneySectionTitle from './JourneySectionTitle';
import RequiredDetailsStage from './RequiredDetailsStage';

const PriorJourneyContainer = styled.div`
    border-radius: 12px;
    padding: 32px;
    background-color: #f7f7f7;
`;

const PriorJourneyChildrenContainer = styled.div`
    width: auto;
    margin-top: 16px;
`;

const priorStageContent = (stage: JourneyStage, application: Applications) => {
    if (stage === JourneyStage.RequiredDetails && application) {
        return (
            <PriorJourneyChildrenContainer>
                <Col xl={12} xs={24}>
                    <RequiredDetailsStage application={application} />
                </Col>
            </PriorJourneyChildrenContainer>
        );
    }

    return null;
};

const PriorStage = ({
    active,
    allowClick,
    application,
    isPrior,
    onBack,
    stage,
    title,
}: {
    active: boolean;
    allowClick: boolean;
    application: Applications;
    isPrior: boolean;
    onBack: () => void;
    stage: JourneyStage;
    title: string;
} & PropsWithChildren) => (
    <PriorJourneyContainer>
        <JourneySectionTitle active={active} allowClick={allowClick} isPrior={isPrior} onClick={onBack} title={title} />
        {priorStageContent(stage, application)}
    </PriorJourneyContainer>
);

export default PriorStage;

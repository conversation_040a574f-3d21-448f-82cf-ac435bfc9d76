import { PText } from '@porsche-design-system/components-react';
import { Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { type DealerJourneyDataFragment } from '../../../../../api/fragments/DealerJourneyData';
import breakpoints from '../../../../../utilities/breakpoints';
import renderMarkdown from '../../../../../utilities/renderMarkdown';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import { Applications } from '../../../../shared/JourneyPage/JourneySectionWrapper/types';
import Media from '../../../ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/components/Media';

const RequiredDetailsContainer = styled.div`
    border-radius: 12px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 24px;
`;

const VehicleInfoContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    align-items: center;
    gap: 16px;
`;

const VehicleInfoDetails = styled.div`
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: column;
`;

const MediaContainer = styled.div`
    & .ant-image {
        width: 180px;
        margin: auto;
        & > img {
            border-radius: var(--card-border-radius, initial);
            aspect-ratio: 16/9;
            object-fit: cover;
        }
    }

    @media (max-width: ${breakpoints.sm}) {
        & .ant-image {
            width: auto;
            height: 147px;
        }
    }
`;

const StyledSpace = styled(Space)`
    width: 100%;
`;

const VehicleInfo = ({ variant, filename, source }: { variant: string; filename: string; source: string }) => (
    <VehicleInfoContainer>
        <MediaContainer>
            <Media fileName={filename} source={source} />
        </MediaContainer>
        <VehicleInfoDetails>
            <PText size="medium" weight="bold">
                {variant}
            </PText>
        </VehicleInfoDetails>
    </VehicleInfoContainer>
);

const VehicleDetails = ({ application }: { application: Applications }) => {
    const translatedString = useTranslatedString();
    const { t } = useTranslation(['paymentDetails', 'finderJourney']);

    const vehicleData = useMemo(() => {
        if (application.__typename === 'EventApplication' && application.vehicle?.__typename === 'LocalVariant') {
            return {
                make: translatedString(
                    application.vehicle.model.parentModel
                        ? application.vehicle.model.parentModel.make.name
                        : application.vehicle.model.make.name
                ),
                variant: translatedString(application.vehicle.name),

                filename: application.vehicle?.images?.[0]?.filename,
                source: application.vehicle?.images?.[0]?.url,
            };
        }

        return null;
    }, [application, translatedString]);

    return (
        <div>
            <PText size="medium" weight="bold">
                {t('paymentDetails:titles.selectedVehicle')}
            </PText>
            <VehicleInfo {...vehicleData} />
        </div>
    );
};

const DealerInfo = ({ dealer }: { dealer: DealerJourneyDataFragment }) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();

    return (
        <div>
            <StyledSpace direction="vertical" size={4}>
                <PText size="medium" weight="bold">
                    {translatedString(dealer.legalName)}
                </PText>
                {(dealer.contact.telephone || dealer.contact.email || dealer.contact.address) && (
                    <Space direction="vertical" size={4}>
                        {dealer.contact.address?.defaultValue && (
                            <PText>{translatedString(dealer.contact.address)}</PText>
                        )}

                        {dealer.contact.telephone && (
                            <PText>
                                {t('configuratorJourney:thankyou.contents.dealerInfo.phone', dealer.contact.telephone)}
                            </PText>
                        )}

                        {dealer.contact.email && (
                            <PText>
                                {t('configuratorJourney:thankyou.contents.dealerInfo.email', {
                                    email: dealer.contact.email,
                                })}
                            </PText>
                        )}
                    </Space>
                )}
                {dealer.contact.additionalInfo?.defaultValue && (
                    <PText>{renderMarkdown(translatedString(dealer.contact.additionalInfo))}</PText>
                )}
            </StyledSpace>
        </div>
    );
};

const RequiredDetailsStage = ({ application }: { application: Applications }) => (
    <RequiredDetailsContainer>
        {application.vehicle && <VehicleDetails application={application} />}
        <DealerInfo dealer={application.dealer} />
    </RequiredDetailsContainer>
);

export default RequiredDetailsStage;

import { PHeading } from '@porsche-design-system/components-react';
import styled from 'styled-components';

const TitleContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
    column-gap: 16px;
    align-items: center;
`;

const TitleWrapper = styled.div<{
    active: boolean;
    isPrior: boolean;
    allowClick: boolean;
}>`
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    color: ${({ active, isPrior }) => (active || isPrior ? '#000' : '#949598')};
    ${({ allowClick }) => allowClick && 'cursor: pointer;'};
`;

type JourneySectionTitleProps = {
    active: boolean; // is in current stage
    isPrior: boolean; // is in prior stage
    title: string;
    allowClick?: boolean;
    onClick?: () => void;
};

const JourneySectionTitle = ({ active, title, isPrior, allowClick = false, onClick }: JourneySectionTitleProps) => (
    <TitleWrapper active={active} allowClick={allowClick} isPrior={isPrior} onClick={allowClick ? onClick : undefined}>
        <TitleContainer>
            <PHeading size="large">{title}</PHeading>
        </TitleContainer>
    </TitleWrapper>
);

export default JourneySectionTitle;

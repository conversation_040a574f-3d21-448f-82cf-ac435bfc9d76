import { useTranslation } from 'react-i18next';
import Button from '../../../../themes/porscheV3/Button';

const BackButton = ({
    disabled = false,
    onBackButtonClicked,
}: {
    disabled?: boolean;
    onBackButtonClicked: () => void;
}) => {
    const { t } = useTranslation(['eventApplicantForm']);

    return (
        <Button
            key="back"
            disabled={disabled}
            htmlType="button"
            onClick={onBackButtonClicked}
            porscheTheme="light"
            type="tertiary"
        >
            {t('eventApplicantForm:actions.back')}
        </Button>
    );
};

export default BackButton;

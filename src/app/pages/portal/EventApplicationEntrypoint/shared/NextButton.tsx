import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import Button from '../../../../themes/porscheV3/Button';
import { NextButtonProps } from '../../StandardApplicationEntrypoint/shared/JourneyButton';

type EventNextButtonProps = NextButtonProps & {
    key?: string;
    children?: React.ReactNode;
    htmlType?: 'button' | 'reset' | 'submit';
};

const NextButton = ({ key, children, form, htmlType, onSubmit, disabled }: EventNextButtonProps) => {
    const { t } = useTranslation('customerDetails');
    const { isSubmitting } = useFormikContext();

    return (
        <Button
            key={key ?? 'submit'}
            disabled={disabled || isSubmitting}
            form={form}
            htmlType={htmlType ?? 'button'}
            onClick={onSubmit}
            porscheTheme="light"
            type="primary"
        >
            {children ?? t('customerDetails:nextButton')}
        </Button>
    );
};

export default NextButton;

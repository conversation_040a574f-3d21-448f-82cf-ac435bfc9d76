import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import Steps from '../../../../themes/porscheV3/Steps';
import type { JourneyStepsProps } from '../../../shared/JourneyPage/JourneySteps';
import { useJourneyStepsContext } from '../../../shared/JourneyPage/JourneyStepsContext';
import { getCurrentStageIndex } from '../../../shared/JourneyPage/mapJourneySteps';
import { JourneyStage } from '../../StandardApplicationEntrypoint/Journey/shared';

const StepWrapper = styled.div`
    padding-bottom: 36px;
`;

const JourneySteps = ({ stages: initialStages, currentStage: initialCurrentStage }: JourneyStepsProps) => {
    const { t } = useTranslation('journey');
    const { onBack, shouldAllowBack } = useJourneyStepsContext();

    const { currentStage, stages } = useMemo(() => {
        /*
            On VF-1502, the Login or Register and Customer Details step would be combined into Required Details
        */
        const adjustedStages = initialStages.filter(
            stage => ![JourneyStage.ApplicantKYC, JourneyStage.PorscheIdLoginRegister].includes(stage)
        );

        if (
            [JourneyStage.ApplicantKYC, JourneyStage.PorscheIdLoginRegister, JourneyStage.RequiredDetails].includes(
                initialCurrentStage
            )
        ) {
            return { currentStage: JourneyStage.RequiredDetails, stages: adjustedStages };
        }

        return { currentStage: initialCurrentStage, stages: adjustedStages };
    }, [initialCurrentStage, initialStages]);

    const current = useMemo(() => getCurrentStageIndex(stages || [], currentStage), [stages, currentStage]);

    const stepOnClick = useCallback(
        (index: number) => {
            if (index > current || current - index > 1 || !shouldAllowBack) {
                return;
            }

            onBack();
        },
        [current, onBack, shouldAllowBack]
    );

    return (
        stages.length > 1 && (
            <StepWrapper>
                <Steps
                    current={current}
                    items={stages.map((stage, index) => ({
                        index,
                        title: t(`journey:steps.${stage}`),
                    }))}
                    stepRedirectOnClick={stepOnClick}
                />
            </StepWrapper>
        )
    );
};

export default JourneySteps;

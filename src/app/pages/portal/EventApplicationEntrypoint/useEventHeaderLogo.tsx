import { useEffect, useMemo } from 'react';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useHeaderContext, type LogoTheme } from '../../../layouts/HeaderContextManager';

const useEventHeaderLogo = (logoTheme: LogoTheme | null) => {
    const company = useCompany();

    const { setLogo } = useHeaderContext();

    const logoUrl = useMemo(() => {
        if (!logoTheme) {
            return null;
        }

        // currently, no need to override logo on mobile screen
        // desktop screen
        if (logoTheme === 'dark') {
            return company?.logoNonWhiteBackground?.url ?? null;
        }

        return company?.logo?.url ?? null;
    }, [company?.logo?.url, company?.logoNonWhiteBackground?.url, logoTheme]);

    useEffect(() => {
        setLogo({
            url: logoUrl,
            theme: logoTheme,
        });
    }, [logoUrl, logoTheme, setLogo]);
};

export default useEventHeaderLogo;

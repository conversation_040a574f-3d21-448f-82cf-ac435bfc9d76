import { useCallback, useMemo, useRef } from 'react';
import type { KycFieldSpecsFragment } from '../../../../../api/fragments/KYCFieldSpecs';
import { LocalCustomerFieldKey, TradeInVehiclePayload } from '../../../../../api/types';
import type { SearchedCustomer } from '../../../../../components/cap/searchCustomersAndLeads/useReducer';
import { hasSearchedCustomerValues } from '../../../../../components/cap/searchCustomersAndLeads/utils';
import type { KYCPresetFormFields } from '../../../../../utilities/kycPresets';
import { getInitialValues } from '../../../../../utilities/kycPresets';
import getKYCDataFromSearchedCustomer from '../../../../../utilities/kycPresets/getKYCDataFromSearchedCustomer';

interface UseKycAutoFillProps {
    kycFields: KycFieldSpecsFragment[];
}

/**
 * Custom hook for managing KYC field auto-fill functionality with intelligent reset capabilities.
 *
 * This hook provides a complete solution for:
 * - Auto-filling KYC fields from searched customer data (CAP integration)
 * - Tracking which fields were auto-filled vs user-entered
 * - Resetting only previously auto-filled fields when user chooses "Create New"
 * - Preserving user-entered data during reset operations
 *
 * Key features:
 * - Memory of auto-filled fields to enable selective reset
 * - Phone number parsing with international format support
 * - Email validation before auto-fill
 * - Performance optimizations with memoized values
 *
 * @param kycFields - Array of KYC field specifications that define available fields
 * @returns Object containing auto-fill functions, handlers, and computed values
 */
export default function useKycAutoFillFromBpSearch({ kycFields }: UseKycAutoFillProps) {
    // Track which KYC fields were auto-filled from CAP search
    const prevCustomerFieldAutoFillKeysRef = useRef<Set<LocalCustomerFieldKey>>(new Set());

    // Track if VIN was auto-filled to enable reset
    const prevVinAutoFilledRef = useRef<boolean>(false);

    // Cache the last searched customer until user confirms Create New
    const lastSearchedCustomerRef = useRef<SearchedCustomer | null>(null);

    // Memoize initial KYC fields to avoid expensive recalculation
    const initialKycFields = useMemo(() => getInitialValues([], kycFields), [kycFields]);

    const handleAutoFillFromSearchedCustomer = useCallback(
        (
            searchedCustomer: SearchedCustomer,
            formValues: { customer: { fields: KYCPresetFormFields }; tradeInVehicle: TradeInVehiclePayload[] }
        ) => {
            const newlyFilled = new Set<LocalCustomerFieldKey>();

            // Use the shared utility with tracking callback
            const updatedFormValues = getKYCDataFromSearchedCustomer(searchedCustomer, kycFields, formValues, {
                onFieldFilled: (fieldKey: LocalCustomerFieldKey) => {
                    newlyFilled.add(fieldKey);
                },
            });

            // Remember which fields we auto-filled last time
            prevCustomerFieldAutoFillKeysRef.current = newlyFilled;

            // Track if VIN was auto-filled
            prevVinAutoFilledRef.current = !!searchedCustomer.vin;

            return updatedFormValues;
        },
        [kycFields]
    );

    // Reset only previously auto-filled KYC fields (not all fields)
    const resetPreviouslyAutofilledKycFields = useCallback(
        (currentFields: KYCPresetFormFields): KYCPresetFormFields => {
            // Early returns for performance
            if (!currentFields || !prevCustomerFieldAutoFillKeysRef.current.size) {
                return currentFields;
            }

            const resetFields = { ...currentFields };

            // Reset each previously auto-filled field to its initial value or remove it
            prevCustomerFieldAutoFillKeysRef.current.forEach(fieldKey => {
                if (fieldKey === LocalCustomerFieldKey.Phone) {
                    // For phone fields, keep the prefix from initial values but reset the value
                    resetFields[fieldKey] = {
                        ...currentFields[fieldKey],
                        value: {
                            prefix: currentFields[fieldKey]?.value?.prefix,
                            value: '',
                        },
                    };
                } else {
                    // For non-phone fields, do complete reset
                    resetFields[fieldKey] = initialKycFields[fieldKey];
                }
            });

            // Clear the tracking set for next operation
            prevCustomerFieldAutoFillKeysRef.current = new Set();

            return resetFields;
        },
        [initialKycFields]
    );

    // Reset VIN if it was auto-filled
    const resetPreviouslyAutoFilledVin = useCallback(
        (currentTradeInVehicle: TradeInVehiclePayload[]): TradeInVehiclePayload[] => {
            if (!prevVinAutoFilledRef.current || !currentTradeInVehicle.length) {
                return currentTradeInVehicle;
            }

            const resetTradeInVehicle = [...currentTradeInVehicle];
            if (resetTradeInVehicle[0]) {
                resetTradeInVehicle[0] = {
                    ...resetTradeInVehicle[0],
                    vin: '', // Reset VIN to empty string
                };
            }

            // Clear the VIN tracking flag
            prevVinAutoFilledRef.current = false;

            return resetTradeInVehicle;
        },
        []
    );

    const onSearchedCustomerDataAvailable = useCallback((sc: SearchedCustomer) => {
        if (hasSearchedCustomerValues(sc)) {
            lastSearchedCustomerRef.current = sc;
        }
    }, []);

    // Handle auto-fill with reset logic
    const handleAutoFillKyc = useCallback(
        (
            currentFields: KYCPresetFormFields,
            currentTradeInVehicle: TradeInVehiclePayload[],
            onFieldsUpdate: (fields: KYCPresetFormFields) => void,
            onTradeInVehicleUpdate: (tradeInVehicle: TradeInVehiclePayload[]) => void
        ) => {
            const sc = lastSearchedCustomerRef.current;
            if (sc && hasSearchedCustomerValues(sc)) {
                // First reset previously auto-filled fields
                const resetFields = resetPreviouslyAutofilledKycFields(currentFields);

                // Also reset previously auto-filled VIN
                const resetTradeInVehicle = resetPreviouslyAutoFilledVin(currentTradeInVehicle);

                // Then apply new auto-filled data to the reset fields
                const updatedFormValues = handleAutoFillFromSearchedCustomer(sc, {
                    customer: { fields: resetFields },
                    tradeInVehicle: resetTradeInVehicle,
                });

                // Update both KYC fields and tradeInVehicle via callbacks
                onFieldsUpdate(updatedFormValues.customer.fields);
                if (updatedFormValues?.tradeInVehicle) {
                    onTradeInVehicleUpdate(updatedFormValues.tradeInVehicle);
                }
            }
        },
        [handleAutoFillFromSearchedCustomer, resetPreviouslyAutofilledKycFields, resetPreviouslyAutoFilledVin]
    );

    return {
        onSearchedCustomerDataAvailable,
        handleAutoFillKyc,
    };
}

import { PHeading } from '@porsche-design-system/components-react';
import { Col, Row, Space } from 'antd';
import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import FormAutoTouch from '../../../../../components/FormAutoTouch';
import MandatoryPorscheIDLogin from '../../../../../components/PorscheID/MandatoryPorscheIDLogin';
import ScrollToTop from '../../../../../components/ScrollToTop';
import { useRouter } from '../../../../../components/contexts/shared';
import Form from '../../../../../components/fields/Form';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import CustomerDetails from '../../../../shared/JourneyPage/CustomerDetails';
import { JourneyStage } from '../../../StandardApplicationEntrypoint/Journey/shared';
import { fullWidthColSpan } from '../../../StandardApplicationEntrypoint/KYCPage/shared';
import { useEventJourneyKycAndAgreementContext } from '../../Entrypoint/EventJourneyKycAndAgreement';
import { useEventJourneySetupContext } from '../../Entrypoint/EventJourneySetup';
import { usePersistEventJourneyValues } from '../../Journey/usePersistEventJourneyValues';
import JourneySectionWrapper from '../../shared/JourneySectionWrapper';
import JourneySteps from '../../shared/JourneySteps';
import useEventHeaderLogo from '../../useEventHeaderLogo';
import Banner from '../Banner';
import ConsentsAndDeclarations from '../ConsentsAndDeclarations';
import CustomizedFieldsSection from '../CustomizedFieldsSection';
import LiveChat from '../LiveChat';
import { ApplicantFormValues } from '../shared';
import ApplicantIntegrationSection from './ApplicantIntegrationSection';
import RequiredDetails from './RequiredDetails';
import type { InnerType } from './types';

const InnerWrapper = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 32px;
`;

// ensure inputs in a form with two inputs and a button to have their content graciously visible
const StyledCol = styled(Col)`
    @media (min-width: 993px) and (max-width: 1200px) {
        display: block;
        flex: 0 0 100%;
        max-width: 100%;
    }
`;

const Inner = ({
    state,
    capIntegration,
    currentStage,
    formName,
    kycExtraSettings,
    myInfoIntegration,
    porscheIdIntegration,
    removeDocument,
    resetFormHandler,
    showCommentsToBank,
    stages,
    uploadDocument,
}: InnerType) => {
    const { t } = useTranslation(['eventApplicantForm']);

    const { save: persistEventJourneyValue, persistedValue: persistedEventValue } = usePersistEventJourneyValues();

    const { id: routerId } = useRouter();
    const { event, endpoint, liveChatSetting, showResetKYCButton, showLiveChat, variants } =
        useEventJourneySetupContext();
    const { kycState, agreementState, customerKind, hasGuarantorPreset, setIsCorporate, setPrefill } =
        useEventJourneyKycAndAgreementContext();

    const { immediateOpenCapCustomerSearch, showSearchCapCustomerButton } = capIntegration;
    const {
        isPorscheIdLoginMandatory,
        onPorscheIDCustomerFetched,
        porscheIdIntegrationEnabled,
        setIsPorscheIDFetchLoading,
        submitDraftWithPorscheId,
    } = porscheIdIntegration;

    const { withMyInfo } = myInfoIntegration;

    const { values, handleSubmit } = useFormikContext<ApplicantFormValues>();

    const persistValueFn = useMemo(
        () => () => {
            // Set appointment values inside temporary storage, that will be retrieved later
            persistEventJourneyValue({
                ...persistedEventValue,
                appointment: values.appointment,
                visitAppointment: values.visitAppointment,
            });
        },
        [persistEventJourneyValue, persistedEventValue, values.appointment, values.visitAppointment]
    );

    const InnerSection = useMemo(() => {
        switch (currentStage) {
            case JourneyStage.PorscheIdLoginRegister:
                return (
                    <MandatoryPorscheIDLogin
                        applicationId={state?.application?.id}
                        endpointId={endpoint.id}
                        onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                        persistValueFn={persistValueFn}
                        routerId={routerId}
                        setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                        submitDraft={submitDraftWithPorscheId}
                        isEvent
                    />
                );

            case JourneyStage.ApplicantKYC:
                return (
                    <>
                        <ApplicantIntegrationSection
                            capIntegration={capIntegration}
                            myInfoIntegration={myInfoIntegration}
                            porscheIdIntegration={porscheIdIntegration}
                            state={state}
                        />
                        <CustomerDetails
                            applicationId={state?.application?.id}
                            colSpan={fullWidthColSpan}
                            customerKind={customerKind}
                            endpointId={endpoint.id}
                            gutter={16}
                            hasGuarantorPreset={hasGuarantorPreset}
                            hasUploadDocuments={false}
                            hasVSOUpload={false}
                            immediateOpenCapCustomerSearch={immediateOpenCapCustomerSearch}
                            isApplyingFromDetails={false}
                            isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                            kycExtraSettings={kycExtraSettings}
                            kycPresets={kycState.active}
                            onPorscheIDCustomerFetched={onPorscheIDCustomerFetched}
                            porscheIdIntegrationEnabled={porscheIdIntegrationEnabled}
                            removeDocument={removeDocument}
                            resetFormHandler={resetFormHandler}
                            routerId={routerId}
                            setIsCorporate={setIsCorporate}
                            setIsPorscheIDFetchLoading={setIsPorscheIDFetchLoading}
                            setPrefill={setPrefill}
                            showCommentsToInsurer={false}
                            showRemarks={showCommentsToBank}
                            showResetButton={showResetKYCButton && !withMyInfo}
                            showTabs={kycState.corporate?.length > 0}
                            showTitle={false}
                            showTradeInCheckbox={event.isAllowTradeIn}
                            submitDraftWithPorscheId={submitDraftWithPorscheId}
                            uploadDocument={uploadDocument}
                            withFinancing={false}
                            enableMobileVerification
                            hidePorscheIdLoginButton
                            isEvent
                        />
                        {values.customizedFields.length > 0 && <CustomizedFieldsSection />}
                    </>
                );

            default:
                return null;
        }
    }, [
        capIntegration,
        currentStage,
        customerKind,
        endpoint.id,
        event.isAllowTradeIn,
        hasGuarantorPreset,
        immediateOpenCapCustomerSearch,
        isPorscheIdLoginMandatory,
        kycExtraSettings,
        kycState.active,
        kycState.corporate?.length,
        myInfoIntegration,
        onPorscheIDCustomerFetched,
        persistValueFn,
        porscheIdIntegration,
        porscheIdIntegrationEnabled,
        removeDocument,
        resetFormHandler,
        routerId,
        setIsCorporate,
        setIsPorscheIDFetchLoading,
        setPrefill,
        showCommentsToBank,
        showResetKYCButton,
        state,
        submitDraftWithPorscheId,
        uploadDocument,
        values.customizedFields.length,
        withMyInfo,
    ]);

    const consentSection = useMemo(
        () =>
            currentStage === JourneyStage.ApplicantKYC ? (
                <ConsentsAndDeclarations
                    TitleNode={
                        <div style={{ marginBottom: '16px' }}>
                            <PHeading size="large">{t('eventApplicantForm:consents.title')}</PHeading>
                        </div>
                    }
                    applicationAgreements={agreementState.data}
                    hideDivider
                />
            ) : null,
        [agreementState.data, currentStage, t]
    );

    useEventHeaderLogo(event?.hasCustomiseBanner && !!event?.banner ? 'dark' : 'light');

    return (
        <>
            {event?.hasCustomiseBanner && event?.banner && <Banner banner={event?.banner} />}
            <BasicProLayoutContainer id="formSection">
                <JourneySteps currentStage={currentStage} stages={stages} />
                <FormAutoTouch />
                <Form data-cy="leadGenFormId" id={formName} name={formName} onSubmitCapture={handleSubmit}>
                    <ScrollToTop />
                    <InnerWrapper>
                        <RequiredDetails stage={currentStage} stages={stages} />
                        <Row>
                            <StyledCol span={24}>
                                <JourneySectionWrapper
                                    application={state?.application}
                                    consentSection={consentSection}
                                    isPorscheIdLoginMandatory={isPorscheIdLoginMandatory}
                                    stage={currentStage}
                                    stages={stages}
                                    withCapSearchButton={showSearchCapCustomerButton}
                                >
                                    <Space direction="vertical" size={20} style={{ width: '100%' }}>
                                        {InnerSection}
                                    </Space>
                                </JourneySectionWrapper>
                            </StyledCol>
                        </Row>
                    </InnerWrapper>
                </Form>
                {showLiveChat && <LiveChat chatSetting={liveChatSetting} variants={variants} />}
            </BasicProLayoutContainer>
        </>
    );
};

export default Inner;

import { PHeading } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { defaultFilterOption } from '../../../../../components/fields/SelectField';
import CheckboxField from '../../../../../components/fields/ci/CheckboxField';
import Checkbox from '../../../../../themes/porscheV3/Checkbox/Checkbox';
import NearbyDealer from '../../../../../themes/porscheV3/Fields/NearbyDealer';
import SelectField from '../../../../../themes/porscheV3/Fields/SelectField';
import Tooltip from '../../../../../themes/porscheV3/Tooltip';
import {
    hasAppointmentScenario,
    hasVisitAppointmentScenario,
} from '../../../../admin/ModuleDetailsPage/modules/implementations/shared';
import { StyledInfoCircleFilled } from '../../../../shared/CIPage/ui';
import { useEventJourneySetupContext } from '../../Entrypoint/EventJourneySetup';
import useModelVariantOptions from '../../RequiredDetails/shared/useModelVariantOptions';
import type { StageProps } from '../../RequiredDetails/types';
import { CheckboxContainer, FieldRowContainer } from '../../RequiredDetails/ui';
import AppointmentDetailsSection, { AppointmentType } from '../AppointmentDetailsSection';
import DealershipVehicleInterest from '../DealershipVehicleInterest';
import { allowedDealerInfoField, type ApplicantFormValues } from '../shared';

const colSpan = { xs: 24, lg: 12, xl: 6 };
const appointmentColSpan = { xs: 24, lg: 12 };

const RequiredDetails = ({ stage, stages }: StageProps) => {
    const company = useCompany();

    const { t } = useTranslation([
        'eventApplicantForm',
        'eventRequiredDetails',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'configuratorJourney',
        'appointmentPage',
    ]);

    const { event, eventModule, variants } = useEventJourneySetupContext();

    const { values, setFieldValue } = useFormikContext<ApplicantFormValues>();

    const [modelOptions, variantOptions] = useModelVariantOptions({
        dealerId: values.dealerId,
        modelId: values.vehicleInterest?.model,
        availableLocalVariantsForModule: variants,
        dealerVehicles: event.dealerVehicles,
    });

    const { isAllowTestDrive, isAllowShowroomVisit } = useMemo(
        () => ({
            isAllowTestDrive:
                (hasAppointmentScenario(event.scenarios) && event.isAllowTestDrive) || values.configuration.testDrive,
            isAllowShowroomVisit: hasVisitAppointmentScenario(event.scenarios) || values.configuration.visitAppointment,
        }),
        [event.isAllowTestDrive, event.scenarios, values.configuration.testDrive, values.configuration.visitAppointment]
    );

    const { showTestDriveDatePicker, showShowroomVisitDatePicker } = useMemo(
        () => ({
            showTestDriveDatePicker:
                eventModule?.appointmentModule &&
                hasAppointmentScenario(event.scenarios) &&
                eventModule?.displayAppointmentDatepicker &&
                values.configuration.testDrive,
            showShowroomVisitDatePicker:
                eventModule?.visitAppointmentModule &&
                hasVisitAppointmentScenario(event.scenarios) &&
                eventModule?.displayAppointmentDatepicker &&
                values.configuration.visitAppointment,
        }),
        [
            event.scenarios,
            eventModule?.appointmentModule,
            eventModule?.displayAppointmentDatepicker,
            eventModule?.visitAppointmentModule,
            values.configuration.testDrive,
            values.configuration.visitAppointment,
        ]
    );

    const onDealerChange = useCallback(() => {
        setFieldValue('vehicleInterest.model', null);
        setFieldValue('vehicleInterest.variant', null);
    }, [setFieldValue]);

    const onModelChange = useCallback(() => {
        setFieldValue('vehicleInterest.variant', null);
    }, [setFieldValue]);

    const requiredDetailsTitle = useMemo(() => {
        if (event?.showDealership && event?.hasVehicleIntegration) {
            return t('eventRequiredDetails:title.main');
        }

        if (event?.showDealership) {
            return t('eventRequiredDetails:title.dealership');
        }

        if (event?.hasVehicleIntegration) {
            return t('eventRequiredDetails:title.vehicle');
        }

        return '';
    }, [event?.hasVehicleIntegration, event?.showDealership, t]);

    const appointmentTitle = useMemo(() => {
        if (isAllowTestDrive && isAllowShowroomVisit) {
            return t('eventRequiredDetails:title.testDriveAndShowroomVisit');
        }

        if (isAllowTestDrive) {
            return t('eventRequiredDetails:title.testDrive');
        }

        if (isAllowShowroomVisit) {
            return t('eventRequiredDetails:title.showroomVisit');
        }

        return '';
    }, [isAllowShowroomVisit, isAllowTestDrive, t]);

    const [hasDealerAndVehicleSection, hasAppointmentAndTradeInSection] = useMemo(
        () => [event?.showDealership || event?.hasVehicleIntegration, isAllowTestDrive || isAllowShowroomVisit],
        [event?.hasVehicleIntegration, event?.showDealership, isAllowShowroomVisit, isAllowTestDrive]
    );

    if (!hasDealerAndVehicleSection && !hasAppointmentAndTradeInSection) {
        return null;
    }

    return (
        <Row gutter={[0, 32]}>
            <Col span={24}>
                <div className="v3-layout-card">
                    <PHeading size="large">{requiredDetailsTitle}</PHeading>
                    {hasDealerAndVehicleSection && (
                        <FieldRowContainer gutter={[16, 0]}>
                            {event?.showDealership && (
                                <Col {...colSpan}>
                                    {company?.findNearbyDealer ? (
                                        <NearbyDealer
                                            allowedInfo={allowedDealerInfoField}
                                            data-cy="vehicleInterestDealer"
                                            event={event}
                                            label={t('eventApplicantForm:fields.findNearbyDealer.label')}
                                            name="dealerId"
                                            onChange={onDealerChange}
                                            required
                                        />
                                    ) : (
                                        <DealershipVehicleInterest
                                            {...t('eventApplicantForm:fields.preferredDealership', {
                                                returnObjects: true,
                                            })}
                                            allowedInfo={allowedDealerInfoField}
                                            data-cy="vehicleInterestDealer"
                                            event={event}
                                            name="dealerId"
                                            onChange={onDealerChange}
                                            isTranslatedOption
                                            required
                                        />
                                    )}
                                </Col>
                            )}
                            {event?.hasVehicleIntegration && (
                                <>
                                    <Col {...colSpan}>
                                        <SelectField
                                            {...t('eventRequiredDetails:fields.model', {
                                                returnObjects: true,
                                            })}
                                            data-cy="vehicleInterestSubmodel"
                                            disabled={!values.dealerId}
                                            filterOption={defaultFilterOption}
                                            name="vehicleInterest.model"
                                            onChange={onModelChange}
                                            options={modelOptions}
                                            required
                                            showSearch
                                        />
                                    </Col>
                                    <Col {...colSpan}>
                                        <SelectField
                                            {...t('eventRequiredDetails:fields.variant', {
                                                returnObjects: true,
                                            })}
                                            data-cy="vehicleInterestVariant"
                                            disabled={!values.vehicleInterest?.model}
                                            filterOption={defaultFilterOption}
                                            name="vehicleInterest.variant"
                                            options={variantOptions}
                                            required
                                            showSearch
                                        />
                                    </Col>
                                </>
                            )}
                        </FieldRowContainer>
                    )}
                </div>
            </Col>
            {hasAppointmentAndTradeInSection && (
                <Col span={24}>
                    <div className="v3-layout-card">
                        <Row gutter={[0, 16]}>
                            <Col span={24}>
                                <PHeading size="large">{appointmentTitle}</PHeading>
                            </Col>
                            <Col span={24}>
                                <Row gutter={[16, 16]}>
                                    {isAllowTestDrive && (
                                        <Col {...appointmentColSpan}>
                                            <Row gutter={[24, 16]}>
                                                <Col span={24}>
                                                    <CheckboxContainer>
                                                        <CheckboxField
                                                            customComponent={Checkbox}
                                                            name="configuration.testDrive"
                                                        >
                                                            {t('eventRequiredDetails:fields.testDrive.label')}
                                                        </CheckboxField>
                                                        <Tooltip
                                                            placement="top"
                                                            title={t('eventRequiredDetails:fields.testDrive.tooltip')}
                                                        >
                                                            <StyledInfoCircleFilled />
                                                        </Tooltip>
                                                    </CheckboxContainer>
                                                </Col>
                                                {showTestDriveDatePicker && (
                                                    <Col span={24}>
                                                        <AppointmentDetailsSection
                                                            applicationModule={event.module}
                                                            appointmentType={AppointmentType.Appointment}
                                                            colSpan={{ lg: 12, xs: 24 }}
                                                            event={event}
                                                            showSkipValidation={event.privateAccess}
                                                        />
                                                    </Col>
                                                )}
                                            </Row>
                                        </Col>
                                    )}

                                    {isAllowShowroomVisit && (
                                        <Col {...appointmentColSpan}>
                                            <Row gutter={[24, 16]}>
                                                <Col span={24}>
                                                    <CheckboxContainer>
                                                        <CheckboxField
                                                            customComponent={Checkbox}
                                                            name="configuration.visitAppointment"
                                                        >
                                                            {t(
                                                                'eventRequiredDetails:fields.requestShowroomVisit.label'
                                                            )}
                                                        </CheckboxField>
                                                    </CheckboxContainer>
                                                </Col>
                                                {showShowroomVisitDatePicker && (
                                                    <Col span={24}>
                                                        <AppointmentDetailsSection
                                                            applicationModule={event.module}
                                                            appointmentType={AppointmentType.VisitAppointment}
                                                            colSpan={{ lg: 12, xs: 24 }}
                                                            event={event}
                                                            showSkipValidation={event.privateAccess}
                                                        />
                                                    </Col>
                                                )}
                                            </Row>
                                        </Col>
                                    )}
                                </Row>
                            </Col>
                        </Row>
                    </div>
                </Col>
            )}
        </Row>
    );
};

export default RequiredDetails;

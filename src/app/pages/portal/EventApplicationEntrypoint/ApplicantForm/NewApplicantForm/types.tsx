import { Dispatch, SetStateAction } from 'react';
import { type DealerJourneyDataFragment } from '../../../../../api/fragments/DealerJourneyData';
import { type PorscheIdData, type LocalCustomerManagementModule } from '../../../../../api/types';
import type { UploadDocumentProp } from '../../../../../utilities/kycPresets/shared';
import { type Action, JourneyStage, type State } from '../../../StandardApplicationEntrypoint/Journey/shared';
import { type EventApplicationState } from '../../Journey/shared';

type StateAndDispatchType = {
    state?: State<EventApplicationState>;
    dispatch?: Dispatch<Action<EventApplicationState>>;
};

type CapIntegrationType = {
    showSearchCapCustomerButton: boolean;
    immediateOpenCapCustomerSearch: boolean;
};

type PorscheIdIntegrationType = {
    isPorscheIDFetchLoading: boolean;
    isPorscheIdLoginMandatory: boolean;
    porscheIdIntegrationEnabled: boolean;
    onPorscheIDCustomerFetched: (porscheIdData: PorscheIdData) => void;
    setIsPorscheIDFetchLoading: Dispatch<SetStateAction<boolean>>;
    submitDraftWithPorscheId: () => Promise<string>;
};

type MyInfoIntegrationType = {
    withMyInfo: boolean;
    setWithMyInfo: Dispatch<SetStateAction<boolean>>;
};

export type InnerType = {
    capIntegration: CapIntegrationType;
    currentStage: JourneyStage;
    dealer: DealerJourneyDataFragment;
    formName: string;
    kycExtraSettings: LocalCustomerManagementModule['extraSettings'];
    myInfoIntegration: MyInfoIntegrationType;
    porscheIdIntegration: PorscheIdIntegrationType;
    resetFormHandler: () => void;
    showCommentsToBank: boolean;
    stages: JourneyStage[];
} & StateAndDispatchType &
    Partial<UploadDocumentProp>;

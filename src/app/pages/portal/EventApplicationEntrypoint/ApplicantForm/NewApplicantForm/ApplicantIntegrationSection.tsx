import { useApolloClient } from '@apollo/client';
import { <PERSON><PERSON>uttonPure, PHeading } from '@porsche-design-system/components-react';
import { Col, Grid, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    GetCustomerFieldsFromFilesDocument,
    type GetCustomerFieldsFromFilesQuery,
    type GetCustomerFieldsFromFilesQueryVariables,
} from '../../../../../api/queries/getCustomerFieldsFromFiles';
import useFetchPorscheIdAuthorizedUrl from '../../../../../components/PorscheID/useFetchPorscheIdAuthorizedUrl';
import { useCompany } from '../../../../../components/contexts/CompanyContextManager';
import { useRouter } from '../../../../../components/contexts/shared';
import { useOcrDetectedHandler } from '../../../../../components/ocr';
import { useOcrFilesManagerContext } from '../../../../../components/ocr/OcrFilesManager';
import OcrModal from '../../../../../components/ocr/OcrModal';
import Button from '../../../../../themes/porscheV3/Button';
import notification from '../../../../../themes/porscheV3/notification';
import getApolloErrors from '../../../../../utilities/getApolloErrors';
import useDealershipSettingId from '../../../../../utilities/useDealershipSettingId';
import { useSearchCapCustomerContext } from '../../../../shared/JourneyPage/C@P/SearchCapCustomerOnKYC/shared';
import { useEventJourneyKycAndAgreementContext } from '../../Entrypoint/EventJourneyKycAndAgreement';
import { useEventJourneySetupContext } from '../../Entrypoint/EventJourneySetup';
import { usePersistEventJourneyValues } from '../../Journey/usePersistEventJourneyValues';
import OcrAndMyinfo from '../CustomerDetails/OcrAndMyInfo';
import type { ApplicantFormValues } from '../shared';
import type { InnerType } from './types';
import { ApplicantIntegrationSectionContainer, ButtonContainer } from './ui';
import useApplicationIntegrationOnMobile from './useApplicationIntegrationOnMobile';

const ApplicantIntegrationSection = ({
    capIntegration,
    myInfoIntegration,
    porscheIdIntegration,
    state,
}: Pick<InnerType, 'capIntegration' | 'myInfoIntegration' | 'porscheIdIntegration' | 'state'>) => {
    const screens = Grid.useBreakpoint();

    const { t } = useTranslation([
        'eventApplicantForm',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'capApplication',
        'launchpadLeadDetails',
        'launchpadLeadList',
        'applicationDetails',
    ]);
    const company = useCompany();
    const router = useRouter();

    const { save: persistEventJourneyValue, persistedValue: persistedEventValue } = usePersistEventJourneyValues();
    const { endpoint, event } = useEventJourneySetupContext();
    const { kycState } = useEventJourneyKycAndAgreementContext();
    const { values } = useFormikContext<ApplicantFormValues>();
    const applicationIntegrationOnMobile = useApplicationIntegrationOnMobile();

    // C@P Integration functions
    const { showSearchCapCustomerButton } = capIntegration;
    const searchCapCustomerManager = useSearchCapCustomerContext();
    const searchCapCustomerAction = useCallback(
        (e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation();
            applicationIntegrationOnMobile.close();
            searchCapCustomerManager.showSearchComponent(true);
        },
        [applicationIntegrationOnMobile, searchCapCustomerManager]
    );

    // MyInfo Integration functions
    const dealershipSettingId = useDealershipSettingId(values.dealerId);
    const { setWithMyInfo, withMyInfo } = myInfoIntegration;
    const myInfoEnabled = !!dealershipSettingId(event.myInfoSetting);

    // OCR Integration functions
    const [ocrModalVisible, setOcrModalVisible] = useState(false);
    const ocrModalHandlers = useMemo(
        () => ({ hideModal: () => setOcrModalVisible(false), showModal: () => setOcrModalVisible(true) }),
        [setOcrModalVisible]
    );
    const onOcrDetected = useOcrDetectedHandler(kycState.active);
    const { files } = useOcrFilesManagerContext();
    const client = useApolloClient();
    const onOcrConfirm = useCallback(async () => {
        try {
            const response = await client.query<
                GetCustomerFieldsFromFilesQuery,
                GetCustomerFieldsFromFilesQueryVariables
            >({
                query: GetCustomerFieldsFromFilesDocument,
                variables: {
                    countryCode: company?.countryCode,
                    files: Object.entries(files).map(([kind, file]) => ({ kind, file })),
                },
                fetchPolicy: 'no-cache',
            });

            if (response.data?.fields) {
                onOcrDetected(response.data.fields);
            }
        } catch (error) {
            const apolloErrors = getApolloErrors(error);

            if (apolloErrors !== null) {
                const { $root: rootError } = apolloErrors;

                if (rootError) {
                    notification.error(rootError);
                }
            } else {
                console.error(error);
            }
        }
    }, [client, company?.countryCode, files, onOcrDetected]);

    // Porsche ID integration functions
    const { porscheIdIntegrationEnabled, submitDraftWithPorscheId } = porscheIdIntegration;
    const fetchPorscheIdAuthorizedUrl = useFetchPorscheIdAuthorizedUrl();

    const redirectToPorscheIDPortal = useCallback(
        async (e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation();

            // Set appointment values inside temporary storage, that will be retrieved later
            persistEventJourneyValue({
                ...persistedEventValue,
                appointment: values.appointment,
                visitAppointment: values.visitAppointment,
            });

            if (state?.application) {
                fetchPorscheIdAuthorizedUrl.requestForAuthorizedUrl({
                    applicationId: state?.application.id,
                    routerId: router.id,
                    endpointId: endpoint.id,
                });
            } else {
                notification.loading({
                    content: t('eventApplicantForm:messages.creationSubmitting'),
                    duration: 0,
                    key: 'primary',
                });

                const newApplicationId = await submitDraftWithPorscheId();
                if (newApplicationId) {
                    fetchPorscheIdAuthorizedUrl.requestForAuthorizedUrl({
                        applicationId: newApplicationId,
                        routerId: router.id,
                        endpointId: endpoint.id,
                    });
                }
            }
        },
        [
            endpoint.id,
            fetchPorscheIdAuthorizedUrl,
            persistEventJourneyValue,
            persistedEventValue,
            router.id,
            state?.application,
            submitDraftWithPorscheId,
            t,
            values.appointment,
            values.visitAppointment,
        ]
    );

    const actionButtons = useMemo(
        () =>
            [
                company?.countryCode === 'SG' && !withMyInfo && myInfoEnabled && (
                    <div className="myinfo-button">
                        <OcrAndMyinfo
                            dealerId={values.dealerId}
                            endpoint={endpoint}
                            event={event}
                            kycPresets={kycState.active}
                            setWithMyInfo={setWithMyInfo}
                            state={state}
                            withMyInfo={withMyInfo}
                            singpassButtonOnly
                        />
                    </div>
                ),
                porscheIdIntegrationEnabled && !values.customerCiamId && (
                    <Button onClick={redirectToPorscheIDPortal} porscheFallbackIcon="user" type="secondary">
                        {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingPorscheId')}
                    </Button>
                ),
                showSearchCapCustomerButton && !values.capValues?.businessPartnerGuid && (
                    <Button
                        disabled={!values.dealerId}
                        onClick={searchCapCustomerAction}
                        porscheFallbackIcon="search"
                        type="secondary"
                    >
                        {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveFromCap')}
                    </Button>
                ),
                !withMyInfo && endpoint.eventApplicationModule.isOcrEnabled && (
                    <>
                        <Button porscheFallbackIcon="camera" type="secondary">
                            {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingId')}
                        </Button>
                        <OcrModal
                            hide={ocrModalHandlers.hideModal}
                            onConfirm={onOcrConfirm}
                            visible={ocrModalVisible}
                        />
                    </>
                ),
            ].filter(Boolean),
        [
            company?.countryCode,
            endpoint,
            event,
            kycState.active,
            myInfoEnabled,
            ocrModalHandlers.hideModal,
            ocrModalVisible,
            onOcrConfirm,
            porscheIdIntegrationEnabled,
            redirectToPorscheIDPortal,
            searchCapCustomerAction,
            setWithMyInfo,
            showSearchCapCustomerButton,
            state,
            t,
            values.capValues?.businessPartnerGuid,
            values.customerCiamId,
            values.dealerId,
            withMyInfo,
        ]
    );

    // Keeping this for future testing with scenario of showing all buttons
    // const actionButtons = useMemo(
    //     () =>
    //         [
    //             <div className="myinfo-button">
    //                 <OcrAndMyinfo
    //                     dealerId={values.dealerId}
    //                     endpoint={endpoint}
    //                     event={event}
    //                     kycPresets={kycState.active}
    //                     setWithMyInfo={setWithMyInfo}
    //                     state={state}
    //                     withMyInfo={withMyInfo}
    //                     singpassButtonOnly
    //                 />
    //             </div>,
    //             <Button onClick={redirectToPorscheIDPortal} porscheFallbackIcon="user" type="secondary">
    //                 {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingPorscheId')}
    //             </Button>,
    //             <Button onClick={searchCapCustomerAction} porscheFallbackIcon="search" type="secondary">
    //                 {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveFromCap')}
    //             </Button>,
    //             <>
    //                 <Button porscheFallbackIcon="camera" type="secondary">
    //                     {t('eventApplicantForm:applicantIntegrationSection.buttons.retrieveUsingId')}
    //                 </Button>
    //                 <OcrModal hide={ocrModalHandlers.hideModal} onConfirm={onOcrConfirm} visible={ocrModalVisible} />
    //             </>,
    //             <Button porscheFallbackIcon="mobile" type="secondary">
    //                 {t('eventApplicantForm:applicantIntegrationSection.buttons.continueOnCustomerDevice')}
    //             </Button>,
    //         ].filter(Boolean),
    //     [
    //         endpoint,
    //         event,
    //         kycState.active,
    //         ocrModalHandlers.hideModal,
    //         ocrModalVisible,
    //         onOcrConfirm,
    //         redirectToPorscheIDPortal,
    //         searchCapCustomerAction,
    //         setWithMyInfo,
    //         state,
    //         t,
    //         values.dealerId,
    //         withMyInfo,
    //     ]
    // );

    if (!actionButtons.length) {
        return null;
    }

    return (
        <>
            {screens.md ? (
                <ApplicantIntegrationSectionContainer>
                    <Col span={24}>
                        <Row>
                            <PHeading size="small">
                                {t('eventApplicantForm:applicantIntegrationSection.title')}
                            </PHeading>
                        </Row>
                        <ButtonContainer buttonCount={actionButtons.length}>
                            {actionButtons.map(button => button)}
                        </ButtonContainer>
                    </Col>
                </ApplicantIntegrationSectionContainer>
            ) : (
                <Col span={24}>
                    <PButtonPure icon="arrow-right" onClick={applicationIntegrationOnMobile.open} type="button">
                        {t('eventApplicantForm:applicantIntegrationSection.mobileCta')}
                    </PButtonPure>
                </Col>
            )}
            {applicationIntegrationOnMobile.render({ actionButtons })}
        </>
    );
};
export default ApplicantIntegrationSection;

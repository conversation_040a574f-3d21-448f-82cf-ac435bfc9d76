import { PText } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Modal from '../../../../../themes/porscheV3/Modal';
import { ButtonContainer } from './ui';

const useApplicationIntegrationOnMobile = () => {
    const { t } = useTranslation([
        'eventApplicantForm',

        // Load these first, when there are components that call these translation
        // It won't trigger portal loading
        'capApplication',
        'launchpadLeadDetails',
        'applicationDetails',
    ]);

    const [isModalOpen, setIsModalOpen] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setIsModalOpen(true),
            close: () => setIsModalOpen(false),
        }),
        []
    );

    return {
        ...actions,
        render: ({ actionButtons }: { actionButtons: JSX.Element[] }) => (
            <Modal
                footer={null}
                onCancel={actions.close}
                open={isModalOpen}
                title={t('eventApplicantForm:applicantIntegrationSection.modal.title')}
                closable
                destroyOnClose
            >
                <Col span={24}>
                    <Row>
                        <PText>{t('eventApplicantForm:applicantIntegrationSection.modal.subtitle')}</PText>
                    </Row>
                    <ButtonContainer buttonCount={actionButtons.length} isMobile>
                        {actionButtons.map(button => button)}
                    </ButtonContainer>
                </Col>
            </Modal>
        ),
    };
};

export default useApplicationIntegrationOnMobile;

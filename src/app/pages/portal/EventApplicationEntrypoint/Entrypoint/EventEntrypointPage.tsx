import dayjs from 'dayjs';
import type { EventApplicationEntrypointContextDataFragment } from '../../../../api/fragments/EventApplicationEntrypointContextData';
import type { JourneyEventDataFragment } from '../../../../api/fragments/JourneyEventData';
import UserSessionExpireModal from '../../../../components/UserSessionExpireModal';
import OcrFilesManager from '../../../../components/ocr/OcrFilesManager';
import LeadGenFormUnavailableResult from '../../../../components/results/LeadGenFormUnavailableResult';
import EventEntrypointInner from './EventEntrypointInner';
import { EventJourneyKycAndAgreementProvider } from './EventJourneyKycAndAgreement';
import { EventJourneySetupProvider } from './EventJourneySetup';

type EventEntrypointPageProps = {
    endpoint: EventApplicationEntrypointContextDataFragment;
    event: JourneyEventDataFragment;
    eventName: string;
};

const EventEntrypointPage = ({ endpoint, event, eventName }: EventEntrypointPageProps) => {
    // if event is expired, return expiry page
    if (dayjs().isAfter(event.period.end)) {
        return <LeadGenFormUnavailableResult eventName={eventName} />;
    }

    // if event is not expired, return form
    return (
        <OcrFilesManager>
            {event.privateAccess && <UserSessionExpireModal />}

            <EventJourneySetupProvider endpoint={endpoint} event={event}>
                <EventJourneyKycAndAgreementProvider>
                    <EventEntrypointInner />
                </EventJourneyKycAndAgreementProvider>
            </EventJourneySetupProvider>
        </OcrFilesManager>
    );
};

export default EventEntrypointPage;

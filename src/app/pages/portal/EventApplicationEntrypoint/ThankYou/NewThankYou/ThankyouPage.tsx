import { PHeading, PText } from '@porsche-design-system/components-react';
import { Col, Row, Space } from 'antd';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import UserSessionExpireModal from '../../../../../components/UserSessionExpireModal';
import BasicProLayoutContainer from '../../../../../layouts/BasicProLayout/BasicProLayoutContainer';
import { useThemeComponents } from '../../../../../themes/hooks';
import Button from '../../../../../themes/porscheV3/Button';
import breakpoints from '../../../../../utilities/breakpoints';
import useDealerTranslatedString from '../../../../../utilities/useDealerTranslatedString';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import Media from '../../../ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/components/Media';
import type { EventApplicationState } from '../../Journey/shared';
import { usePersistEventJourneyValues } from '../../Journey/usePersistEventJourneyValues';
import BookingDeposit from '../../PaymentPage/NewPaymentPage/shared/BookingDeposit';
import type { ThankyouPageContentProps } from '../types';
import DealerInfo from './DealerInfo';
import SubmittedContent from './SubmittedContent';

const TitleContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 36px;
    margin-top: 12px;
`;

const ContentContainer = styled.div`
    background-color: #ffffff;
    padding: 32px;
    border-radius: 12px;
    width: 892px;
`;

const VehicleInfoContainer = styled.div`
    margin-top: 16px;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
`;

const MediaContainer = styled.div`
    & .ant-image {
        width: auto;
        height: 181px;
        margin: auto;
        & > img {
            border-radius: var(--card-border-radius, initial);
            aspect-ratio: 16/9;
            object-fit: cover;
        }
    }

    @media (min-width: ${breakpoints.sm}) {
        & .ant-image {
            width: 180px;
            height: auto;
        }
    }
`;

const VehicleInfoDetails = styled.div`
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: column;
`;

const VehicleDetails = ({ application }: { application: EventApplicationState }) => {
    const translatedString = useTranslatedString();
    const { t } = useTranslation(['eventThankYou']);

    const vehicleData = useMemo(() => {
        if (application.vehicle?.__typename === 'LocalVariant') {
            return {
                variant: translatedString(application.vehicle.name),
                filename: application.vehicle?.images?.[0]?.filename,
                source: application.vehicle?.images?.[0]?.url,
            };
        }

        return null;
    }, [application, translatedString]);

    return (
        <>
            <PText size="medium" weight="bold">
                {t('eventThankYou:vehicleDetails.selectedVehicle')}
            </PText>
            <VehicleInfoContainer>
                <MediaContainer>
                    <Media fileName={vehicleData.filename} source={vehicleData.source} />
                </MediaContainer>
                <VehicleInfoDetails>
                    <PText size="medium" weight="bold">
                        {vehicleData.variant}
                    </PText>
                </VehicleInfoDetails>
            </VehicleInfoContainer>
        </>
    );
};

const ThankYouPage = ({
    contents,
    depositAmount,
    hasTestDriveProcessForPrivateAccess,
    onDone,
    renderTestDriveModal,
    state,
}: ThankyouPageContentProps) => {
    const { t } = useTranslation('eventThankYou');
    const { StandardLayout } = useThemeComponents();
    const { remove: removePersistedEventValue } = usePersistEventJourneyValues();

    const { application } = state;

    const { dealer, deposit, event } = application;

    const hasDeposit = !!deposit;
    const defaultButtonText = useMemo(
        () =>
            !hasTestDriveProcessForPrivateAccess
                ? t('eventThankYou:actions.done')
                : t('eventThankYou:actions.startTestDrive'),
        [hasTestDriveProcessForPrivateAccess, t]
    );

    const dealerTranslatedString = useDealerTranslatedString(dealer.id);
    const buttonText = useMemo(
        () =>
            event.hasCustomiseThankYouPage && event.thankYouPageContent.isCustomRedirectionButton
                ? dealerTranslatedString(event.thankYouPageContent.redirectButton?.title)
                : defaultButtonText,
        [
            dealerTranslatedString,
            defaultButtonText,
            event.hasCustomiseThankYouPage,
            event.thankYouPageContent.isCustomRedirectionButton,
            event.thankYouPageContent.redirectButton?.title,
        ]
    );

    const onDoneAction = useCallback(() => {
        onDone();
    }, [onDone]);

    useEffect(() => {
        // The temporary value will be removed when thank you page rendered
        removePersistedEventValue();
    }, [removePersistedEventValue]);

    return (
        <div data-cy="eventThankyouPage">
            <StandardLayout>
                <BasicProLayoutContainer>
                    <TitleContainer>
                        <PHeading>{t('eventThankYou:title')}</PHeading>
                    </TitleContainer>
                    <Row gutter={[0, 24]}>
                        {(application.vehicle || hasDeposit) && (
                            <Col span={24}>
                                <Row justify="center">
                                    <ContentContainer>
                                        <Row gutter={[16, 16]}>
                                            {application.vehicle && (
                                                <Col span={24}>
                                                    <VehicleDetails application={application} />
                                                </Col>
                                            )}
                                            {hasDeposit && (
                                                <Col span={24}>
                                                    <BookingDeposit depositAmount={depositAmount} />
                                                </Col>
                                            )}
                                        </Row>
                                    </ContentContainer>
                                </Row>
                            </Col>
                        )}
                        <Col span={24}>
                            <Row justify="center">
                                <ContentContainer>
                                    <Space direction="vertical" size={36} style={{ width: '100%' }}>
                                        <Space direction="vertical" size={36}>
                                            <SubmittedContent {...contents} />
                                        </Space>
                                        {dealer && event.showDealership && <DealerInfo dealer={dealer} />}
                                        <Button
                                            key="done"
                                            data-cy="eventThankPageButton"
                                            htmlType="button"
                                            onClick={onDoneAction}
                                            type="primary"
                                            block
                                        >
                                            {buttonText}
                                        </Button>
                                    </Space>
                                </ContentContainer>
                            </Row>
                        </Col>
                    </Row>
                    {renderTestDriveModal()}
                    {event?.privateAccess && <UserSessionExpireModal />}
                </BasicProLayoutContainer>
            </StandardLayout>
        </div>
    );
};

export default ThankYouPage;

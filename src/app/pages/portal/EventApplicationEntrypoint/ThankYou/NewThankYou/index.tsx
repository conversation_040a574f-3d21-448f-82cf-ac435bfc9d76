import { isEmpty, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import type { StartTestDriveMutation } from '../../../../../api/mutations/startTestDrive';
import { ApplicationStage, Maybe } from '../../../../../api/types';
import NotFoundResult from '../../../../../components/results/NotFoundResult';
import { getApplicationIdentifier } from '../../../../../utilities/application';
import useDealerTranslatedString from '../../../../../utilities/useDealerTranslatedString';
import useTranslatedString from '../../../../../utilities/useTranslatedString';
import useTestDriveStartModal from '../../../../shared/ApplicationDetailsPage/generic/TestDriveModal/TestDriveStartedModal';
import type { State } from '../../../StandardApplicationEntrypoint/Journey/shared';
import type { EventApplicationState } from '../../Journey/shared';
import ThankyouPage from './ThankyouPage';

export type ThankYouPageProps = {
    state: State<EventApplicationState>;
};

const getEventApplication = (application: EventApplicationState) => {
    if (application.__typename === 'EventApplication') {
        return application;
    }

    throw new Error('Invalid event application');
};

/* Since VF-1406: This page is inactive and using the one on NewThankYou folder instead
    but keep it in case of revert needed */

const ThankYouPageContent = ({ state }: ThankYouPageProps) => {
    const { t } = useTranslation('eventThankYou');
    const translatedString = useTranslatedString();
    const navigate = useNavigate();
    const { application } = state;
    const { module, event, dealerId } = application;

    const dealerTranslatedString = useDealerTranslatedString(dealerId);

    const eventModule = useMemo(() => (module.__typename === 'EventApplicationModule' ? module : null), [module]);

    const eventApplication = getEventApplication(application);

    const onFinishModal = useCallback((data: StartTestDriveMutation) => {
        window.location.href = data?.result?.redirectionLink;
    }, []);

    const [startTestDrive, renderTestDriveModal] = useTestDriveStartModal(
        {
            __typename: 'EventApplication',
            id: application.id,
            vehicle: application.vehicle?.__typename === 'LocalVariant' && application.vehicle,
            appointmentStage: application.appointmentStage,
            draftFlow: application.draftFlow,
            moduleId: eventModule?.id,
            dealer: application.dealer,
            vehicleId: application.vehicle?.__typename === 'LocalVariant' && application.vehicle.id,
        },
        ApplicationStage.Appointment,
        onFinishModal
    );

    const hasTestDriveProcessForPrivateAccess = useMemo(
        () =>
            application.event.privateAccess &&
            !isNil(application.appointmentStage) &&
            application.configuration.testDrive &&
            application.appointmentStage.appointmentModule.__typename === 'AppointmentModule' &&
            application.appointmentStage.appointmentModule.hasTestDriveProcess &&
            application.appointmentStage.appointmentModule.hasTestDriveSigning,
        [application.appointmentStage, application.configuration.testDrive, application.event.privateAccess]
    );

    const onDone = useCallback(() => {
        if (
            event.hasCustomiseThankYouPage &&
            event.thankYouPageContent.isCustomRedirectionButton &&
            !isEmpty(event.thankYouPageContent.redirectButton.url)
        ) {
            window.location.replace(event.thankYouPageContent.redirectButton.url);

            return;
        }
        if (hasTestDriveProcessForPrivateAccess) {
            startTestDrive();
        } else {
            navigate(`../${eventApplication.event.urlSlug}`);
        }
    }, [
        event.hasCustomiseThankYouPage,
        event.thankYouPageContent.isCustomRedirectionButton,
        event.thankYouPageContent.redirectButton.url,
        hasTestDriveProcessForPrivateAccess,
        startTestDrive,
        navigate,
        eventApplication.event.urlSlug,
    ]);

    const make = useMemo(() => {
        if (application.__typename === 'EventApplication' && application.vehicle?.__typename === 'LocalVariant') {
            return translatedString(
                application.vehicle.model.parentModel
                    ? application.vehicle.model.parentModel.make.name
                    : application.vehicle.model.make.name
            );
        }

        return '';
    }, [application, translatedString]);

    const depositAmount = useMemo(() => {
        if (application.__typename === 'EventApplication') {
            const { dealerId, event } = application;

            const { defaultValue, overrides } = event.depositAmount;
            if (event.depositAmount.overrides.length) {
                const dealerSpecific = overrides.find(override => override.dealerId === dealerId);

                const value = !dealerSpecific ? defaultValue : dealerSpecific.value;

                return value;
            }

            return defaultValue;
        }

        return application.deposit.amount;
    }, [application]);

    const identifier = useMemo(
        () =>
            getApplicationIdentifier(application, [
                ApplicationStage.Financing,
                ApplicationStage.Reservation,
                ApplicationStage.Lead,
                ApplicationStage.Appointment,
                ApplicationStage.VisitAppointment,
            ]) ?? application.lead?.identifier,
        [application]
    );

    const contentProps = useMemo(
        () => ({
            title: event.hasCustomiseThankYouPage
                ? dealerTranslatedString(event.thankYouPageContent.introTitle, { make })
                : t('eventThankYou:contents.title', { make }),
            description: event.hasCustomiseThankYouPage
                ? dealerTranslatedString(event.thankYouPageContent.contentText)
                : t('eventThankYou:contents.description'),
            reference: t('eventThankYou:contents.reference', { reference: identifier }),
        }),
        [
            dealerTranslatedString,
            event.hasCustomiseThankYouPage,
            event.thankYouPageContent.contentText,
            event.thankYouPageContent.introTitle,
            identifier,
            make,
            t,
        ]
    );

    return (
        <ThankyouPage
            contents={contentProps}
            depositAmount={depositAmount}
            hasTestDriveProcessForPrivateAccess={hasTestDriveProcessForPrivateAccess}
            onDone={onDone}
            renderTestDriveModal={renderTestDriveModal}
            state={state}
        />
    );
};

const ThankYouPage = () => {
    const state = useLocation().state as Maybe<State<EventApplicationState>>;

    if (!state) {
        return <NotFoundResult />;
    }

    return <ThankYouPageContent state={state} />;
};

export default ThankYouPage;

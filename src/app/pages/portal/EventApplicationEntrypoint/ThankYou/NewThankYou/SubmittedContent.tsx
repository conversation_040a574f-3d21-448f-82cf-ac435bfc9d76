import { componentsReady, PHeading, PText } from '@porsche-design-system/components-react';
import { isEmpty } from 'lodash/fp';
import { useEffect, useRef } from 'react';
import styled from 'styled-components';

const Container = styled.div`
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;

    gap: 36px;
    width: 100%;
`;

const ContentContainer = styled.div`
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;

    gap: 16px;
    width: 100%;
`;

export type SubmittedContentProps = {
    title: string;
    reference: string;
    description: string;
    subDescription?: string;
};

const SubmittedContent = ({ title, reference, description, subDescription }: SubmittedContentProps) => {
    const wrapperRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const elm = wrapper.querySelector('p-heading');
            const elmShaRoot = elm?.shadowRoot;
            if (!elm || !elmShaRoot) {
                return;
            }

            const rootHeadingElm = elmShaRoot.querySelector('.root');
            if (rootHeadingElm) {
                rootHeadingElm.setAttribute('style', 'font-weight:400');
            }
        });
    }, []);

    return (
        <div ref={wrapperRef}>
            <Container>
                <PHeading>{title}</PHeading>
                <ContentContainer>
                    <PText size="medium">{reference}</PText>
                    <PText size="medium">{description}</PText>
                    {!isEmpty(subDescription) && <PText size="medium">{subDescription}</PText>}
                </ContentContainer>
            </Container>
        </div>
    );
};

export default SubmittedContent;

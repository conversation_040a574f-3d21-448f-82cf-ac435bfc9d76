/* eslint-disable max-len */
import { useApolloClient } from '@apollo/client';
import { useCallback } from 'react';
import {
    type UpdateEventApplicationMutation,
    type UpdateEventApplicationMutationVariables,
    UpdateEventApplicationDocument,
} from '../../../../api/mutations/updateEventApplication';
import { CustomerKind } from '../../../../api/types';
import { State } from '../../StandardApplicationEntrypoint/Journey/shared';
import { EventApplicationState } from '../Journey/shared';
import type { RequiredDetailsFormValues } from './types';

const useUpdateRequiredDetails = (state: State<EventApplicationState>) => {
    const apolloClient = useApolloClient();

    const application = state?.application;
    const token = state?.token;

    return useCallback(
        async (values: RequiredDetailsFormValues) => {
            const { data } = await apolloClient.mutate<
                UpdateEventApplicationMutation,
                UpdateEventApplicationMutationVariables
            >({
                mutation: UpdateEventApplicationDocument,
                variables: {
                    token,
                    dealerId: values.dealerId,
                    vehicle: {
                        existingSimpleLocalVehicleId: values.vehicleId,
                    },
                    configuration: {
                        ...values.configuration,
                        assetCondition: application?.configuration?.assetCondition,
                    },
                    tradeInVehicle: application?.tradeInVehicle,
                    customerKind: CustomerKind.Local,
                    customizedFields: (application?.customizedFields || []).map(customizedField => ({
                        characterLimit: customizedField.characterLimit,
                        displayName: customizedField.displayName,
                        isMandatory: customizedField.isMandatory,
                        value: customizedField.value,
                    })),
                    remarks: application?.remarks,
                },
            });

            return data.result;
        },
        [
            apolloClient,
            application?.configuration?.assetCondition,
            application?.customizedFields,
            application?.remarks,
            application?.tradeInVehicle,
            token,
        ]
    );
};

export default useUpdateRequiredDetails;

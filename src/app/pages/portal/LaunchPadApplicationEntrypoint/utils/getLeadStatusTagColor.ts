import type { PTagProps } from '@porsche-design-system/components-react';
import { LeadStatus } from '../../../../api/types';

const getLeadStatusTagColor = (leadStatus: LeadStatus): PTagProps['color'] => {
    if (leadStatus === LeadStatus.SubmissionFailed || leadStatus === LeadStatus.SubmittedWithError) {
        return 'notification-error-soft';
    }

    return 'background-surface';
};

export default getLeadStatusTagColor;

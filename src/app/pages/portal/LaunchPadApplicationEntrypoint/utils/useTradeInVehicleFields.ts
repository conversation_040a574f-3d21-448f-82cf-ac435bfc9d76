import { useMemo } from 'react';
import type { LeadDataFragment } from '../../../../api/fragments/LeadData';
import { LocalCustomerFieldSource } from '../../../../api/types';
import isKYCPresetHasCurrentVehicleFields from '../../../../utilities/kycPresets/isKYCPresetHasCurrentVehicleFields';
import type { LeadFormValues } from './types';

type UseTradeInVehicleFieldsResult = {
    hasCurrentVehicleFields: boolean;
    areTradeInValuesFromMyInfo: boolean;
};

const useTradeInVehicleFields = (
    customerKYC: LeadDataFragment['customerKYC'],
    tradeInVehicle?: LeadFormValues['tradeInVehicle']
): UseTradeInVehicleFieldsResult =>
    useMemo(
        () => ({
            hasCurrentVehicleFields: isKYCPresetHasCurrentVehicleFields(customerKYC),
            areTradeInValuesFromMyInfo: (tradeInVehicle || []).every(
                vehicle => vehicle.source === LocalCustomerFieldSource.MyInfo
            ),
        }),
        [customerKYC, tradeInVehicle]
    );

export default useTradeInVehicleFields;

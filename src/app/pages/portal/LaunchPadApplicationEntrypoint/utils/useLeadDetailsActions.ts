import { useMemo } from 'react';
import type { LaunchPadModuleSpecsFragment } from '../../../../api/fragments/LaunchPadModuleSpecs';
import type { LeadDataFragment } from '../../../../api/fragments/LeadData';
import { LeadStatus } from '../../../../api/types';
import type { useLeadDetailsContext } from '../LeadDetails/LeadDetailsContext';

type UseLeadDetailsActionsProps = {
    lead: LeadDataFragment;
    permissions: ReturnType<typeof useLeadDetailsContext>['permissions'];
    launchpadModule: LaunchPadModuleSpecsFragment;
    capIntegrationIsOn: boolean;
    financeAndInsuranceCalculatorEndpoint: string | null;
};

type UseLeadDetailsActionsReturn = {
    // Contact (non-lead) actions
    hasIntentAndAssignAction: boolean;
    hasQualifyAction: boolean;
    hasMarkAsContactedAction: boolean;
    hasUnqualifyAction: boolean;

    // Lead actions
    hasShowroomVisitAction: boolean;
    hasTestDriveAction: boolean;
    hasFinanceAndInsuranceAction: boolean;
    hasAssignStockAction: boolean;
    hasTradeInAction: boolean;
    hasSalesOfferAction: boolean;
    hasFollowUpAction: boolean;

    hasContactAction: boolean;
    hasLeadAction: boolean;
    hasAnyAction: boolean;
};

const useLeadDetailsActions = ({
    lead,
    permissions,
    launchpadModule,
    capIntegrationIsOn,
    financeAndInsuranceCalculatorEndpoint,
}: UseLeadDetailsActionsProps): UseLeadDetailsActionsReturn =>
    useMemo(() => {
        // Contact (non-lead) actions
        const hasIntentAndAssignAction = !lead.isLead && permissions.hasUpdatePermission;

        const hasQualifyAction =
            !lead.isLead &&
            permissions.hasUpdatePermission &&
            (lead.status === LeadStatus.PendingQualify || lead.status === LeadStatus.Contacted) &&
            capIntegrationIsOn;

        const hasMarkAsContactedAction =
            !lead.isLead &&
            permissions.hasUpdatePermission &&
            lead.status === LeadStatus.PendingQualify &&
            capIntegrationIsOn;

        const hasUnqualifyAction =
            !lead.isLead &&
            permissions.hasUpdatePermission &&
            (lead.status === LeadStatus.PendingQualify || lead.status === LeadStatus.Contacted) &&
            capIntegrationIsOn;

        // Lead actions
        const hasShowroomVisitAction =
            lead.isLead && permissions.hasCreateShowroomVisitPermission && !!launchpadModule.visitAppointmentModuleId;

        const hasTestDriveAction =
            lead.isLead && permissions.hasCreateTestDrivePermission && !!launchpadModule.appointmentModuleId;

        const hasFinanceAndInsuranceAction =
            lead.isLead &&
            permissions.hasCreateFinanceAndInsurancePermission &&
            !!launchpadModule.financeAndInsuranceCalculator &&
            !!financeAndInsuranceCalculatorEndpoint;

        const hasAssignStockAction =
            lead.isLead &&
            permissions.hasCreateFinderApplicationFromLeadPermission &&
            !!launchpadModule.finderAssignedStockEntrypoint;

        const hasTradeInAction =
            lead.isLead && permissions.hasCreateTradeInPermission && !!launchpadModule.salesManager;

        const hasSalesOfferAction =
            lead.isLead && permissions.hasCreateSalesOfferPermission && !!launchpadModule.salesOfferModuleId;

        const hasFollowUpAction = lead.isLead && permissions.hasCreateFollowUpPermission;

        const hasContactAction =
            hasIntentAndAssignAction || hasQualifyAction || hasMarkAsContactedAction || hasUnqualifyAction;

        const hasLeadAction =
            hasShowroomVisitAction ||
            hasTestDriveAction ||
            hasFinanceAndInsuranceAction ||
            hasAssignStockAction ||
            hasTradeInAction ||
            hasSalesOfferAction ||
            hasFollowUpAction;

        const hasAnyAction = hasContactAction || hasLeadAction;

        return {
            // Contact actions
            hasIntentAndAssignAction,
            hasQualifyAction,
            hasMarkAsContactedAction,
            hasUnqualifyAction,

            // Lead actions
            hasShowroomVisitAction,
            hasTestDriveAction,
            hasFinanceAndInsuranceAction,
            hasAssignStockAction,
            hasTradeInAction,
            hasSalesOfferAction,
            hasFollowUpAction,

            hasContactAction,
            hasLeadAction,
            hasAnyAction,
        };
    }, [
        lead.isLead,
        lead.status,
        permissions.hasUpdatePermission,
        permissions.hasCreateShowroomVisitPermission,
        permissions.hasCreateTestDrivePermission,
        permissions.hasCreateFinanceAndInsurancePermission,
        permissions.hasCreateFinderApplicationFromLeadPermission,
        permissions.hasCreateTradeInPermission,
        permissions.hasCreateSalesOfferPermission,
        permissions.hasCreateFollowUpPermission,
        launchpadModule.visitAppointmentModuleId,
        launchpadModule.appointmentModuleId,
        launchpadModule.financeAndInsuranceCalculator,
        launchpadModule.finderAssignedStockEntrypoint,
        launchpadModule.salesManager,
        launchpadModule.salesOfferModuleId,
        capIntegrationIsOn,
        financeAndInsuranceCalculatorEndpoint,
    ]);

export default useLeadDetailsActions;

import { useMemo } from 'react';
import type { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';

// eslint-disable-next-line import/prefer-default-export
export const useGetApplicationForTestDrive = (application: ApplicationDataFragment) =>
    useMemo(() => {
        if (
            application.__typename === 'ConfiguratorApplication' ||
            application.__typename === 'MobilityApplication' ||
            application.__typename === 'SalesOfferApplication'
        ) {
            throw new Error('Application type is not applicable for test drive');
        }

        return application;
    }, [application]);

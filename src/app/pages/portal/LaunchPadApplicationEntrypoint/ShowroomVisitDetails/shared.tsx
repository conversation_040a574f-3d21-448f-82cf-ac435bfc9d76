import { useMemo } from 'react';
import type { ApplicationDataFragment } from '../../../../api/fragments/ApplicationData';

// eslint-disable-next-line import/prefer-default-export
export const useGetApplicationForShowroomVisit = (application: ApplicationDataFragment) =>
    useMemo(() => {
        if (application.__typename === 'EventApplication' || application.__typename === 'LaunchpadApplication') {
            return application;
        }

        throw new Error('Application type is not applicable for showroom visit');
    }, [application]);

import {
    LeadDataFragment,
    SalesOfferApplicationSpecsFragment,
    SalesOfferFeatureKind,
    SalesOfferSpecsFragment,
} from '../../../../../api';

export enum JourneyStep {
    /* we are not yet aware of which set is first so we let the system initialize it */
    Initialize = 'initialize',

    /* Deposit */
    Deposit = 'deposit',

    /* Namirial */
    Namirial = 'namirial',

    /* Namirial redirect page */
    NamirialRedirect = 'namirialRedirect',

    /* Namirial Signing Reject Page */
    NamirialReject = 'namirialReject',

    /* Namirial Timeout Page */
    NamirialTimeout = 'namirialTimeout',

    /* Error fallback */
    Unknown = 'unknown',
}

export type SalesOfferJourneyState = {
    isVsaSigningForSalesManager: boolean;
    step: JourneyStep;
    token: string;
    featureKinds: SalesOfferFeatureKind[];
    salesOffer: SalesOfferSpecsFragment;
    lead: LeadDataFragment;
    latestReservationApplication?: SalesOfferApplicationSpecsFragment;
    latestFinancingApplication?: SalesOfferApplicationSpecsFragment;
    latestInsuranceApplication?: SalesOfferApplicationSpecsFragment;
};
export type SalesOfferJourneyAction =
    | { type: 'refreshToken'; token: string }
    | { type: 'goTo'; step: JourneyStep }
    | { type: 'refreshJourney'; journey: Omit<SalesOfferJourneyState, 'step' | 'isVsaSigningForSalesManager'> };

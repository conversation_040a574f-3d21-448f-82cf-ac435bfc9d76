import { ApolloError } from '@apollo/client';
import { PText } from '@porsche-design-system/components-react';
import { useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useDeleteSalesOfferDocumentMutation } from '../../../../../../api';
import { useThemeComponents } from '../../../../../../themes/hooks';
import Modal from '../../../../../../themes/porscheV3/Modal';
import type { SalesOfferDocumentInterface } from './typings';

interface DeleteConfirmModalProps {
    onClose: () => void;
    visible: boolean;
    salesOfferId: string;
    document: SalesOfferDocumentInterface;
}
const DeleteConfirmModal = ({ onClose, visible, document, salesOfferId }: DeleteConfirmModalProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const { notification } = useThemeComponents();

    const handleClose = useCallback(() => {
        onClose();
    }, [onClose]);

    const [mutation] = useDeleteSalesOfferDocumentMutation();
    const onDelete = useCallback(async () => {
        try {
            notification.loading({
                content: t('launchpadSalesOfferDetails:messages.deleteDocument'),
                duration: 0,
                key: 'primary',
            });

            const { errors } = await mutation({
                variables: {
                    salesOfferId,
                    fileId: document.id,
                },
            });

            notification.destroy('primary');

            if (!errors) {
                notification.success({
                    content: t('launchpadSalesOfferDetails:messages.deleteDocumentSuccessful', {
                        filename: document.filename,
                    }),
                    key: 'primary',
                });
                handleClose();
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error(error.graphQLErrors[0].message);
            } else {
                console.error(error);
            }
        }
    }, [document?.filename, document?.id, mutation, notification, salesOfferId, t, handleClose]);

    if (!visible || !document) {
        return null;
    }

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            okText={t('launchpadSalesOfferDetails:sections.documents.deleteConfirmModal.okText')}
            onCancel={handleClose}
            onOk={onDelete}
            open={visible}
            title={t('launchpadSalesOfferDetails:sections.documents.deleteConfirmModal.title')}
            centered
        >
            <PText>
                {t('launchpadSalesOfferDetails:sections.documents.deleteConfirmModal.content', {
                    filename: document.filename,
                })}
            </PText>
        </Modal>
    );
};

const useDeleteConfirmModal = (salesOfferId: string) => {
    const [visible, setVisible] = useState(false);
    const [document, setDocument] = useState<SalesOfferDocumentInterface | null>(null);

    const actions = useMemo(
        () => ({
            open: (item: SalesOfferDocumentInterface) => {
                setDocument(item);
                setVisible(true);
            },
            close: () => {
                setVisible(false);
                setDocument(null);
            },
        }),
        [setVisible]
    );

    return useMemo(
        () => ({
            ...actions,
            render: () => (
                <DeleteConfirmModal
                    document={document}
                    onClose={actions.close}
                    salesOfferId={salesOfferId}
                    visible={visible}
                />
            ),
        }),
        [actions, visible, document, salesOfferId]
    );
};

export type UseDeleteConfirmModalReturn = ReturnType<typeof useDeleteConfirmModal>;
export default useDeleteConfirmModal;

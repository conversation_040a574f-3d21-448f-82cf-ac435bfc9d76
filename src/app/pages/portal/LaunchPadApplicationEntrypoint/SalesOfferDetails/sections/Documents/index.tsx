import styled from 'styled-components';
import { ImagePreview, PdfPreview } from '../../../../../shared/ApplicationDetailsPage/standard/DocumentTab/Preview';
import { extractSalesOfferFromLaunchpadLead } from '../shared';
import useDeleteConfirmModal from './DeleteConfirmModal';
import OtherDocuments from './OtherDocuments';
import useShareModal from './ShareModal';
import SystemDocuments from './SystemDocuments';
import { useActions, useSendShare } from './shared';

export const StyledContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 36px;
`;

const Documents = () => {
    const { id } = extractSalesOfferFromLaunchpadLead();
    const onSend = useSendShare(id);
    const shareModal = useShareModal({ onSend });
    const deleteConfirmModal = useDeleteConfirmModal(id);
    const { onDownload, onShare, onDelete, preview, onCancelPreview, onDocumentPreview } = useActions(
        id,
        shareModal,
        deleteConfirmModal
    );

    return (
        <StyledContainer>
            <SystemDocuments onDownload={onDownload} onPreview={onDocumentPreview} onShare={onShare} />
            <OtherDocuments
                onDelete={onDelete}
                onDownload={onDownload}
                onPreview={onDocumentPreview}
                onShare={onShare}
            />
            {shareModal.render()}
            {deleteConfirmModal.render()}
            {preview?.kind === 'image' && <ImagePreview onCancelPreview={onCancelPreview} url={preview.url} />}
            {preview?.kind === 'pdf' && <PdfPreview onCancelPreview={onCancelPreview} url={preview.url} />}
        </StyledContainer>
    );
};

export default Documents;

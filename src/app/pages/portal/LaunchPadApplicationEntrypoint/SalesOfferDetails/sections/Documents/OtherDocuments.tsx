import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import { CollapsibleContentItem } from '../../../ui/ContentItem';
import DocumentRow, { DocumentRowActionProps } from './DocumentRow';
import UploadDocument from './UploadDocument';
import { useOtherDocuments } from './shared';

const OtherDocuments = ({ onDownload, onShare, onPreview, onDelete }: DocumentRowActionProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const otherDocuments = useOtherDocuments();

    return (
        <CollapsibleContentItem
            title={t('launchpadSalesOfferDetails:sections.documents.otherDocumentsTitle')}
            defaultOpen
        >
            <Col span={24}>
                <Row gutter={[0, 32]}>
                    {otherDocuments.length > 0 && (
                        <Col span={24}>
                            {otherDocuments.map((item, index) => (
                                <DocumentRow
                                    key={item.id}
                                    index={index}
                                    item={item}
                                    onDelete={onDelete}
                                    onDownload={onDownload}
                                    onPreview={onPreview}
                                    onShare={onShare}
                                    type="other"
                                />
                            ))}
                        </Col>
                    )}
                    <Col span={24}>
                        <UploadDocument />
                    </Col>
                </Row>
            </Col>
        </CollapsibleContentItem>
    );
};

export default OtherDocuments;

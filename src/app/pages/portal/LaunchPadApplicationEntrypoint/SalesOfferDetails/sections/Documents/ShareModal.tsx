import { Col, Row } from 'antd';
import { Formik, FormikProps } from 'formik';
import { useMemo, useState, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import Form from '../../../../../../components/fields/Form';
import { useThemeComponents } from '../../../../../../themes/hooks';
import Button from '../../../../../../themes/porscheV3/Button';
import Modal from '../../../../../../themes/porscheV3/Modal';
import useValidator from '../../../../../../utilities/useValidator';
import validators from '../../../../../../utilities/validators';
import type { SalesOfferDocumentInterface, ShareValues, ShareSendParams } from './typings';

interface ShareModalProps {
    onClose: () => void;
    visible: boolean;
    document: SalesOfferDocumentInterface;
    onSend: (values: ShareSendParams) => void;
}
const ShareModal = ({ onClose, visible, onSend, document }: ShareModalProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const {
        FormFields: { InputField },
    } = useThemeComponents();

    const formRef = useRef<FormikProps<ShareValues>>(null);

    const handleClose = useCallback(() => {
        formRef.current?.resetForm();
        onClose();
    }, [onClose]);

    const onSubmit = useCallback(
        (values: ShareValues) => {
            onSend({ ...values, document });
            handleClose();
        },
        [onSend, handleClose, document]
    );

    const footerButton = useMemo(
        () => [
            <Button key="submit" form="shareDocumentForm" htmlType="submit" type="primary" block>
                {t('launchpadSalesOfferDetails:sections.documents.shareModal.send')}
            </Button>,
            <Button key="cancel" onClick={handleClose} type="secondary" block>
                {t('common:actions.cancel')}
            </Button>,
        ],
        [Button, handleClose, t]
    );

    const validation = useValidator(
        validators.compose(
            validators.requiredString('firstName'),
            validators.requiredString('lastName'),
            validators.validEmail('email')
        )
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButton}
            onCancel={handleClose}
            open={visible}
            title={t('launchpadSalesOfferDetails:sections.documents.shareModal.title')}
            centered
        >
            <Formik initialValues={{}} innerRef={formRef} onSubmit={onSubmit} validate={validation}>
                {({ handleSubmit }) => (
                    <Form
                        data-cy="shareDocumentForm"
                        id="shareDocumentForm"
                        name="shareDocumentForm"
                        onSubmitCapture={handleSubmit}
                        style={{ maxWidth: '400px' }}
                    >
                        <Row gutter={[16, 16]}>
                            <Col sm={24}>
                                <Row gutter={[16, 16]}>
                                    <Col sm={12} xs={24}>
                                        <InputField
                                            label={t(
                                                // eslint-disable-next-line max-len
                                                'launchpadSalesOfferDetails:sections.documents.shareModal.fields.firstName'
                                            )}
                                            name="firstName"
                                            required
                                        />
                                    </Col>
                                    <Col sm={12} xs={24}>
                                        <InputField
                                            label={t(
                                                // eslint-disable-next-line max-len
                                                'launchpadSalesOfferDetails:sections.documents.shareModal.fields.lastName'
                                            )}
                                            name="lastName"
                                            required
                                        />
                                    </Col>
                                </Row>
                            </Col>
                            <Col xs={24}>
                                <InputField
                                    label={t('launchpadSalesOfferDetails:sections.documents.shareModal.fields.email')}
                                    name="email"
                                    required
                                />
                            </Col>
                        </Row>
                    </Form>
                )}
            </Formik>
        </Modal>
    );
};

const useShareModal = ({ onSend }: { onSend: (values: ShareSendParams) => void }) => {
    const [visible, setVisible] = useState(false);
    const [document, setDocument] = useState<SalesOfferDocumentInterface | null>(null);

    const actions = useMemo(
        () => ({
            open: (item: SalesOfferDocumentInterface) => {
                setDocument(item);
                setVisible(true);
            },
            close: () => {
                setDocument(null);
                setVisible(false);
            },
        }),
        [setVisible]
    );

    return useMemo(
        () => ({
            ...actions,
            render: () => <ShareModal document={document} onClose={actions.close} onSend={onSend} visible={visible} />,
        }),
        [actions, visible, onSend, document]
    );
};

export type UseShareModalReturn = ReturnType<typeof useShareModal>;
export default useShareModal;

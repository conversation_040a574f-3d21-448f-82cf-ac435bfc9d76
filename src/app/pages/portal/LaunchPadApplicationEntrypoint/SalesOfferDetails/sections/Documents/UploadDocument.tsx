import { ApolloError } from '@apollo/client';
import { PButtonPure } from '@porsche-design-system/components-react';
import { Upload, UploadProps } from 'antd';
import { pull } from 'lodash/fp';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { validateExtensions } from '../../../../../../../server/utils/extensions';
import { useUploadSalesOfferDocumentMutation } from '../../../../../../api';
import ThemeUpload from '../../../../../../components/ThemeUpload';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { allowedExtensions } from '../../../../../../utilities/extensions';
import { extractSalesOfferFromLaunchpadLead } from '../shared';

const ALLOWED_EXTENSIONS = [...pull('.svg', allowedExtensions.image), allowedExtensions.pdf];
const sizeLimitInMiB = 2;
const UploadDocument = () => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const { notification } = useThemeComponents();
    const { id } = extractSalesOfferFromLaunchpadLead();

    const [mutation] = useUploadSalesOfferDocumentMutation();
    const onUpload = useCallback(
        async inputFile => {
            try {
                if (inputFile instanceof File) {
                    notification.loading({
                        content: t('launchpadSalesOfferDetails:messages.uploadDocument'),
                        duration: 0,
                        key: 'primary',
                    });

                    const { errors } = await mutation({
                        variables: {
                            salesOfferId: id,
                            file: inputFile,
                        },
                    });

                    notification.destroy('primary');

                    if (!errors) {
                        notification.success({
                            content: t('launchpadSalesOfferDetails:messages.uploadDocumentSuccessful', {
                                filename: inputFile.name,
                            }),
                            key: 'primary',
                        });
                    }
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            }
        },
        [id, mutation, notification, t]
    );

    const handleOnChange = useCallback(
        ({ file }) => {
            if (file instanceof File) {
                onUpload(file);
            }
        },
        [onUpload]
    );

    const beforeUpload = useCallback<NonNullable<UploadProps['beforeUpload']>>(
        file => {
            if (file.size > sizeLimitInMiB * 1024 * 1024) {
                notification.error({
                    content: t('common:upload.messages.sizeExceedsLimit', { limit: sizeLimitInMiB }),
                    key: 'secondary',
                });

                return Upload.LIST_IGNORE;
            }

            const reader = new FileReader();
            const promise = new Promise<boolean | string>((resolve, reject) => {
                reader.onload = async event => {
                    const content = event.target.result as string;
                    const validationResult = await validateExtensions(content, file.name);
                    if (validationResult.valid) {
                        return resolve(false);
                    }

                    notification.error({
                        content: t('common:upload.messages.fileError'),
                        key: 'secondary',
                    });

                    return resolve(Upload.LIST_IGNORE);
                };

                reader.onerror = event => resolve(Upload.LIST_IGNORE);
            });

            reader.readAsBinaryString(file);

            return Promise.resolve(promise);
        },
        [t, notification]
    );

    const props: UploadProps = useMemo(
        () => ({
            accept: ALLOWED_EXTENSIONS.join(','),
            showUploadList: false,
            onChange: handleOnChange,
            beforeUpload,
        }),
        [beforeUpload, handleOnChange]
    );

    return (
        <ThemeUpload {...props}>
            <PButtonPure icon="plus">{t('launchpadSalesOfferDetails:sections.documents.uploadFile')}</PButtonPure>
        </ThemeUpload>
    );
};

export default UploadDocument;

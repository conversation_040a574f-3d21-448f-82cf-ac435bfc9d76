import { ApolloError } from '@apollo/client';
import {
    themeLightNotificationSuccessSoft,
    themeLightNotificationWarningSoft,
    themeLightPrimary,
} from '@porsche-design-system/components-react/styles';
import { UploadFile } from 'antd';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import { TFunction } from 'i18next';
import { omit } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
    useDownloadSalesOfferDocumentMutation,
    useShareSalesOfferDocumentMutation,
} from '../../../../../../api/mutations';
import {
    ApplicationStatus,
    SalesOfferDocumentKind,
    SalesOfferDocumentStatus,
    SalesOfferDocument,
} from '../../../../../../api/types';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { usePreview } from '../../../../../shared/ApplicationDetailsPage/standard/DocumentTab/Preview';
import { extractSalesOfferFromLaunchpadLead } from '../shared';
import { UseDeleteConfirmModalReturn } from './DeleteConfirmModal';
import { UseShareModalReturn } from './ShareModal';
import type { ShareSendParams, SalesOfferDocumentInterface } from './typings';

export const getStatusLabel = (status: SalesOfferDocumentStatus | ApplicationStatus, t: TFunction) => {
    switch (status) {
        case SalesOfferDocumentStatus.PendingManager:
        case SalesOfferDocumentStatus.PendingCustomer:
        case SalesOfferDocumentStatus.PaymentCompleted:
        case SalesOfferDocumentStatus.Signed:
        case SalesOfferDocumentStatus.Expired:
            return t(`launchpadSalesOfferDetails:sections.summaryDetails.status.${status}`);

        default:
            return '';
    }
};

export const getStatusStyle = (status: SalesOfferDocumentStatus | ApplicationStatus) => {
    switch (status) {
        case SalesOfferDocumentStatus.PaymentCompleted:
        case SalesOfferDocumentStatus.Signed:
            return {
                backgroundColor: themeLightNotificationSuccessSoft,
                color: themeLightPrimary,
                borderColor: themeLightNotificationSuccessSoft,
            };
        case SalesOfferDocumentStatus.PendingManager:
        case SalesOfferDocumentStatus.PendingCustomer:
            return {
                backgroundColor: themeLightNotificationWarningSoft,
                color: themeLightPrimary,
                borderColor: themeLightNotificationWarningSoft,
            };

        case SalesOfferDocumentStatus.Expired:
            return {
                backgroundColor: '#FFE2E4',
                color: themeLightPrimary,
                borderColor: '#FFE2E4',
            };

        default:
            return {};
    }
};

export const getDocumentDisplayName = (document: SalesOfferDocument, t: TFunction) => {
    switch (document.kind) {
        case SalesOfferDocumentKind.MainDetails:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.coe');

        case SalesOfferDocumentKind.Vehicle:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.vehicle');

        case SalesOfferDocumentKind.Deposit:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.deposit');

        case SalesOfferDocumentKind.Finance:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.finance');

        case SalesOfferDocumentKind.Insurance:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.insurance');

        case SalesOfferDocumentKind.Vsa:
            return t('launchpadSalesOfferDetails:sections.documents.salesOfferDisplayName', {
                date: dayjs(document.createdAt).format('D MMM YYYY'),
            });

        case SalesOfferDocumentKind.Others:
            return document.filename;

        default:
            return '';
    }
};

const sortByCreatedAt = (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix();

export const useSystemDocuments = () => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const { mainDetails, vehicle, deposit, insurance, finance, vsa } = extractSalesOfferFromLaunchpadLead();

    return useMemo(() => {
        const documents = [
            ...[...vsa.documents].sort(sortByCreatedAt),
            ...mainDetails.documents,
            ...vehicle.documents,
            ...deposit.documents,
            ...finance.documents,
            ...insurance.documents,
        ] as SalesOfferDocument[];

        return documents
            .filter(i => i.kind !== SalesOfferDocumentKind.Others)
            .map(i => ({
                ...i,
                displayName: getDocumentDisplayName(i, t),
            }));
    }, [
        deposit.documents,
        finance.documents,
        insurance.documents,
        mainDetails.documents,
        t,
        vehicle.documents,
        vsa.documents,
    ]);
};

export const useOtherDocuments = () => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const { otherDocuments } = extractSalesOfferFromLaunchpadLead();

    return useMemo(
        () =>
            (otherDocuments as SalesOfferDocument[])
                .map(i => ({
                    ...i,
                    displayName: getDocumentDisplayName(i, t),
                }))
                .sort(sortByCreatedAt),
        [otherDocuments, t]
    );
};

export const useActions = (
    salesOfferId,
    shareModal: UseShareModalReturn,
    deleteConfirmModal: UseDeleteConfirmModalReturn
) => {
    const { notification } = useThemeComponents();
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);

    const [download] = useDownloadSalesOfferDocumentMutation();
    const onDownload = useCallback(
        async (item: SalesOfferDocumentInterface) => {
            try {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.downloadDocument'),
                    duration: 0,
                    key: 'primary',
                });

                const { data } = await download({
                    variables: {
                        salesOfferId,
                        fileId: item.id,
                        kind: item.kind,
                    },
                });

                if (data.downloadSalesOfferDocument) {
                    saveAs(data.downloadSalesOfferDocument);

                    notification.destroy('primary');
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.downloadDocumentSuccessful', {
                            filename: item.filename,
                        }),
                        key: 'primary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            }
        },
        [download, notification, salesOfferId, t]
    );

    const onShare = useCallback(
        (item: SalesOfferDocumentInterface) => {
            shareModal.open(item);
        },
        [shareModal]
    );

    const onDelete = useCallback(
        (item: SalesOfferDocumentInterface) => {
            deleteConfirmModal.open(item);
        },
        [deleteConfirmModal]
    );

    const { preview, onPreview, onCancelPreview } = usePreview();
    const onDocumentPreview = useCallback(
        (item: SalesOfferDocumentInterface) => {
            onPreview({ name: item.filename, url: item.url } as UploadFile);
        },
        [onPreview]
    );

    return { onDownload, onShare, onDelete, onDocumentPreview, preview, onCancelPreview };
};

export const useSendShare = (salesOfferId: string) => {
    const { notification } = useThemeComponents();
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);

    const [mutation] = useShareSalesOfferDocumentMutation();

    return useCallback(
        async (values: ShareSendParams) => {
            try {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.shareDocument'),
                    duration: 0,
                    key: 'primary',
                });

                const { data } = await mutation({
                    variables: {
                        params: {
                            salesOfferId,
                            fileId: values.document.id,
                            kind: values.document.kind,
                            ...omit('document', values),
                        },
                    },
                });

                if (data.shareSalesOfferDocument) {
                    notification.destroy('primary');
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.shareDocumentSuccessful', {
                            filename: values.document.filename,
                        }),
                        key: 'primary',
                    });
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            }
        },
        [mutation, notification, salesOfferId, t]
    );
};

import { Col } from 'antd';
import { useTranslation } from 'react-i18next';
import { CollapsibleContentItem } from '../../../ui/ContentItem';
import DocumentRow, { DocumentRowActionProps } from './DocumentRow';
import { useSystemDocuments } from './shared';

const SystemDocuments = ({ onDownload, onShare, onPreview }: DocumentRowActionProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails']);
    const systemDocuments = useSystemDocuments();

    return (
        <CollapsibleContentItem
            title={t('launchpadSalesOfferDetails:sections.documents.systemDocumentsTitle')}
            defaultOpen
        >
            <Col span={24}>
                {systemDocuments.map((item, index) => (
                    <DocumentRow
                        key={item.id}
                        index={index}
                        item={item}
                        onDownload={onDownload}
                        onPreview={onPreview}
                        onShare={onShare}
                        type="system"
                    />
                ))}
            </Col>
        </CollapsibleContentItem>
    );
};

export default SystemDocuments;

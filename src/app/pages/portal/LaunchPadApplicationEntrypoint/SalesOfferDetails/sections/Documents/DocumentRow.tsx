import { PIcon, PText, PButtonPure } from '@porsche-design-system/components-react';
import { themeLightContrastLow } from '@porsche-design-system/components-react/styles';
import { Tag } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import usePublic from '../../../../../../utilities/usePublic';
import { getStatusLabel, getStatusStyle } from './shared';
import type { SalesOfferDocumentInterface } from './typings';

const SummaryRow = styled.div<{ isFirst?: boolean }>`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid ${themeLightContrastLow};
    padding: 13px 0;
    ${({ isFirst }) => (isFirst ? `border-top: 1px solid ${themeLightContrastLow};` : null)}
`;

const FirstColumn = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1 1 0;
    gap: 8px;
    @media (min-width: 620px) {
        flex-direction: row;
        align-items: center;
    }
`;

const FirstSubColumn = styled.div<{ isStatus?: boolean }>`
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1 1 0;
    ${({ isStatus }) => (isStatus ? `padding-left: 34px;` : null)}
`;

const LastColumn = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 88px;
`;

export type DocumentRowActionProps = {
    onDownload: (item: SalesOfferDocumentInterface) => void;
    onShare: (item: SalesOfferDocumentInterface) => void;
    onDelete?: (item: SalesOfferDocumentInterface) => void;
    onPreview: (item: SalesOfferDocumentInterface) => void;
};

type DocumentRowProps = {
    type: 'system' | 'other';
    item: SalesOfferDocumentInterface;
    index: number;
} & DocumentRowActionProps;

const DocumentRow = ({ type, item, index, onDownload, onShare, onDelete, onPreview }: DocumentRowProps) => {
    const { t } = useTranslation(['common', 'launchpadSalesOfferDetails', 'applicationList']);
    const fileIcon = usePublic('file.svg');

    return (
        <SummaryRow isFirst={index === 0}>
            <FirstColumn>
                <FirstSubColumn>
                    <PIcon size="xx-small" source={fileIcon} style={{ margin: '0 7px' }} />
                    <PButtonPure icon="none" onClick={() => onPreview(item)} style={{ wordBreak: 'break-all' }}>
                        {item.displayName}
                    </PButtonPure>
                </FirstSubColumn>
                {type === 'system' && item.status && (
                    <FirstSubColumn isStatus>
                        <Tag
                            style={{
                                ...getStatusStyle(item.status),
                                padding: '4px 8px',
                                borderRadius: '4px',
                                margin: '0px',
                            }}
                        >
                            <PText size="x-small">
                                {getStatusLabel(item.status, t)} ({dayjs(item.lastUpdatedAt).format('D MMM YYYY')})
                            </PText>
                        </Tag>
                    </FirstSubColumn>
                )}
            </FirstColumn>
            <LastColumn>
                {type === 'other' && onDelete && (
                    <PButtonPure
                        iconSource="delete"
                        onClick={() => onDelete(item)}
                        style={{ margin: '0 10px' }}
                        hideLabel
                    />
                )}
                <PButtonPure
                    iconSource="download"
                    onClick={() => onDownload(item)}
                    style={{ margin: '0 10px' }}
                    hideLabel
                />
                <PButtonPure iconSource="share" onClick={() => onShare(item)} style={{ margin: '0 10px' }} hideLabel />
            </LastColumn>
        </SummaryRow>
    );
};

export default DocumentRow;

import { ApolloError } from '@apollo/client';
import { PIcon } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { saveAs } from 'file-saver';
import { useFormikContext } from 'formik';
import { isEmpty, isNil } from 'lodash/fp';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {
    useDownloadSpecificationDocumentMutation,
    useUploadVehicleSalesOfferSpecificationDocumentMutation,
} from '../../../../../../api';
import SingleUploadField from '../../../../../../components/fields/SingleUploadField';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { allowedExtensions } from '../../../../../../utilities/extensions';
import { useVsaSigned } from '../shared';
import { VehicleValues } from './typings';

const StyledContainer = styled.div`
    margin-top: 26px;
`;

type SpecificationUploadProps = {
    salesOfferId: string;
};
const SpecificationUpload = ({ salesOfferId }: SpecificationUploadProps) => {
    const { t } = useTranslation(['launchpadSalesOfferDetails']);
    const {
        FormFields: { DisplayField },
        notification,
    } = useThemeComponents();

    const isVSASigned = useVsaSigned();

    const [mutation] = useUploadVehicleSalesOfferSpecificationDocumentMutation();
    const [documentMutation] = useDownloadSpecificationDocumentMutation();

    const { values } = useFormikContext<VehicleValues>();

    const fileName = useMemo(() => {
        if (values.specificationDocument instanceof File) {
            return values.specificationDocument?.name;
        }

        return values.specificationDocument?.filename || '';
    }, [values]);

    const downloadFile = useCallback(async () => {
        const { data } = await documentMutation({
            variables: {
                id: salesOfferId,
            },
        });

        saveAs(data.downloadSpecificationDocument);
    }, [documentMutation, salesOfferId]);

    const onSubmit = useCallback(async () => {
        try {
            const { specificationDocument } = values;
            if (specificationDocument instanceof File) {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.uploadSpecificationDocument'),
                    duration: 0,
                    key: 'primary',
                });

                const { errors } = await mutation({
                    variables: {
                        id: salesOfferId,
                        file: specificationDocument,
                    },
                });

                notification.destroy('primary');

                if (!errors) {
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.uploadSpecificationDocumentSuccessful'),
                        key: 'primary',
                    });
                }
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error(error.graphQLErrors[0].message);
            } else {
                console.error(error);
            }
        }
    }, [mutation, notification, salesOfferId, t, values]);

    return (
        <Row gutter={[16, 16]}>
            <Col span={18}>
                <DisplayField
                    actionIcon={
                        !isNil(values.specificationDocument) &&
                        !isEmpty(values.specificationDocument) && (
                            <PIcon
                                aria={{ 'aria-label': 'Download icon' }}
                                name="download"
                                onClick={downloadFile}
                                style={{ position: 'absolute', right: '15px', top: '40px', cursor: 'pointer' }}
                            />
                        )
                    }
                    label={t(
                        // eslint-disable-next-line max-len
                        'launchpadSalesOfferDetails:sections.vehicle.fields.specificationDocument.label'
                    )}
                    value={fileName}
                />
            </Col>
            <Col span={6}>
                <StyledContainer>
                    <SingleUploadField
                        disabled={isVSASigned}
                        extensions={[allowedExtensions.pdf]}
                        iconRender={null}
                        listType="picture"
                        name="specificationDocument"
                        onChange={onSubmit}
                        showUploadList={false}
                        alwaysShowUploadButton
                    />
                </StyledContainer>
            </Col>
        </Row>
    );
};

export default SpecificationUpload;

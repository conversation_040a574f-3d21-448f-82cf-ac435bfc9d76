import type { CurrentUserDataFragment } from '../../../api';

export type FormValues = { email: string; password: string; remember?: boolean };
export type AuthenticateStepProps = { actions: ActionHandlers };
export type ActionHandlers = {
    completeAuthentication: (token: string, user: CurrentUserDataFragment) => Promise<void>;
    moveToTOTP: (token: string) => void;
    moveToSmsOTP: (token: string, values: FormValues) => void;
    moveToPasswordExpired: (token: string) => void;
    goBackToAuthenticate: () => void;
};

import { ApplicationAgreementDataFragment } from '../../../../api/fragments/ApplicationAgreementData';
import { ConsentsAndDeclarationsType } from '../../../../api/types';

export type GroupedAgreement = {
    id: string;
    type: ConsentsAndDeclarationsType.Group;
    group: ApplicationAgreementDataFragment; // The group-type agreement
    children: ApplicationAgreementDataFragment[]; // The checkbox agreements with matching parentId
};

export type AgreementItem = ApplicationAgreementDataFragment | GroupedAgreement;

/**
 * Groups agreements by parentId relationships.
 *
 * This function takes a flat array of agreements and groups them according to their parentId:
 * - Group-type agreements become group headers
 * - Checkbox agreements with parentId are grouped under their parent
 * - Standalone agreements (no parentId) remain as individual items
 *
 * @param agreements - Flat array of agreements from the backend
 * @returns Array of grouped and standalone agreements
 */
export const groupAgreements = (agreements: ApplicationAgreementDataFragment[]): AgreementItem[] => {
    const groups = agreements.filter(agreement => agreement.type === ConsentsAndDeclarationsType.Group);
    const checkboxes = agreements.filter(agreement => agreement.type === ConsentsAndDeclarationsType.Checkbox);
    const others = agreements.filter(
        agreement =>
            agreement.type !== ConsentsAndDeclarationsType.Group &&
            agreement.type !== ConsentsAndDeclarationsType.Checkbox
    );

    const checkboxesByParent = checkboxes.reduce(
        (acc, checkbox) => {
            const parentId = checkbox.__typename === 'CheckboxApplicationAgreement' ? checkbox.parentId : null;
            const key = parentId || 'standalone';

            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(checkbox);

            return acc;
        },
        {} as Record<string, ApplicationAgreementDataFragment[]>
    );

    const result: AgreementItem[] = [
        ...groups.map(group => {
            const children = checkboxesByParent[group.id]?.sort((a, b) => a.orderNumber - b.orderNumber) || [];

            return children.length > 0
                ? ({
                      id: group.id,
                      type: ConsentsAndDeclarationsType.Group,
                      group,
                      children,
                  } as GroupedAgreement)
                : group;
        }),
        ...(checkboxesByParent.standalone || []),
        ...others,
    ];

    // Sort the final result by orderNumber
    return result.sort((a, b) => {
        const aOrder = isGroupedAgreement(a) ? a.group.orderNumber : a.orderNumber;
        const bOrder = isGroupedAgreement(b) ? b.group.orderNumber : b.orderNumber;

        return aOrder - bOrder;
    });
};

/**
 * Checks if an agreement item is a grouped agreement
 */
export const isGroupedAgreement = (item: AgreementItem): item is GroupedAgreement =>
    'type' in item && item.type === ConsentsAndDeclarationsType.Group;

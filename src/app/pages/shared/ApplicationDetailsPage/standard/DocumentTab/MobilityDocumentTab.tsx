import { Col, Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ApplicationDocumentKind, ApplicationStage, LocalCustomerFieldKey } from '../../../../../api/types';
import { useApplicationDetailsExtraContext } from '../../../../../components/contexts/ApplicationDetailsExtraContext';
import ApplicationDetailsPanel from '../Panel';
import DocumentUploadField, { DocumentKind } from './DocumentUploadField';
import { DOCUMENT_TAB_ALLOWED_EXTENSIONS, DocumentTabProps, isNotTemporaryDocument } from './shared';
import { useGetApplicationDocuments } from './useGetApplicationDocuments';

const colSpan = { lg: 24, md: 24, xs: 24 };

const MobilityDocumentTab = ({ application }: DocumentTabProps) => {
    const { t } = useTranslation('applicationDetails');
    const { forCI } = useApplicationDetailsExtraContext();

    const {
        customerDLCopy,
        customerEidPassportCopy,
        otherAttachmentDocuments,
        agreementDocuments,
        kycIdentityUpload,
        kycLicenseUpload,
    } = useGetApplicationDocuments(application.documents?.filter(isNotTemporaryDocument));

    const [showUaeIdentity, showUaeDrivingLicense, showInternationalIdentity, showInternationalDrivingLicense] =
        useMemo(() => {
            const checkKyc = (keys: LocalCustomerFieldKey[]) =>
                application.__typename === 'MobilityApplication' &&
                (application.applicantKYC.some(kyc => keys.includes(kyc.key)) ||
                    application.corporateKYC.some(kyc => keys.includes(kyc.key)));

            return [
                checkKyc([LocalCustomerFieldKey.UaeDrivingLicense]),
                checkKyc([LocalCustomerFieldKey.UaeDrivingLicense]),
                checkKyc([LocalCustomerFieldKey.UploadIdentity]),
                checkKyc([LocalCustomerFieldKey.UploadDrivingLicense]),
            ];
        }, [application.__typename, application.applicantKYC, application.corporateKYC]);

    return (
        <Space direction="vertical" size={forCI ? 48 : 16} style={{ display: 'flex' }}>
            {showUaeDrivingLicense && (
                <ApplicationDetailsPanel
                    forCI={forCI}
                    header={t('applicationDetails:panels.document.customerDrivinglicenseCopy.header')}
                    name={ApplicationDocumentKind.CustomerDlCopy}
                    defaultExpanded
                >
                    <Col {...colSpan}>
                        <DocumentUploadField
                            application={application}
                            applicationId={application.id}
                            currentStage={ApplicationStage.Mobility}
                            documentKind={DocumentKind.Application}
                            documents={customerDLCopy}
                            extensions={DOCUMENT_TAB_ALLOWED_EXTENSIONS}
                            kind={ApplicationDocumentKind.CustomerDlCopy}
                        />
                    </Col>
                </ApplicationDetailsPanel>
            )}

            {showUaeIdentity && (
                <ApplicationDetailsPanel
                    forCI={forCI}
                    header={t('applicationDetails:panels.document.customerEidPassportCopy.header')}
                    name={ApplicationDocumentKind.CustomerEidPassportCopy}
                    defaultExpanded
                >
                    <Col {...colSpan}>
                        <DocumentUploadField
                            application={application}
                            applicationId={application.id}
                            currentStage={ApplicationStage.Mobility}
                            documentKind={DocumentKind.Application}
                            documents={customerEidPassportCopy}
                            extensions={DOCUMENT_TAB_ALLOWED_EXTENSIONS}
                            kind={ApplicationDocumentKind.CustomerEidPassportCopy}
                        />
                    </Col>
                </ApplicationDetailsPanel>
            )}

            {showInternationalIdentity && (
                <ApplicationDetailsPanel
                    forCI={forCI}
                    header={t('applicationDetails:panels.document.identityUpload.header')}
                    name={ApplicationDocumentKind.KycIdentityUpload}
                    defaultExpanded
                >
                    <Col {...colSpan}>
                        <DocumentUploadField
                            application={application}
                            applicationId={application.id}
                            currentStage={ApplicationStage.Mobility}
                            documentKind={DocumentKind.Application}
                            documents={kycIdentityUpload}
                            extensions={DOCUMENT_TAB_ALLOWED_EXTENSIONS}
                            kind={ApplicationDocumentKind.KycIdentityUpload}
                        />
                    </Col>
                </ApplicationDetailsPanel>
            )}

            {showInternationalDrivingLicense && (
                <ApplicationDetailsPanel
                    forCI={forCI}
                    header={t('applicationDetails:panels.document.licenseUpload.header')}
                    name={ApplicationDocumentKind.KycLicenseUpload}
                    defaultExpanded
                >
                    <Col {...colSpan}>
                        <DocumentUploadField
                            application={application}
                            applicationId={application.id}
                            currentStage={ApplicationStage.Mobility}
                            documentKind={DocumentKind.Application}
                            documents={kycLicenseUpload}
                            extensions={DOCUMENT_TAB_ALLOWED_EXTENSIONS}
                            kind={ApplicationDocumentKind.KycLicenseUpload}
                        />
                    </Col>
                </ApplicationDetailsPanel>
            )}

            <ApplicationDetailsPanel
                forCI={forCI}
                header={t('applicationDetails:panels.document.otherAttachment.header')}
                name={ApplicationDocumentKind.OtherAttachment}
                defaultExpanded
            >
                <Col {...colSpan}>
                    <DocumentUploadField
                        application={application}
                        applicationId={application.id}
                        currentStage={ApplicationStage.Mobility}
                        documentKind={DocumentKind.Application}
                        documents={[...agreementDocuments, ...otherAttachmentDocuments]}
                        extensions={DOCUMENT_TAB_ALLOWED_EXTENSIONS}
                        kind={ApplicationDocumentKind.OtherAttachment}
                    />
                </Col>
            </ApplicationDetailsPanel>
        </Space>
    );
};

export default MobilityDocumentTab;

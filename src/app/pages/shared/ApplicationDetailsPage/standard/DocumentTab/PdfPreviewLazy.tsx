/* eslint-disable no-alert */
import { Divider, Modal } from 'antd';
import { PasswordResponses } from 'pdfjs-dist';
import workerSrc from 'pdfjs-dist/legacy/build/pdf.worker.entry';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Document, DocumentProps, Page, pdfjs } from 'react-pdf';
import { useMeasure } from 'react-use';
import styled from 'styled-components';
import breakpoints from '../../../../../utilities/breakpoints';
import useDisableContextMenu from '../../../../../utilities/useDisableContextMenu';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure worker using legacy entry for compatibility; loaded only when this lazy chunk is requested
pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;

const PdfModal = styled(Modal)`
    @media (min-width: ${breakpoints.lg}) {
        width: 60% !important;
    }
    @media (max-width: ${breakpoints.md}) {
        max-width: 100vw;
        margin: 0 auto;

        & .react-pdf__Document {
            border-top: 24px solid #fff;
        }
    }

    &.loaded .ant-modal-content {
        background-color: transparent;
        box-shadow: none;

        & > .ant-modal-body {
            padding: 0;
        }
    }
`;

type Props = {
    onCancelPreview: () => unknown;
    url: string;
};

const PdfPreviewLazy = ({ onCancelPreview, url }: Props) => {
    const [numPages, setNumPages] = useState<number | undefined>();
    const [loaded, setLoaded] = useState<boolean>(false);
    const [ref, { width }] = useMeasure();

    const onPdfPreviewLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
        setLoaded(true);
        setNumPages(numPages);
    }, []);

    const { t } = useTranslation('applicationDetails');
    const onPassword = useCallback<DocumentProps['onPassword']>(
        (callback, reason) => {
            const response = reason as unknown as number;

            switch (response) {
                case PasswordResponses.INCORRECT_PASSWORD:
                case PasswordResponses.NEED_PASSWORD: {
                    const password = prompt(
                        t(
                            `applicationDetails:messages.${
                                response === PasswordResponses.NEED_PASSWORD ? 'needPassword' : 'incorrectPassword'
                            }`
                        )
                    );

                    if (password === null) {
                        onCancelPreview();
                    }

                    callback(password);
                    break;
                }

                default:
                    break;
            }
        },
        [onCancelPreview, t]
    );

    useDisableContextMenu();

    return (
        <PdfModal className={loaded ? 'loaded' : undefined} footer={null} onCancel={onCancelPreview} centered open>
            <div ref={ref}>
                <Document file={url} onLoadSuccess={onPdfPreviewLoadSuccess} onPassword={onPassword}>
                    {Array.from(new Array(numPages), (_, index) => (
                        <>
                            <Page key={`page_${index + 1}`} pageNumber={index + 1} width={width} />
                            {index + 1 !== numPages && <Divider />}
                        </>
                    ))}
                </Document>
            </div>
        </PdfModal>
    );
};

export default PdfPreviewLazy;

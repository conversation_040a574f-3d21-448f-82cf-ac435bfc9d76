import type { TFunction } from 'i18next';
import type { LeadDataFragment } from '../../../api/fragments/LeadData';
import type { LeadListDataFragment } from '../../../api/fragments/LeadListData';
import { LeadStatus } from '../../../api/types';
import renderMarkdown from '../../../utilities/renderMarkdown';

const isLeadDataFragment = (lead: LeadDataFragment | LeadListDataFragment): lead is LeadDataFragment =>
    // Check for properties that exist only in LeadDataFragment
    'mergedToLeadSuiteId' in lead && 'mergedToLeadIdentifier' in lead;

const getTranslatedLeadStatus = (lead: LeadDataFragment | LeadListDataFragment, t: TFunction) => {
    if (!lead?.status) {
        return '';
    }

    const translatedStatus =
        lead.status === LeadStatus.Merged
            ? t('leadListPage:status.MergedInto')
            : t(`leadListPage:status.${lead.status}`);

    if (lead.status === LeadStatus.Merged && isLeadDataFragment(lead)) {
        const linkElement = `<a style="color: inherit; text-decoration: underline;" 
                href="${lead.mergedToLeadSuiteId}">${lead.mergedToLeadIdentifier}</a>`;

        return renderMarkdown(`${translatedStatus} ${linkElement}`, false, false);
    }

    return translatedStatus;
};

export default getTranslatedLeadStatus;

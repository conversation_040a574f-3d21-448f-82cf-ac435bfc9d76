import { PText } from '@porsche-design-system/components-react';
import { Col, Space } from 'antd';
import dayjs from 'dayjs';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { type DealerJourneyDataFragment } from '../../../../api/fragments/DealerJourneyData';
import { type Period } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import breakpoints from '../../../../utilities/breakpoints';
import renderMarkdown from '../../../../utilities/renderMarkdown';
import useCompanyFormats from '../../../../utilities/useCompanyFormats';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import Media from '../../../portal/ConfiguratorApplicationEntrypoint/ModelConfiguratorDetailsPage/components/Media';
import calculateTotalPrice from '../../../portal/ConfiguratorApplicationEntrypoint/helper';
import FinderVehicleInfo from '../../../portal/FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInfo';
import VehicleInterest from '../../../portal/FinderApplicationPublicAccessEntrypoint/ApplicantKYC/VehicleInterest';
import LocationInfo from '../../../portal/MobilityApplicationEntrypoint/Components/LocationInfo';
import OrderSummary from '../../../portal/MobilityApplicationEntrypoint/Components/OrderSummary';
import { type Applications } from './types';

const RequiredDetailsContainer = styled.div`
    border-radius: 12px;
    background-color: #fff;
`;

const VehicleInfoContainer = styled.div`
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    align-items: center;
    gap: 16px;
`;

const VehicleInfoDetails = styled.div`
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: column;
`;

const MediaContainer = styled.div`
    & .ant-image {
        width: 180px;
        margin: auto;
        & > img {
            border-radius: var(--card-border-radius, initial);
            aspect-ratio: 16/9;
            object-fit: cover;
        }
    }

    @media (max-width: ${breakpoints.sm}) {
        & .ant-image {
            width: auto;
            height: 147px;
        }
    }
`;

const VehicleDetailsCard = styled.div`
    padding: 24px;
`;

const DealerInfoContainer = styled.div`
    padding: 0px 24px 24px;
`;

const StyledSpace = styled(Space)`
    width: 100%;
`;

const VehicleInfo = ({
    variant,
    totalPrice,
    filename,
    source,
    period,
}: {
    variant: string;
    totalPrice: number;
    filename: string;
    source: string;
    period?: Period;
}) => {
    const { t } = useTranslation('common');

    const formats = useCompanyFormats();

    const periodInfo = useMemo(() => {
        if (period) {
            const start = dayjs(period.start);
            const end = dayjs(period.end);

            const from = start.format(t('common:formats.dateTimeFormat'));
            const to = end.format(t('common:formats.dateTimeFormat'));

            return t('common:period', { from, to });
        }

        return null;
    }, [period, t]);

    return (
        <VehicleInfoContainer>
            <MediaContainer>
                <Media fileName={filename} source={source} />
            </MediaContainer>
            <VehicleInfoDetails>
                <PText size="medium" weight="bold">
                    {variant}
                </PText>
                {!isNil(totalPrice) && <PText>{formats.formatAmountWithCurrency(totalPrice)}</PText>}
                {period && <PText>{periodInfo}</PText>}
            </VehicleInfoDetails>
        </VehicleInfoContainer>
    );
};

const VehicleDetails = ({ application }: { application: Applications }) => {
    const translatedString = useTranslatedString();
    const { t } = useTranslation(['paymentDetails', 'finderJourney']);
    const { layout } = useRouter();

    const vehicleData = useMemo(() => {
        if (
            application.__typename === 'ConfiguratorApplication' ||
            application.__typename === 'EventApplication' ||
            application.__typename === 'StandardApplication'
        ) {
            switch (application.vehicle?.__typename) {
                case 'LocalVariant':
                    return {
                        make: translatedString(
                            application.vehicle.model.parentModel
                                ? application.vehicle.model.parentModel.make.name
                                : application.vehicle.model.make.name
                        ),
                        variant: translatedString(application.vehicle.name),
                        totalPrice:
                            application.__typename === 'ConfiguratorApplication'
                                ? calculateTotalPrice(application)
                                : null,
                        filename:
                            application.__typename === 'ConfiguratorApplication'
                                ? application.vehicleImage?.filename
                                : application.vehicle?.images?.[0]?.filename,
                        source:
                            application.__typename === 'ConfiguratorApplication'
                                ? application.vehicleImage?.url
                                : application.vehicle?.images?.[0]?.url,
                    };

                default:
                    throw new Error('not implemented');
            }
        }

        return null;
    }, [application, translatedString]);

    if (
        !vehicleData &&
        application.__typename !== 'MobilityApplication' &&
        application.__typename !== 'FinderApplication'
    ) {
        return null;
    }

    if (application.__typename === 'MobilityApplication') {
        return (
            <Col xs={24}>
                <PText size="medium" weight="bold">
                    {t('paymentDetails:titles.orderSummary')}
                </PText>
                <OrderSummary application={application} />
                <LocationInfo application={application} />
            </Col>
        );
    }

    if (application.__typename === 'FinderApplication') {
        if (layout?.__typename === 'PorscheV3Layout') {
            return <VehicleInterest application={application} />;
        }

        return (
            <Col xs={24}>
                <PText size="medium" weight="bold">
                    {t('finderJourney:payment.title')}
                </PText>
                <FinderVehicleInfo application={application} />
            </Col>
        );
    }

    return (
        <VehicleDetailsCard>
            <PText size="medium" weight="bold">
                {t('paymentDetails:titles.selectedVehicle')}
            </PText>
            <VehicleInfo {...vehicleData} />
        </VehicleDetailsCard>
    );
};

const DealerInfo = ({ dealer }: { dealer: DealerJourneyDataFragment }) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();

    return (
        <DealerInfoContainer>
            <StyledSpace direction="vertical" size={4}>
                <PText size="medium" weight="bold">
                    {translatedString(dealer.legalName)}
                </PText>
                {(dealer.contact.telephone || dealer.contact.email || dealer.contact.address) && (
                    <Space direction="vertical" size={4}>
                        {dealer.contact.address?.defaultValue && (
                            <PText>{translatedString(dealer.contact.address)}</PText>
                        )}

                        {dealer.contact.telephone && (
                            <PText>
                                {t('configuratorJourney:thankyou.contents.dealerInfo.phone', dealer.contact.telephone)}
                            </PText>
                        )}

                        {dealer.contact.email && (
                            <PText>
                                {t('configuratorJourney:thankyou.contents.dealerInfo.email', {
                                    email: dealer.contact.email,
                                })}
                            </PText>
                        )}
                    </Space>
                )}
                {dealer.contact.additionalInfo?.defaultValue && (
                    <PText>{renderMarkdown(translatedString(dealer.contact.additionalInfo))}</PText>
                )}
            </StyledSpace>
        </DealerInfoContainer>
    );
};

const RequiredDetailsStage = ({ application }: { application: Applications }) => (
    <RequiredDetailsContainer>
        <VehicleDetails application={application} />
        <DealerInfo dealer={application.dealer} />
    </RequiredDetailsContainer>
);

export default RequiredDetailsStage;

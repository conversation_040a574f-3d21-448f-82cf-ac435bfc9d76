import dayjs, { Dayjs } from 'dayjs';
import type {
    EventInput,
    CustomTestDriveBookingSlotsInput,
    TestDriveFixedPeriodInput,
    AppointmentTimeSlotInput,
    TestDriveBookingWindowSettingsInput,
} from '../../../../api/types';
import type { BannerFormikValue } from '../../BannerDetailsPage/shared';
import type { AppointmentEmailFormValues } from '../../ModuleDetailsPage/AppointmentModulePage/shared';
import type { EventApplicationModuleConfirmEmailContents } from '../../ModuleDetailsPage/EventApplicationModulePage/type';
import type { EventKycFormInput } from '../EventKycFields/types';

export type EventEmailFormValues = {
    submitOrder: EventApplicationModuleConfirmEmailContents;
    testDrive: { customer: AppointmentEmailFormValues['customer'] };
};

export type TestDriveFixedPeriodForm = Omit<TestDriveFixedPeriodInput, 'bookingTimeSlot'> & {
    bookingTimeSlot: Array<
        Omit<AppointmentTimeSlotInput, 'slot'> & {
            slot: dayjs.Dayjs;
        }
    >;
};

export type TestDriveFixedPeriodFormValidation = Omit<TestDriveFixedPeriodForm, 'startDate' | 'endDate'> & {
    dateRange?: {
        start: string | Date;
        end: string | Date;
    };
};

export type BookingWindowSettingsForm = Omit<TestDriveBookingWindowSettingsInput, 'bookingTimeSlot'> & {
    bookingTimeSlot: Array<
        Omit<AppointmentTimeSlotInput, 'slot'> & {
            slot: dayjs.Dayjs;
        }
    >;
};

export type EventFormValues = Omit<
    EventInput,
    'customTestDriveBookingSlots' | 'period' | 'eventLevelEmailSettings' | 'hasVehicleIntegration'
> & {
    eventLevelEmailSettings: EventEmailFormValues;
    dealerVariantsMapping?: { [dealerId: string]: string[] };
    period: {
        start: Dayjs;
        end: Dayjs;
    };
    startTime?: Dayjs;
    endTime?: Dayjs;
    moduleId?: string;
    banner: BannerFormikValue;
    excludeVehicleOfInterest: boolean;
    moduleLevelMobileVerification?: boolean;
    customTestDriveBookingSlots?: Omit<CustomTestDriveBookingSlotsInput, 'fixedPeriods' | 'bookingWindowSettings'> & {
        fixedPeriods?: Array<TestDriveFixedPeriodForm>;
        bookingWindowSettings?: BookingWindowSettingsForm;
    };
} & EventKycFormInput;

export type CreateEventFormValues = EventFormValues & {
    moduleId: string;
};

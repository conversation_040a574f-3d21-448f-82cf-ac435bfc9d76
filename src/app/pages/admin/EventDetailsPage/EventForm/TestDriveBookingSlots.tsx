import { Row } from 'antd';
import { useFormikContext } from 'formik';
import { BookingPeriodType } from '../../../../api/types';
import type { PageType } from '../../../shared/EventList/RenderShareUrl';
import { BookingPeriodTypeSelector, BookingWindowSettings, FixedPeriodsSettings } from './TestDriveBookingSlots/index';
import type { EventFormValues } from './types';

export type TestDriveBookingSlotsProps = {
    disabled?: boolean;
    timeZone?: string;
    pageType: PageType;
};

const TestDriveBookingSlots = ({ disabled, timeZone, pageType }: TestDriveBookingSlotsProps) => {
    const { values } = useFormikContext<EventFormValues>();

    const customSlots = values.customTestDriveBookingSlots;
    const isEnabled = customSlots?.isEnabled;
    const bookingPeriodType = customSlots?.bookingPeriodType;

    if (!isEnabled) {
        return null;
    }

    return (
        <Row gutter={16}>
            <BookingPeriodTypeSelector disabled={disabled} />
            {bookingPeriodType === BookingPeriodType.FixedPeriod && (
                <FixedPeriodsSettings disabled={disabled} pageType={pageType} timeZone={timeZone} />
            )}
            {bookingPeriodType === BookingPeriodType.BookingWindow && (
                <BookingWindowSettings disabled={disabled} timeZone={timeZone} />
            )}
        </Row>
    );
};

export default TestDriveBookingSlots;

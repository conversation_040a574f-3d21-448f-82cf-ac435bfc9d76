import PlusOutlined from '@ant-design/icons/lib/icons/PlusOutlined';
import { Alert, Button, Col, Divider, Row } from 'antd';
import dayjs from 'dayjs';
import { FieldArray, useFormikContext } from 'formik';
import { get } from 'lodash/fp';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import type { AppointmentTimeSlotInput } from '../../../../../api/types';
import CollapsibleWrapper, { Panel } from '../../../../../components/wrappers/CollapsibleWrapper';
import { useThemeComponents } from '../../../../../themes/hooks';
import breakpoints from '../../../../../utilities/breakpoints';
import type { PageType } from '../../../../shared/EventList/RenderShareUrl';
import type { EventFormValues } from '../types';
import TimeSlotTable from './TimeSlotTable';

type FixedPeriod = {
    dateRange?: {
        start: string | Date;
        end: string | Date;
    };
    bookingTimeSlot: Array<Omit<AppointmentTimeSlotInput, 'slot'> & { slot: dayjs.Dayjs }>;
    advancedBookingLimit?: number;
};

type FixedPeriodsSettingsProps = {
    disabled?: boolean;
    timeZone?: string;
    pageType: PageType;
};

const StyledContainer = styled.div<{ $pageType: PageType }>`
    ${props =>
        props.$pageType === 'Admin' &&
        css`
            .ant-collapse {
                border-radius: 8px;
                background: white;
                .ant-collapse-item {
                    margin-bottom: 10px;
                    border: 0;
                }
            }
            @media screen and (max-width: ${breakpoints.md}) {
                padding: 24px;
            }
        `}

    ${props =>
        props.$pageType === 'Portal' &&
        css`
            .ant-collapse {
                .ant-collapse-item {
                    border-top: 0;
                    border-left: 0;
                    border-right: 0;
                }
            }
        `}
`;

const FixedPeriodsSettings = ({ disabled, timeZone, pageType }: FixedPeriodsSettingsProps) => {
    const { t } = useTranslation(['eventDetails']);
    const { FormFields } = useThemeComponents();
    const { values, errors, submitCount } = useFormikContext<EventFormValues>();

    // dont want to show error if the form is not submitted yet
    const fixedPeriodsError = submitCount > 0 ? get('customTestDriveBookingSlots.fixedPeriods')(errors) : null;

    // Function to check if a date is within any existing period's date range (excluding current period)
    const isDateDisabled = useCallback(
        (currentDate: dayjs.Dayjs, currentPeriodIndex: number) =>
            values.customTestDriveBookingSlots?.fixedPeriods?.some((period, index) => {
                // Skip the current period being edited
                if (index === currentPeriodIndex) {
                    return false;
                }

                // Skip periods without valid date ranges
                // Handle the form structure vs API structure mismatch
                const formPeriod = period as FixedPeriod;
                if (!formPeriod.dateRange?.start || !formPeriod.dateRange?.end) {
                    return false;
                }

                const existingStart = dayjs(formPeriod.dateRange.start);
                const existingEnd = dayjs(formPeriod.dateRange.end);

                // Check if current date falls within existing period's range (inclusive)
                return (
                    currentDate.isSameOrAfter(existingStart, 'day') && currentDate.isSameOrBefore(existingEnd, 'day')
                );
            }),
        [values.customTestDriveBookingSlots?.fixedPeriods]
    );

    // Create disabledDate function for a specific period
    const createDisabledDateFunction = useCallback(
        (periodIndex: number) => (current: dayjs.Dayjs) => {
            if (!current) {
                return false;
            }

            // Disable dates that overlap with other periods
            const isOverlapping = isDateDisabled(current, periodIndex);

            return isOverlapping;
        },
        [isDateDisabled]
    );

    const renderFixedPeriodItem = (period: FixedPeriod, periodIndex: number, arrayHelpers: any) => {
        const periodKey = `period_${periodIndex}`;

        const startDate = period.dateRange?.start ? dayjs(period.dateRange.start).format('DD MMM YYYY') : null;
        const endDate = period.dateRange?.end ? dayjs(period.dateRange.end).format('DD MMM YYYY') : null;

        const baseFieldName = `customTestDriveBookingSlots.fixedPeriods.${periodIndex}`;
        const timePath = `${baseFieldName}.bookingTimeSlot`;

        const headerText =
            startDate && endDate
                ? t('eventDetails:labels.specificDatesWithRange', { startDate, endDate })
                : t('eventDetails:labels.specificDates');

        const advancedBookingLimitFieldName = `${baseFieldName}.advancedBookingLimit`;

        return (
            <StyledContainer $pageType={pageType}>
                <CollapsibleWrapper key={periodKey} activeKey={periodKey} defaultActiveKey="submitOrderDefault">
                    <Panel
                        key={periodKey}
                        extra={
                            <Button disabled={disabled} onClick={() => arrayHelpers.remove(periodIndex)} danger>
                                {t('eventDetails:actions.delete')}
                            </Button>
                        }
                        header={headerText}
                        style={{ marginBottom: '24px' }}
                    >
                        <Row gutter={[24, 0]}>
                            <Col lg={8} md={12} sm={24}>
                                <FormFields.RangePickerField
                                    disabled={disabled}
                                    disabledDate={createDisabledDateFunction(periodIndex)}
                                    label={t('eventDetails:fields.startEndDate.label')}
                                    name={`${baseFieldName}.dateRange`}
                                    required
                                />
                            </Col>
                            <Col lg={8} md={12} sm={24}>
                                <FormFields.InputField
                                    {...t('eventDetails:fields.advancedBookingLimit', {
                                        returnObjects: true,
                                    })}
                                    addonAfter={t('eventDetails:labels.days')}
                                    disabled={disabled}
                                    min={0}
                                    name={advancedBookingLimitFieldName}
                                    placeholder={t('eventDetails:placeholders.zero')}
                                    type="number"
                                    required
                                />
                            </Col>
                        </Row>

                        <TimeSlotTable
                            bookingTimeSlot={period.bookingTimeSlot}
                            disabled={disabled}
                            errorPath={`customTestDriveBookingSlots.fixedPeriods.${periodIndex}.bookingTimeSlot`}
                            timePath={timePath}
                            timeZone={timeZone}
                        />
                    </Panel>
                </CollapsibleWrapper>
            </StyledContainer>
        );
    };

    return (
        <>
            <Col span={24}>
                <Divider style={{ marginTop: '0' }} />
            </Col>
            {fixedPeriodsError && typeof fixedPeriodsError === 'string' && fixedPeriodsError.trim() !== '' && (
                <Col span={24}>
                    <Alert message={fixedPeriodsError} style={{ marginBottom: '24px' }} type="error" showIcon />
                </Col>
            )}
            <FieldArray name="customTestDriveBookingSlots.fixedPeriods">
                {arrayHelpers => (
                    <>
                        <Col span={24}>
                            {values.customTestDriveBookingSlots?.fixedPeriods?.map((period, periodIndex) =>
                                renderFixedPeriodItem(period, periodIndex, arrayHelpers)
                            )}
                        </Col>
                        <Col span={24}>
                            <Button
                                disabled={disabled}
                                icon={<PlusOutlined />}
                                onClick={() =>
                                    arrayHelpers.push({
                                        dateRange: undefined,
                                        bookingTimeSlot: [],
                                        advancedBookingLimit: 0,
                                    })
                                }
                                style={{ marginLeft: '-15px' }} // to match the exact placement based on the design
                                type="link"
                            >
                                {t('eventDetails:actions.addSpecificDates')}
                            </Button>
                        </Col>
                    </>
                )}
            </FieldArray>
        </>
    );
};

export default FixedPeriodsSettings;

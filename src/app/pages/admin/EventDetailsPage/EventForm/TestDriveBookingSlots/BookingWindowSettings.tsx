import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../../themes/hooks';
import useSystemOptions from '../../../../../utilities/useSystemOptions';
import type { EventFormValues } from '../types';
import TimeSlotTable from './TimeSlotTable';

type BookingWindowSettingsProps = {
    disabled?: boolean;
    timeZone?: string;
};

const BookingWindowSettings = ({ disabled, timeZone }: BookingWindowSettingsProps) => {
    const { t } = useTranslation(['eventDetails']);
    const { FormFields } = useThemeComponents();
    const { values } = useFormikContext<EventFormValues>();
    const { dayOfWeek } = useSystemOptions();

    const bookingWindowSettings = values.customTestDriveBookingSlots?.bookingWindowSettings;
    const bookingTimeSlot = bookingWindowSettings?.bookingTimeSlot || [];

    return (
        <>
            <Col lg={8} md={12} sm={24}>
                <FormFields.SelectField
                    disabled={disabled}
                    label={t('eventDetails:fields.unavailableDayOfWeek.label')}
                    mode="multiple"
                    name="customTestDriveBookingSlots.bookingWindowSettings.unavailableDayOfWeek"
                    options={dayOfWeek}
                    placeholder={t('eventDetails:placeholders.pleaseSelect')}
                    showSearch
                />
            </Col>
            <Col lg={8} md={12} sm={24}>
                <FormFields.InputNumberField
                    addonAfter={t('eventDetails:labels.days')}
                    disabled={disabled}
                    label={t('eventDetails:fields.advancedBookingLimit.label')}
                    min={0}
                    name="customTestDriveBookingSlots.bookingWindowSettings.advancedBookingLimit"
                    placeholder="0"
                    tooltip={t('eventDetails:tooltips.advancedBookingLimit')}
                    required
                />
            </Col>

            <Col lg={8} md={12} sm={24}>
                <FormFields.InputNumberField
                    addonAfter={t('eventDetails:labels.days')}
                    disabled={disabled}
                    label={t('eventDetails:fields.maxAdvancedBookingLimit.label')}
                    min={0}
                    name="customTestDriveBookingSlots.bookingWindowSettings.maxAdvancedBookingLimit"
                    placeholder="0"
                    tooltip={t('eventDetails:tooltips.maxAdvancedBookingLimit')}
                />
            </Col>

            <Col span={24}>
                <TimeSlotTable
                    bookingTimeSlot={bookingTimeSlot}
                    disabled={disabled}
                    errorPath="customTestDriveBookingSlots.bookingWindowSettings.bookingTimeSlot"
                    timePath="customTestDriveBookingSlots.bookingWindowSettings.bookingTimeSlot"
                    timeZone={timeZone}
                />
            </Col>
        </>
    );
};

export default BookingWindowSettings;

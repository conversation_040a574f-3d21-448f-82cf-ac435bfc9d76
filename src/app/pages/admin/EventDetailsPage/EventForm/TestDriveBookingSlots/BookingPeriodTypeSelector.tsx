import { Col } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { BookingPeriodType } from '../../../../../api/types';
import { useThemeComponents } from '../../../../../themes/hooks';
import type { EventFormValues } from '../types';

type BookingPeriodTypeSelectorProps = {
    disabled?: boolean;
};

const BookingPeriodTypeSelector = ({ disabled }: BookingPeriodTypeSelectorProps) => {
    const { t } = useTranslation(['eventDetails']);
    const { FormFields } = useThemeComponents();
    const { setFieldValue } = useFormikContext<EventFormValues>();

    const options = useMemo(
        () => [
            {
                label: t('eventDetails:options.bookingPeriodType.fixedPeriod'),
                value: BookingPeriodType.FixedPeriod,
            },
            {
                label: t('eventDetails:options.bookingPeriodType.bookingWindow'),
                value: BookingPeriodType.BookingWindow,
            },
        ],
        [t]
    );

    const handleBookingPeriodTypeChange = useCallback(
        (value: BookingPeriodType) => {
            const baseSettings = {
                isEnabled: true,
                bookingPeriodType: value || undefined,
                fixedPeriods: [],
                bookingWindowSettings: undefined,
            };

            switch (value) {
                case BookingPeriodType.FixedPeriod:
                    setFieldValue('customTestDriveBookingSlots', {
                        ...baseSettings,
                        fixedPeriods: [
                            {
                                dateRange: undefined,
                                bookingTimeSlot: [],
                                advancedBookingLimit: 0,
                            },
                        ],
                    });
                    break;
                case BookingPeriodType.BookingWindow:
                    setFieldValue('customTestDriveBookingSlots', {
                        ...baseSettings,
                        bookingWindowSettings: {
                            unavailableDayOfWeek: [],
                            advancedBookingLimit: 0,
                            maxAdvancedBookingLimit: undefined,
                            bookingTimeSlot: [],
                        },
                    });
                    break;
                default:
                    setFieldValue('customTestDriveBookingSlots', baseSettings);
                    break;
            }
        },
        [setFieldValue]
    );

    return (
        <Col lg={8} md={12} sm={24}>
            <FormFields.SelectField
                {...t('eventDetails:fields.bookingPeriodType', { returnObjects: true })}
                disabled={disabled}
                name="customTestDriveBookingSlots.bookingPeriodType"
                onChange={handleBookingPeriodTypeChange}
                options={options}
                placeholder={t('eventDetails:placeholders.pleaseSelect')}
                required
                showSearch
            />
        </Col>
    );
};

export default BookingPeriodTypeSelector;

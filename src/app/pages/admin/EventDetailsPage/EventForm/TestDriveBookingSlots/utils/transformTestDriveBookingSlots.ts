import dayjs from 'dayjs';
import { BookingPeriodType, type CustomTestDriveBookingSlotsInput, type DayOfWeek } from '../../../../../../api/types';
import type { CreateEventFormValues, EventFormValues, TestDriveFixedPeriodFormValidation } from '../../types';

/**
 * Transforms custom test drive booking slots for create event operation
 * Handles conditional inclusion based on displayAppointmentDatepicker
 */
export const transformCustomTestDriveBookingSlotsForCreate = (
    formValues: CreateEventFormValues,
    companyTimeZone: string
): CustomTestDriveBookingSlotsInput | undefined => {
    // Scenario 1: When displayAppointmentDatepicker is false, don't include the field
    if (!formValues.displayAppointmentDatepicker) {
        return undefined;
    }

    const customSlots = formValues.customTestDriveBookingSlots;

    // Scenario 2: When displayAppointmentDatepicker is true but customSlots is disabled or incomplete
    if (!customSlots?.isEnabled || !customSlots.bookingPeriodType) {
        return {
            isEnabled: false,
            bookingPeriodType: BookingPeriodType.FixedPeriod,
            fixedPeriods: [],
        };
    }

    // Scenario 3: When enabled, transform the data properly
    return transformTestDriveBookingSlotsForApi(customSlots, companyTimeZone);
};

/**
 * Transforms custom test drive booking slots for update event operation
 * Handles conditional inclusion based on displayAppointmentDatepicker
 */
export const transformCustomTestDriveBookingSlotsForUpdate = (
    formValues: EventFormValues,
    companyTimeZone: string
): CustomTestDriveBookingSlotsInput | undefined => {
    // Scenario 1: When displayAppointmentDatepicker is false, don't include the field
    if (!formValues.displayAppointmentDatepicker) {
        return undefined;
    }

    const customSlots = formValues.customTestDriveBookingSlots;

    // Scenario 2: When displayAppointmentDatepicker is true but customSlots is disabled or incomplete
    if (!customSlots?.isEnabled || !customSlots.bookingPeriodType) {
        return {
            isEnabled: false,
            bookingPeriodType: BookingPeriodType.FixedPeriod,
            fixedPeriods: [],
        };
    }

    // Scenario 3: When enabled, transform the data properly
    return transformTestDriveBookingSlotsForApi(customSlots, companyTimeZone);
};

/**
 * Transforms form data for test drive booking slots to match the GraphQL schema
 * - Converts dayjs objects to timezone-aware Date objects following AppointmentModule pattern
 */
const transformTestDriveBookingSlotsForApi = (
    formData: EventFormValues['customTestDriveBookingSlots'] | CreateEventFormValues['customTestDriveBookingSlots'],
    companyTimeZone: string
): CustomTestDriveBookingSlotsInput => {
    if (!formData || !formData.isEnabled || !formData.bookingPeriodType) {
        return {
            isEnabled: false,
            bookingPeriodType: formData?.bookingPeriodType ?? BookingPeriodType.FixedPeriod,
            fixedPeriods: [],
        };
    }

    const transformed: CustomTestDriveBookingSlotsInput = {
        isEnabled: formData.isEnabled,
        bookingPeriodType: formData.bookingPeriodType,
        fixedPeriods: [],
    };

    // Transform fixed periods
    if (formData.fixedPeriods && Array.isArray(formData.fixedPeriods)) {
        transformed.fixedPeriods = formData.fixedPeriods.map((period: TestDriveFixedPeriodFormValidation) => {
            const transformedPeriod = {
                startDate: period.dateRange?.start ? dayjs(period.dateRange.start).toISOString() : null,
                endDate: period.dateRange?.end ? dayjs(period.dateRange.end).toISOString() : null,
                advancedBookingLimit: period.advancedBookingLimit,
                bookingTimeSlot: [] as Array<{ slot: Date; bookingLimit: number }>,
            };

            // Transform booking time slots
            transformedPeriod.bookingTimeSlot = period.bookingTimeSlot.map(slot => {
                // Follow Visit Appointment Module pattern for timezone conversion
                const transformedSlot = dayjs(slot.slot)
                    .set('hour', slot.slot.hour())
                    .set('minute', slot.slot.minute())
                    .set('second', 0)
                    .tz(companyTimeZone, true)
                    .toDate();

                return {
                    slot: transformedSlot,
                    bookingLimit: slot.bookingLimit,
                };
            });

            return transformedPeriod;
        });
    }

    // Transform booking window settings
    if (formData.bookingWindowSettings) {
        const settings = formData.bookingWindowSettings;
        transformed.bookingWindowSettings = {
            unavailableDayOfWeek: (settings.unavailableDayOfWeek || []) as DayOfWeek[],
            advancedBookingLimit: settings.advancedBookingLimit,
            maxAdvancedBookingLimit: settings.maxAdvancedBookingLimit,
            bookingTimeSlot: [],
        };

        // Transform booking time slots for booking window
        transformed.bookingWindowSettings.bookingTimeSlot = settings.bookingTimeSlot.map(slot => {
            // Follow Visit Appointment Module pattern for timezone conversion
            const transformedSlot = dayjs(slot.slot)
                .set('hour', slot.slot.hour())
                .set('minute', slot.slot.minute())
                .set('second', 0)
                .tz(companyTimeZone, true)
                .toDate();

            return {
                slot: transformedSlot,
                bookingLimit: slot.bookingLimit,
            };
        });
    }

    return transformed;
};

export default transformTestDriveBookingSlotsForApi;

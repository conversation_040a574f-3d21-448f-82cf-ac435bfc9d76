import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Divider, Row, Table, Typography, Empty, Alert } from 'antd';
import dayjs from 'dayjs';
import { FieldArray, FieldArrayRenderProps, useFormikContext } from 'formik';
import { get } from 'lodash/fp';
import { useTranslation } from 'react-i18next';
import type { AppointmentTimeSlotInput } from '../../../../../api/types';
import FormItem from '../../../../../components/fields/FormItem';
import { useThemeComponents } from '../../../../../themes/hooks';
import { getOffset } from '../../../../../utilities/date';

type BookingTimeSlot = Omit<AppointmentTimeSlotInput, 'slot'> & { slot: dayjs.Dayjs };

type TimeSlotTableProps = {
    disabled?: boolean;
    bookingTimeSlot: BookingTimeSlot[];
    timePath: string;
    timeZone?: string;
    errorPath?: string;
};

const TimeSlotTable = ({ disabled, bookingTimeSlot, timePath, timeZone, errorPath }: TimeSlotTableProps) => {
    const { t } = useTranslation(['eventDetails', 'common']);
    const { FormFields, Table: StyledTable } = useThemeComponents();
    const { errors, submitCount } = useFormikContext();

    // dont want to show error if the form is not submitted yet
    const timeSlotError = errorPath && submitCount > 0 ? get(errorPath)(errors) : null;
    // Only display alert for top-level string errors (e.g., "At least one time slot is required")
    // Individual field errors (slot, bookingLimit) are handled by their respective form fields
    const showTimeSlotError = timeSlotError && typeof timeSlotError === 'string';

    const emptyState = (
        <Empty
            description={t('eventDetails:labels.noData')}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            imageStyle={{
                height: 60,
            }}
        />
    );

    return (
        <Row gutter={[24, 24]}>
            <Col span={24}>
                <Divider style={{ margin: '0' }} />
            </Col>
            <Col span={24}>
                <Typography.Text strong>{t('eventDetails:subSections.availableTimeSlot')}</Typography.Text>
                {showTimeSlotError && (
                    <Alert message={timeSlotError} style={{ marginTop: '10px' }} type="error" showIcon />
                )}
            </Col>
            <FieldArray
                name={timePath}
                render={(arrayHelpers: FieldArrayRenderProps) => (
                    <>
                        <Col span={24}>
                            <StyledTable
                                dataSource={bookingTimeSlot || []}
                                locale={{
                                    emptyText: emptyState,
                                }}
                                pagination={false}
                                rowKey={(_, index: number) => `${timePath}_${index.toString()}`}
                            >
                                <Table.Column
                                    dataIndex="slot"
                                    render={(_, __, index) => (
                                        <FormFields.TimePickerField
                                            disabled={disabled}
                                            format="h:mm a"
                                            minuteStep={30}
                                            name={`${timePath}[${index}].slot`}
                                        />
                                    )}
                                    title={
                                        <FormItem
                                            label={t('eventDetails:fields.timeSlot.label', {
                                                offset: getOffset(t, timeZone),
                                            })}
                                            required
                                        />
                                    }
                                />
                                <Table.Column
                                    dataIndex="bookingLimit"
                                    render={(_, __, index) => (
                                        <FormFields.InputNumberField
                                            disabled={disabled}
                                            min={0}
                                            name={`${timePath}.[${index}].bookingLimit`}
                                        />
                                    )}
                                    title={
                                        <FormItem
                                            label={t('eventDetails:fields.bookingLimit.label')}
                                            tooltip={t('eventDetails:tooltips.bookingLimit')}
                                        />
                                    }
                                />
                                <Table.Column
                                    key="actions"
                                    align="center"
                                    render={(_, __, index) => (
                                        <Button
                                            disabled={disabled}
                                            icon={<DeleteOutlined />}
                                            onClick={() => {
                                                arrayHelpers.remove(index);
                                            }}
                                            size="middle"
                                            type="link"
                                        />
                                    )}
                                    title={t('common:actions.action')}
                                    width={100}
                                />
                            </StyledTable>
                        </Col>
                        <Col span={24}>
                            <Button
                                disabled={disabled}
                                icon={<PlusOutlined />}
                                onClick={() => {
                                    const newTimeSlot = {
                                        slot: null,
                                        bookingLimit: 1,
                                        _type: 'APPOINTMENT',
                                    };
                                    arrayHelpers.push(newTimeSlot);
                                }}
                                style={{ marginLeft: '-15px' }} // to match the exact placement based on the design
                                type="link"
                            >
                                {t('eventDetails:actions.addTimeSlot')}
                            </Button>
                        </Col>
                    </>
                )}
            />
        </Row>
    );
};

export default TimeSlotTable;

import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import DealershipTranslationTextField from '../../../../components/fields/DealershipFields/DealershipTranslationTextField';
import { useThemeComponents } from '../../../../themes/hooks';
import useLeadGenTooltip from '../../../../utilities/useLeadGenTooltip';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import type { EventFormValues } from './types';

export const colSpan = { lg: 8, md: 12, xs: 24 };

export type CustomThankYouPageFormProps = {
    companyId: string;
    dealerIds: string[];
    disabled?: boolean;
    prefix: string;
};

const CustomThankYouPageForm = ({
    companyId,
    dealerIds = [],
    disabled = false,
    prefix,
}: CustomThankYouPageFormProps) => {
    const { t } = useTranslation(['eventDetails', 'common']);
    const { values } = useFormikContext<EventFormValues>();
    const { dealerIdsByCompanyId } = useMultipleDealerIds();

    const targetDealerIds = useMemo(
        () => (!isNil(dealerIds) ? dealerIds : dealerIdsByCompanyId(companyId)),
        [companyId, dealerIds, dealerIdsByCompanyId]
    );

    const { yesNoSwitch } = useSystemSwitchData();
    const { FormFields } = useThemeComponents();

    const customRedirectButtonTooltip = useLeadGenTooltip('useCustomRedirectButton');

    return (
        <Row gutter={16}>
            <Col {...colSpan}>
                <DealershipTranslationTextField
                    dealerIds={targetDealerIds}
                    defaultValueDisabled={false}
                    disabled={disabled}
                    label={t('eventDetails:fields.thankYouPageIntro.label')}
                    name={`${prefix}.introTitle`}
                />
            </Col>
            <Col {...colSpan}>
                <DealershipTranslationTextField
                    dealerIds={targetDealerIds}
                    defaultValueDisabled={false}
                    disabled={disabled}
                    label={t('eventDetails:fields.thankYouPageContent.label')}
                    name={`${prefix}.contentText`}
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.SwitchField
                    {...yesNoSwitch}
                    disabled={disabled}
                    label={t('eventDetails:fields.useCustomRedirectButton.label')}
                    name={`${prefix}.isCustomRedirectionButton`}
                    tooltip={customRedirectButtonTooltip}
                />
            </Col>
            {values.thankYouPageContent.isCustomRedirectionButton && (
                <>
                    <Col {...colSpan}>
                        <DealershipTranslationTextField
                            dealerIds={targetDealerIds}
                            defaultValueDisabled={false}
                            disabled={disabled}
                            label={t('eventDetails:fields.redirectButtonTitle.label')}
                            name={`${prefix}.redirectButton.title`}
                            required
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.InputField
                            disabled={disabled}
                            label={t('eventDetails:fields.redirectUrl.label')}
                            name={`${prefix}.redirectButton.url`}
                            required
                        />
                    </Col>
                </>
            )}
        </Row>
    );
};

export default CustomThankYouPageForm;

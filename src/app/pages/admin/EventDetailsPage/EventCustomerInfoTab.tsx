import { useFormikContext } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { LocalCustomerFieldKey, type Company } from '../../../api/types';
import CollapsibleWrapper from '../../../components/wrappers/CollapsibleWrapper';
import { PageType } from '../../shared/EventList/RenderShareUrl';
import KYCFieldsExtraSettings from '../ModuleDetailsPage/LocalCustomerModuleDetailPage/KYCFieldsExtraSettings';
import CustomizedField from './EventForm/CustomizedField';
import { EventInnerStyledPanel } from './EventForm/shared';
import type { EventFormValues } from './EventForm/types';
import EventKycTableField from './EventKycFields/EventKycTableField';
import { getTitleForKycSection } from './EventKycFields/utils';

type EventCustomerInfoTabProps = {
    disabled?: boolean;
    pageType: PageType;
    company?: Pick<Company, 'id' | 'displayName' | 'countryCode'>;
};

const EventCustomerInfoTab = ({ disabled, pageType, company }: EventCustomerInfoTabProps) => {
    const { t } = useTranslation(['eventDetails', 'localCustomerModuleDetails']);

    const { values } = useFormikContext<EventFormValues>();
    const kycFieldsExpandedKeys = useMemo(
        () => values.kycPresets.map(item => `kycField_${item.displayName}`),
        [values.kycPresets]
    );

    const hasBirthdayField = useMemo(
        () =>
            values.kycPresets.some(preset => preset.fields.some(field => field.key === LocalCustomerFieldKey.Birthday)),
        [values]
    );

    return (
        <CollapsibleWrapper defaultActiveKey={[...kycFieldsExpandedKeys, 'customizedFields']}>
            {values.kycPresets.map((item, index) => (
                <EventInnerStyledPanel
                    key={`kycField_${item.displayName}`}
                    $pageType={pageType}
                    header={getTitleForKycSection(t, item.displayName)}
                >
                    <EventKycTableField company={company} disabled={disabled} name={`kycPresets.${index}.fields`} />
                </EventInnerStyledPanel>
            ))}

            <KYCFieldsExtraSettings
                disabled={disabled}
                hasBirthdayField={hasBirthdayField}
                pageType={pageType}
                prefixKey="kycExtraSettings"
                showAdditionalSettings={values.moduleLevelMobileVerification}
            />

            <EventInnerStyledPanel
                key="customizedFields"
                $pageType={pageType}
                header={t('eventDetails:panelTitles.customizedFields')}
            >
                <CustomizedField disabled={disabled} name="customizedFields" />
            </EventInnerStyledPanel>
        </CollapsibleWrapper>
    );
};

export default EventCustomerInfoTab;

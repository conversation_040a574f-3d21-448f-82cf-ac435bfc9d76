import { Suspense, lazy } from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleSpecsFragment } from '../../../../api/fragments/ModuleSpecs';
import { ConditionType } from '../../../../api/types';
import { BaseConditionRenderer } from './BaseConditionRenderer';

const LogicConditionFieldArray = lazy(() => import('./LogicConditionFieldArray'));

export type ConditionRenderProps = {
    conditionType: ConditionType;
    prefix: string;
    module?: ModuleSpecsFragment;
};

export const ConditionFieldRenderer = ({ conditionType, prefix, module }: ConditionRenderProps) => {
    const { t } = useTranslation('consentsAndDeclarations');

    switch (conditionType) {
        case ConditionType.Or:
            return (
                <Suspense fallback={<div>Loading...</div>}>
                    <LogicConditionFieldArray
                        labelForAdd={t('consentsAndDeclarations:addLogicCondition', { conditionType })}
                        name={`${prefix}.logicConditionSettings.children`}
                    />
                </Suspense>
            );

        default:
            return <BaseConditionRenderer conditionType={conditionType} module={module} prefix={prefix} />;
    }
};

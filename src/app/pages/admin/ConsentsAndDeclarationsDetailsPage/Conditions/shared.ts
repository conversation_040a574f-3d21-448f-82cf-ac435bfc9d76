import { ConditionSettings, ConditionType } from '../../../../api';

export const isConditionSettingsComplete = (conditionSetting: ConditionSettings): boolean => {
    if (!conditionSetting.type) {
        return false;
    }

    switch (conditionSetting.type) {
        case ConditionType.And:
        case ConditionType.Or: {
            if (!conditionSetting.logicConditionSettings) {
                return false;
            }
            const { children } = conditionSetting.logicConditionSettings;

            // every child condition should be complete as well
            return !!conditionSetting.type && children.every(setting => isConditionSettingsComplete(setting));
        }

        case ConditionType.IsApplicationModule:
            return !!conditionSetting.type && !!conditionSetting.applicationModuleConditionSettings;

        case ConditionType.IsBank:
            return !!conditionSetting.type && !!conditionSetting.bankConditionSettings;

        case ConditionType.IsDealer:
            return !!conditionSetting.type && !!conditionSetting.dealerConditionsSettings;

        case ConditionType.IsInsurer:
            return !!conditionSetting.insurerConditionSettings;

        case ConditionType.IsLocation:
            return !!conditionSetting.type && !!conditionSetting.locationConditionSettings;

        case ConditionType.IsGiftVoucher:
            return !!conditionSetting.type && !!conditionSetting.giftVoucherConditionSettings;

        case ConditionType.SalesOfferAgreements: {
            return !!conditionSetting.type && !!conditionSetting.salesOfferConditionSettings;
        }

        default:
            return !!conditionSetting.type;
    }
};

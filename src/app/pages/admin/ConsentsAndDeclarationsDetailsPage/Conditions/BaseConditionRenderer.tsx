import { useTranslation } from 'react-i18next';
import { ModuleSpecsFragment } from '../../../../api/fragments/ModuleSpecs';
import { ConditionType, ModuleType } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import BankSelectField from '../../../../components/fields/BankSelectField';
import DealerSelectField from '../../../../components/fields/DealerSelectField';
import InsurerSelectField from '../../../../components/fields/InsurerSelectField';
import LocationSelectField from '../../../../components/fields/LocationSelectField';
import ModuleSelectField from '../../../../components/fields/ModuleSelectField';
import SalesOfferAgreementField from '../../../../components/fields/SalesOfferAgreementField';

export type BaseConditionRenderProps = {
    conditionType: ConditionType;
    prefix: string;
    module?: ModuleSpecsFragment;
};

export const BaseConditionRenderer = ({ conditionType, prefix, module }: BaseConditionRenderProps) => {
    const { t } = useTranslation('consentsAndDeclarations');
    const company = useCompany(true);

    switch (conditionType) {
        case ConditionType.IsApplicationModule:
            return (
                <ModuleSelectField
                    {...t('consentsAndDeclarations:fields.applicationModule', {
                        returnObjects: true,
                    })}
                    moduleTypes={[
                        ModuleType.StandardApplicationModule,
                        ModuleType.EventApplicationModule,
                        ModuleType.ConfiguratorModule,
                        ModuleType.FinderApplicationPublicModule,
                        ModuleType.FinderApplicationPrivateModule,
                        ModuleType.LaunchPadModule,
                        ModuleType.SalesOfferModule,
                    ]}
                    name={`${prefix}.applicationModuleConditionSettings.moduleId`}
                    required
                />
            );

        case ConditionType.IsBank:
            return (
                <BankSelectField
                    {...t('consentsAndDeclarations:fields.bank', {
                        returnObjects: true,
                    })}
                    companyId={company?.id}
                    name={`${prefix}.bankConditionSettings.bankId`}
                    required
                />
            );

        case ConditionType.IsDealer:
            return (
                <DealerSelectField
                    {...t('consentsAndDeclarations:fields.dealer', {
                        returnObjects: true,
                    })}
                    companyId={company?.id}
                    name={`${prefix}.dealerConditionsSettings.dealerId`}
                    required
                />
            );

        case ConditionType.IsInsurer:
            return (
                <InsurerSelectField
                    {...t('consentsAndDeclarations:fields.insurer', {
                        returnObjects: true,
                    })}
                    companyId={company?.id}
                    name={`${prefix}.insurerConditionSettings.insurerId`}
                    required
                />
            );

        case ConditionType.IsLocation:
            return (
                <LocationSelectField
                    {...t('consentsAndDeclarations:fields.location', {
                        returnObjects: true,
                    })}
                    companyId={company?.id}
                    module={module}
                    name={`${prefix}.locationConditionSettings.locationId`}
                    parentName={`${prefix}.locationConditionSettings`}
                    required
                />
            );

        case ConditionType.IsGiftVoucher:
            return (
                <ModuleSelectField
                    {...t('consentsAndDeclarations:fields.giftVoucherModule', {
                        returnObjects: true,
                    })}
                    moduleTypes={[ModuleType.GiftVoucherModule]}
                    name={`${prefix}.giftVoucherConditionSettings.moduleId`}
                    required
                />
            );

        case ConditionType.SalesOfferAgreements:
            return (
                <SalesOfferAgreementField
                    {...t('consentsAndDeclarations:fields.salesOfferAgreement', {
                        returnObjects: true,
                    })}
                    name={`${prefix}.salesOfferConditionSettings.feature`}
                    required
                />
            );

        default:
            return null;
    }
};

import { ConditionSpecsFragment } from '../../../../api/fragments/ConditionSpecs';
import { ConditionSettings, ConditionType } from '../../../../api/types';
import ConditionContainer from './ConditionContainer';
import { ConditionGroup } from './ConditionGroup';

export type ConditionRendererProps = {
    conditionSettings: ConditionSettings;
    onClick: () => void;
    onDelete: () => void;
    logicCondition?: boolean;
};

export const ConditionRenderer = ({ conditionSettings, onClick, onDelete, logicCondition }: ConditionRendererProps) => {
    if (conditionSettings.type === ConditionType.Or) {
        const { logicConditionSettings } = conditionSettings;
        const { children } = logicConditionSettings;
        const childrenConditions = children as ConditionSpecsFragment[];

        // Recursively render each child condition
        const renderedChildren = childrenConditions.map((child, index) => (
            <ConditionRenderer
                // eslint-disable-next-line react/no-array-index-key
                key={index}
                conditionSettings={child}
                onClick={onClick}
                onDelete={onDelete}
                logicCondition
            />
        ));

        return (
            <ConditionGroup onClick={onClick} onGroupDelete={onDelete}>
                {renderedChildren}
            </ConditionGroup>
        );
    }

    return (
        <ConditionContainer
            conditionSettings={conditionSettings}
            logicCondition={logicCondition}
            onClick={onClick}
            onDelete={onDelete}
        />
    );
};

export default ConditionRenderer;

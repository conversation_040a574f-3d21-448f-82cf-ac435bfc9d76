import { useApolloClient } from '@apollo/client';
import { <PERSON><PERSON>, Row, Typography, Upload, type UploadProps, message, Space } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {
    ImportDealersDocument,
    type ImportDealersMutation,
    type ImportDealersMutationVariables,
} from '../../../api/mutations/importDealers';
import { CompanyTheme, type ImportDealerSuccess } from '../../../api/types';
import ThemeUpload from '../../../components/ThemeUpload';
import { ErrorContent } from '../../../components/importExport';
import CancelButton from '../../../components/modal/CancelButton';
import OkButton from '../../../components/modal/OkButton';
import { useThemeComponents } from '../../../themes/hooks';
import { allowedExtensions } from '../../../utilities/extensions';

const Container = styled(Space)`
    width: 100%;
    justify-content: center;
`;

export type ImportDealerModalProps = {
    open: boolean;
    onDownloadClick: () => void;
    onClose: () => void;
    onImportSuccess?: () => void;
    companyId?: string;
};

const ImportDealerModal = ({ open, onDownloadClick, onClose, onImportSuccess, companyId }: ImportDealerModalProps) => {
    const { t } = useTranslation(['dealerList']);
    const apolloClient = useApolloClient();
    const [errors, setErrors] = useState<string>(null);
    const [importedCount, setImportedCount] = useState<ImportDealerSuccess>(null);
    const [isUploading, setIsUploading] = useState(false);
    const { theme, Modal } = useThemeComponents();

    const accept = useMemo(() => allowedExtensions.excel.join(','), []);

    const handleOnClose = useCallback(() => {
        if (!isNil(importedCount) && onImportSuccess) {
            onImportSuccess();
        }

        onClose();
        setErrors(null);
        setImportedCount(null);
        setIsUploading(false);
    }, [onClose, onImportSuccess, importedCount]);

    const handleOnChange = useCallback(
        async ({ file }) => {
            if (file instanceof File && companyId) {
                setIsUploading(true);
                setErrors(null);
                setImportedCount(null);

                try {
                    const { data } = await apolloClient.mutate<ImportDealersMutation, ImportDealersMutationVariables>({
                        mutation: ImportDealersDocument,
                        variables: {
                            companyId,
                            upload: file,
                        },
                    });

                    switch (data.importDealers.__typename) {
                        case 'ImportDealerSuccess':
                            setImportedCount(data.importDealers);
                            break;

                        case 'ImportDealerFail':
                            setErrors((data.importDealers?.errors ?? []).join('\n') || data.importDealers.message);
                            break;

                        default:
                            setErrors('An unexpected error occurred during import');
                            break;
                    }
                } catch (error) {
                    setErrors('An unexpected error occurred during import');
                } finally {
                    setIsUploading(false);
                }
            }
        },
        [apolloClient, companyId]
    );

    const beforeUpload = useCallback<NonNullable<UploadProps['beforeUpload']>>(
        file => {
            const fileExtension = file.name.toLowerCase().split('.').pop();
            const isValidExtension = ['xlsx', 'csv'].includes(fileExtension);

            if (!isValidExtension) {
                message.error(t('common:upload.messages.fileError'));

                return Upload.LIST_IGNORE;
            }

            return false;
        },
        [t]
    );

    const content = useMemo(() => {
        if (!isNil(errors)) {
            return <ErrorContent errors={errors} />;
        }

        if (!isNil(importedCount)) {
            return (
                <Row align="middle" gutter={[14, 14]} justify="center">
                    <Typography>
                        {t('dealerList:messages.importDealerSuccess', {
                            createdCount: importedCount.createdCount,
                            createdRoleCount: importedCount.createdRoleCount,
                            createdUserGroupCount: importedCount.createdUserGroupCount,
                        })}
                    </Typography>
                </Row>
            );
        }

        return (
            <Row align="middle" gutter={[14, 14]} justify="center">
                <Typography>{t('dealerList:modals.importDealer.message')}</Typography>
                <Container direction="horizontal">
                    <Button htmlType="button" onClick={onDownloadClick} type="link">
                        {t('dealerList:modals.importDealer.buttons.downloadTemplate')}
                    </Button>
                </Container>
            </Row>
        );
    }, [errors, importedCount, t, onDownloadClick]);

    const footer = useMemo(() => {
        const cancelButton = (
            <CancelButton key="close" onClick={handleOnClose}>
                {t('dealerList:modals.importDealer.buttons.cancel')}
            </CancelButton>
        );

        if (!isNil(errors) || !isNil(importedCount)) {
            return [cancelButton];
        }

        const okButton = (
            <ThemeUpload
                key="upload"
                accept={accept}
                beforeUpload={beforeUpload}
                disabled={isUploading}
                onChange={handleOnChange}
                showUploadList={false}
            >
                <OkButton disabled={isUploading} loading={isUploading}>
                    {isUploading
                        ? t('dealerList:modals.importDealer.buttons.uploading')
                        : t('dealerList:modals.importDealer.buttons.uploadFile')}
                </OkButton>
            </ThemeUpload>
        );

        if (theme === CompanyTheme.PorscheV3 || theme === CompanyTheme.Porsche) {
            return [okButton, cancelButton];
        }

        return [cancelButton, okButton];
    }, [accept, beforeUpload, errors, handleOnChange, handleOnClose, importedCount, isUploading, theme, t]);

    return (
        <Modal
            footer={footer}
            onCancel={handleOnClose}
            open={open}
            title={t('dealerList:modals.importDealer.title')}
            width={550}
        >
            {content}
        </Modal>
    );
};

export default ImportDealerModal;

export type ImportDealerProps = Pick<ImportDealerModalProps, 'onDownloadClick' | 'onImportSuccess' | 'companyId'>;

export const useImportDealerModal = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (props: ImportDealerProps) => <ImportDealerModal onClose={actions.close} open={visible} {...props} />,
    };
};

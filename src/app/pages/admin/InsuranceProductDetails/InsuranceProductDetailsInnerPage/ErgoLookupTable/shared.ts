import { ErgoLookupTableCell, ErgoLookupTableSettingsDetailsFragment, LocalVariant } from '../../../../../api';
import { InsuranceProductFormValues } from '../shared';

export type ErgoInsuranceProductFormValues = InsuranceProductFormValues & {
    ergoLookupTable: ErgoLookupTableSettingsDetailsFragment;
};
export type ErgoLookupRecord = Pick<ErgoLookupTableCell, 'modelSuiteId' | 'sumInsured' | 'totalPremium'> & {
    key: string;
    model: Pick<LocalVariant['model'], 'id' | 'name' | 'identifier'>;
};

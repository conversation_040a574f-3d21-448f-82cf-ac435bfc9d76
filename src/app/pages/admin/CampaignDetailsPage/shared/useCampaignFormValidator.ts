import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';

const validator = validators.compose(
    validators.requiredString('campaignId'),
    validators.requiredString('description.defaultValue'),
    validators.requiredBoolean('isActive')
);

const useCampaignFormValidator = () => useValidator(validator);

export default useCampaignFormValidator;

import { useMemo } from 'react';
import { useParams } from 'react-router';
import { useGetCampaignQuery } from '../../../api/queries/getCampaign';
import LoadingElement from '../../../components/LoadingElement';
import NotFoundResult from '../../../components/results/NotFoundResult';
import CampaignDetailsInnerPage from './CampaignDetailsInnerPage';

const CampaignDetailsPage = () => {
    const { campaignId } = useParams<{ campaignId: string }>();

    const { data, loading } = useGetCampaignQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            id: campaignId,
        },
    });

    const campaign = useMemo(() => data?.campaign || null, [data?.campaign]);

    if (!campaign) {
        return loading ? <LoadingElement /> : <NotFoundResult />;
    }

    return <CampaignDetailsInnerPage campaign={campaign} />;
};

export default CampaignDetailsPage;

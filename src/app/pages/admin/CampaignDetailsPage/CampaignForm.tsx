import { Col, Row } from 'antd';
import { useTranslation } from 'react-i18next';
import CollapsibleWrapper, { Panel } from '../../../components/wrappers/CollapsibleWrapper';
import FormFields from '../../../themes/admin/Fields/FormFields';
import useSystemSwitchData from '../../../utilities/useSystemSwitchData';

const colSpan = { lg: 8, md: 12, xs: 24 };

export type CampaignFormProps = {
    disabled: boolean;
};

const CampaignForm = ({ disabled }: CampaignFormProps) => {
    const { t } = useTranslation('campaignDetails');
    const { yesNoSwitch } = useSystemSwitchData();

    return (
        <CollapsibleWrapper defaultActiveKey="campaignMainDetails">
            <Panel key="campaignMainDetails" header={t('campaignDetails:sectionTitle')}>
                <Row gutter={16}>
                    <Col {...colSpan}>
                        <FormFields.InputField
                            {...t('campaignDetails:fields.campaignId', { returnObjects: true })}
                            disabled={disabled}
                            name="campaignId"
                            required
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.TranslatedInputField
                            {...t('campaignDetails:fields.description', { returnObjects: true })}
                            disabled={disabled}
                            name="description"
                            required
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.SwitchField
                            {...t('campaignDetails:fields.isActive', { returnObjects: true })}
                            disabled={disabled}
                            name="isActive"
                            {...yesNoSwitch}
                        />
                    </Col>
                </Row>
            </Panel>
        </CollapsibleWrapper>
    );
};

export default CampaignForm;

import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import { Formik } from 'formik';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
    CreateCampaignDocument,
    type CreateCampaignMutation,
    type CreateCampaignMutationVariables,
} from '../../../api/mutations/createCampaign';
import FormAutoTouch from '../../../components/FormAutoTouch';
import useLoadingButton from '../../../components/button/useLoadingButton';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import Form from '../../../components/fields/Form';
import NoItemResult from '../../../components/results/NoItemResult';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import useHandleError from '../../../utilities/useHandleError';
import CampaignForm from '../CampaignDetailsPage/CampaignForm';
import type { CampaignFormValues } from '../CampaignDetailsPage/shared/types';
import { defaultValues } from '../CampaignDetailsPage/shared/types';
import useCampaignFormValidator from '../CampaignDetailsPage/shared/useCampaignFormValidator';

const AddCampaignPage = () => {
    const { t } = useTranslation(['campaignDetails']);
    const navigate = useNavigate();
    const validate = useCampaignFormValidator();
    const { setLoading, LoadingButton } = useLoadingButton();
    const company = useCompany(true);

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError<CampaignFormValues>(
        async values => {
            if (!company) {
                return;
            }

            setLoading(true);

            message.loading({
                content: t('campaignDetails:messages.createSubmitting'),
                key: 'primary',
                duration: 0,
            });

            await apolloClient.mutate<CreateCampaignMutation, CreateCampaignMutationVariables>({
                mutation: CreateCampaignDocument,
                variables: {
                    companyId: company.id,
                    settings: values,
                },
            });

            message.success({
                content: t('campaignDetails:messages.createSuccessful'),
                key: 'primary',
            });

            navigate(`/admin/campaigns`);
        },
        [apolloClient, company, navigate, setLoading, t],
        undefined,
        () => {
            setLoading(false);
        }
    );

    if (!company || !company.availableModules.hasCapModules) {
        return (
            <ConsolePageWithHeader onBack={() => navigate('/admin/campaigns')} title={t('campaignDetails:title')}>
                <NoItemResult
                    subTitle={
                        company && !company.availableModules.hasCapModules
                            ? t('campaignDetails:capModuleNotAvailable')
                            : t('campaignDetails:companyNotSelected')
                    }
                />
            </ConsolePageWithHeader>
        );
    }

    return (
        <ConsolePageWithHeader
            footer={[
                <LoadingButton key="submit" form="campaignForm" htmlType="submit" type="primary">
                    {t('campaignDetails:actions.create')}
                </LoadingButton>,
            ]}
            onBack={() => navigate('/admin/campaigns')}
            title={t('campaignDetails:title')}
        >
            <Formik initialValues={defaultValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="campaignForm" name="campaignForm" onSubmitCapture={handleSubmit}>
                        <FormAutoTouch />
                        <CampaignForm disabled={false} />
                    </Form>
                )}
            </Formik>
        </ConsolePageWithHeader>
    );
};

export default AddCampaignPage;

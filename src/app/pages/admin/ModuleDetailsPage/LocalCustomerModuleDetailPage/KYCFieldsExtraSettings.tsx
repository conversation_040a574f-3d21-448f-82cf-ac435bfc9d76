import { InfoCircleOutlined } from '@ant-design/icons';
import { Row, Space, Typography } from 'antd';
import { isEmpty } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled, { css } from 'styled-components';
import { AgeCalculationMethod } from '../../../../api/types';
import RadioGroupField from '../../../../components/fields/RadioGroupField';
import CollapsibleWrapper, { Panel } from '../../../../components/wrappers/CollapsibleWrapper';
import { useThemeComponents } from '../../../../themes/hooks';
import { onNumericKeyPress } from '../../../../utilities/form';

const StyledPanel = styled(Panel)<{ $pageType: 'Portal' | 'Admin' }>`
    ${props =>
        props.$pageType === 'Admin' &&
        css`
            background: white;
            margin-bottom: 10px;
            &.ant-collapse-item {
                border: 0;
            }
        `}
`;

const ExtraSettingsFieldsContainer = styled.div`
    width: 100%;

    .ant-row.ant-form-item-row {
        flex-direction: row;
        align-items: center;
    }

    .ant-form-item-label {
        white-space: nowrap;
        text-align: end;
        padding: 0;
        flex: 0 0 200px;
    }

    .ant-form-item-control {
        flex: 1 1 0;
        min-width: 0;
    }

    .ant-form-item-label > label {
        justify-content: flex-start;

        &::after {
            display: block;
            content: ':';
            position: relative;
            margin-block: 0;
            margin-inline-start: 2px;
            margin-inline-end: 8px;
        }
    }
`;

type KYCFieldsExtraSettingsProps = {
    hasBirthdayField: boolean;
    showAdditionalSettings?: boolean;
    pageType: 'Portal' | 'Admin';
    prefixKey?: string;
    disabled?: boolean;
};
const KYCFieldsExtraSettings = ({
    hasBirthdayField,
    showAdditionalSettings = false,
    pageType,
    prefixKey,
    disabled = false,
}: KYCFieldsExtraSettingsProps) => {
    const { t } = useTranslation(['localCustomerModuleDetails']);
    const kycExtraSettingsTranslationKey = 'localCustomerModuleDetails:tabs.kycFields.extraSettings';
    const panelKey = 'localCustomerModuleDetails_kycFieldsForm_extraSettings';

    const {
        FormFields: { InputNumberField, SwitchField },
        Tooltip,
    } = useThemeComponents();
    const ageCalculationMethodOptions = [
        {
            label: (
                <Space>
                    {t(`${kycExtraSettingsTranslationKey}.options.ageCalculationMethod.birthdayBased.label`)}
                    <Tooltip
                        placement="top"
                        title={t(
                            `${kycExtraSettingsTranslationKey}.options.ageCalculationMethod.birthdayBased.tooltip`
                        )}
                    >
                        <InfoCircleOutlined />
                    </Tooltip>
                </Space>
            ),
            value: AgeCalculationMethod.BirthdayBased,
        },
        {
            label: (
                <Space>
                    {t(`${kycExtraSettingsTranslationKey}.options.ageCalculationMethod.calendarYearBased.label`)}
                    <Tooltip
                        placement="top"
                        title={t(
                            `${kycExtraSettingsTranslationKey}.options.ageCalculationMethod.calendarYearBased.tooltip`
                        )}
                    >
                        <InfoCircleOutlined />
                    </Tooltip>
                </Space>
            ),
            value: AgeCalculationMethod.CalendarYearBased,
        },
    ];

    const fieldPrefixKey = useMemo(() => (!isEmpty(prefixKey) ? `${prefixKey}.` : ''), [prefixKey]);

    return (
        <>
            {hasBirthdayField && (
                <CollapsibleWrapper defaultActiveKey={`${panelKey}_birthdayFields`}>
                    <StyledPanel
                        key={`${panelKey}_birthdayFields`}
                        $pageType={pageType}
                        header={
                            <Typography.Title level={5}>
                                {t(`${kycExtraSettingsTranslationKey}.title.ageValidationSettings`)}
                            </Typography.Title>
                        }
                    >
                        <Row gutter={[12, 12]}>
                            <ExtraSettingsFieldsContainer>
                                <InputNumberField
                                    {...t(`${kycExtraSettingsTranslationKey}.fields.minimumAgeRequirement`, {
                                        returnObjects: true,
                                    })}
                                    disabled={disabled}
                                    max={99}
                                    min={0}
                                    name={`${fieldPrefixKey}minimumAge`}
                                    onKeyPress={onNumericKeyPress}
                                    style={{ maxWidth: '8rem' }}
                                    required
                                />

                                <RadioGroupField
                                    {...t(`${kycExtraSettingsTranslationKey}.fields.ageCalculationMethod`, {
                                        returnObjects: true,
                                    })}
                                    disabled={disabled}
                                    name={`${fieldPrefixKey}ageCalculationMethod`}
                                    optionType="default"
                                    options={ageCalculationMethodOptions}
                                    style={{ display: 'flex', flexDirection: 'column' }}
                                    required
                                />
                            </ExtraSettingsFieldsContainer>
                        </Row>
                    </StyledPanel>
                </CollapsibleWrapper>
            )}

            {showAdditionalSettings && (
                <CollapsibleWrapper defaultActiveKey={panelKey}>
                    <StyledPanel
                        key={panelKey}
                        $pageType={pageType}
                        header={
                            <Typography.Title level={5}>
                                {t(`${kycExtraSettingsTranslationKey}.title.additionalKycSettings`)}
                            </Typography.Title>
                        }
                    >
                        <Row gutter={[12, 12]}>
                            <ExtraSettingsFieldsContainer>
                                <SwitchField
                                    {...t(`${kycExtraSettingsTranslationKey}.fields.mobileVerification`, {
                                        returnObjects: true,
                                    })}
                                    disabled={disabled}
                                    name={`${fieldPrefixKey}mobileVerification`}
                                    required
                                />
                            </ExtraSettingsFieldsContainer>
                        </Row>
                    </StyledPanel>
                </CollapsibleWrapper>
            )}
        </>
    );
};

export default KYCFieldsExtraSettings;

import { useTranslation } from 'react-i18next';
import { SalesControlBoardModuleSpecsFragment } from '../../../../../api/fragments/SalesControlBoardModuleSpecs';
import {
    CreateSalesControlBoardModuleDocument,
    CreateSalesControlBoardModuleMutation,
    CreateSalesControlBoardModuleMutationVariables,
} from '../../../../../api/mutations/createSalesControlBoardModule';
import InputField from '../../../../../components/fields/InputField';
import validators from '../../../../../utilities/validators';
import SalesControlBoardPage, { SalesControlBoardModulePageProps } from '../../SalesControlBoardModulePage';
import { ModuleImplementation } from './types';

const implementation: ModuleImplementation<SalesControlBoardModuleSpecsFragment, { displayName: string }> = {
    PageComponent: ({ module }: SalesControlBoardModulePageProps) => <SalesControlBoardPage module={module} />,
    getTitle: t => t('moduleDetails:implementations.SalesControlBoardModule.className'),
    getValidator: () => validators.requiredNonEmptyString('displayName'),
    FormComponent: () => {
        const { t } = useTranslation(['moduleDetails']);

        return (
            <InputField
                {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                name="displayName"
                required
            />
        );
    },
    create: async (apolloClient, companyId, values) =>
        apolloClient
            .mutate<CreateSalesControlBoardModuleMutation, CreateSalesControlBoardModuleMutationVariables>({
                mutation: CreateSalesControlBoardModuleDocument,
                variables: {
                    companyId,
                    displayName: values.displayName,
                },
            })
            .then(({ data }) => data.module),
};

export default implementation;

import { DeleteOutlined, DownOutlined } from '@ant-design/icons';
import { Button, Dropdown, Menu, Space } from 'antd';
import { useTranslation } from 'react-i18next';
import { SalesControlBoardModuleSpecsFragment } from '../../../../api';
import { PermissionValues } from '../../../../utilities/hasPermissions';
import useDeleteModule from '../useDeleteModule';

export type SalesControlBoardModuleActionsProps = {
    module: SalesControlBoardModuleSpecsFragment;
    permissions: Pick<PermissionValues, 'hasDeletePermission'>;
};

const SalesControlBoardModuleActions = ({ module, permissions }: SalesControlBoardModuleActionsProps) => {
    const { t } = useTranslation(['salesControlBoardModuleDetails']);
    const deleteModule = useDeleteModule(module.id);
    const { hasDeletePermission } = permissions;

    const items = [
        hasDeletePermission && {
            label: t('salesControlBoardModuleDetails:mainActionButtons.delete'),
            key: 'delete',
            icon: <DeleteOutlined />,
            onClick: deleteModule,
            danger: true,
        },
    ].filter(Boolean);

    if (!items.length) {
        return null;
    }

    return (
        <Dropdown overlay={<Menu items={items} />}>
            <Button type="primary">
                <Space>
                    <span>{t('salesControlBoardModuleDetails:mainActionButtons.actions')}</span>
                    <DownOutlined />
                </Space>
            </Button>
        </Dropdown>
    );
};

export default SalesControlBoardModuleActions;

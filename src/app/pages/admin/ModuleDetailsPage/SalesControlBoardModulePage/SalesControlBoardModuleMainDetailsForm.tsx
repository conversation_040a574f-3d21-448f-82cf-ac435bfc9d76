import { Col } from 'antd';
import { useTranslation } from 'react-i18next';
import InputField from '../../../../components/fields/InputField';
import { colSpan } from '../../WebpageDetailsPage/WebpageBlock/ColumnListBlock';
import { StyledContainer } from '../shared/ui';

const SalesControlBoardModuleMainDetailsForm = () => {
    const { t } = useTranslation(['moduleDetails']);

    return (
        <StyledContainer gutter={10}>
            <Col {...colSpan}>
                <InputField
                    {...t('moduleDetails:fields.displayName', { returnObjects: true })}
                    name="displayName"
                    required
                />
            </Col>
        </StyledContainer>
    );
};

export default SalesControlBoardModuleMainDetailsForm;

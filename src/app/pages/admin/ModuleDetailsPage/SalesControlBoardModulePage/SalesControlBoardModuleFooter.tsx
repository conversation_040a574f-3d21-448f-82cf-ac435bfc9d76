import { Button } from 'antd';
import { useTranslation } from 'react-i18next';

const SalesControlBoardModuleFooter = () => {
    const { t } = useTranslation(['salesControlBoardModuleDetails']);

    return (
        <Button form="updateMainDetails" htmlType="submit" type="primary">
            {t('salesControlBoardModuleDetails:actions.update')}
        </Button>
    );
};

export default SalesControlBoardModuleFooter;

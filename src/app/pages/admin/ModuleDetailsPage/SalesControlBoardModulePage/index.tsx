import { useApolloClient } from '@apollo/client';
import { Descriptions, Grid, message } from 'antd';
import { Formik } from 'formik';
import { pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import * as permissionKind from '../../../../../shared/permissions';
// eslint-disable-next-line max-len
import { SalesControlBoardModuleWithPermissionsSpecsFragment } from '../../../../api/fragments/SalesControlBoardModuleWithPermissionsSpecs';
import {
    UpdateSalesControlBoardModuleDocument,
    UpdateSalesControlBoardModuleMutation,
    UpdateSalesControlBoardModuleMutationVariables,
} from '../../../../api/mutations/updateSalesControlBoardModule';
import Form from '../../../../components/fields/Form';
import ConsolePageWithHeader from '../../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../../utilities/hasPermissions';
import useFormattedSimpleVersioning from '../../../../utilities/useFormattedSimpleVersioning';
import useHandleError from '../../../../utilities/useHandleError';
import useValidator from '../../../../utilities/useValidator';
import validators from '../../../../utilities/validators';
import SalesControlBoardModuleActions from './SalesControlBoardModuleActions';
import SalesControlBoardModuleFooter from './SalesControlBoardModuleFooter';
import SalesControlBoardModuleMainDetailsForm from './SalesControlBoardModuleMainDetailsForm';

export type FormValues = {
    displayName: string;
};
export type SalesControlBoardModulePageProps = { module: SalesControlBoardModuleWithPermissionsSpecsFragment };

const SalesControlBoardModulePage = ({ module }: SalesControlBoardModulePageProps) => {
    const { t } = useTranslation(['salesControlBoardModuleDetails', 'moduleDetails', 'common']);
    const navigate = useNavigate();
    const { updated, offset } = useFormattedSimpleVersioning({ versioning: module.versioning, moduleId: module.id });

    const permissions = {
        hasDeletePermission: hasPermissions(module.permissions, [permissionKind.deleteModule]),
    };
    const screens = Grid.useBreakpoint();

    const content = (
        <Descriptions column={{ xs: 1, md: 2 }} bordered>
            <Descriptions.Item label={t('moduleDetails:fields.displayName.label')}>
                {module.displayName}
            </Descriptions.Item>

            <Descriptions.Item {...t('common:fields.updated', { returnObjects: true, offset })}>
                {updated}
            </Descriptions.Item>
        </Descriptions>
    );

    const initialValues = useMemo((): FormValues => pick(['displayName'], module), [module]);

    const apolloClient = useApolloClient();

    const onSubmit = useHandleError<FormValues>(
        async values => {
            // submitting message
            message.loading({
                content: t('salesControlBoardModuleDetails:messages.updatingSalesControlBoardModule'),
                key: 'primary',
                duration: 0,
            });

            // submit creation
            await apolloClient
                .mutate<UpdateSalesControlBoardModuleMutation, UpdateSalesControlBoardModuleMutationVariables>({
                    mutation: UpdateSalesControlBoardModuleDocument,
                    variables: {
                        moduleId: module.id,
                        displayName: values.displayName,
                    },
                })
                .finally(() => {
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('salesControlBoardModuleDetails:messages.updateSalesControlBoardModuleSuccessful'),
                key: 'primary',
            });
        },
        [apolloClient, module.id, t]
    );

    const validate = useValidator(validators.requiredNonEmptyString('displayName'));

    return (
        <ConsolePageWithHeader
            content={content}
            extra={<SalesControlBoardModuleActions module={module} permissions={permissions} />}
            footer={[<SalesControlBoardModuleFooter />]}
            onBack={() => navigate('/admin/system/modules')}
            title={
                screens.md
                    ? t('salesControlBoardModuleDetails:moduleTitle', { name: module.displayName })
                    : module.displayName
            }
        >
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="updateMainDetails" name="updateMainDetails" onSubmitCapture={handleSubmit}>
                        <SalesControlBoardModuleMainDetailsForm />
                    </Form>
                )}
            </Formik>
        </ConsolePageWithHeader>
    );
};

export default SalesControlBoardModulePage;

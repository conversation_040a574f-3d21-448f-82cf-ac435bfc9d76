import { useReducer } from 'react';
import {
    type CampaignFilteringRule,
    type CampaignSortingRule,
    CampaignSortingField,
    SortingOrder,
} from '../../../api/types';
import type { ExtendedPageState, ExtendedPageAction } from '../../../components/PaginatedTableWithContext';
import { extendedReducer } from '../../../components/PaginatedTableWithContext';
import { TableSortingOrder, type SortOrderValue } from '../../../utilities/useSortAndFilterCache';

type SortRule = CampaignSortingRule & SortOrderValue;

type State = ExtendedPageState & {
    sort: SortRule;
    filter: CampaignFilteringRule;
};

export type SetSortAction = { type: 'setSort'; sortBy: SortRule };

export type SetFilterAction = { type: 'setFilter'; filterBy: CampaignFilteringRule };

export type Action = ExtendedPageAction | SetSortAction | SetFilterAction;

const reducer = (state: State, action: Action): State => {
    switch (action.type) {
        case 'setPage':
            return { ...state, page: action.page };

        case 'setPageSize':
            return { ...state, page: 1, pageSize: action.pageSize };

        case 'setSort':
            return { ...state, page: 1, sort: action.sortBy };

        case 'setFilter':
            return { ...state, page: 1, filter: action.filterBy };

        default:
            return extendedReducer(state, action);
    }
};

const useListReducer = (cache?: State) =>
    useReducer(reducer, {
        companyId: cache?.companyId,
        page: cache?.page || 1,
        pageSize: cache?.pageSize || 10,

        sort: cache?.sort || {
            field: 'CampaignId' as CampaignSortingField,
            order: 'Asc' as SortingOrder,
            orderValue: 'ascend' as TableSortingOrder,
        },

        filter: cache?.filter || {},
    });

export default useListReducer;

import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../shared/permissions';
import { useAccount } from '../../../components/contexts/AccountContextManager';
import NoItemResult from '../../../components/results/NoItemResult';
import hasPermissions from '../../../utilities/hasPermissions';
import useGoTo from '../../../utilities/useGoTo';

const CampaignEmptyListPage = () => {
    const { t } = useTranslation('campaignList');
    const { permissions: accountPermissions } = useAccount();

    const goToNewCampaignPage = useGoTo('/admin/campaigns/add');

    return (
        <NoItemResult
            extra={
                hasPermissions(accountPermissions, [permissionKind.manageCampaigns]) ? (
                    <Button onClick={goToNewCampaignPage} type="primary">
                        {t('campaignList:noData.action')}
                    </Button>
                ) : null
            }
            subTitle={t('campaignList:noData.message')}
        />
    );
};

export default CampaignEmptyListPage;

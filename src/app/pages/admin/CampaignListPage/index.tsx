import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../shared/permissions';
import { useAccount } from '../../../components/contexts/AccountContextManager';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import hasPermissions from '../../../utilities/hasPermissions';
import useGoTo from '../../../utilities/useGoTo';
import CampaignList from './CampaignList';

const CampaignListPage = () => {
    const { t } = useTranslation('campaignList');
    const company = useCompany(true);

    const goToNewCampaignPage = useGoTo('/admin/campaigns/add');
    const { permissions: accountPermissions } = useAccount();

    const extra = useMemo(
        () =>
            company &&
            company.availableModules.hasCapModules &&
            hasPermissions(accountPermissions, [permissionKind.manageCampaigns]) ? (
                <Button icon={<PlusOutlined />} onClick={goToNewCampaignPage} type="primary">
                    {t('campaignList:actions.newCampaign')}
                </Button>
            ) : null,
        [accountPermissions, company, goToNewCampaignPage, t]
    );

    return (
        <ConsolePageWithHeader extra={extra} title={t('campaignList:title')}>
            <CampaignList />
        </ConsolePageWithHeader>
    );
};

export default CampaignListPage;

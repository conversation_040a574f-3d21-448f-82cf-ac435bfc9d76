import { Table, type TableProps } from 'antd';
import { pick } from 'lodash/fp';
import { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import type { CampaignDataFragment } from '../../../api/fragments/CampaignData';
import { useListCampaignsQuery } from '../../../api/queries/listCampaigns';
import { CampaignSortingField } from '../../../api/types';
import PaginatedTableWithContext from '../../../components/PaginatedTableWithContext';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import InternalErrorResult from '../../../components/results/InternalErrorResult';
import useSearchInDropDown from '../../../components/useSearchInDropDown';
import { checkHasFilter } from '../../../utilities/common';
import makeGetSortingRule from '../../../utilities/makeGetSortingRule';
import renderBooleanIcon from '../../../utilities/renderBooleanIcon';
import renderSearchIcon from '../../../utilities/renderSearchIcon';
import useSortAndFilterCache, { SortAndFilterCacheKey } from '../../../utilities/useSortAndFilterCache';
import CampaignEmptyListPage from './CampaignEmptyListPage';
import useListReducer from './useListReducer';

const getSortField = makeGetSortingRule((field): CampaignSortingField => {
    switch (field) {
        case 'campaignId':
            return CampaignSortingField.CampaignId;

        case 'description':
            return CampaignSortingField.Description;

        case 'isActive':
            return CampaignSortingField.IsActive;

        default:
            throw new Error('Unknown Sorting Field!');
    }
});

const CampaignList = () => {
    const { t } = useTranslation('campaignList');

    const navigate = useNavigate();

    const company = useCompany(true);

    const [currentCache, setCache] = useSortAndFilterCache(SortAndFilterCacheKey.Campaign);

    const [state, dispatch] = useListReducer(currentCache);

    const { page, pageSize, sort, filter } = state;

    const { data, loading, error } = useListCampaignsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            pagination: { offset: (page - 1) * pageSize, limit: pageSize },
            sort: pick(['field', 'order'], sort),
            filter: { ...filter, companyId: company?.id },
        },
    });

    const onChange: TableProps<CampaignDataFragment>['onChange'] = useCallback(
        (pagination, filters, sorter, extra) => {
            switch (extra.action) {
                case 'sort':
                    return dispatch({
                        type: 'setSort',
                        sortBy: getSortField(sorter),
                    });

                case 'filter':
                    return dispatch({
                        type: 'setFilter',
                        filterBy: {
                            campaignId: filters.campaignId ? (filters.campaignId[0] as string) : undefined,
                            companyId: filters.companyId ? (filters.companyId[0] as string) : undefined,
                            description: filters.description ? (filters.description[0] as string) : undefined,
                        },
                    });

                default:
                    return undefined;
            }
        },
        [dispatch]
    );

    const searchBox = useSearchInDropDown();

    const dataSource = useMemo(() => (data?.list?.items || []).map(item => ({ ...item, key: item.id })), [data]);

    const total = data?.list?.count || 0;

    if (!loading) {
        if (error) {
            return <InternalErrorResult />;
        }

        if (total === 0 && !checkHasFilter(filter)) {
            return <CampaignEmptyListPage />;
        }
    }

    return (
        <PaginatedTableWithContext
            company={company}
            dataSource={dataSource}
            dispatch={dispatch}
            loading={loading}
            onChange={onChange}
            onRow={(row: CampaignDataFragment) => ({
                onClick: () => {
                    setCache(state);
                    navigate(`/admin/campaigns/${row.id}`);
                },
                style: { cursor: 'pointer' },
            })}
            rowKey="id"
            state={state}
            tableName={t('campaignList:tableName')}
            total={total}
        >
            {!company && (
                <Table.Column
                    key="company"
                    dataIndex={['company', 'displayName']}
                    title={t('campaignList:columns.company')}
                />
            )}
            <Table.Column
                key="campaignId"
                dataIndex="campaignId"
                filterDropdown={searchBox.render(t('campaignList:columns.campaignId'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter?.campaignId ? [filter.campaignId] : undefined}
                onFilterDropdownOpenChange={searchBox.autoFocus}
                sortOrder={sort?.field === CampaignSortingField.CampaignId ? sort.orderValue : undefined}
                title={t('campaignList:columns.campaignId')}
                sorter
            />
            <Table.Column
                key="description"
                dataIndex="description"
                filterDropdown={searchBox.render(t('campaignList:columns.description'))}
                filterIcon={renderSearchIcon}
                filteredValue={filter?.description ? [filter.description] : undefined}
                onFilterDropdownOpenChange={searchBox.autoFocus}
                render={item => item?.defaultValue || '-'}
                sortOrder={sort?.field === CampaignSortingField.Description ? sort.orderValue : undefined}
                title={t('campaignList:columns.description')}
                sorter
            />
            <Table.Column
                key="isActive"
                align="center"
                dataIndex="isActive"
                render={value => renderBooleanIcon(value, true)}
                sortOrder={sort?.field === CampaignSortingField.IsActive ? sort.orderValue : undefined}
                title={t('campaignList:columns.active')}
                width={120}
                sorter
            />
        </PaginatedTableWithContext>
    );
};

export default CampaignList;

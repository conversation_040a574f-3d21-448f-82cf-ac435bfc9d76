import { useTranslation } from 'react-i18next';
import { ModuleType } from '../../../api';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../components/contexts/DealerContextManager';
import NoItemResult from '../../../components/results/NoItemResult';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import NotFoundPage from '../../portal/NotFoundPage';
import SalesControlBoardContent from './Content';
import useImportModal from './ImportAction/useImportModal';
import SalesControlBoardManager from './SalesControlBoardManager';
import usePermission from './usePermission';

const SalesControlBoard = () => {
    const { t } = useTranslation(['salesControlBoard']);

    const company = useCompany(true);
    const { dealerIds } = useMultipleDealerIds();
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const importModal = useImportModal();

    if (!company || !company.modules.find(i => i.__typename === ModuleType.SalesControlBoardModule)) {
        return <NoItemResult subTitle={`${t('salesControlBoard:noCompanySelected')}`} />;
    }

    if (dealerIds?.length !== 1) {
        return <NoItemResult subTitle={`${t('salesControlBoard:noDealerSelected')}`} />;
    }

    if (!hasSalesManagerPermission && !hasSalesConsultantPermission) {
        return <NotFoundPage />;
    }

    return (
        <ConsolePageWithHeader isDashboard>
            <SalesControlBoardManager>
                <SalesControlBoardContent open={importModal.open} />
                {importModal.render()}
            </SalesControlBoardManager>
        </ConsolePageWithHeader>
    );
};

export default SalesControlBoard;

import { breakpointS } from '@porsche-design-system/components-react/styles';
import { Space, Col } from 'antd';
import dayjs from 'dayjs';
import { pick } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CollapsibleContentItem } from '../../../portal/LaunchPadApplicationEntrypoint/ui/ContentItem';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import useContainerWidth from '../useContainerWidth';
import usePermission from '../usePermission';
import Chart, { ChartData } from './Chart';
import Table, { TableData } from './Table';

type WeeklySalesFunnelProps = {
    isForExportPdf?: boolean;
};
const WeekSalesFunnel = ({ isForExportPdf = false }: WeeklySalesFunnelProps) => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission } = usePermission();

    const { data, monthOfImportOptions } = useSalesControlBoardContext();

    const { containerRef, containerWidth } = useContainerWidth();
    const gridTemplateColumns = useMemo(() => {
        if (containerWidth >= breakpointS) {
            return 'repeat(2, minmax(0, 1fr))';
        }

        return 'repeat(1, minmax(0, 1fr))';
    }, [containerWidth]);

    if (!hasSalesManagerPermission || monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <Space direction="vertical" size={36} style={{ width: '100%' }}>
            {(data?.weekFunnels || []).map((week, index) => (
                <CollapsibleContentItem
                    // eslint-disable-next-line react/no-array-index-key
                    key={index.toString()}
                    title={t('salesControlBoard:weekSalesFunnel.title', {
                        count: index + 1,
                        dateRange: dayjs(week.start).isSame(dayjs(week.end), 'day')
                            ? dayjs(week.start).format('D MMMM YYYY')
                            : `${dayjs(week.start).format('D')} - ${dayjs(week.end).format('D MMMM YYYY')}`,
                    })}
                    defaultOpen
                >
                    <Col
                        ref={containerRef}
                        span={24}
                        style={{ containerType: 'inline-size', containerName: 'weekFunnel' }}
                    >
                        <div
                            style={{
                                display: 'grid',
                                gap: '24px',
                                gridTemplateColumns,
                            }}
                        >
                            <Chart
                                data={
                                    pick(
                                        ['leadsCreated', 'testDrives', 'salesOffers', 'orderIntakes', 'retails'],
                                        week
                                    ) as ChartData
                                }
                                isForExportPdf={isForExportPdf}
                            />
                            <Table
                                data={
                                    pick(
                                        [
                                            'leadToTestDriveRate',
                                            'testDriveToSalesOfferRate',
                                            'salesOfferToOrderIntakeRate',
                                            'orderIntakeToRetailRate',
                                        ],
                                        week
                                    ) as TableData
                                }
                            />
                        </div>
                    </Col>
                </CollapsibleContentItem>
            ))}
        </Space>
    );
};

export default WeekSalesFunnel;

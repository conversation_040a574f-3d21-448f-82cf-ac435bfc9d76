import { PHeading, PText } from '@porsche-design-system/components-react';
import { themeLightBackgroundSurface, themeLightContrastLow } from '@porsche-design-system/components-react/styles';
import { Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

const Container = styled.div`
    background-color: ${themeLightBackgroundSurface};
    border-radius: 12px;
    padding: 16px;
`;

const ConversionRateWrapper = styled.div<{ last: boolean }>`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid ${themeLightContrastLow};
    margin-bottom: ${({ last }) => (last ? '24px' : '0')};

    & p-text {
        flex: 1;
    }
`;

export type TableData = {
    leadToTestDriveRate: number;
    testDriveToSalesOfferRate: number;
    salesOfferToOrderIntakeRate: number;
    orderIntakeToRetailRate: number;
};

type TableProps = {
    data: TableData;
};
const Table = ({ data }: TableProps) => {
    const { t } = useTranslation('salesControlBoard');
    const result = useMemo(
        () => [
            {
                label: t('salesControlBoard:weekSalesFunnel.leadToTestDrive'),
                value: data.leadToTestDriveRate,
            },
            {
                label: t('salesControlBoard:weekSalesFunnel.testDriveToSalesOffer'),
                value: data.testDriveToSalesOfferRate,
            },
            {
                label: t('salesControlBoard:weekSalesFunnel.salesOfferToOrderIntake'),
                value: data.salesOfferToOrderIntakeRate,
            },
            {
                label: t('salesControlBoard:weekSalesFunnel.orderIntakeToRetail'),
                value: data.orderIntakeToRetailRate,
            },
        ],
        [
            data.leadToTestDriveRate,
            data.orderIntakeToRetailRate,
            data.salesOfferToOrderIntakeRate,
            data.testDriveToSalesOfferRate,
            t,
        ]
    );

    return (
        <Container>
            <Space direction="vertical" size={10} style={{ width: '100%' }}>
                <PHeading size="small" tag="h2">
                    {t('salesControlBoard:weekSalesFunnel.conversionRate')}
                </PHeading>
                {result.map((item, index) => (
                    <ConversionRateWrapper key={item.label} last={index === result.length - 1}>
                        <PText size="small">{item.label}</PText>
                        <PText size="small">{item.value}%</PText>
                    </ConversionRateWrapper>
                ))}
            </Space>
        </Container>
    );
};

export default Table;

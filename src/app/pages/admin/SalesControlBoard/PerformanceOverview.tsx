import { PHeading } from '@porsche-design-system/components-react';
import { breakpointS, breakpointXS, themeLightBackgroundSurface } from '@porsche-design-system/components-react/styles';
import { Col } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { SalesControlBoardModelTotalPerformance } from '../../../api/types';
import { CollapsibleContentItem } from '../../portal/LaunchPadApplicationEntrypoint/ui/ContentItem';
import { useSalesControlBoardContext } from './SalesControlBoardManager';
import useSelectedMonth from './SalesControlBoardManager/useSelectedMonth';
import { OverviewBlock } from './ui';
import useContainerWidth from './useContainerWidth';
import usePermission from './usePermission';

const PerformanceOverview = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const { monthOfImportOptions, data } = useSalesControlBoardContext();
    const selectedMonth = useSelectedMonth();

    // write a summation of data?.performance?.model of each properties
    const totalModelData = useMemo(
        () =>
            data?.performance?.model.reduce(
                (acc, item) => {
                    Object.keys(item).forEach(key => {
                        if (key !== 'modelName' && key !== '__typename') {
                            acc[key] = (acc[key] || 0) + (item[key] || 0);
                        }
                    });

                    acc.modelName = 'Total';

                    return acc;
                },
                { modelName: '' } as SalesControlBoardModelTotalPerformance
            ),
        [data?.performance?.model]
    );

    const overviewData = useMemo(
        () => [
            {
                label: t('salesControlBoard:performanceOverview.leadsCreated'),
                value: totalModelData?.totalLeadActual ?? 0,
                color: themeLightBackgroundSurface,
            },
            {
                label: t('salesControlBoard:performanceOverview.testDrives'),
                value: totalModelData?.totalTestDriveActual ?? 0,
                color: '#FFE2E4',
            },
            {
                label: t('salesControlBoard:performanceOverview.salesOffers'),
                value: totalModelData?.totalSalesOfferActual ?? 0,
                color: '#FFF4D2',
            },
            {
                label: t('salesControlBoard:performanceOverview.orderIntakes'),
                value: totalModelData?.totalOrderIntakeActual ?? 0,
                color: '#D3E1FF',
            },
            {
                label: t('salesControlBoard:performanceOverview.retails'),
                value: totalModelData?.totalRetailActual ?? 0,
                color: '#FFE2D2',
            },
        ],
        [
            t,
            totalModelData?.totalLeadActual,
            totalModelData?.totalOrderIntakeActual,
            totalModelData?.totalRetailActual,
            totalModelData?.totalSalesOfferActual,
            totalModelData?.totalTestDriveActual,
        ]
    );

    const title = useMemo(() => {
        if (hasSalesManagerPermission) {
            return t('salesControlBoard:performanceOverview.titleForManager', {
                selectedMonth,
            });
        }

        if (hasSalesConsultantPermission) {
            return t('salesControlBoard:performanceOverview.titleForConsultant', {
                selectedMonth,
            });
        }

        return '';
    }, [hasSalesManagerPermission, hasSalesConsultantPermission, t, selectedMonth]);

    const { containerRef, containerWidth } = useContainerWidth();
    const gridTemplateColumns = useMemo(() => {
        if (containerWidth >= breakpointS) {
            return 'repeat(5, minmax(0, 1fr))';
        }

        if (containerWidth >= breakpointXS && containerWidth < breakpointS) {
            return 'repeat(3, minmax(0, 1fr))';
        }

        return 'repeat(1, minmax(0, 1fr))';
    }, [containerWidth]);

    if (monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <CollapsibleContentItem title={title} defaultOpen>
            <Col
                ref={containerRef}
                span={24}
                style={{ containerType: 'inline-size', containerName: 'performanceOverview' }}
            >
                <div
                    style={{
                        display: 'grid',
                        gap: '24px',
                        gridTemplateColumns,
                    }}
                >
                    {overviewData.map(item => (
                        <OverviewBlock key={item.label} color={item.color}>
                            <PHeading size="small">{item.label}</PHeading>
                            <PHeading size="large">{item.value}</PHeading>
                        </OverviewBlock>
                    ))}
                </div>
            </Col>
        </CollapsibleContentItem>
    );
};

export default PerformanceOverview;

import * as permissionKind from '../../../../shared/permissions';
import { useAccount } from '../../../components/contexts/AccountContextManager';
import hasPermissions from '../../../utilities/hasPermissions';

const usePermission = () => {
    const { permissions: accountPermissions } = useAccount();

    const hasSalesManagerPermission = hasPermissions(accountPermissions, [permissionKind.viewSalesControlBoardManager]);
    const hasSalesConsultantPermission = hasPermissions(accountPermissions, [
        permissionKind.viewSalesControlBoardSalesConsultant,
    ]);

    return { hasSalesManagerPermission, hasSalesConsultantPermission };
};

export default usePermission;

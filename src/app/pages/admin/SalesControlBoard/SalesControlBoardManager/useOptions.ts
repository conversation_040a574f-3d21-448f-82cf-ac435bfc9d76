import { useEffect, useMemo } from 'react';
import { useGetMonthOfImportOptionsQuery } from '../../../../api/queries/getMonthOfImportOptions';
import { useGetSalesControlBoardFilterOptionsQuery } from '../../../../api/queries/getSalesControlBoardFilterOptions';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';

const useFilterOptions = (state, dispatch) => {
    const { dealerIds } = useMultipleDealerIds();

    const {
        data: monthOfImportData,
        loading: monthOfImportLoading,
        refetch: refetchMonthOfImport,
    } = useGetMonthOfImportOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            dealerId: dealerIds[0],
        },
        skip: !dealerIds.length || dealerIds.length > 1,
    });

    const { data: filterOptionsData, loading: filterOptionsDataLoading } = useGetSalesControlBoardFilterOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            dealerId: dealerIds[0],
        },
        skip: !dealerIds.length || dealerIds.length > 1,
    });

    const monthOfImportOptions = useMemo(
        () =>
            (monthOfImportData?.monthOfImportOptions ?? []).map(i => ({
                value: i.value,
                label: i.label,
            })),
        [monthOfImportData?.monthOfImportOptions]
    );

    useEffect(() => {
        if (monthOfImportOptions?.length && state.filters.monthOfImport === '') {
            dispatch({ type: 'setFilters', updates: { monthOfImport: monthOfImportOptions[0]?.value } });
        }
    }, [monthOfImportOptions, dispatch, state.filters.monthOfImport]);

    const salesConsultantOptions = useMemo(
        () =>
            (filterOptionsData?.getSalesControlBoardFilterOptions?.salesConsultantOptions ?? []).map(i => ({
                value: i.value,
                label: i.label,
            })),
        [filterOptionsData?.getSalesControlBoardFilterOptions?.salesConsultantOptions]
    );

    const vehicleModelOptions = useMemo(
        () =>
            (filterOptionsData?.getSalesControlBoardFilterOptions?.vehicleModelOptions ?? []).map(i => ({
                value: i.value,
                label: i.label,
            })),
        [filterOptionsData?.getSalesControlBoardFilterOptions?.vehicleModelOptions]
    );

    return {
        monthOfImportOptions,
        salesConsultantOptions,
        vehicleModelOptions,
        monthOfImportLoading,
        refetchMonthOfImport,
        filterOptionsDataLoading,
    };
};

export default useFilterOptions;

import dayjs from 'dayjs';
import { useMemo } from 'react';
import { ReportingViewType } from './useSalesControlBoardReducer';
import { useSalesControlBoardContext } from '.';

const useSelectedMonth = () => {
    const { state } = useSalesControlBoardContext();

    return useMemo(() => {
        const selectedMonthText = state.filters.monthOfImport
            ? dayjs(state.filters.monthOfImport).format('MMMM YYYY')
            : '';

        const ytd = state.filters.reportingView === ReportingViewType.YTD ? ' YTD' : '';

        return `${selectedMonthText}${ytd}`;
    }, [state.filters.monthOfImport, state.filters.reportingView]);
};

export default useSelectedMonth;

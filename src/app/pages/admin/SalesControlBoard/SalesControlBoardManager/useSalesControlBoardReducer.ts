export enum ReportingViewType {
    Month = 'month',
    YTD = 'ytd',
}

export type SalesControlBoardState = {
    filters: {
        monthOfImport: string;
        reportingView: ReportingViewType;
        salesConsultantIds: string[];
        vehicleModelIds: string[];
    };
};

export type SalesControlBoardAction = { type: 'setFilters'; updates: Partial<SalesControlBoardState['filters']> };

export const initialSalesControlBoardState: SalesControlBoardState = {
    filters: {
        monthOfImport: '',
        reportingView: ReportingViewType.Month,
        salesConsultantIds: [],
        vehicleModelIds: [],
    },
};

export const reducer = (state: SalesControlBoardState, action: SalesControlBoardAction): SalesControlBoardState => {
    switch (action.type) {
        case 'setFilters':
            return {
                ...state,
                filters: {
                    ...state.filters,
                    ...action.updates,
                },
            };

        default:
            return state;
    }
};

import { PHeading, PText } from '@porsche-design-system/components-react';
import {
    themeLightNotificationSuccessSoft,
    themeLightNotificationInfoSoft,
    themeLightNotificationWarningSoft,
    themeLightNotificationErrorSoft,
    themeLightNotificationSuccess,
    themeLightNotificationInfo,
    themeLightNotificationWarning,
    themeLightNotificationError,
} from '@porsche-design-system/components-react/styles';
import { Row, Col } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import ProgressBar from '../../../components/ProgressBar';
import { ContentItem, ContentItemTitle } from '../../portal/LaunchPadApplicationEntrypoint/ui/ContentItem';
import { useSalesControlBoardContext } from './SalesControlBoardManager';
import { ReportingViewType } from './SalesControlBoardManager/useSalesControlBoardReducer';
import useSelectedMonth from './SalesControlBoardManager/useSelectedMonth';
import { Badge, ProgressWrapper } from './ui';
import usePermission from './usePermission';

const getBadgeColor = (value: number) => {
    if (value >= 100) {
        return themeLightNotificationSuccess;
    }

    if (value >= 80 && value <= 99) {
        return themeLightNotificationInfo;
    }

    if (value >= 50 && value <= 79) {
        return themeLightNotificationWarning;
    }

    return themeLightNotificationError;
};

const getBadgeBgColor = (value: number) => {
    if (value >= 100) {
        return themeLightNotificationSuccessSoft;
    }

    if (value >= 80 && value <= 99) {
        return themeLightNotificationInfoSoft;
    }

    if (value >= 50 && value <= 79) {
        return themeLightNotificationWarningSoft;
    }

    return themeLightNotificationErrorSoft;
};

const ProgressToGoal = () => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const { monthOfImportOptions, data, state } = useSalesControlBoardContext();
    const selectedMonth = useSelectedMonth();

    const {
        retailTargetMonth,
        retailActualMonth,
        retailMonthRate,
        retailMonthDev,
        retailTargetYtd,
        retailActualYtd,
        retailYtdRate,
        retailYtdDev,
        financeTarget,
        financeActualRate,
        insuranceTarget,
        insuranceActualRate,
    } = useMemo(
        () => ({
            retailTargetMonth: data?.progressGoal?.retailTargetMonth ?? 0,
            retailActualMonth: data?.progressGoal?.retailActualMonth ?? 0,
            retailMonthRate: data?.progressGoal?.retailMonthRate ?? 0,
            retailMonthDev: data?.progressGoal?.retailMonthDev ?? 0,
            retailTargetYtd: data?.progressGoal?.retailTargetYtd ?? 0,
            retailActualYtd: data?.progressGoal?.retailActualYtd ?? 0,
            retailYtdRate: data?.progressGoal?.retailYtdRate ?? 0,
            retailYtdDev: data?.progressGoal?.retailYtdDev ?? 0,
            financeTarget: data?.progressGoal?.financeTarget ?? 0,
            financeActualRate: data?.progressGoal?.financeActualRate ?? 0,
            insuranceTarget: data?.progressGoal?.insuranceTarget ?? 0,
            insuranceActualRate: data?.progressGoal?.insuranceActualRate ?? 0,
        }),
        [data?.progressGoal]
    );

    const mtdDevColor = useMemo(() => getBadgeColor(retailMonthRate), [retailMonthRate]);
    const mtdDevBgColor = useMemo(() => getBadgeBgColor(retailMonthRate), [retailMonthRate]);
    const ytdDevColor = useMemo(() => getBadgeColor(retailYtdRate), [retailYtdRate]);
    const ytdDevBgColor = useMemo(() => getBadgeBgColor(retailYtdRate), [retailYtdRate]);

    if (hasSalesManagerPermission || !hasSalesConsultantPermission || monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <ContentItem>
            <ContentItemTitle>
                {t('salesControlBoard:progressToGoal.title', {
                    selectedMonth,
                })}
            </ContentItemTitle>
            <Col span={24}>
                <Row gutter={[35, 24]}>
                    <Col span={24}>
                        <Row gutter={[0, 24]}>
                            <Col flex="100px">
                                <PText color="contrast-medium" size="x-small">
                                    {t('salesControlBoard:progressToGoal.mtd')}
                                </PText>
                                <PHeading size="medium" style={{ marginTop: '4px' }} tag="h2">
                                    {t('salesControlBoard:progressToGoal.percentage', {
                                        percentage: retailMonthRate,
                                    })}
                                </PHeading>
                            </Col>
                            <Col flex="auto">
                                <ProgressWrapper>
                                    <Badge bgColor={mtdDevBgColor} color={mtdDevColor}>
                                        {t('salesControlBoard:progressToGoal.dev', {
                                            dev: retailMonthDev > 0 ? `+${retailMonthDev}` : retailMonthDev,
                                        })}
                                    </Badge>
                                    <ProgressBar value={retailMonthRate} />
                                    <PText size="x-small">
                                        {t('salesControlBoard:progressToGoal.actualOfTarget', {
                                            actual: retailActualMonth,
                                            target: retailTargetMonth,
                                        })}
                                    </PText>
                                </ProgressWrapper>
                            </Col>
                        </Row>
                    </Col>
                    <Col span={24}>
                        <Row gutter={[0, 24]}>
                            <Col flex="100px">
                                <PText color="contrast-medium" size="x-small">
                                    {t('salesControlBoard:progressToGoal.ytd')}
                                </PText>
                                <PHeading size="medium" style={{ marginTop: '4px' }} tag="h2">
                                    {t('salesControlBoard:progressToGoal.percentage', { percentage: retailYtdRate })}
                                </PHeading>
                            </Col>
                            <Col flex="auto">
                                <ProgressWrapper>
                                    <Badge bgColor={ytdDevBgColor} color={ytdDevColor}>
                                        {t('salesControlBoard:progressToGoal.dev', {
                                            dev: retailYtdDev > 0 ? `+${retailYtdDev}` : retailYtdDev,
                                        })}
                                    </Badge>
                                    <ProgressBar value={retailYtdRate} />
                                    <PText size="x-small">
                                        {t('salesControlBoard:progressToGoal.actualOfTarget', {
                                            actual: retailActualYtd,
                                            target: retailTargetYtd,
                                        })}
                                    </PText>
                                </ProgressWrapper>
                            </Col>
                        </Row>
                    </Col>
                    <Col sm={12} xs={24}>
                        <Row gutter={[0, 24]}>
                            <Col flex="100px">
                                <PText color="contrast-medium" size="x-small">
                                    {t('salesControlBoard:progressToGoal.finance')}
                                </PText>
                                <PHeading size="medium" style={{ marginTop: '4px' }} tag="h2">
                                    {t('salesControlBoard:progressToGoal.percentage', {
                                        percentage: financeActualRate,
                                    })}
                                </PHeading>
                            </Col>
                            <Col flex="auto">
                                <ProgressWrapper>
                                    <div style={{ height: '22px' }} />
                                    <ProgressBar value={financeActualRate} />
                                    <PText size="x-small">
                                        {t('salesControlBoard:progressToGoal.target')}
                                        {t('salesControlBoard:progressToGoal.percentage', {
                                            percentage: financeTarget,
                                        })}
                                        {t('salesControlBoard:progressToGoal.ofTarget', {
                                            target:
                                                state.filters.reportingView === ReportingViewType.Month
                                                    ? retailActualMonth
                                                    : retailActualYtd,
                                        })}
                                    </PText>
                                </ProgressWrapper>
                            </Col>
                        </Row>
                    </Col>
                    <Col sm={12} xs={24}>
                        <Row gutter={[0, 24]}>
                            <Col flex="100px">
                                <PText color="contrast-medium" size="x-small">
                                    {t('salesControlBoard:progressToGoal.insurance')}
                                </PText>
                                <PHeading size="medium" style={{ marginTop: '4px' }} tag="h2">
                                    {t('salesControlBoard:progressToGoal.percentage', {
                                        percentage: insuranceActualRate,
                                    })}
                                </PHeading>
                            </Col>
                            <Col flex="auto">
                                <ProgressWrapper>
                                    <div style={{ height: '22px' }} />
                                    <ProgressBar value={insuranceActualRate} />
                                    <PText size="x-small">
                                        {t('salesControlBoard:progressToGoal.target')}
                                        {t('salesControlBoard:progressToGoal.percentage', {
                                            percentage: insuranceTarget,
                                        })}
                                        {t('salesControlBoard:progressToGoal.ofTarget', {
                                            target:
                                                state.filters.reportingView === ReportingViewType.Month
                                                    ? retailActualMonth
                                                    : retailActualYtd,
                                        })}
                                    </PText>
                                </ProgressWrapper>
                            </Col>
                        </Row>
                    </Col>
                </Row>
            </Col>
        </ContentItem>
    );
};

export default ProgressToGoal;

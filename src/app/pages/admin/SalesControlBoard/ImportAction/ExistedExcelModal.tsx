import { PText } from '@porsche-design-system/components-react';
import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Button from '../../../../themes/porscheV3/Button';
import Modal from '../../../../themes/porscheV3/Modal';

export const useExistedExcelModal = (setReimportNotification: Dispatch<SetStateAction<boolean | null>>) => {
    const [isOpen, setIsOpen] = useState(false);
    const [date, setDate] = useState<string | null>(null);

    const action = useMemo(
        () => ({
            open: (reportingDate: string) => {
                setIsOpen(true);
                setDate(reportingDate);
            },
            close: () => {
                setIsOpen(false);
                setDate(null);
                setReimportNotification(false);
            },
        }),
        [setReimportNotification]
    );

    return {
        ...action,
        render: () => <ExistedExcelModal close={action.close} date={date} open={isOpen} />,
    };
};

type ExistedExcelModalProps = {
    open: boolean;
    close: () => void;
    date: string | null;
};
const ExistedExcelModal = ({ open, close, date }: ExistedExcelModalProps) => {
    const { t } = useTranslation();

    const handleClose = useCallback(() => {
        close();
    }, [close]);

    const footerButton = useMemo(
        () => [
            <Button key="submit" form="importModal" htmlType="submit" type="primary" block>
                {t('salesControlBoard:modal.buttons.yes')}
            </Button>,
            <Button key="cancel" onClick={handleClose} type="tertiary" block>
                {t('salesControlBoard:modal.buttons.no')}
            </Button>,
        ],
        [handleClose, t]
    );

    return (
        <Modal
            className="salesControlBoard-reconfirmUpload"
            closable={false}
            footer={footerButton}
            onCancel={() => handleClose()}
            open={open}
            title=""
            width={800}
            centered
            destroyOnClose
        >
            <PText size="x-large" style={{ paddingBottom: '10px' }} weight="semibold">
                {t('salesControlBoard:modal.reimportModel.title')}
            </PText>

            <PText>{t('salesControlBoard:modal.reimportModel.description', { month: date })}</PText>
            <PText style={{ paddingBottom: '24px' }}>{t('salesControlBoard:modal.reimportModel.subDescription')}</PText>
        </Modal>
    );
};

export default ExistedExcelModal;

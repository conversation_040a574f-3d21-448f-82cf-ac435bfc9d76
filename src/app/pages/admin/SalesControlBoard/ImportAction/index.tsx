import { PButton } from '@porsche-design-system/components-react';
import { useTranslation } from 'react-i18next';
import usePermission from '../usePermission';

const ImportAction = ({ open }: { open: () => void }) => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission } = usePermission();

    if (!hasSalesManagerPermission) {
        return null;
    }

    return (
        <PButton onClick={() => open()} type="button" variant="secondary">
            {t('salesControlBoard:actions.import')}
        </PButton>
    );
};

export default ImportAction;

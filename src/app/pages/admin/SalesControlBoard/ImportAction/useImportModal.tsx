import { useMemo, useState } from 'react';
import ImportModal from './ImportModal';

const useImportModal = () => {
    const [open, setOpen] = useState(false);

    const action = useMemo(
        () => ({
            open: () => setOpen(true),
            close: () => {
                setOpen(false);
            },
        }),
        []
    );

    return { ...action, render: () => <ImportModal open={open} setOpen={setOpen} /> };
};

export default useImportModal;

import { lazy } from '@loadable/component';
import { PButton } from '@porsche-design-system/components-react';
import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../themes/hooks';

const Download = lazy(() => import(/* webpackChunkName: "SalesControlBoardExport" */ './Download'));
const ExportAction = () => {
    const { t } = useTranslation('salesControlBoard');
    const { notification } = useThemeComponents();

    const [isPrinting, setIsPrinting] = useState(false);

    const onPrint = () => {
        setIsPrinting(true);
        notification.loading({
            content: t('salesControlBoard:messages.exporting'),
            duration: 0,
            key: 'primary',
        });
    };

    const handleAfterPrint = (result: boolean) => {
        setIsPrinting(false);
        notification.success({
            content: t(`salesControlBoard:messages.${result ? 'exportSuccess' : 'exportFailed'}`),
            key: 'primary',
        });
    };

    return (
        <>
            <PButton onClick={onPrint} type="button" variant="secondary">
                {t('salesControlBoard:actions.export')}
            </PButton>
            {isPrinting &&
                createPortal(<Download isPrinting={isPrinting} onAfterPrint={handleAfterPrint} />, document.body)}
        </>
    );
};

export default ExportAction;

import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import Performance from '../Performance';
import PerformanceOverview from '../PerformanceOverview';
import ProgressToGoal from '../ProgressToGoal';
import useSelectedMonth from '../SalesControlBoardManager/useSelectedMonth';
import WeekSalesFunnel from '../WeekSalesFunnel';

const Download = ({ onAfterPrint, isPrinting }: { onAfterPrint: (result: boolean) => void; isPrinting: boolean }) => {
    const { t } = useTranslation('salesControlBoard');
    const selectedMonth = useSelectedMonth();
    const chartsRef = useRef(null);
    const weekFunnelRef = useRef(null);

    const download = useCallback(async () => {
        if (isPrinting) {
            try {
                // eslint-disable-next-line new-cap
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4',
                    compress: true,
                });

                const renderTarget = async targetElement => {
                    const targetCanvas = await html2canvas(targetElement, {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        logging: false,
                    });

                    // get pdf page size
                    const pageWidth = pdf.internal.pageSize.getWidth();
                    const pageHeight = pdf.internal.pageSize.getHeight();

                    // set margins
                    const marginTop = 10;
                    const marginBottom = 10;
                    const contentHeight = pageHeight - marginTop - marginBottom;

                    const imgWidth = targetCanvas.width;
                    const imgHeight = targetCanvas.height;

                    const scale = pageWidth / imgWidth;
                    const scaledHeight = imgHeight * scale;

                    const pages = Math.ceil(scaledHeight / contentHeight);

                    if (pages === 1) {
                        pdf.addImage(
                            targetCanvas.toDataURL('image/png'),
                            'PNG',
                            0,
                            marginTop,
                            pageWidth,
                            scaledHeight,
                            undefined,
                            'FAST'
                        );

                        return;
                    }

                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    const dpi = 300;
                    const canvasWidth = (pageWidth * dpi) / 25.4;
                    const canvasHeight = (contentHeight * dpi) / 25.4;

                    canvas.width = canvasWidth;
                    canvas.height = canvasHeight;

                    ctx.fillStyle = '#ffffff';

                    for (let i = 0; i < pages; i++) {
                        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

                        const srcY = (i * contentHeight) / scale;
                        const srcHeight = contentHeight / scale;

                        ctx.drawImage(targetCanvas, 0, srcY, imgWidth, srcHeight, 0, 0, canvasWidth, canvasHeight);

                        const imgData = canvas.toDataURL('image/png');

                        if (i > 0) {
                            pdf.addPage();
                        }

                        pdf.addImage(imgData, 'PNG', 0, marginTop, pageWidth, contentHeight, undefined, 'FAST');
                    }
                };

                await renderTarget(chartsRef.current);

                pdf.addPage();

                await renderTarget(weekFunnelRef.current);

                pdf.save(t('salesControlBoard:exportFilename', { selectedMonth }));
                onAfterPrint(true);
            } catch (error) {
                console.error('PDF generation failed:', error);
                onAfterPrint(false);
            }
        }
    }, [isPrinting, onAfterPrint, selectedMonth, t]);

    useEffect(() => {
        setTimeout(() => {
            download();
        }, 1000);
    }, [download]);

    return (
        <div
            style={{
                position: 'fixed',
                left: 0,
                top: 0,
                width: '1800px',
                display: 'flex',
                flexDirection: 'column',
                gap: '36px',
                zIndex: '-1',
            }}
        >
            <div ref={chartsRef} style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: '36px' }}>
                <PerformanceOverview />
                <ProgressToGoal />
                <Performance isForExportPdf />
            </div>
            <div ref={weekFunnelRef} style={{ width: '100%' }}>
                <WeekSalesFunnel isForExportPdf />
            </div>
        </div>
    );
};

export default Download;

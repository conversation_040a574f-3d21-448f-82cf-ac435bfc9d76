import { PButton } from '@porsche-design-system/components-react';
import { Grid as AntdGrid } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import useFilterFlyout from './FilterFlyout';

const FilterButton = () => {
    const { t } = useTranslation(['salesControlBoard']);
    const screens = AntdGrid.useBreakpoint();
    const isDesktop = useMemo(() => screens.lg, [screens]);
    const filterFlyout = useFilterFlyout();

    return (
        <>
            {!isDesktop && (
                <PButton icon="filter" onClick={filterFlyout.open} type="button" variant="secondary">
                    {t('salesControlBoard:actions.filter')}
                </PButton>
            )}
            {filterFlyout.render()}
        </>
    );
};

export default FilterButton;

import { PButtonPure, PHeading, PText } from '@porsche-design-system/components-react';
import { Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import Button from '../../../../themes/porscheV3/Button';
import SelectField from '../../../../themes/porscheV3/Fields/SelectField';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import { SalesControlBoardState } from '../SalesControlBoardManager/useSalesControlBoardReducer';
import { StyledRadioGroupField } from '../ui';
import usePermission from '../usePermission';
import useFilterOptions from './useFilterOptions';

const FilterContentWrapper = styled.div`
    max-width: 368px;
`;

const FilterContent = () => {
    const { t } = useTranslation(['salesControlBoard']);
    const { hasSalesManagerPermission } = usePermission();

    const { setValues, handleSubmit } = useFormikContext<SalesControlBoardState['filters']>();

    const { dispatch, defaultFilters, monthOfImportOptions, salesConsultantOptions, vehicleModelOptions } =
        useSalesControlBoardContext();

    const { reportingViewOptions } = useFilterOptions();
    const resetFilter = useCallback(() => {
        setValues(defaultFilters);
        dispatch({ type: 'setFilters', updates: defaultFilters });
    }, [defaultFilters, dispatch, setValues]);

    return (
        <>
            <PHeading size="large" style={{ marginBottom: '28px' }} tag="h2">
                {t('salesControlBoard:filter.title')}
            </PHeading>
            <FilterContentWrapper>
                <Row>
                    <Col span={24}>
                        <SelectField
                            {...t('salesControlBoard:filter.fields.monthOfImport', { returnObjects: true })}
                            name="monthOfImport"
                            options={monthOfImportOptions}
                            showSearch
                        />
                    </Col>

                    <Col span={24}>
                        <PText size="small" style={{ marginBottom: '4px' }}>
                            {t('salesControlBoard:filter.fields.reportingView.label')}
                        </PText>
                        <StyledRadioGroupField name="reportingView" options={reportingViewOptions} />
                    </Col>

                    {hasSalesManagerPermission && (
                        <Col span={24}>
                            <SelectField
                                {...t('salesControlBoard:filter.fields.salesConsultant', { returnObjects: true })}
                                mode="multiple"
                                name="salesConsultantIds"
                                options={salesConsultantOptions}
                                showSearch
                            />
                        </Col>
                    )}
                    <Col span={24}>
                        <SelectField
                            {...t('salesControlBoard:filter.fields.vehicleModel', { returnObjects: true })}
                            mode="multiple"
                            name="vehicleModelIds"
                            options={vehicleModelOptions}
                            showSearch
                        />
                    </Col>

                    <Col span={24}>
                        <Button
                            onClick={() => handleSubmit()}
                            style={{ width: '100%', marginBottom: '15px' }}
                            type="primary"
                        >
                            {t('salesControlBoard:actions.applyFilters')}
                        </Button>
                    </Col>

                    <Col span={24}>
                        <div style={{ display: 'flex', justifyContent: 'center' }}>
                            <PButtonPure icon="none" onClick={resetFilter} type="button" underline>
                                {t('salesControlBoard:actions.reset')}
                            </PButtonPure>
                        </div>
                    </Col>
                </Row>
            </FilterContentWrapper>
        </>
    );
};

export default FilterContent;

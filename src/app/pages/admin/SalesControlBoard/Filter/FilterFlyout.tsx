import { PFlyout } from '@porsche-design-system/components-react';
import { useMemo, useState } from 'react';
import FilterContent from './FilterContent';

export type FilterFlyoutProps = {
    isFlyoutOpen?: boolean;
    onDismiss?: () => void;
};

const FilterFlyout = ({ isFlyoutOpen, onDismiss }: FilterFlyoutProps) => (
    <PFlyout aria={{ 'aria-label': 'Some Filters' }} onDismiss={onDismiss} open={isFlyoutOpen}>
        <FilterContent />
    </PFlyout>
);

const useFilterFlyout = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => {
                setVisible(false);
            },
        }),
        [setVisible]
    );

    return {
        visible,
        ...actions,
        render: () => <FilterFlyout isFlyoutOpen={visible} onDismiss={actions.close} />,
    };
};

export default useFilterFlyout;

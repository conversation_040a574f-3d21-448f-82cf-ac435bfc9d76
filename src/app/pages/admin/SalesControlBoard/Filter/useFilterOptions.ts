import { useTranslation } from 'react-i18next';
import { ReportingViewType } from '../SalesControlBoardManager/useSalesControlBoardReducer';

const useFilterOptions = () => {
    const { t } = useTranslation('salesControlBoard');

    const reportingViewOptions = [
        {
            value: ReportingViewType.Month,
            label: t('salesControlBoard:filter.fields.reportingViewOptions.month'),
        },
        {
            value: ReportingViewType.YTD,
            label: t('salesControlBoard:filter.fields.reportingViewOptions.ytd'),
        },
    ];

    return {
        reportingViewOptions,
    };
};

export default useFilterOptions;

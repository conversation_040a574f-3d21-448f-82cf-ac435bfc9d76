import { Grid as AntdGrid } from 'antd';
import { useMemo } from 'react';
import { StyledFilterSideMenu } from '../ui';
import FilterContent from './FilterContent';

const FilterSection = () => {
    const screens = AntdGrid.useBreakpoint();
    const isDesktop = useMemo(() => screens.lg, [screens]);

    if (!isDesktop) {
        return null;
    }

    return (
        <StyledFilterSideMenu>
            <FilterContent />
        </StyledFilterSideMenu>
    );
};

export default FilterSection;

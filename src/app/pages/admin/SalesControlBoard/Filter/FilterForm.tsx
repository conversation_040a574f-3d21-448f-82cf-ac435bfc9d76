import { Formik } from 'formik';
import { ReactNode, useCallback } from 'react';
import Form from '../../../../components/fields/Form';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import { type SalesControlBoardState } from '../SalesControlBoardManager/useSalesControlBoardReducer';

type FilterFormProps = {
    children: ReactNode;
};

const FilterForm = ({ children }: FilterFormProps) => {
    const { state, dispatch } = useSalesControlBoardContext();
    const onSubmit = useCallback(
        (values: SalesControlBoardState['filters']) => {
            dispatch({ type: 'setFilters', updates: { ...values } });
        },
        [dispatch]
    );

    return (
        <Formik initialValues={state.filters} onSubmit={onSubmit}>
            {({ values }) => <Form>{children}</Form>}
        </Formik>
    );
};

export default FilterForm;

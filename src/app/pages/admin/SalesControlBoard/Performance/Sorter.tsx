import { PIcon } from '@porsche-design-system/components-react';
import usePublic from '../../../../utilities/usePublic';

export enum SortingOrder {
    /** Ascending order */
    Asc = 'ascend',
    /** Descending order */
    Desc = 'descend',
}

export type SortOrder = SortingOrder | null;

export type SortValue = {
    columnKey: string;
    sortOrder?: SortOrder;
};

export enum PerformanceTableKey {
    ConsultantModel = 'salesConsultantName',
    LioPIInMonth = 'totalPi',
    Leads = 'totalLeadActual',
    TestDrives = 'totalTestDriveActual',
    SalesOffers = 'totalSalesOffersActual',
    OrderIntakes = 'totalOrderIntakesActual',
    Retails = 'totalRetailsActual',
    Lio = 'totalLio',
    Ulr = 'totalUlr',
    Olr = 'totalOlr',
}

export enum PerformanceTableValueKey {
    ModelName = 'modelName',
    LioPIInMonth = 'lead.pi',
    LeadsTarget = 'lead.target',
    LeadsActual = 'lead.actual',
    LeadsDev = 'lead.dev',
    TestDrivesTarget = 'testDrive.target',
    TestDrivesActual = 'testDrive.actual',
    TestDrivesDev = 'testDrive.dev',
    SalesOffersTarget = 'salesOffer.target',
    SalesOffersActual = 'salesOffer.actual',
    SalesOffersDev = 'salesOffer.dev',
    OrderIntakesTarget = 'orderIntake.target',
    OrderIntakesActual = 'orderIntake.actual',
    OrderIntakesDev = 'orderIntake.dev',
    RetailsTarget = 'retail.target',
    RetailsActual = 'retail.actual',
    RetailsDev = 'retail.dev',
    Lio = 'lead.lio',
    Ulr = 'lead.ulr',
    Olr = 'lead.olr',
}

export enum FICommissionColumnKey {
    SalesConsultantName = 'salesConsultantName',
    InHouseFinanceTarget = 'inHouseFinanceTarget',
    InHouseFinanceMtd = 'inHouseFinanceMtd',
    InHouseFinanceYtd = 'inHouseFinanceYtd',
    InHouseFinance3MAvg = 'inHouseFinance3MAvg',
    InHouseInsuranceTarget = 'inHouseInsuranceTarget',
    InHouseInsuranceMtd = 'inHouseInsuranceMtd',
    InHouseInsuranceYtd = 'inHouseInsuranceYtd',
    InHouseInsurance3MAvg = 'inHouseInsurance3MAvg',
}

export interface Column {
    key: FICommissionColumnKey | PerformanceTableKey;
    label: string;
    sortOrder?: SortOrder;
    sorter?: boolean;
}

interface SorterProps {
    index: number;
    column: Column;
    onSortOrderChange: (key: string, sortOrder: SortOrder) => void;
}

const Sorter = ({ index, column, onSortOrderChange }: SorterProps) => {
    const arrowDownActive = usePublic('icons/arrow/arrow-compact-down-active.svg');
    const arrowDownInActive = usePublic('icons/arrow/arrow-compact-down.svg');
    const arrowUpActive = usePublic('icons/arrow/arrow-compact-up-active.svg');

    if (column.sorter) {
        return (
            <button
                onClick={() => onSortOrderChange(column.key, column.sortOrder)}
                style={{
                    display: 'inline-block',
                    height: '24px',
                    width: '24px',
                    padding: 0,
                    border: 0,
                    background: 'none',
                    cursor: 'pointer',
                    position: 'relative',
                    top: '-2px',
                }}
                type="button"
            >
                {column.sortOrder === SortingOrder.Asc && <PIcon size="small" source={arrowUpActive} />}
                {column.sortOrder === SortingOrder.Desc && <PIcon size="small" source={arrowDownActive} />}
                {!column.sortOrder && <PIcon size="small" source={arrowDownInActive} />}
            </button>
        );
    }

    return null;
};

export default Sorter;

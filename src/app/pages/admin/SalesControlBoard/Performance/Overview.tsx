import {
    PTable,
    PTableBody,
    PTableHead,
    PTableHeadCell,
    PTableHeadRow,
    PText,
} from '@porsche-design-system/components-react';
import { get, groupBy, isNil } from 'lodash/fp';
import { useCallback, useEffect, useMemo, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import { SalesControlBoardItemPerformance, SalesControlBoardModelTotalPerformance } from '../../../../api/types';

import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import usePermission from '../usePermission';
import ModelInnerCell from './ModelInnerCell';
import Pagination from './Pagination';
import PerformanceTableRowCollapsibleContent from './PerformanceTableRowCollapsibleContent';
import SalesConsultantInnerCell from './SalesConsultantInnerCell';
import Sorter, {
    Column,
    PerformanceTableKey,
    PerformanceTableValue<PERSON>ey,
    type SortOrder,
    type SortValue,
} from './Sorter';
import { initialPerformanceState, sourcePerformanceReducer } from './useSourcePerformanceReducer';

export const getStyles = index => {
    if ([2, 5, 8, 11, 14, 17].includes(index)) {
        return { borderLeft: '1px solid #D8D8DB', maxWidth: '60px' };
    }

    if ([2, 3, 4, 6, 7, 8].includes(index)) {
        return { maxWidth: '60px' };
    }

    if (index === 1) {
        return { maxWidth: '75px' };
    }

    if (index === 0) {
        return { maxWidth: '200px', paddingLeft: '25px' };
    }

    return {};
};

const useHeaders = (sortValue: SortValue) => {
    const { t } = useTranslation('salesControlBoard');

    const headerColumns: Column[] = useMemo(
        () => [
            {
                label: t('salesControlBoard:performance.performanceTable.consultantModel'),
                key: PerformanceTableKey.ConsultantModel,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.lioPIInMonth'),
                key: PerformanceTableKey.LioPIInMonth,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.LioPIInMonth ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.leads'),
                key: PerformanceTableKey.Leads,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.Leads ? sortValue.sortOrder : undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.testDrives'),
                key: PerformanceTableKey.TestDrives,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.TestDrives ? sortValue.sortOrder : undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.salesOffers'),
                key: PerformanceTableKey.SalesOffers,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.SalesOffers ? sortValue.sortOrder : undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.orderIntakes'),
                key: PerformanceTableKey.OrderIntakes,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.OrderIntakes ? sortValue.sortOrder : undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.retails'),
                key: PerformanceTableKey.Retails,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.Retails ? sortValue.sortOrder : undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: '',
                key: undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.lio'),
                key: PerformanceTableKey.Lio,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.Lio ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.ulr'),
                key: PerformanceTableKey.Ulr,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.Ulr ? sortValue.sortOrder : undefined,
            },
            {
                label: t('salesControlBoard:performance.performanceTable.olr'),
                key: PerformanceTableKey.Olr,
                sorter: true,
                sortOrder: sortValue.columnKey === PerformanceTableKey.Olr ? sortValue.sortOrder : undefined,
            },
        ],
        [sortValue.columnKey, sortValue.sortOrder, t]
    );

    const valueColumns = [
        {
            label: '',
            key: PerformanceTableValueKey.ModelName,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.piInMonth'),
            key: PerformanceTableValueKey.LioPIInMonth,
        },
        // leads
        {
            label: t('salesControlBoard:performance.performanceTable.feature.target'),
            key: PerformanceTableValueKey.LeadsTarget,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.LeadsActual,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.dev'),
            key: PerformanceTableValueKey.LeadsDev,
        },
        // test drives
        {
            label: t('salesControlBoard:performance.performanceTable.feature.target'),
            key: PerformanceTableValueKey.TestDrivesTarget,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.TestDrivesActual,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.dev'),
            key: PerformanceTableValueKey.TestDrivesDev,
        },
        // sales offers
        {
            label: t('salesControlBoard:performance.performanceTable.feature.target'),
            key: PerformanceTableValueKey.SalesOffersTarget,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.SalesOffersActual,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.dev'),
            key: PerformanceTableValueKey.SalesOffersDev,
        },

        // order intakes
        {
            label: t('salesControlBoard:performance.performanceTable.feature.target'),
            key: PerformanceTableValueKey.OrderIntakesTarget,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.OrderIntakesActual,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.dev'),
            key: PerformanceTableValueKey.OrderIntakesDev,
        },

        // retails
        {
            label: t('salesControlBoard:performance.performanceTable.feature.target'),
            key: PerformanceTableValueKey.RetailsTarget,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.RetailsActual,
        },
        {
            label: t('salesControlBoard:performance.performanceTable.feature.dev'),
            key: PerformanceTableValueKey.RetailsDev,
        },

        // lio
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.Lio,
        },

        // ulr

        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.Ulr,
        },

        // olr
        {
            label: t('salesControlBoard:performance.performanceTable.feature.actual'),
            key: PerformanceTableValueKey.Olr,
        },
    ];

    return {
        firstHeaders: headerColumns,
        columns: valueColumns,
    };
};

type OverviewProps = {
    isForExportPdf?: boolean;
};
const Overview = ({ isForExportPdf }: OverviewProps) => {
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const { data } = useSalesControlBoardContext();

    const [state, dispatch] = useReducer(sourcePerformanceReducer, {
        ...initialPerformanceState,
        dataSource: [...(data?.performance.total || [])].sort(
            (a, b) =>
                get(initialPerformanceState.sortValue.columnKey, b) -
                get(initialPerformanceState.sortValue.columnKey, a)
        ),
    });

    const { firstHeaders, columns } = useHeaders(state.sortValue);

    useEffect(() => {
        dispatch({ type: 'setDataSource', dataSource: data?.performance.total || [] });
    }, [data?.performance.total, dispatch]);

    const onSortOrderChange = useCallback(
        (columnKey: string, currentSortOrder?: SortOrder) => {
            dispatch({ type: 'setSortValue', columnKey, currentSortOrder });
        },
        [dispatch]
    );

    const groupedDataSourceBySalesConsultant: [string, SalesControlBoardItemPerformance[]][] = useMemo(() => {
        // Get the order of sales consultant names from state.dataSource
        const dataSources = isForExportPdf ? state.dataSource : state.currentPageData;
        const orderedNames = dataSources.map(item => item.salesConsultantName);

        // Group items by salesConsultantName
        const grouped = groupBy('salesConsultantName', data?.performance?.items);

        // Sort the grouped entries based on the order in state.dataSource
        return orderedNames.filter(name => grouped[name]).map(name => [name, grouped[name]]);
    }, [data?.performance?.items, isForExportPdf, state.currentPageData, state.dataSource]);

    // write a summation of data?.performance?.model of each properties
    const totalModelData = useMemo(
        () =>
            data?.performance?.model.reduce(
                (acc, item) => {
                    Object.keys(item).forEach(key => {
                        const salesConsultantLength = Object.keys(groupedDataSourceBySalesConsultant).length ?? 1;
                        if (
                            key !== 'modelName' &&
                            key !== 'totalLeadTarget' &&
                            key !== 'totalOrderIntakeTarget' &&
                            key !== 'totalRetailTarget' &&
                            key !== 'totalSalesOfferTarget' &&
                            key !== 'totalTestDriveTarget' &&
                            key !== '__typename'
                        ) {
                            acc[key] = (acc[key] || 0) + (item[key] || 0);
                        }

                        if (key === 'totalLeadTarget') {
                            acc[key] =
                                (data?.monthlyAverageTarget?.leadsMonthlyModelAverageTarget ?? 0) *
                                salesConsultantLength;
                        }

                        if (key === 'totalOrderIntakeTarget') {
                            acc[key] =
                                (data?.monthlyAverageTarget?.orderIntakesMonthlyModelAverageTarget ?? 0) *
                                salesConsultantLength;
                        }
                        if (key === 'totalRetailTarget') {
                            acc[key] =
                                (data?.monthlyAverageTarget?.retailsMonthlyModelAverageTarget ?? 0) *
                                salesConsultantLength;
                        }
                        if (key === 'totalSalesOfferTarget') {
                            acc[key] =
                                (data?.monthlyAverageTarget?.salesOfferMonthlyModelAverageTarget ?? 0) *
                                salesConsultantLength;
                        }
                        if (key === 'totalTestDriveTarget') {
                            acc[key] =
                                (data?.monthlyAverageTarget?.testDriveMonthlyModelAverageTarget ?? 0) *
                                salesConsultantLength;
                        }
                    });

                    acc.modelName = 'Total';
                    acc.totalLeadDev = acc.totalLeadActual - acc.totalLeadTarget;
                    acc.totalOrderIntakeDev = acc.totalOrderIntakeActual - acc.totalOrderIntakeTarget;
                    acc.totalRetailDev = acc.totalRetailActual - acc.totalRetailTarget;
                    acc.totalSalesOfferDev = acc.totalSalesOfferActual - acc.totalSalesOfferTarget;
                    acc.totalTestDriveDev = acc.totalTestDriveActual - acc.totalTestDriveTarget;

                    return acc;
                },
                { modelName: '' } as SalesControlBoardModelTotalPerformance
            ),
        [
            data?.monthlyAverageTarget?.leadsMonthlyModelAverageTarget,
            data?.monthlyAverageTarget?.orderIntakesMonthlyModelAverageTarget,
            data?.monthlyAverageTarget?.retailsMonthlyModelAverageTarget,
            data?.monthlyAverageTarget?.salesOfferMonthlyModelAverageTarget,
            data?.monthlyAverageTarget?.testDriveMonthlyModelAverageTarget,
            data?.performance?.model,
            groupedDataSourceBySalesConsultant,
        ]
    );

    if (!data) {
        return null;
    }

    return (
        <>
            <PTable>
                <PTableHead>
                    <PTableHeadRow>
                        {firstHeaders.map((column, index) => (
                            <PTableHeadCell key={column.key} style={getStyles(index)}>
                                <div
                                    style={Object.assign(
                                        column.sorter
                                            ? { display: 'flex', flexDirection: 'row', alignItems: 'center' }
                                            : {}
                                    )}
                                >
                                    <PText size="x-small" weight="semi-bold">
                                        {column.label}
                                    </PText>
                                    <Sorter column={column} index={index} onSortOrderChange={onSortOrderChange} />
                                </div>
                            </PTableHeadCell>
                        ))}
                    </PTableHeadRow>
                    <PTableHeadRow>
                        {columns.map((item, i) => (
                            // eslint-disable-next-line react/no-array-index-key
                            <PTableHeadCell key={i.toString()} style={getStyles(i)}>
                                <PText size="x-small" weight="semi-bold">
                                    {item.label}
                                </PText>
                            </PTableHeadCell>
                        ))}
                    </PTableHeadRow>
                </PTableHead>
                <PTableBody>
                    {!isNil(totalModelData) && (
                        <PerformanceTableRowCollapsibleContent
                            key={totalModelData.modelName}
                            columns={columns}
                            isForExportPdf={isForExportPdf}
                            item={totalModelData}
                            name={totalModelData.modelName}
                            type="modelName"
                            defaultOpen
                        >
                            <ModelInnerCell
                                columns={columns}
                                data={data}
                                hasSalesConsultantPermission={hasSalesConsultantPermission}
                                hasSalesManagerPermission={hasSalesManagerPermission}
                                isForExportPdf={isForExportPdf}
                            />
                        </PerformanceTableRowCollapsibleContent>
                    )}
                    {hasSalesManagerPermission &&
                        groupedDataSourceBySalesConsultant.map(([key, items], groupIndex) => (
                            <SalesConsultantInnerCell
                                key={key}
                                columns={columns}
                                data={data}
                                isForExportPdf={isForExportPdf}
                                items={items}
                                salesConsultantKey={key}
                            />
                        ))}
                </PTableBody>
            </PTable>
            {!isForExportPdf && <Pagination dispatch={dispatch} state={state} />}
        </>
    );
};

export default Overview;

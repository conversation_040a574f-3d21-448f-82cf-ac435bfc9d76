import { Dispatch, useCallback } from 'react';
import Pagination from '../../../../themes/porscheV3/Pagination';
import { SourcePerformanceAction, SourcePerformanceState } from './useSourcePerformanceReducer';
import { SourceAction, SourceState } from './useSourceReducer';

type SPaginationProps = {
    state: SourceState | SourcePerformanceState;
    dispatch: Dispatch<SourceAction | SourcePerformanceAction>;
};
const SPagination = ({ state, dispatch }: SPaginationProps) => {
    const total = state.dataSource.length;
    const onPageChange = useCallback(
        (current: number) => {
            dispatch({ type: 'setPage', page: current });
        },
        [dispatch]
    );

    if (total <= 10) {
        return null;
    }

    return (
        <Pagination
            current={state.page}
            onChange={onPageChange}
            pageSize={state.pageSize}
            total={total}
            alignPaginationCenter
        />
    );
};

export default SPagination;

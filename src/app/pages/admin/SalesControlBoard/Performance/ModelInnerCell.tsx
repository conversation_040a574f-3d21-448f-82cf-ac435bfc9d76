import { P<PERSON><PERSON>onPure, PTableCell, PTableRow, PText } from '@porsche-design-system/components-react';
import { Row } from 'antd';
import { isNil } from 'lodash/fp';
import { useMemo } from 'react';
import { GetSalesControlBoardQuery } from '../../../../api';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import { PerformanceTableValueKey } from './Sorter';

export const MODEL_NAME_PADDING_LEFT = '33px';
const DEFAULT_PADDING_LEFT = '8px';

const featureDevStyle = (value: number, index: number) => {
    if ([4, 7, 10, 13, 16].includes(index)) {
        if (value > 0) {
            return 'notification-success';
        }

        if (value < 0) {
            return 'notification-error';
        }

        return 'primary';
    }

    return 'primary';
};

const mapTableKeysToTotalKeys = () => {
    const mapping = {
        salesConsultantName: 'salesConsultantName',
        modelName: 'modelName',
        totalLeadActual: 'lead.actual',
        totalLeadTarget: 'lead.target',
        totalLeadDev: 'lead.dev',
        // testDrive
        totalTestDriveActual: 'testDrive.actual',
        totalTestDriveTarget: 'testDrive.target',
        totalTestDriveDev: 'testDrive.dev',
        // sales offer
        totalSalesOfferActual: 'salesOffer.actual',
        totalSalesOfferTarget: 'salesOffer.target',
        totalSalesOfferDev: 'salesOffer.dev',
        // order intakes
        totalOrderIntakeActual: 'orderIntake.actual',
        totalOrderIntakeTarget: 'orderIntake.target',
        totalOrderIntakeDev: 'orderIntake.dev',
        // retails
        totalRetailActual: 'retail.actual',
        totalRetailTarget: 'retail.target',
        totalRetailDev: 'retail.dev',
        totalPi: 'lead.pi',
        totalLio: 'lead.lio',
        totalUlr: 'lead.ulr',
        totalOlr: 'lead.olr',
    };

    // Transpose: value -> key
    return Object.fromEntries(Object.entries(mapping).map(([k, v]) => [v, k]));
};

const maskSalesConsultantFeatureTargetAndDeviation = (
    hasSalesConsultantPermission: boolean,
    hasSalesManagerPermission: boolean
) =>
    useMemo(() => {
        if (hasSalesManagerPermission) {
            return null;
        }

        if (hasSalesConsultantPermission) {
            // lead ,test drives, sales offers, order intakes, retails
            return [
                'totalLeadTarget',
                'totalLeadDev',
                'totalTestDriveTarget',
                'totalTestDriveDev',
                'totalSalesOfferTarget',
                'totalSalesOfferDev',
                'totalOrderIntakeTarget',
                'totalOrderIntakeDev',
                'totalRetailTarget',
                'totalRetailDev',
            ];
        }

        // do nothing when the user is sales manager
        return null;
    }, [hasSalesConsultantPermission, hasSalesManagerPermission]);

export const getTotalValueBySalesConsultant = (
    type: 'salesConsultantName' | 'modelName',
    item:
        | GetSalesControlBoardQuery['salesControlBoard']['performance']['total'][number]
        | GetSalesControlBoardQuery['salesControlBoard']['performance']['model'][number],
    columns: { key: PerformanceTableValueKey; label: string }[],
    isOpen: boolean,
    isForExportPdf: boolean,
    maskedKeys: string[] | null = null,
    hideButton = false,
    isTitle: boolean = false
) => {
    // if item is an array, return null (should never be array here)
    if (Array.isArray(item)) {
        return null;
    }

    return columns.map(({ key }, columnIndex) => {
        const totalKey = mapTableKeysToTotalKeys()[key];
        if (!totalKey) {
            return null;
        }

        if (totalKey === 'salesConsultantName' || totalKey === 'modelName') {
            return (
                <PTableCell>
                    <Row>
                        {!isForExportPdf && !hideButton && (
                            <PButtonPure icon={isOpen ? 'minus' : 'plus'} type="button" hideLabel />
                        )}
                        <PText
                            size="x-small"
                            style={
                                totalKey === 'modelName' && !isTitle
                                    ? { paddingLeft: MODEL_NAME_PADDING_LEFT }
                                    : { paddingLeft: DEFAULT_PADDING_LEFT }
                            }
                            weight={hideButton ? 'thin' : 'bold'}
                        >
                            {item[type]}
                        </PText>
                    </Row>
                </PTableCell>
            );
        }

        const value = column => {
            if (!isNil(maskedKeys) && maskedKeys.includes(totalKey)) {
                return '-';
            }

            if (item[column] === undefined) {
                return null;
            }

            if (column === 'totalPi') {
                return `${parseFloat(item[column].toFixed(0))}`;
            }

            if (column === 'totalLio') {
                return `${parseFloat(item[column].toFixed(0))}`;
            }

            if (column === 'totalUlr' || column === 'totalOlr') {
                return `${parseFloat(item[column].toFixed(0))}%`;
            }

            return parseFloat(item[column].toFixed(1));
        };

        const getStyles = (index: number) => {
            if ([4, 7, 10, 13, 16].includes(index)) {
                return { borderRight: '1px solid #D8D8DB', maxWidth: '60px' };
            }

            if (index === 1) {
                return { borderRight: '1px solid #D8D8DB', maxWidth: '75px' };
            }

            if (index === 0) {
                return { maxWidth: '200px', paddingLeft: '25px' };
            }

            return {};
        };

        return (
            <PTableCell style={getStyles(columnIndex)}>
                <PText color={featureDevStyle(item[totalKey], columnIndex)} size="x-small">
                    {value(totalKey)}
                </PText>
            </PTableCell>
        );
    });
};

const ModelInnerCell = ({
    data,
    columns,
    hasSalesConsultantPermission,
    hasSalesManagerPermission,
    isForExportPdf,
}: {
    data: GetSalesControlBoardQuery['salesControlBoard'];
    columns: { key: PerformanceTableValueKey; label: string }[];
    hasSalesConsultantPermission: boolean;
    hasSalesManagerPermission: boolean;
    isForExportPdf: boolean;
}): React.ReactNode => {
    const { performance } = data || {};
    const { model } = performance || {};

    const { vehicleModelOptions, state } = useSalesControlBoardContext();

    const maskedKeys = maskSalesConsultantFeatureTargetAndDeviation(
        hasSalesConsultantPermission,
        hasSalesManagerPermission
    );

    const selectedFiltersModels = useMemo(
        () => vehicleModelOptions.filter(option => state.filters.vehicleModelIds.includes(option.value)),
        [vehicleModelOptions, state.filters.vehicleModelIds]
    );

    const selectedVehicleModels = useMemo(() => {
        if (state.filters.vehicleModelIds.length > 0) {
            return model.filter(item => selectedFiltersModels.some(option => option.label === item.modelName));
        }

        return model;
    }, [model, selectedFiltersModels, state.filters.vehicleModelIds.length]);

    if (!model) {
        return null;
    }

    return selectedVehicleModels.map((item, groupIndex) => (
        <PTableRow key={item.modelName}>
            {getTotalValueBySalesConsultant('modelName', item, columns, false, isForExportPdf, maskedKeys, true)}
        </PTableRow>
    ));
};

export default ModelInnerCell;

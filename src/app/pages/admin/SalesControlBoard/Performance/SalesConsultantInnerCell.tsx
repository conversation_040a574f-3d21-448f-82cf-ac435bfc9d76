import { PTableCell, PTableRow, PText } from '@porsche-design-system/components-react';
import { get } from 'lodash/fp';
import { GetSalesControlBoardQuery } from '../../../../api/queries/getSalesControlBoard';
import { SalesControlBoardItemPerformance } from '../../../../api/types';
import { MODEL_NAME_PADDING_LEFT } from './ModelInnerCell';
import { getStyles } from './Overview';
import PerformanceTableRowCollapsibleContent, { featureDevStyle } from './PerformanceTableRowCollapsibleContent';
import { PerformanceTableValueKey } from './Sorter';

const FIXED_MODELS = ['911', '718', 'Cayenne', 'Macan', 'Panamera', 'Taycan'];

type SalesConsultantInnerCellProps = {
    items: Array<SalesControlBoardItemPerformance>;
    data: GetSalesControlBoardQuery['salesControlBoard'];
    columns: Array<{
        label: string;
        key: PerformanceTableValueKey;
    }>;
    salesConsultantKey: string;
    isForExportPdf?: boolean;
};

const SalesConsultantInnerCell = ({
    items,
    columns,
    data,
    salesConsultantKey,
    isForExportPdf,
}: SalesConsultantInnerCellProps) => {
    const sortedItems = items.sort((a, b) => {
        const modelA = FIXED_MODELS.indexOf(a.modelName);
        const modelB = FIXED_MODELS.indexOf(b.modelName);

        return modelA - modelB;
    });

    const salesConsultantBreakDown = sortedItems.map((salesConsultantArray, itemIndex) => {
        const columnText = columns.map((column, columnIndex) => {
            const value = get(column.key, salesConsultantArray);
            if (value === undefined) {
                return null;
            }

            const featureValue = () => {
                if (column.key === 'lead.ulr' || column.key === 'lead.olr') {
                    return `${value}%`;
                }

                return value;
            };

            return (
                <PTableCell
                    // eslint-disable-next-line react/no-array-index-key
                    key={`${column.key}-${itemIndex}-${columnIndex}`}
                    style={getStyles(columnIndex)}
                >
                    <PText
                        color={featureDevStyle(value, columnIndex)}
                        size="x-small"
                        style={column.key === 'modelName' ? { paddingLeft: MODEL_NAME_PADDING_LEFT } : {}}
                    >
                        {featureValue()}
                    </PText>
                </PTableCell>
            );
        });

        return (
            <PTableRow key={`sales-consultant-${salesConsultantKey}_${salesConsultantArray.modelName || itemIndex}`}>
                {columnText}
            </PTableRow>
        );
    });

    return (
        <PerformanceTableRowCollapsibleContent
            key={salesConsultantKey}
            columns={columns}
            defaultOpen={isForExportPdf}
            isForExportPdf={isForExportPdf}
            item={data.performance.total.find(item => item.salesConsultantName === salesConsultantKey)}
            name={salesConsultantKey}
            type="salesConsultantName"
        >
            {salesConsultantBreakDown}
        </PerformanceTableRowCollapsibleContent>
    );
};

export default SalesConsultantInnerCell;

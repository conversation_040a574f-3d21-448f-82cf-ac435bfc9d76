import { componentsReady } from '@porsche-design-system/components-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { css } from 'styled-components';
import { useThemeComponents } from '../../../../themes/hooks';
import FICommissions from './FICommissions';
import Overview from './Overview';

enum PerformanceTabKey {
    Overview = 'Overview',
    FICommissions = 'FICommissions',
}

const tabsBarCss = css`
    :host .root {
        margin-bottom: 16px;
    }
`;

const SectionTabs = () => {
    const { t } = useTranslation(['salesControlBoard']);
    const { Tabs } = useThemeComponents();
    const [activeTab, setActiveTab] = useState<PerformanceTabKey>(PerformanceTabKey.Overview);

    const containerRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        if (!containerRef.current) {
            return;
        }

        const container = containerRef.current;
        componentsReady(container).then(() => {
            const tabsBarShadowRoot = (container.querySelector('p-tabs') as HTMLDivElement)?.shadowRoot;

            if (tabsBarShadowRoot) {
                const styleElement = document.createElement('style');
                styleElement.textContent = tabsBarCss.toString();
                tabsBarShadowRoot.appendChild(styleElement);
            }
        });
    }, [containerRef]);

    const tabs = useMemo(
        () => [
            {
                key: PerformanceTabKey.Overview,
                label: t('salesControlBoard:performance.tabs.overview'),
                children: <Overview />,
            },
            {
                key: PerformanceTabKey.FICommissions,
                label: t('salesControlBoard:performance.tabs.fiCommissions'),
                children: <FICommissions />,
            },
        ],
        [t]
    );

    const onTabChange = useCallback((tab: PerformanceTabKey) => setActiveTab(tab), []);

    return (
        <div ref={containerRef}>
            <Tabs activeKey={activeTab} items={tabs} onChange={onTabChange} />
        </div>
    );
};

export default SectionTabs;

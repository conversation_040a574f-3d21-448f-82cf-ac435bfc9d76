import { PText } from '@porsche-design-system/components-react';
import { Col, Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ContentItem, ContentItemTitle } from '../../../portal/LaunchPadApplicationEntrypoint/ui/ContentItem';
import { useSalesControlBoardContext } from '../SalesControlBoardManager';
import useSelectedMonth from '../SalesControlBoardManager/useSelectedMonth';
import usePermission from '../usePermission';
import FICommissions from './FICommissions';
import Overview from './Overview';
import SectionTabs from './SectionTabs';

type PerformanceProps = {
    isForExportPdf?: boolean;
};
const Performance = ({ isForExportPdf }: PerformanceProps) => {
    const { t } = useTranslation('salesControlBoard');
    const { hasSalesManagerPermission, hasSalesConsultantPermission } = usePermission();
    const { monthOfImportOptions } = useSalesControlBoardContext();
    const selectedMonth = useSelectedMonth();

    const title = useMemo(() => {
        if (hasSalesManagerPermission) {
            return t('salesControlBoard:performance.titleForManager', {
                selectedMonth,
            });
        }

        if (hasSalesConsultantPermission) {
            return t('salesControlBoard:performance.titleForConsultant', {
                selectedMonth,
            });
        }

        return '';
    }, [t, hasSalesManagerPermission, hasSalesConsultantPermission, selectedMonth]);

    if (monthOfImportOptions.length === 0) {
        return null;
    }

    return (
        <ContentItem>
            <ContentItemTitle>{title}</ContentItemTitle>
            <Col span={24}>
                {hasSalesManagerPermission && isForExportPdf && (
                    <Space direction="vertical" size={36} style={{ width: '100%' }}>
                        <PText size="small" weight="semi-bold">
                            {t('salesControlBoard:performance.tabs.overview')}
                        </PText>
                        <Overview isForExportPdf={isForExportPdf} />
                        <PText size="small" weight="semi-bold">
                            {t('salesControlBoard:performance.tabs.fiCommissions')}
                        </PText>
                        <FICommissions isForExportPdf={isForExportPdf} />
                    </Space>
                )}
                {hasSalesManagerPermission && !isForExportPdf && <SectionTabs />}
                {!hasSalesManagerPermission && hasSalesConsultantPermission && <Overview />}
            </Col>
        </ContentItem>
    );
};

export default Performance;

import { GetSalesControlBoardQuery } from '../../../../api';
import { FICommissionColumnKey, SortOrder, SortingOrder } from './Sorter';

type SortValue = { columnKey: string; sortOrder?: SortOrder };

export type SourceState = {
    dataSource: GetSalesControlBoardQuery['salesControlBoard']['fiCommissions'];
    sortValue: SortValue;
    currentPageData: GetSalesControlBoardQuery['salesControlBoard']['fiCommissions'];
    page: number;
    pageSize: number;
};

export type SourceAction =
    | { type: 'setSortValue'; columnKey: string; currentSortOrder?: SortOrder }
    | { type: 'setPage'; page: number }
    | { type: 'setDataSource'; dataSource: any[] };

export const initialSourceState: SourceState = {
    dataSource: [],
    sortValue: {
        columnKey: FICommissionColumnKey.InHouseFinanceMtd,
        sortOrder: SortingOrder.Desc,
    },
    currentPageData: [],
    page: 1,
    pageSize: 10,
};

const getCurrentPageData = (dataSource: any[], page: number, pageSize: number = initialSourceState.pageSize): any[] => {
    const start = pageSize * (page - 1);
    const end = start + pageSize;

    return dataSource.slice(start, end);
};

export const sourceReducer = (state: SourceState, action: SourceAction): SourceState => {
    switch (action.type) {
        case 'setSortValue': {
            let next: SortOrder = SortingOrder.Asc;
            if (action.currentSortOrder === SortingOrder.Asc) {
                next = SortingOrder.Desc;
            } else if (action.currentSortOrder === SortingOrder.Desc) {
                next = null;
            }

            if (next === null) {
                return {
                    ...state,
                    sortValue: {
                        columnKey: action.columnKey,
                        sortOrder: next,
                    },
                };
            }

            const newDataSource = [...state.dataSource].sort((a, b) =>
                next === SortingOrder.Asc
                    ? a[action.columnKey] - b[action.columnKey]
                    : b[action.columnKey] - a[action.columnKey]
            );

            return {
                ...state,
                dataSource: newDataSource,
                currentPageData: getCurrentPageData(newDataSource, state.page, state.pageSize),
                sortValue: {
                    columnKey: action.columnKey,
                    sortOrder: next,
                },
            };
        }

        case 'setPage':
            return {
                ...state,
                currentPageData: getCurrentPageData(state.dataSource, action.page, state.pageSize),
                page: action.page,
            };

        case 'setDataSource': {
            const { columnKey, sortOrder } = state.sortValue;
            const newDataSource = [...action.dataSource].sort((a, b) =>
                sortOrder === SortingOrder.Asc ? a[columnKey] - b[columnKey] : b[columnKey] - a[columnKey]
            );

            return {
                ...state,
                dataSource: newDataSource,
                currentPageData: getCurrentPageData(newDataSource, state.page, state.pageSize),
            };
        }

        default:
            return state;
    }
};

import { GetSalesControlBoardQuery } from '../../../../api';
import { PerformanceTableKey, SortOrder, SortingOrder } from './Sorter';

type SortValue = { columnKey: string; sortOrder?: SortOrder };

export type SourcePerformanceState = {
    dataSource: GetSalesControlBoardQuery['salesControlBoard']['performance']['total'];
    sortValue: SortValue;
    currentPageData: GetSalesControlBoardQuery['salesControlBoard']['performance']['total'];
    page: number;
    pageSize: number;
};

export type SourcePerformanceAction =
    | { type: 'setSortValue'; columnKey: string; currentSortOrder?: SortOrder }
    | { type: 'setPage'; page: number }
    | { type: 'setDataSource'; dataSource: any[] };

export const initialPerformanceState: SourcePerformanceState = {
    dataSource: [],
    sortValue: {
        columnKey: PerformanceTableKey.LioPIInMonth,
        sortOrder: SortingOrder.Desc,
    },
    currentPageData: [],
    page: 1,
    pageSize: 10,
};

const getCurrentPageData = (
    dataSource: any[],
    page: number,
    pageSize: number = initialPerformanceState.pageSize
): any[] => {
    const start = pageSize * (page - 1);
    const end = start + pageSize;

    return dataSource.slice(start, end);
};

export const sourcePerformanceReducer = (
    state: SourcePerformanceState,
    action: SourcePerformanceAction
): SourcePerformanceState => {
    switch (action.type) {
        // Toggle sort order

        case 'setSortValue': {
            const orderToChange = state.sortValue.sortOrder === SortingOrder.Asc ? SortingOrder.Desc : SortingOrder.Asc;

            const newDataSource = [...state.dataSource].sort((a, b) =>
                (
                    orderToChange === SortingOrder.Asc
                        ? a[action.columnKey] - b[action.columnKey]
                        : b[action.columnKey] - a[action.columnKey]
                )
                    ? 1
                    : -1
            );

            return {
                ...state,
                dataSource: newDataSource,
                currentPageData: getCurrentPageData(newDataSource, state.page, state.pageSize),
                sortValue: {
                    columnKey: action.columnKey,
                    sortOrder: orderToChange,
                },
            };
        }

        case 'setPage':
            return {
                ...state,
                currentPageData: getCurrentPageData(state.dataSource, action.page, state.pageSize),
                page: action.page,
            };

        case 'setDataSource': {
            const { columnKey, sortOrder } = state.sortValue;
            const newDataSource = [...action.dataSource].sort((a, b) =>
                sortOrder === SortingOrder.Asc ? a[columnKey] - b[columnKey] : b[columnKey] - a[columnKey]
            );

            return {
                ...state,
                dataSource: newDataSource,
                currentPageData: getCurrentPageData(newDataSource, state.page, state.pageSize),
            };
        }

        default:
            return state;
    }
};

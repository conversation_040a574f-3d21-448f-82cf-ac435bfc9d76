import { PInlineNotification } from '@porsche-design-system/components-react';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { SalesControlBoardDataType } from '../../../api';
import { useSalesControlBoardContext } from './SalesControlBoardManager';

const Warning = () => {
    const { t } = useTranslation(['salesControlBoard', 'common']);
    const { monthOfImportOptions, state, data } = useSalesControlBoardContext();
    const lostDataType = useMemo(
        () =>
            (data?.lostDataType ?? []).map(i => {
                switch (i) {
                    case SalesControlBoardDataType.Leads:
                        return t('common:options.salesControlBoardDataType.leads');

                    case SalesControlBoardDataType.OrderIntakes:
                        return t('common:options.salesControlBoardDataType.orderIntakes');

                    case SalesControlBoardDataType.Retails:
                        return t('common:options.salesControlBoardDataType.retails');

                    default:
                        return '';
                }
            }),
        [data?.lostDataType, t]
    );

    const info = useMemo(() => {
        if (monthOfImportOptions.length === 0) {
            return t('salesControlBoard:noDataWarning');
        }

        return t('salesControlBoard:noDataWarningOnMonth', {
            types: lostDataType.map(i => `‘${i}’`).join(' and '),
            month: dayjs(state.filters.monthOfImport).format('MMM YYYY'),
        });
    }, [monthOfImportOptions.length, state.filters.monthOfImport, t, lostDataType]);

    if (lostDataType.length === 0 && monthOfImportOptions.length > 0) {
        return null;
    }

    return <PInlineNotification dismissButton={false} heading={info} headingTag="h2" state="warning" />;
};

export default Warning;

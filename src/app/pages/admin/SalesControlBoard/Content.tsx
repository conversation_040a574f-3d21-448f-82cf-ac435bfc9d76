import { PHeading } from '@porsche-design-system/components-react';
import { Grid as AntdGrid, Space } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import ExportAction from './ExportAction';
import Filter from './Filter';
import FilterButton from './Filter/FilterButton';
import FilterForm from './Filter/FilterForm';
import ImportAction from './ImportAction';
import Performance from './Performance';
import PerformanceOverview from './PerformanceOverview';
import ProgressToGoal from './ProgressToGoal';
import Warning from './Warning';
import WeekSalesFunnel from './WeekSalesFunnel';
import { ActionWrapper, ContentWrapper, HeaderWrapper } from './ui';

type SalesControlBoardContentProps = {
    open: () => void;
};
const SalesControlBoardContent = ({ open }: SalesControlBoardContentProps) => {
    const { t } = useTranslation(['salesControlBoard']);
    const screens = AntdGrid.useBreakpoint();
    const isDesktop = useMemo(() => screens.lg, [screens]);

    return (
        <FilterForm>
            <ContentWrapper isDesktop={isDesktop}>
                <Space direction="vertical" size={36}>
                    <HeaderWrapper>
                        <PHeading size="x-large" tag="h1">
                            {t('salesControlBoard:title')}
                        </PHeading>
                        <ActionWrapper>
                            <ImportAction open={open} />
                            <ExportAction />
                            <FilterButton />
                        </ActionWrapper>
                    </HeaderWrapper>
                    <Warning />
                    <PerformanceOverview />
                    <ProgressToGoal />
                    <Performance />
                    <WeekSalesFunnel />
                </Space>
                <Filter />
            </ContentWrapper>
        </FilterForm>
    );
};

export default SalesControlBoardContent;

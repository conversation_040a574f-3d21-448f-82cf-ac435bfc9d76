import { useMutation } from '@apollo/client';
import { Col, Row, Typography, Upload, type UploadProps, message } from 'antd';
import { isNil } from 'lodash/fp';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ImportCompanyConfigurationDocument,
    type ImportCompanyConfigurationMutation,
    type ImportCompanyConfigurationMutationVariables,
} from '../../../api/mutations/importCompanyConfiguration';
import { CompanyTheme } from '../../../api/types';
import ThemeUpload from '../../../components/ThemeUpload';
import { ErrorContent } from '../../../components/importExport';
import CancelButton from '../../../components/modal/CancelButton';
import OkButton from '../../../components/modal/OkButton';
import { useThemeComponents } from '../../../themes/hooks';
import { allowedExtensions } from '../../../utilities/extensions';

export type ImportCompanyModalProps = {
    open: boolean;
    onImportSuccess: () => void;
    onClose: () => void;
};

const ImportCompanyModal = ({ open, onImportSuccess, onClose }: ImportCompanyModalProps) => {
    const { t } = useTranslation(['companyList', 'common']);
    const [errors, setErrors] = useState<string>(null);
    const [isUploading, setIsUploading] = useState(false);
    const { theme, Modal } = useThemeComponents();

    const [importCompanyConfiguration] = useMutation<
        ImportCompanyConfigurationMutation,
        ImportCompanyConfigurationMutationVariables
    >(ImportCompanyConfigurationDocument);

    const accept = useMemo(() => [...allowedExtensions.archive, '.json'].join(','), []);

    const handleOnClose = useCallback(() => {
        onClose();
        setErrors(null);
        setIsUploading(false);
    }, [onClose]);

    const handleOnChange = useCallback(
        async ({ file }) => {
            if (file instanceof File) {
                setIsUploading(true);
                setErrors(null);

                try {
                    const { data } = await importCompanyConfiguration({
                        variables: { upload: file },
                    });

                    const result = data?.importCompanyConfiguration;

                    if (result?.success) {
                        message.success(result.message || t('companyList:import.success'));
                        handleOnClose(); // Close the modal first
                        onImportSuccess(); // Then trigger the success callback
                    } else {
                        setErrors(
                            (result?.errors ?? []).join('\n') || result?.message || t('companyList:import.unknownError')
                        );
                    }
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : t('companyList:import.unknownError');
                    setErrors(errorMessage);
                    message.error(errorMessage);
                } finally {
                    setIsUploading(false);
                }
            }
        },
        [importCompanyConfiguration, t, handleOnClose, onImportSuccess]
    );

    const beforeUpload = useCallback<NonNullable<UploadProps['beforeUpload']>>(
        file => {
            const fileExtension = file.name.toLowerCase().split('.').pop();
            const isValidExtension = ['zip', 'json'].includes(fileExtension);

            if (!isValidExtension) {
                message.error(t('common:upload.messages.fileError'));

                return Upload.LIST_IGNORE;
            }

            return false;
        },
        [t]
    );

    const content = useMemo(() => {
        if (!isNil(errors)) {
            return <ErrorContent errors={errors} />;
        }

        return (
            <Row gutter={[0, 16]}>
                <Col span={24}>
                    <Typography.Text>{t('companyList:import.description')}</Typography.Text>
                </Col>
            </Row>
        );
    }, [errors, t]);

    const footer = useMemo(() => {
        const cancelButton = (
            <CancelButton key="close" onClick={handleOnClose}>
                {t('common:actions.cancel')}
            </CancelButton>
        );

        if (!isNil(errors)) {
            return [cancelButton];
        }

        const okButton = (
            <ThemeUpload
                key="upload"
                accept={accept}
                beforeUpload={beforeUpload}
                disabled={isUploading}
                onChange={handleOnChange}
                showUploadList={false}
            >
                <OkButton disabled={isUploading} loading={isUploading}>
                    {(() => {
                        if (isUploading) {
                            return t('companyList:import.actions.uploading');
                        }

                        return t('companyList:import.actions.import');
                    })()}
                </OkButton>
            </ThemeUpload>
        );

        if (theme === CompanyTheme.PorscheV3 || theme === CompanyTheme.Porsche) {
            return [okButton, cancelButton];
        }

        return [cancelButton, okButton];
    }, [accept, beforeUpload, errors, handleOnChange, handleOnClose, theme, isUploading, t]);

    return (
        <Modal
            footer={footer}
            onCancel={handleOnClose}
            open={open}
            title={t('companyList:import.title')}
            width={600}
            destroyOnClose
        >
            {content}
        </Modal>
    );
};

export default ImportCompanyModal;

export const useImportCompanyModal = () => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: () => setVisible(false),
        }),
        [setVisible]
    );

    return {
        ...actions,
        render: (props: Omit<ImportCompanyModalProps, 'open' | 'onClose'>) => (
            <ImportCompanyModal onClose={actions.close} open={visible} {...props} />
        ),
    };
};

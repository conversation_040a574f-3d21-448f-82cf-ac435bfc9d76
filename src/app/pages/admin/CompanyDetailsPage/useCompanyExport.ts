import { message } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useExportCompanyConfigurationMutation } from '../../../api/mutations/exportCompanyConfiguration';
import { CompanyExportFormat } from '../../../api/types';

export interface ExportOptions {
    includeFiles?: boolean;
    compress?: boolean;
    format?: CompanyExportFormat;
}

export const useCompanyExport = () => {
    const { t } = useTranslation(['companyDetails']);
    const [loading, setLoading] = useState(false);
    const [exportCompanyConfiguration] = useExportCompanyConfigurationMutation();

    const exportCompany = async (companyId: string, options: ExportOptions = {}) => {
        setLoading(true);
        const messageKey = 'export-company';

        try {
            message.loading({
                content: t('companyDetails:export.preparing'),
                key: messageKey,
                duration: 0,
            });

            const result = await exportCompanyConfiguration({
                variables: {
                    companyId,
                    options: {
                        includeFiles: options.includeFiles ?? true,
                        compress: options.compress ?? true,
                        format: options.format ?? CompanyExportFormat.Ejson,
                    },
                },
            });

            const exportResult = result.data?.exportCompanyConfiguration;

            if (exportResult?.success && exportResult.downloadUrl) {
                message.success({
                    content: t('companyDetails:export.success'),
                    key: messageKey,
                });

                // Trigger download
                const link = document.createElement('a');
                link.href = exportResult.downloadUrl;
                link.download = exportResult.filename || 'company-export.zip';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                return {
                    success: true,
                    filename: exportResult.filename,
                    statistics: exportResult.statistics,
                };
            }
            throw new Error(exportResult?.error || 'Export failed');
        } catch (error) {
            console.error('Export failed:', error);
            message.error({
                content: t('companyDetails:export.error', {
                    error: error instanceof Error ? error.message : 'Unknown error',
                }),
                key: messageKey,
            });

            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        } finally {
            setLoading(false);
        }
    };

    return {
        exportCompany,
        loading,
    };
};

import { DownloadOutlined } from '@ant-design/icons';
import { Typography } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { CompanyExportFormat } from '../../../api/types';
import { useThemeComponents } from '../../../themes/hooks';
import { useCompanyExport } from './useCompanyExport';

export type CompanyExportModalProps = {
    companyId: string;
    onClose: () => void;
    open: boolean;
};

const CompanyExportModal = ({ companyId, onClose, open }: CompanyExportModalProps) => {
    const { t } = useTranslation(['companyDetails']);
    const { Modal } = useThemeComponents();
    const { exportCompany } = useCompanyExport();
    const [isExporting, setIsExporting] = useState(false);

    const handleExport = useCallback(async () => {
        setIsExporting(true);
        try {
            const result = await exportCompany(companyId, {
                format: CompanyExportFormat.Ejson,
                includeFiles: true,
                compress: true,
            });

            if (result.success) {
                onClose();
            }
        } finally {
            setIsExporting(false);
        }
    }, [companyId, exportCompany, onClose]);

    const handleClose = useCallback(() => {
        if (!isExporting) {
            onClose();
        }
    }, [isExporting, onClose]);

    return (
        <Modal
            cancelButtonProps={{
                disabled: isExporting,
            }}
            cancelText={t('companyDetails:exportModal.cancelText')}
            maskClosable={!isExporting}
            okButtonProps={{
                disabled: isExporting,
                icon: <DownloadOutlined />,
                loading: isExporting,
            }}
            okText={t('companyDetails:exportModal.okText')}
            onCancel={handleClose}
            onOk={handleExport}
            open={open}
            title={t('companyDetails:exportModal.title')}
        >
            <Typography>{t('companyDetails:exportModal.content')}</Typography>
            <ul style={{ marginTop: '24px' }}>
                <li>
                    <Typography>{t('companyDetails:exportModal.info.includedData')}</Typography>
                </li>
                <li>
                    <Typography>{t('companyDetails:exportModal.info.backgroundProcess')}</Typography>
                </li>
            </ul>
        </Modal>
    );
};

export const useCompanyExportModal = () => {
    const [open, setOpen] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setOpen(true),
            close: () => setOpen(false),
        }),
        [setOpen]
    );

    return {
        ...actions,
        render: (companyId: string) => <CompanyExportModal companyId={companyId} onClose={actions.close} open={open} />,
    };
};

export default CompanyExportModal;

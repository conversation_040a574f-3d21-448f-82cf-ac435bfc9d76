import {
    InsurerDetailsDataFragment,
    InsurerIntegrationProvider,
    InsurerIntegrationSettings,
    InsurerSettings,
} from '../../../../api';

export type InsurerFormValues = InsurerSettings;
export type InsurerIntegrationValues = InsurerDetailsDataFragment['integration'];
export type NewInsurerFormValues = InsurerFormValues & {
    integrationProvider: InsurerIntegrationProvider;
    integrationSettings: InsurerIntegrationSettings;
};

import { omit } from 'lodash/fp';
import { MenuItemSpecsFragment, RouterSpecsFragment } from '../../../../api';

export const getMenuItemValue = (menuItems: MenuItemSpecsFragment[]) =>
    menuItems.map(item => {
        switch (item.__typename) {
            case 'MenuCustomPathItem': {
                return {
                    customPathItem: {
                        ...omit(['id', '__typename'], item),
                    },
                };
            }

            case 'MenuEndpointItem': {
                return {
                    endpointItem: {
                        ...omit(['id', 'endpoint', '__typename'], item),
                    },
                };
            }

            default: {
                return {
                    logoutActionItem: {
                        ...omit(['id', '__typename'], item),
                    },
                };
            }
        }
    });

export enum RouterMenuType {
    Endpoint = 'endpoint',
    Custom = 'custom',
    LogOut = 'logout',
}

export type RouterMenuItemProps = {
    router: RouterSpecsFragment;
};

import dayjs from 'dayjs';
import { PromoCodeSettings } from '../../../../api';

export type PromoCodeFormValues = Omit<PromoCodeSettings, 'moduleVariants'> & {
    variantModuleIds: string[];
    // module variant mapping
    // as module id as the key and values are variant ids
    moduleVariantsMapping?: { [moduleId: string]: string[] };
    startTime: dayjs.Dayjs | null;
    endTime: dayjs.Dayjs | null;
};

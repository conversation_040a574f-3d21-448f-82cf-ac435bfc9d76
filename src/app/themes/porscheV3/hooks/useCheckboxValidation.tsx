import { useField } from 'formik';
import isEqual from 'lodash/isEqual';
import { useEffect, useRef, useMemo } from 'react';

const useCheckboxValidation = name => {
    const result = {
        hasError: false,
        errorMessage: '',
    };

    const fieldHook = typeof name === 'string' ? useField(name) : null;

    if (!fieldHook) {
        return result;
    }

    const [, meta, { setTouched }] = fieldHook;
    const previousValue = useRef(meta?.value);

    useEffect(() => {
        if (!isEqual(previousValue.current, meta?.value)) {
            previousValue.current = meta?.value;
            setTouched(true);
        }
    }, [meta?.value, setTouched]);

    result.hasError = useMemo(() => !!meta?.error && meta?.touched, [meta?.error, meta?.touched]);

    result.errorMessage = useMemo(() => (Array.isArray(meta?.error) ? '' : meta?.error), [meta?.error]);

    return result;
};

export default useCheckboxValidation;

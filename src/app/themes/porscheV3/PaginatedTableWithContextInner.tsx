import { useEffect, useRef } from 'react';
import styled from 'styled-components';
import type { SetExtraAction, PageAction, PageState, CompanyPaginatedTableProps } from '../../components/shared';
import PaginatedTable, { pageReducer } from './PaginatedTable';

export type ExtendedPageState = PageState & {
    /**
     * Determines whether to reset the current page number.
     *
     * The parent component (e.g., a list page) hosting the [PaginatedTableWithContext] will need to
     * cache it on unmount and restore the state on mount.
     * On remount, it compares the cached ID against the current company ID
     * provided to [PaginatedTableWithContext]. If the IDs do not match,
     * the page number will be reset.
     */
    companyId?: string;
};

export type ExtendedPageAction = PageAction | SetExtraAction;

export const extendedReducer = <State extends ExtendedPageState>(state: State, action: ExtendedPageAction): State => {
    switch (action.type) {
        case 'setCompanyId':
            return {
                ...state,
                companyId: action.companyId,
                page: state.companyId !== action.companyId ? 1 : state.page,
            };

        default:
            return pageReducer(state, action);
    }
};

const StyledTable = styled(PaginatedTable)`
    & .ant-table-tbody > tr {
        &.ant-table-row:hover > td {
            background: #f5f5f5;
        }

        &.ant-table-row-selected,
        &.ant-table-row-selected:hover {
            > td {
                background-color: #01020514;
            }
        }
    }
`;

const PaginatedTableWithContextInner = ({ dispatch, company, state, ...props }: CompanyPaginatedTableProps) => {
    // set page to 1 upon company context change
    const prevCompanyId = useRef(state?.companyId);
    useEffect(() => {
        if (prevCompanyId.current !== company?.id) {
            prevCompanyId.current = company?.id;
            dispatch({
                type: 'setCompanyId',
                companyId: company?.id,
            });
        }
    }, [company?.id, dispatch]);

    return <StyledTable dispatch={dispatch} state={state} {...props} />;
};

export default PaginatedTableWithContextInner;

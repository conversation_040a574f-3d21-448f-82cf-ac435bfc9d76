import { Empty, TableProps } from 'antd';
import { Dispatch, useMemo } from 'react';
import styled from 'styled-components';
import NoSearchResult from '../../components/results/NoSearchResult';
import type { PageAction, PageState } from '../../components/shared';
import ResponsiveStyledTable from '../../pages/shared/ResponsiveStyledTable';
import Pagination from './Pagination';

export const pageReducer = <State extends PageState = PageState>(state: State, action: PageAction): State => {
    switch (action.type) {
        case 'setPage':
            return { ...state, page: action.page };

        case 'setPageSize':
            return { ...state, page: 1, pageSize: action.pageSize };

        default:
            return state;
    }
};

export type PaginatedTableProps<TItem extends object = any> = Omit<TableProps<TItem>, 'pagination'> & {
    total: number;
    state: PageState;
    dispatch: Dispatch<PageAction>;
    tableName?: string;
    porscheV3StyleOverride?: boolean;
    alignPaginationCenter?: boolean;
};

const PaginationContainer = styled.div`
    margin: 16px 0;
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px;
    justify-content: flex-end;
`;

const PaginatedTable = <TItem extends object = any>({
    state,
    dispatch,
    total,
    tableName,
    ...props
}: PaginatedTableProps<TItem>) => {
    const { page, pageSize } = state;
    // memoize actions on the state
    const actions = useMemo(
        () => ({
            onPageChange: (nextPage: number, nextPageSize: number) => {
                dispatch({ type: 'setPage', page: nextPage });
            },
            onPageSizeChange: (current: number, nextPageSize: number) => {
                dispatch({ type: 'setPageSize', pageSize: nextPageSize });
            },
        }),
        [dispatch]
    );

    const locale = useMemo(() => {
        const component = props.loading ? <Empty description="Loading.." /> : <NoSearchResult name={tableName} />;

        return {
            emptyText: component,
        };
    }, [tableName, props.loading]);

    return (
        <>
            <ResponsiveStyledTable locale={locale} {...props} pagination={false} porscheV3StyleOverride />
            {total > pageSize && (
                <PaginationContainer>
                    <Pagination
                        alignPaginationCenter={props.alignPaginationCenter}
                        current={page}
                        onChange={actions.onPageChange}
                        onShowSizeChange={actions.onPageSizeChange}
                        pageSize={pageSize}
                        total={total}
                        showSizeChanger
                    />
                </PaginationContainer>
            )}
        </>
    );
};

export default PaginatedTable;

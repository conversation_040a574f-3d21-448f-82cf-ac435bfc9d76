import { PIcon } from '@porsche-design-system/components-react';
import { Col, Form, Row, UploadProps } from 'antd';
import { RcFile } from 'antd/lib/upload';
import { useField } from 'formik';
import { isObject } from 'lodash/fp';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { UploadFileWithPreviewFormDataFragment } from '../../api/fragments/UploadFileWithPreviewFormData';
import { FormItemProps } from '../../components/fields/FormItem';
import SingleUploadField, { UploadSizeType } from '../../components/fields/SingleUploadField';
import { allowedExtensions } from '../../utilities/extensions';
import { useThemeComponents } from '../hooks';

const StyledButton = styled.div`
    p-button {
        width: 100%;
        height: 54px;

        & button {
            height: 100% !important;
        }
    }

    .p-button button {
        height: 100%;
    }
`;
type PorscheV3UploadProps = Omit<UploadProps, 'listType' | 'maxCount' | 'accept' | 'defaultFileList'> & {
    name: string;
    extensions: string[];
    label?: string;
    required?: boolean;
    sizeLimitInMiB?: number;
    tooltip?: FormItemProps['tooltip'];
    style?: FormItemProps['style'];
    listType?: 'picture' | 'picture-card'; // only allow
    size?: UploadSizeType;
    errorMessage?: String;
    onIconSubmit?: () => void;
};

const PorscheV3Upload = ({
    name,
    label,
    extensions,
    required = false,
    sizeLimitInMiB = 2,
    tooltip,
    style,
    listType = 'picture',
    size = 'default',
    errorMessage,
    onIconSubmit,
    ...props
}: PorscheV3UploadProps) => {
    const { t } = useTranslation(['common']);

    const [field, meta, helpers] = useField<RcFile | UploadFileWithPreviewFormDataFragment | null>(name);

    const value = useMemo(() => {
        if (!(meta.value instanceof File) && isObject(meta.value)) {
            const { value } = meta;

            return value.filename;
        }

        return null;
    }, [meta]);

    const {
        FormFields: { DisplayField },
    } = useThemeComponents();

    return (
        <Form.Item label={label} name={name} required={required} style={style} tooltip={tooltip}>
            <Row gutter={[16, 16]}>
                <Col span={16}>
                    <DisplayField
                        actionIcon={
                            <PIcon aria={{ 'aria-label': 'Download icon' }} name="download" onClick={onIconSubmit} />
                        }
                        value={value}
                    />
                </Col>
                <Col span={8}>
                    <SingleUploadField extensions={[allowedExtensions.pdf]} name={name} />
                </Col>
            </Row>
        </Form.Item>
    );
};

export default PorscheV3Upload;

import { componentsReady, POptgroup, PSelectWrapper } from '@porsche-design-system/components-react';
import { useField } from 'formik';
import { flatten, isBoolean, isEmpty, isNumber, isObject, isString, omit, uniqBy } from 'lodash/fp';
import { memo, useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import FormItem from '../../../components/fields/FormItem';
import { TreeSelectFieldProps } from '../../../components/fields/shared';
import useSelectStyle, { useSelectLayoutEffect } from '../hooks/useSelectStyle';
import useRenderTooltip from './useRenderTooltip';
import { getTextFieldState } from './utils';

type OptionType = { label: string; value: string | number | boolean | Array<any> };
type ModifiedOptionType = { label: string; value: string; originalValue: string | number | boolean | Array<any> };

const getModifiedValue = (option: OptionType): ModifiedOptionType => {
    if (isNumber(option?.value)) {
        return { ...option, value: option.value.toString(), originalValue: option.value };
    }

    if (isBoolean(option?.value)) {
        return { ...option, value: option.value ? 'true' : 'false', originalValue: option.value };
    }

    if (Array.isArray(option?.value) || isObject(option?.value)) {
        return { ...option, value: JSON.stringify(option.value), originalValue: option.value };
    }

    return { ...option, value: option.value.toString(), originalValue: option.value };
};

const parseToString = (value: any): string => {
    if (isNumber(value) || isString(value)) {
        return value.toString();
    }

    if (isBoolean(value)) {
        return value ? 'true' : 'false';
    }

    return '';
};

const useOverrideStyle = (wrapperRef: React.RefObject<HTMLDivElement>, hasTooltip?: boolean) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const elm = wrapper.querySelector('p-select-wrapper');
            const elmShaRoot = elm?.shadowRoot;
            if (!elm || !elmShaRoot) {
                return;
            }

            const rootContainer = elmShaRoot.querySelector('.root');

            if (hasTooltip) {
                rootContainer.setAttribute('style', 'gap:2px');
            }
        });
    }, [hasTooltip, wrapperRef]);
};

const TreeSelectField = ({
    name,
    required,
    label,
    itemProps,
    tooltip,
    onChange,
    onSelect,
    enhanceTreeData,
    treeData,
    dropdownRender: dropdownRenderProp,
    titleSelectable = false,
    ...props
}: TreeSelectFieldProps) => {
    const { t } = useTranslation('common');
    const [field, meta, { setValue }] = useField(name);

    const renderTooltip = useRenderTooltip(tooltip);
    const wrapperRef = useRef<HTMLDivElement>(null);

    // account for both empty array and null values
    const hasValue = useMemo(
        () => !isEmpty(field.value) || (Array.isArray(field.value) && field.value.length !== 0),
        [field.value]
    );

    // To handle value that do not belong to html select options
    const placeholder = useMemo(() => (hasValue ? field.value : t('common:pleaseSelect')), [field.value, hasValue, t]);

    useSelectStyle(wrapperRef, props.disabled, placeholder, null, true, hasValue);
    useSelectLayoutEffect(wrapperRef, false);
    useOverrideStyle(wrapperRef, !!tooltip);

    const enhancedOnChange = useCallback(
        value => {
            setValue(value);

            if (onChange) {
                onChange(value);
            }
        },
        [setValue, onChange]
    );

    const valueOnlyOptions = useMemo(() => {
        if (!treeData) {
            return [];
        }

        return uniqBy(
            'value',
            flatten(
                treeData.map(({ children }) =>
                    children.map(({ title, value }) =>
                        getModifiedValue({
                            label: parseToString(title),
                            value,
                        })
                    )
                )
            )
        );
    }, [treeData]);

    // Placeholders are outside the domain of options
    const placeholderOption = useMemo(
        () =>
            placeholder && (
                <option value="" hidden selected>
                    {placeholder}
                </option>
            ),
        [placeholder]
    );

    const parsedOptions = useMemo(() => {
        if (!treeData) {
            return [];
        }

        const getOption = (option: OptionType) => {
            const optionValue = getModifiedValue(option);

            return (
                <option
                    key={optionValue.value}
                    selected={optionValue.originalValue === field.value}
                    value={optionValue.value}
                >
                    {optionValue.label}
                </option>
            );
        };

        return treeData.map(tree => (
            <POptgroup key={tree.value.toString()} label={parseToString(tree.title)}>
                {tree.children.map(({ title, value }) => getOption({ label: parseToString(title), value }))}
            </POptgroup>
        ));
    }, [field.value, treeData]);

    const onChangeHandler = useCallback(
        (e: React.ChangeEvent<HTMLSelectElement>) => {
            const foundOption = valueOnlyOptions.find(option => option.value === e.target.value);

            enhancedOnChange(foundOption?.originalValue ?? e.target.value);

            if (onSelect) {
                onSelect(foundOption?.originalValue ?? e.target.value, { label: foundOption?.label });
            }
        },
        [enhancedOnChange, onSelect, valueOnlyOptions]
    );

    const { hasError, errorMessage } = useMemo(
        () => ({
            hasError: !!meta?.error && meta?.touched,
            errorMessage: Array.isArray(meta?.error) ? '' : meta?.error,
        }),
        [meta?.error, meta?.touched]
    );

    return (
        <div ref={wrapperRef} className="porsche-v3-calculator-select-field-wrapper">
            <FormItem {...itemProps} required={required}>
                <PSelectWrapper state={hasError ? 'error' : getTextFieldState('')}>
                    {label && (
                        <span slot="label">
                            {label}
                            {renderTooltip()}
                        </span>
                    )}
                    <select
                        {...omit('value', field)}
                        disabled={props.disabled}
                        onChange={onChangeHandler}
                        required={required}
                    >
                        {placeholderOption}
                        {parsedOptions}
                    </select>
                    {hasError && <span slot="message">{errorMessage}</span>}
                </PSelectWrapper>
            </FormItem>
        </div>
    );
};

export default memo(TreeSelectField);

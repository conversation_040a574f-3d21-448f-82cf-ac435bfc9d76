import type * as SchemaTypes from '../types';

import type { SystemMessageData_MaintenanceUpdate_Fragment, SystemMessageData_MessageNotice_Fragment, SystemMessageData_UserSessionRevoked_Fragment } from '../fragments/SystemMessageData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { SystemMessageDataFragmentDoc } from '../fragments/SystemMessageData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListenOnSystemSubscriptionVariables = SchemaTypes.Exact<{
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListenOnSystemSubscription = (
  { __typename: 'Subscription' }
  & { message: (
    { __typename: 'MaintenanceUpdate' }
    & SystemMessageData_MaintenanceUpdate_Fragment
  ) | (
    { __typename: 'MessageNotice' }
    & SystemMessageData_MessageNotice_Fragment
  ) | (
    { __typename: 'UserSessionRevoked' }
    & SystemMessageData_UserSessionRevoked_Fragment
  ) }
);


export const ListenOnSystemDocument = /*#__PURE__*/ gql`
    subscription listenOnSystem($companyId: ObjectID) {
  message: listenSystemMessages(companyId: $companyId) {
    ...SystemMessageData
  }
}
    ${SystemMessageDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListenOnSystemSubscription__
 *
 * To run a query within a React component, call `useListenOnSystemSubscription` and pass it any options that fit your needs.
 * When your component renders, `useListenOnSystemSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListenOnSystemSubscription({
 *   variables: {
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useListenOnSystemSubscription(baseOptions?: Apollo.SubscriptionHookOptions<ListenOnSystemSubscription, ListenOnSystemSubscriptionVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<ListenOnSystemSubscription, ListenOnSystemSubscriptionVariables>(ListenOnSystemDocument, options);
      }
export type ListenOnSystemSubscriptionHookResult = ReturnType<typeof useListenOnSystemSubscription>;
export type ListenOnSystemSubscriptionResult = Apollo.SubscriptionResult<ListenOnSystemSubscription>;
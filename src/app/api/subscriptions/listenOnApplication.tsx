import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListenOnApplicationSubscriptionVariables = SchemaTypes.Exact<{
  sessionId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListenOnApplicationSubscription = (
  { __typename: 'Subscription' }
  & { message: (
    { __typename: 'ApplicationMessage' }
    & Pick<SchemaTypes.ApplicationMessage, 'originPath'>
  ) }
);


export const ListenOnApplicationDocument = /*#__PURE__*/ gql`
    subscription listenOnApplication($sessionId: ObjectID!) {
  message: listenApplicationMessages(sessionId: $sessionId) {
    originPath
  }
}
    `;

/**
 * __useListenOnApplicationSubscription__
 *
 * To run a query within a React component, call `useListenOnApplicationSubscription` and pass it any options that fit your needs.
 * When your component renders, `useListenOnApplicationSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListenOnApplicationSubscription({
 *   variables: {
 *      sessionId: // value for 'sessionId'
 *   },
 * });
 */
export function useListenOnApplicationSubscription(baseOptions: Apollo.SubscriptionHookOptions<ListenOnApplicationSubscription, ListenOnApplicationSubscriptionVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useSubscription<ListenOnApplicationSubscription, ListenOnApplicationSubscriptionVariables>(ListenOnApplicationDocument, options);
      }
export type ListenOnApplicationSubscriptionHookResult = ReturnType<typeof useListenOnApplicationSubscription>;
export type ListenOnApplicationSubscriptionResult = Apollo.SubscriptionResult<ListenOnApplicationSubscription>;
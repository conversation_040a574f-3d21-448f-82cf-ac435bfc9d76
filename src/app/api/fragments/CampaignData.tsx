import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
export type CampaignDataFragment = (
  { __typename: 'Campaign' }
  & Pick<SchemaTypes.Campaign, 'id' | 'companyId' | 'campaignId' | 'isActive' | 'permissions'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'displayName'>
  ), description: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ) }
);

export const CampaignDataFragmentDoc = /*#__PURE__*/ gql`
    fragment CampaignData on Campaign {
  id
  companyId
  company {
    id
    displayName
  }
  campaignId
  description {
    ...TranslatedStringData
  }
  isActive
  permissions
}
    `;
import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
export type CampaignOptionDataFragment = (
  { __typename: 'Campaign' }
  & Pick<SchemaTypes.Campaign, 'id' | 'campaignId' | 'isActive'>
  & { description: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ) }
);

export const CampaignOptionDataFragmentDoc = /*#__PURE__*/ gql`
    fragment CampaignOptionData on Campaign {
  id
  campaignId
  description {
    ...TranslatedStringData
  }
  isActive
}
    `;
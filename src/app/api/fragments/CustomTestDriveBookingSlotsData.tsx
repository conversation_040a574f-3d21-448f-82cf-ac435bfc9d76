import type * as SchemaTypes from '../types';

import type { TestDriveFixedPeriodDataFragment } from './TestDriveFixedPeriodData';
import type { TimeSlotDataFragment } from './TimeSlotData';
import type { TestDriveBookingWindowSettingsDataFragment } from './TestDriveBookingWindowSettingsData';
import { gql } from '@apollo/client';
import { TestDriveFixedPeriodDataFragmentDoc } from './TestDriveFixedPeriodData';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
import { TestDriveBookingWindowSettingsDataFragmentDoc } from './TestDriveBookingWindowSettingsData';
export type CustomTestDriveBookingSlotsDataFragment = (
  { __typename: 'CustomTestDriveBookingSlots' }
  & Pick<SchemaTypes.CustomTestDriveBookingSlots, 'isEnabled' | 'bookingPeriodType'>
  & { fixedPeriods?: SchemaTypes.Maybe<Array<(
    { __typename: 'TestDriveFixedPeriod' }
    & TestDriveFixedPeriodDataFragment
  )>>, bookingWindowSettings?: SchemaTypes.Maybe<(
    { __typename: 'TestDriveBookingWindowSettings' }
    & TestDriveBookingWindowSettingsDataFragment
  )> }
);

export const CustomTestDriveBookingSlotsDataFragmentDoc = /*#__PURE__*/ gql`
    fragment CustomTestDriveBookingSlotsData on CustomTestDriveBookingSlots {
  isEnabled
  bookingPeriodType
  fixedPeriods {
    ...TestDriveFixedPeriodData
  }
  bookingWindowSettings {
    ...TestDriveBookingWindowSettingsData
  }
}
    `;
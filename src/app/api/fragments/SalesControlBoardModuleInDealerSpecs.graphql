fragment SalesControlBoardModuleInDealerSpecs on SalesControlBoardModule {
    displayName
    id
    company {
        id
        currency
        displayName
        timeZone
        countryCode
        users {
            id
            displayName
        }
    }

    testDriveMonthlyTarget {
        ...DealerIntData
    }

    orderIntakesMonthlyTarget {
        ...DealerIntData
    }
    retailsMonthlyTarget {
        ...DealerIntData
    }
    financeCommissionMonthlyTarget {
        ...DealerFloatData
    }
    insuranceCommissionMonthlyTarget {
        ...DealerFloatData
    }
    salesConsultantsAssignments {
        ...DealerObjectIdData
    }
}

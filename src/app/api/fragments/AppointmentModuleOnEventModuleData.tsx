import type * as SchemaTypes from '../types';

import type { AppointmentTimeSlotDataFragment } from './AppointmentTimeSlotData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { gql } from '@apollo/client';
import { AppointmentTimeSlotDataFragmentDoc } from './AppointmentTimeSlotData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
export type AppointmentModuleOnEventModuleDataFragment = (
  { __typename: 'AppointmentModule' }
  & Pick<SchemaTypes.AppointmentModule, 'id' | 'displayName' | 'unavailableDayOfWeek' | 'advancedBookingLimit' | 'maxAdvancedBookingLimit' | 'hasTestDriveProcess' | 'hasTestDriveSigning' | 'timeToSendReminder' | 'isReminderTimeEnabled'>
  & { bookingTimeSlot: Array<(
    { __typename: 'AppointmentTimeSlot' }
    & AppointmentTimeSlotDataFragment
  )>, bookingInformation?: SchemaTypes.Maybe<(
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  )>, company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'timeZone' | 'countryCode'>
    & CompanyInModuleOptionDataFragment
  ) }
);

export const AppointmentModuleOnEventModuleDataFragmentDoc = /*#__PURE__*/ gql`
    fragment AppointmentModuleOnEventModuleData on AppointmentModule {
  id
  displayName
  unavailableDayOfWeek
  bookingTimeSlot {
    ...AppointmentTimeSlotData
  }
  advancedBookingLimit
  maxAdvancedBookingLimit
  bookingInformation {
    ...TranslatedStringData
  }
  hasTestDriveProcess
  hasTestDriveSigning
  timeToSendReminder
  isReminderTimeEnabled
  company {
    ...CompanyInModuleOptionData
    timeZone
    countryCode
  }
}
    `;
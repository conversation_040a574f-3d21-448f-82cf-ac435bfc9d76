import type * as SchemaTypes from '../types';

import type { TimeSlotDataFragment } from './TimeSlotData';
import { gql } from '@apollo/client';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
export type TestDriveBookingWindowSettingsDataFragment = (
  { __typename: 'TestDriveBookingWindowSettings' }
  & Pick<SchemaTypes.TestDriveBookingWindowSettings, 'unavailableDayOfWeek' | 'advancedBookingLimit' | 'maxAdvancedBookingLimit'>
  & { bookingTimeSlot: Array<(
    { __typename: 'TimeSlot' }
    & TimeSlotDataFragment
  )> }
);

export const TestDriveBookingWindowSettingsDataFragmentDoc = /*#__PURE__*/ gql`
    fragment TestDriveBookingWindowSettingsData on TestDriveBookingWindowSettings {
  unavailableDayOfWeek
  advancedBookingLimit
  maxAdvancedBookingLimit
  bookingTimeSlot {
    ...TimeSlotData
  }
}
    `;
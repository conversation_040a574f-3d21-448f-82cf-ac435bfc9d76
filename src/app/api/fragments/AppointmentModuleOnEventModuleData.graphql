fragment AppointmentModuleOnEventModuleData on AppointmentModule {
    id
    displayName
    unavailableDayOfWeek
    bookingTimeSlot {
        ...AppointmentTimeSlotData
    }

    advancedBookingLimit
    maxAdvancedBookingLimit
    bookingInformation {
        ...TranslatedStringData
    }

    hasTestDriveProcess
    hasTestDriveSigning
    timeToSendReminder
    isReminderTimeEnabled

    company {
        ...CompanyInModuleOptionData
        timeZone
        countryCode
    }
}

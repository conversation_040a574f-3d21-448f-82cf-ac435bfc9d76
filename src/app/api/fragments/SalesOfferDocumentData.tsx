import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type SalesOfferDocumentDataFragment = (
  { __typename: 'SalesOfferDocument' }
  & Pick<SchemaTypes.SalesOfferDocument, 'id' | 'kind' | 'filename' | 'etag' | 'uploadedAt' | 'url' | 'status' | 'lastUpdatedAt' | 'createdAt'>
  & { preview?: SchemaTypes.Maybe<(
    { __typename: 'UploadedFile' }
    & Pick<SchemaTypes.UploadedFile, 'id' | 'url'>
  )> }
);

export const SalesOfferDocumentDataFragmentDoc = /*#__PURE__*/ gql`
    fragment SalesOfferDocumentData on SalesOfferDocument {
  id
  kind
  filename
  etag
  uploadedAt
  url
  preview {
    id
    url
  }
  kind
  status
  lastUpdatedAt
  createdAt
}
    `;
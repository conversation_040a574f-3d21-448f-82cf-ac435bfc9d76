import type * as SchemaTypes from '../types';

import type { CompanyInModuleOptionDataFragment } from './CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from './SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from './AuthorData';
import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import { gql } from '@apollo/client';
import { CompanyInModuleOptionDataFragmentDoc } from './CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from './VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from './SimpleVersioningData';
import { AuthorDataFragmentDoc } from './AuthorData';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
export type SalesControlBoardModuleSpecsFragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'id' | 'displayName'>
  & { company: (
    { __typename: 'Company' }
    & CompanyInModuleOptionDataFragment
  ), versioning: (
    { __typename: 'SimpleVersioning' }
    & SimpleVersioningDataFragment
  ), testDriveMonthlyTarget: (
    { __typename: 'DealerInt' }
    & DealerIntDataFragment
  ), orderIntakesMonthlyTarget: (
    { __typename: 'DealerInt' }
    & DealerIntDataFragment
  ), retailsMonthlyTarget: (
    { __typename: 'DealerInt' }
    & DealerIntDataFragment
  ), financeCommissionMonthlyTarget: (
    { __typename: 'DealerFloat' }
    & DealerFloatDataFragment
  ), insuranceCommissionMonthlyTarget: (
    { __typename: 'DealerFloat' }
    & DealerFloatDataFragment
  ), salesConsultantsAssignments: (
    { __typename: 'DealerAssignmentObjectId' }
    & DealerObjectIdDataFragment
  ) }
);

export const SalesControlBoardModuleSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment SalesControlBoardModuleSpecs on SalesControlBoardModule {
  id
  displayName
  company {
    ...CompanyInModuleOptionData
  }
  versioning {
    ...SimpleVersioningData
  }
  testDriveMonthlyTarget {
    ...DealerIntData
  }
  orderIntakesMonthlyTarget {
    ...DealerIntData
  }
  retailsMonthlyTarget {
    ...DealerIntData
  }
  financeCommissionMonthlyTarget {
    ...DealerFloatData
  }
  insuranceCommissionMonthlyTarget {
    ...DealerFloatData
  }
  salesConsultantsAssignments {
    ...DealerObjectIdData
  }
}
    `;
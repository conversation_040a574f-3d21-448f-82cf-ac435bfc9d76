import type * as SchemaTypes from '../types';

import type { DealerIntDataFragment, DealerFloatDataFragment, DealerObjectIdDataFragment } from './DealerIntData';
import { gql } from '@apollo/client';
import { DealerIntDataFragmentDoc, DealerFloatDataFragmentDoc, DealerObjectIdDataFragmentDoc } from './DealerIntData';
export type SalesControlBoardModuleInDealerSpecsFragment = (
  { __typename: 'SalesControlBoardModule' }
  & Pick<SchemaTypes.SalesControlBoardModule, 'displayName' | 'id'>
  & { company: (
    { __typename: 'Company' }
    & Pick<SchemaTypes.Company, 'id' | 'currency' | 'displayName' | 'timeZone' | 'countryCode'>
    & { users: Array<(
      { __typename: 'User' }
      & Pick<SchemaTypes.User, 'id' | 'displayName'>
    )> }
  ), testDriveMonthlyTarget: (
    { __typename: 'DealerInt' }
    & DealerIntDataFragment
  ), orderIntakesMonthlyTarget: (
    { __typename: 'DealerInt' }
    & DealerIntDataFragment
  ), retailsMonthlyTarget: (
    { __typename: 'DealerInt' }
    & DealerIntDataFragment
  ), financeCommissionMonthlyTarget: (
    { __typename: 'DealerFloat' }
    & DealerFloatDataFragment
  ), insuranceCommissionMonthlyTarget: (
    { __typename: 'DealerFloat' }
    & DealerFloatDataFragment
  ), salesConsultantsAssignments: (
    { __typename: 'DealerAssignmentObjectId' }
    & DealerObjectIdDataFragment
  ) }
);

export const SalesControlBoardModuleInDealerSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment SalesControlBoardModuleInDealerSpecs on SalesControlBoardModule {
  displayName
  id
  company {
    id
    currency
    displayName
    timeZone
    countryCode
    users {
      id
      displayName
    }
  }
  testDriveMonthlyTarget {
    ...DealerIntData
  }
  orderIntakesMonthlyTarget {
    ...DealerIntData
  }
  retailsMonthlyTarget {
    ...DealerIntData
  }
  financeCommissionMonthlyTarget {
    ...DealerFloatData
  }
  insuranceCommissionMonthlyTarget {
    ...DealerFloatData
  }
  salesConsultantsAssignments {
    ...DealerObjectIdData
  }
}
    `;
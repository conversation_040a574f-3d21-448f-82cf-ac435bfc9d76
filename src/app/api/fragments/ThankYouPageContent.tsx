import type * as SchemaTypes from '../types';

import type { TranslatedTextDataFragment } from './TranslationTextData';
import type { TranslatedStringDataFragment } from './TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedTextDataFragmentDoc } from './TranslationTextData';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
export type ThankYouPageContentSpecsFragment = (
  { __typename: 'ThankYouPageContent' }
  & Pick<SchemaTypes.ThankYouPageContent, 'isCustomRedirectionButton'>
  & { introTitle: (
    { __typename: 'DealerTranslationText' }
    & TranslatedTextDataFragment
  ), contentText: (
    { __typename: 'DealerTranslationText' }
    & TranslatedTextDataFragment
  ), redirectButton: (
    { __typename: 'ThankyouPageRedirectButton' }
    & Pick<SchemaTypes.ThankyouPageRedirectButton, 'url'>
    & { title: (
      { __typename: 'DealerTranslationText' }
      & TranslatedTextDataFragment
    ) }
  ) }
);

export const ThankYouPageContentSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment ThankYouPageContentSpecs on ThankYouPageContent {
  introTitle {
    ...TranslatedTextData
  }
  contentText {
    ...TranslatedTextData
  }
  isCustomRedirectionButton
  redirectButton {
    title {
      ...TranslatedTextData
    }
    url
  }
}
    `;
import type * as SchemaTypes from '../types';

import type { TimeSlotDataFragment } from './TimeSlotData';
import { gql } from '@apollo/client';
import { TimeSlotDataFragmentDoc } from './TimeSlotData';
export type TestDriveFixedPeriodDataFragment = (
  { __typename: 'TestDriveFixedPeriod' }
  & Pick<SchemaTypes.TestDriveFixedPeriod, '_id' | 'startDate' | 'endDate' | 'advancedBookingLimit'>
  & { bookingTimeSlot: Array<(
    { __typename: 'TimeSlot' }
    & TimeSlotDataFragment
  )> }
);

export const TestDriveFixedPeriodDataFragmentDoc = /*#__PURE__*/ gql`
    fragment TestDriveFixedPeriodData on TestDriveFixedPeriod {
  _id
  startDate
  endDate
  advancedBookingLimit
  bookingTimeSlot {
    ...TimeSlotData
  }
}
    `;
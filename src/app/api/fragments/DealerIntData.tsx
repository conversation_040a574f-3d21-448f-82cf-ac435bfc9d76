import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
export type DealerIntDataFragment = (
  { __typename: 'DealerInt' }
  & Pick<SchemaTypes.DealerInt, 'defaultValue'>
  & { overrides: Array<(
    { __typename: 'DealerIntOverrides' }
    & Pick<SchemaTypes.DealerIntOverrides, 'dealerId' | 'value'>
  )> }
);

export type DealerObjectIdDataFragment = (
  { __typename: 'DealerAssignmentObjectId' }
  & Pick<SchemaTypes.DealerAssignmentObjectId, 'defaultValue'>
  & { overrides: Array<(
    { __typename: 'DealerAssignmentObjectIdOverrides' }
    & Pick<SchemaTypes.DealerAssignmentObjectIdOverrides, 'dealerId' | 'value'>
  )> }
);

export type DealerFloatDataFragment = (
  { __typename: 'DealerFloat' }
  & Pick<SchemaTypes.DealerFloat, 'defaultValue'>
  & { overrides: Array<(
    { __typename: 'DealerFloatOverrides' }
    & Pick<SchemaTypes.DealerFloatOverrides, 'dealerId' | 'value'>
  )> }
);

export const DealerIntDataFragmentDoc = /*#__PURE__*/ gql`
    fragment DealerIntData on DealerInt {
  defaultValue
  overrides {
    dealerId
    value
  }
}
    `;
export const DealerObjectIdDataFragmentDoc = /*#__PURE__*/ gql`
    fragment DealerObjectIdData on DealerAssignmentObjectId {
  defaultValue
  overrides {
    dealerId
    value
  }
}
    `;
export const DealerFloatDataFragmentDoc = /*#__PURE__*/ gql`
    fragment DealerFloatData on DealerFloat {
  defaultValue
  overrides {
    dealerId
    value
  }
}
    `;
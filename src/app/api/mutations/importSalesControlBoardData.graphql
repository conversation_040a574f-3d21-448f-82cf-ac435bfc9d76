mutation importSalesControlBoardData(
    $dataType: SalesControlBoardDataType!
    # format of the date is YYYY-MM
    $reportingPeriod: String!
    $file: Upload!
    $dealerId: ObjectID!
    $isImportOverwriteAllow: Boolean
) {
    importSalesControlBoardData(
        dataType: $dataType
        reportingPeriod: $reportingPeriod
        file: $file
        dealerId: $dealerId
        isImportOverwriteAllow: $isImportOverwriteAllow
    ) {
        hasError
        messages
    }
}

import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ExportCompanyConfigurationMutationVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
  options?: SchemaTypes.InputMaybe<SchemaTypes.CompanyExportOptions>;
}>;


export type ExportCompanyConfigurationMutation = (
  { __typename: 'Mutation' }
  & { exportCompanyConfiguration: (
    { __typename: 'CompanyExportResult' }
    & Pick<SchemaTypes.CompanyExportResult, 'success' | 'downloadUrl' | 'filename' | 'error'>
    & { statistics?: SchemaTypes.Maybe<(
      { __typename: 'CompanyExportStatistics' }
      & Pick<SchemaTypes.CompanyExportStatistics, 'modulesCount' | 'filesCount' | 'collectionsCount' | 'totalSize'>
    )> }
  ) }
);


export const ExportCompanyConfigurationDocument = /*#__PURE__*/ gql`
    mutation ExportCompanyConfiguration($companyId: ObjectID!, $options: CompanyExportOptions) {
  exportCompanyConfiguration(companyId: $companyId, options: $options) {
    success
    downloadUrl
    filename
    statistics {
      modulesCount
      filesCount
      collectionsCount
      totalSize
    }
    error
  }
}
    `;
export type ExportCompanyConfigurationMutationFn = Apollo.MutationFunction<ExportCompanyConfigurationMutation, ExportCompanyConfigurationMutationVariables>;

/**
 * __useExportCompanyConfigurationMutation__
 *
 * To run a mutation, you first call `useExportCompanyConfigurationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useExportCompanyConfigurationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [exportCompanyConfigurationMutation, { data, loading, error }] = useExportCompanyConfigurationMutation({
 *   variables: {
 *      companyId: // value for 'companyId'
 *      options: // value for 'options'
 *   },
 * });
 */
export function useExportCompanyConfigurationMutation(baseOptions?: Apollo.MutationHookOptions<ExportCompanyConfigurationMutation, ExportCompanyConfigurationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ExportCompanyConfigurationMutation, ExportCompanyConfigurationMutationVariables>(ExportCompanyConfigurationDocument, options);
      }
export type ExportCompanyConfigurationMutationHookResult = ReturnType<typeof useExportCompanyConfigurationMutation>;
export type ExportCompanyConfigurationMutationResult = Apollo.MutationResult<ExportCompanyConfigurationMutation>;
export type ExportCompanyConfigurationMutationOptions = Apollo.BaseMutationOptions<ExportCompanyConfigurationMutation, ExportCompanyConfigurationMutationVariables>;
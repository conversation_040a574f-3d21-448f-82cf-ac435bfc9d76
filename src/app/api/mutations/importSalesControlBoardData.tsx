import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ImportSalesControlBoardDataMutationVariables = SchemaTypes.Exact<{
  dataType: SchemaTypes.SalesControlBoardDataType;
  reportingPeriod: SchemaTypes.Scalars['String']['input'];
  file: SchemaTypes.Scalars['Upload']['input'];
  dealerId: SchemaTypes.Scalars['ObjectID']['input'];
  isImportOverwriteAllow?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
}>;


export type ImportSalesControlBoardDataMutation = (
  { __typename: 'Mutation' }
  & { importSalesControlBoardData: (
    { __typename: 'ImportSalesControlBoardResponses' }
    & Pick<SchemaTypes.ImportSalesControlBoardResponses, 'hasError' | 'messages'>
  ) }
);


export const ImportSalesControlBoardDataDocument = /*#__PURE__*/ gql`
    mutation importSalesControlBoardData($dataType: SalesControlBoardDataType!, $reportingPeriod: String!, $file: Upload!, $dealerId: ObjectID!, $isImportOverwriteAllow: Boolean) {
  importSalesControlBoardData(
    dataType: $dataType
    reportingPeriod: $reportingPeriod
    file: $file
    dealerId: $dealerId
    isImportOverwriteAllow: $isImportOverwriteAllow
  ) {
    hasError
    messages
  }
}
    `;
export type ImportSalesControlBoardDataMutationFn = Apollo.MutationFunction<ImportSalesControlBoardDataMutation, ImportSalesControlBoardDataMutationVariables>;

/**
 * __useImportSalesControlBoardDataMutation__
 *
 * To run a mutation, you first call `useImportSalesControlBoardDataMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useImportSalesControlBoardDataMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [importSalesControlBoardDataMutation, { data, loading, error }] = useImportSalesControlBoardDataMutation({
 *   variables: {
 *      dataType: // value for 'dataType'
 *      reportingPeriod: // value for 'reportingPeriod'
 *      file: // value for 'file'
 *      dealerId: // value for 'dealerId'
 *      isImportOverwriteAllow: // value for 'isImportOverwriteAllow'
 *   },
 * });
 */
export function useImportSalesControlBoardDataMutation(baseOptions?: Apollo.MutationHookOptions<ImportSalesControlBoardDataMutation, ImportSalesControlBoardDataMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ImportSalesControlBoardDataMutation, ImportSalesControlBoardDataMutationVariables>(ImportSalesControlBoardDataDocument, options);
      }
export type ImportSalesControlBoardDataMutationHookResult = ReturnType<typeof useImportSalesControlBoardDataMutation>;
export type ImportSalesControlBoardDataMutationResult = Apollo.MutationResult<ImportSalesControlBoardDataMutation>;
export type ImportSalesControlBoardDataMutationOptions = Apollo.BaseMutationOptions<ImportSalesControlBoardDataMutation, ImportSalesControlBoardDataMutationVariables>;
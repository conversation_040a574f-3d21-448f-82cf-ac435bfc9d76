mutation createGroupConsentsAndDeclarations(
    $moduleId: ObjectID!
    $settings: GroupConsentsAndDeclarationsSettings!
    $conditionSettings: [ConditionSettings!]
    $eventId: ObjectID
) {
    consentsAndDeclarations: createGroupConsentsAndDeclarations(
        moduleId: $moduleId
        settings: $settings
        conditionSettings: $conditionSettings
        eventId: $eventId
    ) {
        ...ConsentsAndDeclarationsSpecs
    }
}

import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ImportDealersMutationVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
  upload: SchemaTypes.Scalars['Upload']['input'];
}>;


export type ImportDealersMutation = (
  { __typename: 'Mutation' }
  & { importDealers: (
    { __typename: 'ImportDealerFail' }
    & Pick<SchemaTypes.ImportDealerFail, 'message' | 'errors'>
  ) | (
    { __typename: 'ImportDealerSuccess' }
    & Pick<SchemaTypes.ImportDealerSuccess, 'createdCount' | 'createdRoleCount' | 'createdUserGroupCount'>
  ) }
);


export const ImportDealersDocument = /*#__PURE__*/ gql`
    mutation ImportDealers($companyId: ObjectID!, $upload: Upload!) {
  importDealers(companyId: $companyId, upload: $upload) {
    __typename
    ... on ImportDealerSuccess {
      createdCount
      createdRoleCount
      createdUserGroupCount
    }
    ... on ImportDealerFail {
      message
      errors
    }
  }
}
    `;
export type ImportDealersMutationFn = Apollo.MutationFunction<ImportDealersMutation, ImportDealersMutationVariables>;

/**
 * __useImportDealersMutation__
 *
 * To run a mutation, you first call `useImportDealersMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useImportDealersMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [importDealersMutation, { data, loading, error }] = useImportDealersMutation({
 *   variables: {
 *      companyId: // value for 'companyId'
 *      upload: // value for 'upload'
 *   },
 * });
 */
export function useImportDealersMutation(baseOptions?: Apollo.MutationHookOptions<ImportDealersMutation, ImportDealersMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ImportDealersMutation, ImportDealersMutationVariables>(ImportDealersDocument, options);
      }
export type ImportDealersMutationHookResult = ReturnType<typeof useImportDealersMutation>;
export type ImportDealersMutationResult = Apollo.MutationResult<ImportDealersMutation>;
export type ImportDealersMutationOptions = Apollo.BaseMutationOptions<ImportDealersMutation, ImportDealersMutationVariables>;
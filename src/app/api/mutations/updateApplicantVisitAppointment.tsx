import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type UpdateApplicantVisitAppointmentMutationVariables = SchemaTypes.Exact<{
  token: SchemaTypes.Scalars['String']['input'];
  bookingTimeSlot: SchemaTypes.SubmitAppointmentBookingTimeSlot;
}>;


export type UpdateApplicantVisitAppointmentMutation = (
  { __typename: 'Mutation' }
  & Pick<SchemaTypes.Mutation, 'updateApplicantVisitAppointment'>
);


export const UpdateApplicantVisitAppointmentDocument = /*#__PURE__*/ gql`
    mutation updateApplicantVisitAppointment($token: String!, $bookingTimeSlot: SubmitAppointmentBookingTimeSlot!) {
  updateApplicantVisitAppointment(
    token: $token
    bookingTimeSlot: $bookingTimeSlot
  )
}
    `;
export type UpdateApplicantVisitAppointmentMutationFn = Apollo.MutationFunction<UpdateApplicantVisitAppointmentMutation, UpdateApplicantVisitAppointmentMutationVariables>;

/**
 * __useUpdateApplicantVisitAppointmentMutation__
 *
 * To run a mutation, you first call `useUpdateApplicantVisitAppointmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateApplicantVisitAppointmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateApplicantVisitAppointmentMutation, { data, loading, error }] = useUpdateApplicantVisitAppointmentMutation({
 *   variables: {
 *      token: // value for 'token'
 *      bookingTimeSlot: // value for 'bookingTimeSlot'
 *   },
 * });
 */
export function useUpdateApplicantVisitAppointmentMutation(baseOptions?: Apollo.MutationHookOptions<UpdateApplicantVisitAppointmentMutation, UpdateApplicantVisitAppointmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateApplicantVisitAppointmentMutation, UpdateApplicantVisitAppointmentMutationVariables>(UpdateApplicantVisitAppointmentDocument, options);
      }
export type UpdateApplicantVisitAppointmentMutationHookResult = ReturnType<typeof useUpdateApplicantVisitAppointmentMutation>;
export type UpdateApplicantVisitAppointmentMutationResult = Apollo.MutationResult<UpdateApplicantVisitAppointmentMutation>;
export type UpdateApplicantVisitAppointmentMutationOptions = Apollo.BaseMutationOptions<UpdateApplicantVisitAppointmentMutation, UpdateApplicantVisitAppointmentMutationVariables>;
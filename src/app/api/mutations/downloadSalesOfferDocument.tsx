import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type DownloadSalesOfferDocumentMutationVariables = SchemaTypes.Exact<{
  salesOfferId: SchemaTypes.Scalars['ObjectID']['input'];
  fileId: SchemaTypes.Scalars['ObjectID']['input'];
  kind: SchemaTypes.SalesOfferDocumentKind;
}>;


export type DownloadSalesOfferDocumentMutation = (
  { __typename: 'Mutation' }
  & Pick<SchemaTypes.Mutation, 'downloadSalesOfferDocument'>
);


export const DownloadSalesOfferDocumentDocument = /*#__PURE__*/ gql`
    mutation downloadSalesOfferDocument($salesOfferId: ObjectID!, $fileId: ObjectID!, $kind: SalesOfferDocumentKind!) {
  downloadSalesOfferDocument(
    salesOfferId: $salesOfferId
    fileId: $fileId
    kind: $kind
  )
}
    `;
export type DownloadSalesOfferDocumentMutationFn = Apollo.MutationFunction<DownloadSalesOfferDocumentMutation, DownloadSalesOfferDocumentMutationVariables>;

/**
 * __useDownloadSalesOfferDocumentMutation__
 *
 * To run a mutation, you first call `useDownloadSalesOfferDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDownloadSalesOfferDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [downloadSalesOfferDocumentMutation, { data, loading, error }] = useDownloadSalesOfferDocumentMutation({
 *   variables: {
 *      salesOfferId: // value for 'salesOfferId'
 *      fileId: // value for 'fileId'
 *      kind: // value for 'kind'
 *   },
 * });
 */
export function useDownloadSalesOfferDocumentMutation(baseOptions?: Apollo.MutationHookOptions<DownloadSalesOfferDocumentMutation, DownloadSalesOfferDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DownloadSalesOfferDocumentMutation, DownloadSalesOfferDocumentMutationVariables>(DownloadSalesOfferDocumentDocument, options);
      }
export type DownloadSalesOfferDocumentMutationHookResult = ReturnType<typeof useDownloadSalesOfferDocumentMutation>;
export type DownloadSalesOfferDocumentMutationResult = Apollo.MutationResult<DownloadSalesOfferDocumentMutation>;
export type DownloadSalesOfferDocumentMutationOptions = Apollo.BaseMutationOptions<DownloadSalesOfferDocumentMutation, DownloadSalesOfferDocumentMutationVariables>;
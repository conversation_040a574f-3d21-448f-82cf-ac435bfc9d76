import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type UpdateApplicantAppointmentMutationVariables = SchemaTypes.Exact<{
  token: SchemaTypes.Scalars['String']['input'];
  bookingTimeSlot: SchemaTypes.SubmitAppointmentBookingTimeSlot;
}>;


export type UpdateApplicantAppointmentMutation = (
  { __typename: 'Mutation' }
  & Pick<SchemaTypes.Mutation, 'updateApplicantAppointment'>
);


export const UpdateApplicantAppointmentDocument = /*#__PURE__*/ gql`
    mutation updateApplicantAppointment($token: String!, $bookingTimeSlot: SubmitAppointmentBookingTimeSlot!) {
  updateApplicantAppointment(token: $token, bookingTimeSlot: $bookingTimeSlot)
}
    `;
export type UpdateApplicantAppointmentMutationFn = Apollo.MutationFunction<UpdateApplicantAppointmentMutation, UpdateApplicantAppointmentMutationVariables>;

/**
 * __useUpdateApplicantAppointmentMutation__
 *
 * To run a mutation, you first call `useUpdateApplicantAppointmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateApplicantAppointmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateApplicantAppointmentMutation, { data, loading, error }] = useUpdateApplicantAppointmentMutation({
 *   variables: {
 *      token: // value for 'token'
 *      bookingTimeSlot: // value for 'bookingTimeSlot'
 *   },
 * });
 */
export function useUpdateApplicantAppointmentMutation(baseOptions?: Apollo.MutationHookOptions<UpdateApplicantAppointmentMutation, UpdateApplicantAppointmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateApplicantAppointmentMutation, UpdateApplicantAppointmentMutationVariables>(UpdateApplicantAppointmentDocument, options);
      }
export type UpdateApplicantAppointmentMutationHookResult = ReturnType<typeof useUpdateApplicantAppointmentMutation>;
export type UpdateApplicantAppointmentMutationResult = Apollo.MutationResult<UpdateApplicantAppointmentMutation>;
export type UpdateApplicantAppointmentMutationOptions = Apollo.BaseMutationOptions<UpdateApplicantAppointmentMutation, UpdateApplicantAppointmentMutationVariables>;
import type * as SchemaTypes from '../types';

import type { CampaignDataFragment } from '../fragments/CampaignData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { CampaignDataFragmentDoc } from '../fragments/CampaignData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type CreateCampaignMutationVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
  settings: SchemaTypes.CampaignSettings;
}>;


export type CreateCampaignMutation = (
  { __typename: 'Mutation' }
  & { createCampaign: (
    { __typename: 'Campaign' }
    & CampaignDataFragment
  ) }
);


export const CreateCampaignDocument = /*#__PURE__*/ gql`
    mutation createCampaign($companyId: ObjectID!, $settings: CampaignSettings!) {
  createCampaign(companyId: $companyId, settings: $settings) {
    ...CampaignData
  }
}
    ${CampaignDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;
export type CreateCampaignMutationFn = Apollo.MutationFunction<CreateCampaignMutation, CreateCampaignMutationVariables>;

/**
 * __useCreateCampaignMutation__
 *
 * To run a mutation, you first call `useCreateCampaignMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateCampaignMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createCampaignMutation, { data, loading, error }] = useCreateCampaignMutation({
 *   variables: {
 *      companyId: // value for 'companyId'
 *      settings: // value for 'settings'
 *   },
 * });
 */
export function useCreateCampaignMutation(baseOptions?: Apollo.MutationHookOptions<CreateCampaignMutation, CreateCampaignMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreateCampaignMutation, CreateCampaignMutationVariables>(CreateCampaignDocument, options);
      }
export type CreateCampaignMutationHookResult = ReturnType<typeof useCreateCampaignMutation>;
export type CreateCampaignMutationResult = Apollo.MutationResult<CreateCampaignMutation>;
export type CreateCampaignMutationOptions = Apollo.BaseMutationOptions<CreateCampaignMutation, CreateCampaignMutationVariables>;
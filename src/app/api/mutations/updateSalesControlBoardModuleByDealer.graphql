mutation updateSalesControlBoardModuleByDealer(
    $moduleId: ObjectID!
    $dealerId: ObjectID!
    $retailsMonthlyTarget: Int!
    $testDriveMonthlyTarget: Int!
    $orderIntakesMonthlyTarget: Int!
    $financeCommissionMonthlyTarget: Float!
    $insuranceCommissionMonthlyTarget: Float!
    $salesConsultantsAssignments: [ObjectID!]!
) {
    module: updateSalesControlBoardModuleByDealer(
        moduleId: $moduleId
        dealerId: $dealerId
        retailsMonthlyTarget: $retailsMonthlyTarget
        testDriveMonthlyTarget: $testDriveMonthlyTarget
        orderIntakesMonthlyTarget: $orderIntakesMonthlyTarget
        financeCommissionMonthlyTarget: $financeCommissionMonthlyTarget
        insuranceCommissionMonthlyTarget: $insuranceCommissionMonthlyTarget
        salesConsultantsAssignments: $salesConsultantsAssignments
    ) {
        ...ModuleInDealerSpecs
    }
}

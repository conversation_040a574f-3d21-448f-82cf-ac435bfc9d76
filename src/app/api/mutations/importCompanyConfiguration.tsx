import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ImportCompanyConfigurationMutationVariables = SchemaTypes.Exact<{
  upload: SchemaTypes.Scalars['Upload']['input'];
  options?: SchemaTypes.InputMaybe<SchemaTypes.CompanyImportOptions>;
}>;


export type ImportCompanyConfigurationMutation = (
  { __typename: 'Mutation' }
  & { importCompanyConfiguration: (
    { __typename: 'CompanyImportResult' }
    & Pick<SchemaTypes.CompanyImportResult, 'success' | 'companyId' | 'message' | 'errors'>
    & { conflicts?: SchemaTypes.Maybe<Array<(
      { __typename: 'CompanyImportConflict' }
      & Pick<SchemaTypes.CompanyImportConflict, 'collection' | 'documentId' | 'field' | 'message'>
    )>>, statistics?: SchemaTypes.Maybe<(
      { __typename: 'CompanyImportStatistics' }
      & Pick<SchemaTypes.CompanyImportStatistics, 'modulesCount' | 'filesCount' | 'collectionsCount' | 'totalSize'>
    )> }
  ) }
);


export const ImportCompanyConfigurationDocument = /*#__PURE__*/ gql`
    mutation ImportCompanyConfiguration($upload: Upload!, $options: CompanyImportOptions) {
  importCompanyConfiguration(upload: $upload, options: $options) {
    success
    companyId
    message
    conflicts {
      collection
      documentId
      field
      message
    }
    statistics {
      modulesCount
      filesCount
      collectionsCount
      totalSize
    }
    errors
  }
}
    `;
export type ImportCompanyConfigurationMutationFn = Apollo.MutationFunction<ImportCompanyConfigurationMutation, ImportCompanyConfigurationMutationVariables>;

/**
 * __useImportCompanyConfigurationMutation__
 *
 * To run a mutation, you first call `useImportCompanyConfigurationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useImportCompanyConfigurationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [importCompanyConfigurationMutation, { data, loading, error }] = useImportCompanyConfigurationMutation({
 *   variables: {
 *      upload: // value for 'upload'
 *      options: // value for 'options'
 *   },
 * });
 */
export function useImportCompanyConfigurationMutation(baseOptions?: Apollo.MutationHookOptions<ImportCompanyConfigurationMutation, ImportCompanyConfigurationMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ImportCompanyConfigurationMutation, ImportCompanyConfigurationMutationVariables>(ImportCompanyConfigurationDocument, options);
      }
export type ImportCompanyConfigurationMutationHookResult = ReturnType<typeof useImportCompanyConfigurationMutation>;
export type ImportCompanyConfigurationMutationResult = Apollo.MutationResult<ImportCompanyConfigurationMutation>;
export type ImportCompanyConfigurationMutationOptions = Apollo.BaseMutationOptions<ImportCompanyConfigurationMutation, ImportCompanyConfigurationMutationVariables>;
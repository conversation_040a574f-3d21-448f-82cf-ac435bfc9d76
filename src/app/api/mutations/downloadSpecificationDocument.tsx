import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type DownloadSpecificationDocumentMutationVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type DownloadSpecificationDocumentMutation = (
  { __typename: 'Mutation' }
  & Pick<SchemaTypes.Mutation, 'downloadSpecificationDocument'>
);


export const DownloadSpecificationDocumentDocument = /*#__PURE__*/ gql`
    mutation downloadSpecificationDocument($id: ObjectID!) {
  downloadSpecificationDocument(id: $id)
}
    `;
export type DownloadSpecificationDocumentMutationFn = Apollo.MutationFunction<DownloadSpecificationDocumentMutation, DownloadSpecificationDocumentMutationVariables>;

/**
 * __useDownloadSpecificationDocumentMutation__
 *
 * To run a mutation, you first call `useDownloadSpecificationDocumentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDownloadSpecificationDocumentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [downloadSpecificationDocumentMutation, { data, loading, error }] = useDownloadSpecificationDocumentMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDownloadSpecificationDocumentMutation(baseOptions?: Apollo.MutationHookOptions<DownloadSpecificationDocumentMutation, DownloadSpecificationDocumentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DownloadSpecificationDocumentMutation, DownloadSpecificationDocumentMutationVariables>(DownloadSpecificationDocumentDocument, options);
      }
export type DownloadSpecificationDocumentMutationHookResult = ReturnType<typeof useDownloadSpecificationDocumentMutation>;
export type DownloadSpecificationDocumentMutationResult = Apollo.MutationResult<DownloadSpecificationDocumentMutation>;
export type DownloadSpecificationDocumentMutationOptions = Apollo.BaseMutationOptions<DownloadSpecificationDocumentMutation, DownloadSpecificationDocumentMutationVariables>;
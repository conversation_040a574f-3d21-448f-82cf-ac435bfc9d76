import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type RefineTextQueryVariables = SchemaTypes.Exact<{
  text: SchemaTypes.Scalars['String']['input'];
  source: SchemaTypes.ContentRefinementSourceType;
  languagePackId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
}>;


export type RefineTextQuery = (
  { __typename: 'Query' }
  & { refineText: SchemaTypes.Query['refineText'] }
);


export const RefineTextDocument = /*#__PURE__*/ gql`
    query refineText($text: String!, $source: ContentRefinementSourceType!, $languagePackId: String) {
  refineText: refineText(
    text: $text
    source: $source
    languagePackId: $languagePackId
  )
}
    `;

/**
 * __useRefineTextQuery__
 *
 * To run a query within a React component, call `useRefineTextQuery` and pass it any options that fit your needs.
 * When your component renders, `useRefineTextQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useRefineTextQuery({
 *   variables: {
 *      text: // value for 'text'
 *      source: // value for 'source'
 *      languagePackId: // value for 'languagePackId'
 *   },
 * });
 */
export function useRefineTextQuery(baseOptions: Apollo.QueryHookOptions<RefineTextQuery, RefineTextQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<RefineTextQuery, RefineTextQueryVariables>(RefineTextDocument, options);
      }
export function useRefineTextLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<RefineTextQuery, RefineTextQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<RefineTextQuery, RefineTextQueryVariables>(RefineTextDocument, options);
        }
export type RefineTextQueryHookResult = ReturnType<typeof useRefineTextQuery>;
export type RefineTextLazyQueryHookResult = ReturnType<typeof useRefineTextLazyQuery>;
export type RefineTextQueryResult = Apollo.QueryResult<RefineTextQuery, RefineTextQueryVariables>;
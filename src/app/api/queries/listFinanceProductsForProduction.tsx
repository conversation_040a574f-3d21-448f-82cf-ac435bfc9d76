import type * as SchemaTypes from '../types';

import type { ProductionFinanceProductDetails_LocalDeferredPrincipal_Fragment, ProductionFinanceProductDetails_LocalHirePurchase_Fragment, ProductionFinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment, ProductionFinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment, ProductionFinanceProductDetails_LocalLease_Fragment, ProductionFinanceProductDetails_LocalLeasePurchase_Fragment, ProductionFinanceProductDetails_LocalUcclLeasing_Fragment } from '../fragments/ProductionFinanceProductDetails';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { ProductionBankDetailsFragment } from '../fragments/ProductionBankDetails';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { PaymentSettingsDetailsFragment } from '../fragments/PaymentSettingsDetails';
import type { LoanSettingsDetailsFragment } from '../fragments/LoanSettingsDetails';
import type { TermSettingsDetails_DeferredPrincipalTermSettings_Fragment, TermSettingsDetails_GenericPrincipalTermSettings_Fragment } from '../fragments/TermSettingsDetails';
import type { InterestRateSettingsDetails_InterestRateFixedSettings_Fragment, InterestRateSettingsDetails_InterestRateRangeSettings_Fragment, InterestRateSettingsDetails_InterestRateTableSettings_Fragment } from '../fragments/InterestRateSettingsDetails';
import type { DownPaymentSettingsDetails_DownPaymentRangeSettings_Fragment, DownPaymentSettingsDetails_DownPaymentTableSettings_Fragment } from '../fragments/DownPaymentSettingsDetails';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import type { LeaseSettingsDetailsFragment } from '../fragments/LeaseSettingsDetails';
import type { DepositSettingsDetails_DepositRangeSettings_Fragment, DepositSettingsDetails_DepositTableSettings_Fragment } from '../fragments/DepositSettingsDetails';
import type { ResidualValueSettingsDetailsFragment } from '../fragments/ResidualValueSettingsDetails';
import type { LocalUcclLeasingOnlyDetailsFragment } from '../fragments/LocalUcclLeasingOnlyDetails';
import type { FinanceProductDetailsData_LocalDeferredPrincipal_Fragment, FinanceProductDetailsData_LocalHirePurchase_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductDetailsData_LocalLease_Fragment, FinanceProductDetailsData_LocalLeasePurchase_Fragment, FinanceProductDetailsData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductDetailsData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { ProductionFinanceProductDetailsFragmentDoc } from '../fragments/ProductionFinanceProductDetails';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { ProductionBankDetailsFragmentDoc } from '../fragments/ProductionBankDetails';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { PaymentSettingsDetailsFragmentDoc } from '../fragments/PaymentSettingsDetails';
import { LoanSettingsDetailsFragmentDoc } from '../fragments/LoanSettingsDetails';
import { TermSettingsDetailsFragmentDoc } from '../fragments/TermSettingsDetails';
import { InterestRateSettingsDetailsFragmentDoc } from '../fragments/InterestRateSettingsDetails';
import { DownPaymentSettingsDetailsFragmentDoc } from '../fragments/DownPaymentSettingsDetails';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import { LeaseSettingsDetailsFragmentDoc } from '../fragments/LeaseSettingsDetails';
import { DepositSettingsDetailsFragmentDoc } from '../fragments/DepositSettingsDetails';
import { ResidualValueSettingsDetailsFragmentDoc } from '../fragments/ResidualValueSettingsDetails';
import { LocalUcclLeasingOnlyDetailsFragmentDoc } from '../fragments/LocalUcclLeasingOnlyDetails';
import { FinanceProductDetailsDataFragmentDoc } from '../fragments/FinanceProductDetailsData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListFinanceProductsForProductionQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.FinanceProductFilteringRule>;
}>;


export type ListFinanceProductsForProductionQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedFinanceProducts' }
    & Pick<SchemaTypes.PaginatedFinanceProducts, 'count'>
    & { items: Array<(
      { __typename: 'LocalDeferredPrincipal' }
      & ProductionFinanceProductDetails_LocalDeferredPrincipal_Fragment
    ) | (
      { __typename: 'LocalHirePurchase' }
      & ProductionFinanceProductDetails_LocalHirePurchase_Fragment
    ) | (
      { __typename: 'LocalHirePurchaseWithBalloon' }
      & ProductionFinanceProductDetails_LocalHirePurchaseWithBalloon_Fragment
    ) | (
      { __typename: 'LocalHirePurchaseWithBalloonGFV' }
      & ProductionFinanceProductDetails_LocalHirePurchaseWithBalloonGfv_Fragment
    ) | (
      { __typename: 'LocalLease' }
      & ProductionFinanceProductDetails_LocalLease_Fragment
    ) | (
      { __typename: 'LocalLeasePurchase' }
      & ProductionFinanceProductDetails_LocalLeasePurchase_Fragment
    ) | (
      { __typename: 'LocalUcclLeasing' }
      & ProductionFinanceProductDetails_LocalUcclLeasing_Fragment
    )>, defaultFinanceProduct?: SchemaTypes.Maybe<(
      { __typename: 'LocalDeferredPrincipal' }
      & FinanceProductDetailsData_LocalDeferredPrincipal_Fragment
    ) | (
      { __typename: 'LocalHirePurchase' }
      & FinanceProductDetailsData_LocalHirePurchase_Fragment
    ) | (
      { __typename: 'LocalHirePurchaseWithBalloon' }
      & FinanceProductDetailsData_LocalHirePurchaseWithBalloon_Fragment
    ) | (
      { __typename: 'LocalHirePurchaseWithBalloonGFV' }
      & FinanceProductDetailsData_LocalHirePurchaseWithBalloonGfv_Fragment
    ) | (
      { __typename: 'LocalLease' }
      & FinanceProductDetailsData_LocalLease_Fragment
    ) | (
      { __typename: 'LocalLeasePurchase' }
      & FinanceProductDetailsData_LocalLeasePurchase_Fragment
    ) | (
      { __typename: 'LocalUcclLeasing' }
      & FinanceProductDetailsData_LocalUcclLeasing_Fragment
    )> }
  ) }
);


export const ListFinanceProductsForProductionDocument = /*#__PURE__*/ gql`
    query listFinanceProductsForProduction($filter: FinanceProductFilteringRule) {
  list: listFinanceProducts(filter: $filter) {
    count
    items {
      ...ProductionFinanceProductDetails
    }
    defaultFinanceProduct {
      ...FinanceProductDetailsData
    }
  }
}
    ${ProductionFinanceProductDetailsFragmentDoc}
${TranslatedStringDataFragmentDoc}
${ProductionBankDetailsFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${PaymentSettingsDetailsFragmentDoc}
${LoanSettingsDetailsFragmentDoc}
${TermSettingsDetailsFragmentDoc}
${InterestRateSettingsDetailsFragmentDoc}
${DownPaymentSettingsDetailsFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}
${LeaseSettingsDetailsFragmentDoc}
${DepositSettingsDetailsFragmentDoc}
${ResidualValueSettingsDetailsFragmentDoc}
${LocalUcclLeasingOnlyDetailsFragmentDoc}
${FinanceProductDetailsDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${PeriodDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListFinanceProductsForProductionQuery__
 *
 * To run a query within a React component, call `useListFinanceProductsForProductionQuery` and pass it any options that fit your needs.
 * When your component renders, `useListFinanceProductsForProductionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListFinanceProductsForProductionQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListFinanceProductsForProductionQuery(baseOptions?: Apollo.QueryHookOptions<ListFinanceProductsForProductionQuery, ListFinanceProductsForProductionQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListFinanceProductsForProductionQuery, ListFinanceProductsForProductionQueryVariables>(ListFinanceProductsForProductionDocument, options);
      }
export function useListFinanceProductsForProductionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListFinanceProductsForProductionQuery, ListFinanceProductsForProductionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListFinanceProductsForProductionQuery, ListFinanceProductsForProductionQueryVariables>(ListFinanceProductsForProductionDocument, options);
        }
export type ListFinanceProductsForProductionQueryHookResult = ReturnType<typeof useListFinanceProductsForProductionQuery>;
export type ListFinanceProductsForProductionLazyQueryHookResult = ReturnType<typeof useListFinanceProductsForProductionLazyQuery>;
export type ListFinanceProductsForProductionQueryResult = Apollo.QueryResult<ListFinanceProductsForProductionQuery, ListFinanceProductsForProductionQueryVariables>;
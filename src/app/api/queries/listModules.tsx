import type * as SchemaTypes from '../types';

import type { ModuleListData_AdyenPaymentModule_Fragment, ModuleListData_AppointmentModule_Fragment, ModuleListData_AutoplayModule_Fragment, ModuleListData_BankModule_Fragment, ModuleListData_BasicSigningModule_Fragment, ModuleListData_CapModule_Fragment, ModuleListData_ConfiguratorModule_Fragment, ModuleListData_ConsentsAndDeclarationsModule_Fragment, ModuleListData_CtsModule_Fragment, ModuleListData_DocusignModule_Fragment, ModuleListData_EventApplicationModule_Fragment, ModuleListData_FinderApplicationPrivateModule_Fragment, ModuleListData_FinderApplicationPublicModule_Fragment, ModuleListData_FinderVehicleManagementModule_Fragment, ModuleListData_FiservPaymentModule_Fragment, ModuleListData_GiftVoucherModule_Fragment, ModuleListData_InsuranceModule_Fragment, ModuleListData_LabelsModule_Fragment, ModuleListData_LaunchPadModule_Fragment, ModuleListData_LocalCustomerManagementModule_Fragment, ModuleListData_MaintenanceModule_Fragment, ModuleListData_MarketingModule_Fragment, ModuleListData_MobilityModule_Fragment, ModuleListData_MyInfoModule_Fragment, ModuleListData_NamirialSigningModule_Fragment, ModuleListData_OidcModule_Fragment, ModuleListData_PayGatePaymentModule_Fragment, ModuleListData_PorscheIdModule_Fragment, ModuleListData_PorscheMasterDataModule_Fragment, ModuleListData_PorschePaymentModule_Fragment, ModuleListData_PorscheRetainModule_Fragment, ModuleListData_PromoCodeModule_Fragment, ModuleListData_SalesControlBoardModule_Fragment, ModuleListData_SalesOfferModule_Fragment, ModuleListData_SimpleVehicleManagementModule_Fragment, ModuleListData_StandardApplicationModule_Fragment, ModuleListData_TradeInModule_Fragment, ModuleListData_TtbPaymentModule_Fragment, ModuleListData_UserlikeChatbotModule_Fragment, ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleListData_VisitAppointmentModule_Fragment, ModuleListData_WebsiteModule_Fragment, ModuleListData_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleListData';
import { gql } from '@apollo/client';
import { ModuleListDataFragmentDoc } from '../fragments/ModuleListData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListModulesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModuleFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.ModuleSortingRule>;
}>;


export type ListModulesQuery = (
  { __typename: 'Query' }
  & { page: (
    { __typename: 'PaginatedModules' }
    & Pick<SchemaTypes.PaginatedModules, 'count'>
    & { items: Array<(
      { __typename: 'AdyenPaymentModule' }
      & ModuleListData_AdyenPaymentModule_Fragment
    ) | (
      { __typename: 'AppointmentModule' }
      & ModuleListData_AppointmentModule_Fragment
    ) | (
      { __typename: 'AutoplayModule' }
      & ModuleListData_AutoplayModule_Fragment
    ) | (
      { __typename: 'BankModule' }
      & ModuleListData_BankModule_Fragment
    ) | (
      { __typename: 'BasicSigningModule' }
      & ModuleListData_BasicSigningModule_Fragment
    ) | (
      { __typename: 'CapModule' }
      & ModuleListData_CapModule_Fragment
    ) | (
      { __typename: 'ConfiguratorModule' }
      & ModuleListData_ConfiguratorModule_Fragment
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & ModuleListData_ConsentsAndDeclarationsModule_Fragment
    ) | (
      { __typename: 'CtsModule' }
      & ModuleListData_CtsModule_Fragment
    ) | (
      { __typename: 'DocusignModule' }
      & ModuleListData_DocusignModule_Fragment
    ) | (
      { __typename: 'EventApplicationModule' }
      & ModuleListData_EventApplicationModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & ModuleListData_FinderApplicationPrivateModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & ModuleListData_FinderApplicationPublicModule_Fragment
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & ModuleListData_FinderVehicleManagementModule_Fragment
    ) | (
      { __typename: 'FiservPaymentModule' }
      & ModuleListData_FiservPaymentModule_Fragment
    ) | (
      { __typename: 'GiftVoucherModule' }
      & ModuleListData_GiftVoucherModule_Fragment
    ) | (
      { __typename: 'InsuranceModule' }
      & ModuleListData_InsuranceModule_Fragment
    ) | (
      { __typename: 'LabelsModule' }
      & ModuleListData_LabelsModule_Fragment
    ) | (
      { __typename: 'LaunchPadModule' }
      & ModuleListData_LaunchPadModule_Fragment
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & ModuleListData_LocalCustomerManagementModule_Fragment
    ) | (
      { __typename: 'MaintenanceModule' }
      & ModuleListData_MaintenanceModule_Fragment
    ) | (
      { __typename: 'MarketingModule' }
      & ModuleListData_MarketingModule_Fragment
    ) | (
      { __typename: 'MobilityModule' }
      & ModuleListData_MobilityModule_Fragment
    ) | (
      { __typename: 'MyInfoModule' }
      & ModuleListData_MyInfoModule_Fragment
    ) | (
      { __typename: 'NamirialSigningModule' }
      & ModuleListData_NamirialSigningModule_Fragment
    ) | (
      { __typename: 'OIDCModule' }
      & ModuleListData_OidcModule_Fragment
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & ModuleListData_PayGatePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheIdModule' }
      & ModuleListData_PorscheIdModule_Fragment
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & ModuleListData_PorscheMasterDataModule_Fragment
    ) | (
      { __typename: 'PorschePaymentModule' }
      & ModuleListData_PorschePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheRetainModule' }
      & ModuleListData_PorscheRetainModule_Fragment
    ) | (
      { __typename: 'PromoCodeModule' }
      & ModuleListData_PromoCodeModule_Fragment
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & ModuleListData_SalesControlBoardModule_Fragment
    ) | (
      { __typename: 'SalesOfferModule' }
      & ModuleListData_SalesOfferModule_Fragment
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & ModuleListData_SimpleVehicleManagementModule_Fragment
    ) | (
      { __typename: 'StandardApplicationModule' }
      & ModuleListData_StandardApplicationModule_Fragment
    ) | (
      { __typename: 'TradeInModule' }
      & ModuleListData_TradeInModule_Fragment
    ) | (
      { __typename: 'TtbPaymentModule' }
      & ModuleListData_TtbPaymentModule_Fragment
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & ModuleListData_UserlikeChatbotModule_Fragment
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & ModuleListData_VehicleDataWithPorscheCodeIntegrationModule_Fragment
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & ModuleListData_VisitAppointmentModule_Fragment
    ) | (
      { __typename: 'WebsiteModule' }
      & ModuleListData_WebsiteModule_Fragment
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & ModuleListData_WhatsappLiveChatModule_Fragment
    )> }
  ) }
);


export const ListModulesDocument = /*#__PURE__*/ gql`
    query listModules($pagination: Pagination, $filter: ModuleFilteringRule, $sort: ModuleSortingRule) {
  page: listModules(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...ModuleListData
    }
  }
}
    ${ModuleListDataFragmentDoc}`;

/**
 * __useListModulesQuery__
 *
 * To run a query within a React component, call `useListModulesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListModulesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListModulesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListModulesQuery(baseOptions?: Apollo.QueryHookOptions<ListModulesQuery, ListModulesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListModulesQuery, ListModulesQueryVariables>(ListModulesDocument, options);
      }
export function useListModulesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListModulesQuery, ListModulesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListModulesQuery, ListModulesQueryVariables>(ListModulesDocument, options);
        }
export type ListModulesQueryHookResult = ReturnType<typeof useListModulesQuery>;
export type ListModulesLazyQueryHookResult = ReturnType<typeof useListModulesLazyQuery>;
export type ListModulesQueryResult = Apollo.QueryResult<ListModulesQuery, ListModulesQueryVariables>;
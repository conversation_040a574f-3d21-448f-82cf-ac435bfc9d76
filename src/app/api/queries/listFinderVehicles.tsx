import type * as SchemaTypes from '../types';

import type { FinderVehiclesListDataFragment } from '../fragments/FinderVehiclesListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { FinderVehiclesListDataFragmentDoc } from '../fragments/FinderVehiclesListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListFinderVehiclesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.FinderVehicleSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.FilterVehicleFilteringRule>;
}>;


export type ListFinderVehiclesQuery = (
  { __typename: 'Query' }
  & { vehicles: (
    { __typename: 'ListFinderVehiclesInfo' }
    & Pick<SchemaTypes.ListFinderVehiclesInfo, 'count'>
    & { items: Array<(
      { __typename: 'FinderVehicle' }
      & FinderVehiclesListDataFragment
    )> }
  ) }
);


export const ListFinderVehiclesDocument = /*#__PURE__*/ gql`
    query listFinderVehicles($pagination: Pagination, $sort: FinderVehicleSortingRule, $filter: FilterVehicleFilteringRule) {
  vehicles: listFinderVehicles(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    items {
      ...FinderVehiclesListData
    }
    count
  }
}
    ${FinderVehiclesListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListFinderVehiclesQuery__
 *
 * To run a query within a React component, call `useListFinderVehiclesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListFinderVehiclesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListFinderVehiclesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListFinderVehiclesQuery(baseOptions?: Apollo.QueryHookOptions<ListFinderVehiclesQuery, ListFinderVehiclesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListFinderVehiclesQuery, ListFinderVehiclesQueryVariables>(ListFinderVehiclesDocument, options);
      }
export function useListFinderVehiclesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListFinderVehiclesQuery, ListFinderVehiclesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListFinderVehiclesQuery, ListFinderVehiclesQueryVariables>(ListFinderVehiclesDocument, options);
        }
export type ListFinderVehiclesQueryHookResult = ReturnType<typeof useListFinderVehiclesQuery>;
export type ListFinderVehiclesLazyQueryHookResult = ReturnType<typeof useListFinderVehiclesLazyQuery>;
export type ListFinderVehiclesQueryResult = Apollo.QueryResult<ListFinderVehiclesQuery, ListFinderVehiclesQueryVariables>;
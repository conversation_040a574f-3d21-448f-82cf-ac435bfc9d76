import type * as SchemaTypes from '../types';

import type { AdyenPaymentSettingsSpecFragment } from '../fragments/AdyenPaymentSettingsSpec';
import { gql } from '@apollo/client';
import { AdyenPaymentSettingsSpecFragmentDoc } from '../fragments/AdyenPaymentSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetSettingOptionsForAdyenPaymentModuleQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetSettingOptionsForAdyenPaymentModuleQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedAdyenPaymentSettings' }
    & { items: Array<(
      { __typename: 'AdyenPaymentSetting' }
      & AdyenPaymentSettingsSpecFragment
    )> }
  ) }
);


export const GetSettingOptionsForAdyenPaymentModuleDocument = /*#__PURE__*/ gql`
    query getSettingOptionsForAdyenPaymentModule($moduleId: ObjectID!) {
  settings: listAdyenPaymentSettings(moduleId: $moduleId) {
    items {
      ...AdyenPaymentSettingsSpec
    }
  }
}
    ${AdyenPaymentSettingsSpecFragmentDoc}`;

/**
 * __useGetSettingOptionsForAdyenPaymentModuleQuery__
 *
 * To run a query within a React component, call `useGetSettingOptionsForAdyenPaymentModuleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSettingOptionsForAdyenPaymentModuleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSettingOptionsForAdyenPaymentModuleQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useGetSettingOptionsForAdyenPaymentModuleQuery(baseOptions: Apollo.QueryHookOptions<GetSettingOptionsForAdyenPaymentModuleQuery, GetSettingOptionsForAdyenPaymentModuleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSettingOptionsForAdyenPaymentModuleQuery, GetSettingOptionsForAdyenPaymentModuleQueryVariables>(GetSettingOptionsForAdyenPaymentModuleDocument, options);
      }
export function useGetSettingOptionsForAdyenPaymentModuleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSettingOptionsForAdyenPaymentModuleQuery, GetSettingOptionsForAdyenPaymentModuleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSettingOptionsForAdyenPaymentModuleQuery, GetSettingOptionsForAdyenPaymentModuleQueryVariables>(GetSettingOptionsForAdyenPaymentModuleDocument, options);
        }
export type GetSettingOptionsForAdyenPaymentModuleQueryHookResult = ReturnType<typeof useGetSettingOptionsForAdyenPaymentModuleQuery>;
export type GetSettingOptionsForAdyenPaymentModuleLazyQueryHookResult = ReturnType<typeof useGetSettingOptionsForAdyenPaymentModuleLazyQuery>;
export type GetSettingOptionsForAdyenPaymentModuleQueryResult = Apollo.QueryResult<GetSettingOptionsForAdyenPaymentModuleQuery, GetSettingOptionsForAdyenPaymentModuleQueryVariables>;
import type * as SchemaTypes from '../types';

import type { StatisticNameCounterFragment } from '../fragments/statisticNameCounter';
import type { StatisticNameCountResultFragment } from '../fragments/statisticNameCountResult';
import { gql } from '@apollo/client';
import { StatisticNameCounterFragmentDoc } from '../fragments/statisticNameCounter';
import { StatisticNameCountResultFragmentDoc } from '../fragments/statisticNameCountResult';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetPopularVariantApplicationQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.StatisticFilterPayload>;
}>;


export type GetPopularVariantApplicationQuery = (
  { __typename: 'Query' }
  & { metrics?: SchemaTypes.Maybe<(
    { __typename: 'StatisticMonthlyResult' }
    & StatisticNameCounterFragment
  )> }
);


export const GetPopularVariantApplicationDocument = /*#__PURE__*/ gql`
    query getPopularVariantApplication($filter: StatisticFilterPayload) {
  metrics: getPopularVariant(filter: $filter) {
    ...statisticNameCounter
  }
}
    ${StatisticNameCounterFragmentDoc}
${StatisticNameCountResultFragmentDoc}`;

/**
 * __useGetPopularVariantApplicationQuery__
 *
 * To run a query within a React component, call `useGetPopularVariantApplicationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPopularVariantApplicationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPopularVariantApplicationQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetPopularVariantApplicationQuery(baseOptions?: Apollo.QueryHookOptions<GetPopularVariantApplicationQuery, GetPopularVariantApplicationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPopularVariantApplicationQuery, GetPopularVariantApplicationQueryVariables>(GetPopularVariantApplicationDocument, options);
      }
export function useGetPopularVariantApplicationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPopularVariantApplicationQuery, GetPopularVariantApplicationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPopularVariantApplicationQuery, GetPopularVariantApplicationQueryVariables>(GetPopularVariantApplicationDocument, options);
        }
export type GetPopularVariantApplicationQueryHookResult = ReturnType<typeof useGetPopularVariantApplicationQuery>;
export type GetPopularVariantApplicationLazyQueryHookResult = ReturnType<typeof useGetPopularVariantApplicationLazyQuery>;
export type GetPopularVariantApplicationQueryResult = Apollo.QueryResult<GetPopularVariantApplicationQuery, GetPopularVariantApplicationQueryVariables>;
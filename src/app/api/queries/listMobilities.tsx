import type * as SchemaTypes from '../types';

import type { MobilityListData_MobilityAdditionalInfo_Fragment, MobilityListData_MobilityAddon_Fragment } from '../fragments/MobilityListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { MobilityListDataFragmentDoc } from '../fragments/MobilityListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListMobilitiesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.MobilitySortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.MobilityFilteringRule>;
}>;


export type ListMobilitiesQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedMobilities' }
    & Pick<SchemaTypes.PaginatedMobilities, 'count'>
    & { items: Array<(
      { __typename: 'MobilityAdditionalInfo' }
      & MobilityListData_MobilityAdditionalInfo_Fragment
    ) | (
      { __typename: 'MobilityAddon' }
      & MobilityListData_MobilityAddon_Fragment
    )> }
  ) }
);


export const ListMobilitiesDocument = /*#__PURE__*/ gql`
    query listMobilities($pagination: Pagination, $sort: MobilitySortingRule, $filter: MobilityFilteringRule) {
  list: listMobilities(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...MobilityListData
    }
  }
}
    ${MobilityListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListMobilitiesQuery__
 *
 * To run a query within a React component, call `useListMobilitiesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListMobilitiesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListMobilitiesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListMobilitiesQuery(baseOptions?: Apollo.QueryHookOptions<ListMobilitiesQuery, ListMobilitiesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListMobilitiesQuery, ListMobilitiesQueryVariables>(ListMobilitiesDocument, options);
      }
export function useListMobilitiesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListMobilitiesQuery, ListMobilitiesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListMobilitiesQuery, ListMobilitiesQueryVariables>(ListMobilitiesDocument, options);
        }
export type ListMobilitiesQueryHookResult = ReturnType<typeof useListMobilitiesQuery>;
export type ListMobilitiesLazyQueryHookResult = ReturnType<typeof useListMobilitiesLazyQuery>;
export type ListMobilitiesQueryResult = Apollo.QueryResult<ListMobilitiesQuery, ListMobilitiesQueryVariables>;
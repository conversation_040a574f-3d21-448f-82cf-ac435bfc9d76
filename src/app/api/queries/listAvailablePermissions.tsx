import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListAvailablePermissionsQueryVariables = SchemaTypes.Exact<{
  roleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListAvailablePermissionsQuery = (
  { __typename: 'Query' }
  & { permissions: Array<(
    { __typename: 'Permission' }
    & Pick<SchemaTypes.Permission, 'id' | 'displayName' | 'description' | 'tags' | 'isFromSystem' | 'raw'>
  )> }
);


export const ListAvailablePermissionsDocument = /*#__PURE__*/ gql`
    query listAvailablePermissions($roleId: ObjectID!) {
  permissions: listAvailablePermissions(roleId: $roleId) {
    id
    displayName
    description
    tags
    isFromSystem
    raw
  }
}
    `;

/**
 * __useListAvailablePermissionsQuery__
 *
 * To run a query within a React component, call `useListAvailablePermissionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListAvailablePermissionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListAvailablePermissionsQuery({
 *   variables: {
 *      roleId: // value for 'roleId'
 *   },
 * });
 */
export function useListAvailablePermissionsQuery(baseOptions: Apollo.QueryHookOptions<ListAvailablePermissionsQuery, ListAvailablePermissionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListAvailablePermissionsQuery, ListAvailablePermissionsQueryVariables>(ListAvailablePermissionsDocument, options);
      }
export function useListAvailablePermissionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListAvailablePermissionsQuery, ListAvailablePermissionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListAvailablePermissionsQuery, ListAvailablePermissionsQueryVariables>(ListAvailablePermissionsDocument, options);
        }
export type ListAvailablePermissionsQueryHookResult = ReturnType<typeof useListAvailablePermissionsQuery>;
export type ListAvailablePermissionsLazyQueryHookResult = ReturnType<typeof useListAvailablePermissionsLazyQuery>;
export type ListAvailablePermissionsQueryResult = Apollo.QueryResult<ListAvailablePermissionsQuery, ListAvailablePermissionsQueryVariables>;
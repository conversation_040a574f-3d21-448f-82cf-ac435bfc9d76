import type * as SchemaTypes from '../types';

import type { VehicleOptionsData_FinderVehicle_Fragment, VehicleOptionsData_LocalMake_Fragment, VehicleOptionsData_LocalModel_Fragment, VehicleOptionsData_LocalVariant_Fragment } from '../fragments/VehicleOptionsData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { VehicleOptionsDataFragmentDoc } from '../fragments/VehicleOptionsData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetVehicleOptionsQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.VehicleFilteringRule>;
}>;


export type GetVehicleOptionsQuery = (
  { __typename: 'Query' }
  & { vehicles: (
    { __typename: 'PaginatedVehicles' }
    & { items: Array<(
      { __typename: 'FinderVehicle' }
      & VehicleOptionsData_FinderVehicle_Fragment
    ) | (
      { __typename: 'LocalMake' }
      & VehicleOptionsData_LocalMake_Fragment
    ) | (
      { __typename: 'LocalModel' }
      & VehicleOptionsData_LocalModel_Fragment
    ) | (
      { __typename: 'LocalVariant' }
      & VehicleOptionsData_LocalVariant_Fragment
    )> }
  ) }
);


export const GetVehicleOptionsDocument = /*#__PURE__*/ gql`
    query getVehicleOptions($filter: VehicleFilteringRule) {
  vehicles: listVehicles(filter: $filter) {
    items {
      ...VehicleOptionsData
    }
  }
}
    ${VehicleOptionsDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useGetVehicleOptionsQuery__
 *
 * To run a query within a React component, call `useGetVehicleOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVehicleOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVehicleOptionsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetVehicleOptionsQuery(baseOptions?: Apollo.QueryHookOptions<GetVehicleOptionsQuery, GetVehicleOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVehicleOptionsQuery, GetVehicleOptionsQueryVariables>(GetVehicleOptionsDocument, options);
      }
export function useGetVehicleOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVehicleOptionsQuery, GetVehicleOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVehicleOptionsQuery, GetVehicleOptionsQueryVariables>(GetVehicleOptionsDocument, options);
        }
export type GetVehicleOptionsQueryHookResult = ReturnType<typeof useGetVehicleOptionsQuery>;
export type GetVehicleOptionsLazyQueryHookResult = ReturnType<typeof useGetVehicleOptionsLazyQuery>;
export type GetVehicleOptionsQueryResult = Apollo.QueryResult<GetVehicleOptionsQuery, GetVehicleOptionsQueryVariables>;
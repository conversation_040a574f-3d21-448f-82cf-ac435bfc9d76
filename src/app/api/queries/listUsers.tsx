import type * as SchemaTypes from '../types';

import type { UserListDataFragment } from '../fragments/UserListData';
import { gql } from '@apollo/client';
import { UserListDataFragmentDoc } from '../fragments/UserListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListUsersQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.UserFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.UserSortingRule>;
}>;


export type ListUsersQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedUsers' }
    & Pick<SchemaTypes.PaginatedUsers, 'count'>
    & { items: Array<(
      { __typename: 'User' }
      & UserListDataFragment
    )> }
  ) }
);


export const ListUsersDocument = /*#__PURE__*/ gql`
    query listUsers($pagination: Pagination, $filter: UserFilteringRule, $sort: UserSortingRule) {
  list: listUsers(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...UserListData
    }
  }
}
    ${UserListDataFragmentDoc}`;

/**
 * __useListUsersQuery__
 *
 * To run a query within a React component, call `useListUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListUsersQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListUsersQuery(baseOptions?: Apollo.QueryHookOptions<ListUsersQuery, ListUsersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListUsersQuery, ListUsersQueryVariables>(ListUsersDocument, options);
      }
export function useListUsersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListUsersQuery, ListUsersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListUsersQuery, ListUsersQueryVariables>(ListUsersDocument, options);
        }
export type ListUsersQueryHookResult = ReturnType<typeof useListUsersQuery>;
export type ListUsersLazyQueryHookResult = ReturnType<typeof useListUsersLazyQuery>;
export type ListUsersQueryResult = Apollo.QueryResult<ListUsersQuery, ListUsersQueryVariables>;
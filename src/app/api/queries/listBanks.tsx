import type * as SchemaTypes from '../types';

import type { BankListDataFragment } from '../fragments/BankListData';
import type { DealerDisclaimersConfiguratorDataFragment } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { BankListDataFragmentDoc } from '../fragments/BankListData';
import { DealerDisclaimersConfiguratorDataFragmentDoc } from '../fragments/DealerPriceDisclaimerConfiguratorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListBanksQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.BankSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.BankFilteringRule>;
}>;


export type ListBanksQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedSystemBanks' }
    & Pick<SchemaTypes.PaginatedSystemBanks, 'count'>
    & { items: Array<(
      { __typename: 'SystemBank' }
      & BankListDataFragment
    )> }
  ) }
);


export const ListBanksDocument = /*#__PURE__*/ gql`
    query listBanks($pagination: Pagination, $sort: BankSortingRule, $filter: BankFilteringRule) {
  list: listBanks(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...BankListData
    }
  }
}
    ${BankListDataFragmentDoc}
${DealerDisclaimersConfiguratorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListBanksQuery__
 *
 * To run a query within a React component, call `useListBanksQuery` and pass it any options that fit your needs.
 * When your component renders, `useListBanksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListBanksQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListBanksQuery(baseOptions?: Apollo.QueryHookOptions<ListBanksQuery, ListBanksQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListBanksQuery, ListBanksQueryVariables>(ListBanksDocument, options);
      }
export function useListBanksLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListBanksQuery, ListBanksQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListBanksQuery, ListBanksQueryVariables>(ListBanksDocument, options);
        }
export type ListBanksQueryHookResult = ReturnType<typeof useListBanksQuery>;
export type ListBanksLazyQueryHookResult = ReturnType<typeof useListBanksLazyQuery>;
export type ListBanksQueryResult = Apollo.QueryResult<ListBanksQuery, ListBanksQueryVariables>;
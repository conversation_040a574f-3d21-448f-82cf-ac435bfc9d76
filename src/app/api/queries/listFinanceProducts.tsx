import type * as SchemaTypes from '../types';

import type { FinanceProductListData_LocalDeferredPrincipal_Fragment, FinanceProductListData_LocalHirePurchase_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment, FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment, FinanceProductListData_LocalLease_Fragment, FinanceProductListData_LocalLeasePurchase_Fragment, FinanceProductListData_LocalUcclLeasing_Fragment } from '../fragments/FinanceProductListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { BalloonSettingsDetails_BalloonRangeSettings_Fragment, BalloonSettingsDetails_BalloonTableSettings_Fragment } from '../fragments/BalloonSettingsDetails';
import type { BalloonGfvSettingsDetailsFragment } from '../fragments/BalloonGFVSettingsDetails';
import { gql } from '@apollo/client';
import { FinanceProductListDataFragmentDoc } from '../fragments/FinanceProductListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { BalloonSettingsDetailsFragmentDoc } from '../fragments/BalloonSettingsDetails';
import { BalloonGfvSettingsDetailsFragmentDoc } from '../fragments/BalloonGFVSettingsDetails';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListFinanceProductsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.FinanceProductSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.FinanceProductFilteringRule>;
}>;


export type ListFinanceProductsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedFinanceProducts' }
    & Pick<SchemaTypes.PaginatedFinanceProducts, 'count'>
    & { items: Array<(
      { __typename: 'LocalDeferredPrincipal' }
      & FinanceProductListData_LocalDeferredPrincipal_Fragment
    ) | (
      { __typename: 'LocalHirePurchase' }
      & FinanceProductListData_LocalHirePurchase_Fragment
    ) | (
      { __typename: 'LocalHirePurchaseWithBalloon' }
      & FinanceProductListData_LocalHirePurchaseWithBalloon_Fragment
    ) | (
      { __typename: 'LocalHirePurchaseWithBalloonGFV' }
      & FinanceProductListData_LocalHirePurchaseWithBalloonGfv_Fragment
    ) | (
      { __typename: 'LocalLease' }
      & FinanceProductListData_LocalLease_Fragment
    ) | (
      { __typename: 'LocalLeasePurchase' }
      & FinanceProductListData_LocalLeasePurchase_Fragment
    ) | (
      { __typename: 'LocalUcclLeasing' }
      & FinanceProductListData_LocalUcclLeasing_Fragment
    )> }
  ) }
);


export const ListFinanceProductsDocument = /*#__PURE__*/ gql`
    query listFinanceProducts($pagination: Pagination, $sort: FinanceProductSortingRule, $filter: FinanceProductFilteringRule) {
  list: listFinanceProducts(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...FinanceProductListData
    }
  }
}
    ${FinanceProductListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${PeriodDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${BalloonSettingsDetailsFragmentDoc}
${BalloonGfvSettingsDetailsFragmentDoc}`;

/**
 * __useListFinanceProductsQuery__
 *
 * To run a query within a React component, call `useListFinanceProductsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListFinanceProductsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListFinanceProductsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListFinanceProductsQuery(baseOptions?: Apollo.QueryHookOptions<ListFinanceProductsQuery, ListFinanceProductsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListFinanceProductsQuery, ListFinanceProductsQueryVariables>(ListFinanceProductsDocument, options);
      }
export function useListFinanceProductsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListFinanceProductsQuery, ListFinanceProductsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListFinanceProductsQuery, ListFinanceProductsQueryVariables>(ListFinanceProductsDocument, options);
        }
export type ListFinanceProductsQueryHookResult = ReturnType<typeof useListFinanceProductsQuery>;
export type ListFinanceProductsLazyQueryHookResult = ReturnType<typeof useListFinanceProductsLazyQuery>;
export type ListFinanceProductsQueryResult = Apollo.QueryResult<ListFinanceProductsQuery, ListFinanceProductsQueryVariables>;
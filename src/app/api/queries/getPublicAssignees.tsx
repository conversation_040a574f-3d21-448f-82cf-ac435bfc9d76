import type * as SchemaTypes from '../types';

import type { UsersOptionsDataFragment } from '../fragments/UsersOptionsData';
import { gql } from '@apollo/client';
import { UsersOptionsDataFragmentDoc } from '../fragments/UsersOptionsData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetPublicAssigneesQueryVariables = SchemaTypes.Exact<{
  eventId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type GetPublicAssigneesQuery = (
  { __typename: 'Query' }
  & { result: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )> }
);


export const GetPublicAssigneesDocument = /*#__PURE__*/ gql`
    query getPublicAssignees($eventId: ObjectID, $companyId: ObjectID) {
  result: listPublicAssignees(eventId: $eventId, companyId: $companyId) {
    ...UsersOptionsData
  }
}
    ${UsersOptionsDataFragmentDoc}`;

/**
 * __useGetPublicAssigneesQuery__
 *
 * To run a query within a React component, call `useGetPublicAssigneesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPublicAssigneesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPublicAssigneesQuery({
 *   variables: {
 *      eventId: // value for 'eventId'
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useGetPublicAssigneesQuery(baseOptions?: Apollo.QueryHookOptions<GetPublicAssigneesQuery, GetPublicAssigneesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPublicAssigneesQuery, GetPublicAssigneesQueryVariables>(GetPublicAssigneesDocument, options);
      }
export function useGetPublicAssigneesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPublicAssigneesQuery, GetPublicAssigneesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPublicAssigneesQuery, GetPublicAssigneesQueryVariables>(GetPublicAssigneesDocument, options);
        }
export type GetPublicAssigneesQueryHookResult = ReturnType<typeof useGetPublicAssigneesQuery>;
export type GetPublicAssigneesLazyQueryHookResult = ReturnType<typeof useGetPublicAssigneesLazyQuery>;
export type GetPublicAssigneesQueryResult = Apollo.QueryResult<GetPublicAssigneesQuery, GetPublicAssigneesQueryVariables>;
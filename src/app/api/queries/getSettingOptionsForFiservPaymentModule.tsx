import type * as SchemaTypes from '../types';

import type { FiservPaymentSettingsSpecFragment } from '../fragments/FiservPaymentSettingsSpec';
import { gql } from '@apollo/client';
import { FiservPaymentSettingsSpecFragmentDoc } from '../fragments/FiservPaymentSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetSettingOptionsForFiservPaymentModuleQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetSettingOptionsForFiservPaymentModuleQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedFiservPaymentSettings' }
    & { items: Array<(
      { __typename: 'FiservPaymentSetting' }
      & FiservPaymentSettingsSpecFragment
    )> }
  ) }
);


export const GetSettingOptionsForFiservPaymentModuleDocument = /*#__PURE__*/ gql`
    query getSettingOptionsForFiservPaymentModule($moduleId: ObjectID!) {
  settings: listFiservPaymentSettings(moduleId: $moduleId) {
    items {
      ...FiservPaymentSettingsSpec
    }
  }
}
    ${FiservPaymentSettingsSpecFragmentDoc}`;

/**
 * __useGetSettingOptionsForFiservPaymentModuleQuery__
 *
 * To run a query within a React component, call `useGetSettingOptionsForFiservPaymentModuleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSettingOptionsForFiservPaymentModuleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSettingOptionsForFiservPaymentModuleQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useGetSettingOptionsForFiservPaymentModuleQuery(baseOptions: Apollo.QueryHookOptions<GetSettingOptionsForFiservPaymentModuleQuery, GetSettingOptionsForFiservPaymentModuleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSettingOptionsForFiservPaymentModuleQuery, GetSettingOptionsForFiservPaymentModuleQueryVariables>(GetSettingOptionsForFiservPaymentModuleDocument, options);
      }
export function useGetSettingOptionsForFiservPaymentModuleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSettingOptionsForFiservPaymentModuleQuery, GetSettingOptionsForFiservPaymentModuleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSettingOptionsForFiservPaymentModuleQuery, GetSettingOptionsForFiservPaymentModuleQueryVariables>(GetSettingOptionsForFiservPaymentModuleDocument, options);
        }
export type GetSettingOptionsForFiservPaymentModuleQueryHookResult = ReturnType<typeof useGetSettingOptionsForFiservPaymentModuleQuery>;
export type GetSettingOptionsForFiservPaymentModuleLazyQueryHookResult = ReturnType<typeof useGetSettingOptionsForFiservPaymentModuleLazyQuery>;
export type GetSettingOptionsForFiservPaymentModuleQueryResult = Apollo.QueryResult<GetSettingOptionsForFiservPaymentModuleQuery, GetSettingOptionsForFiservPaymentModuleQueryVariables>;
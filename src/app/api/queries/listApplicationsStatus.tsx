import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListApplicationsStatusQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ApplicationFilteringRule>;
}>;


export type ListApplicationsStatusQuery = (
  { __typename: 'Query' }
  & { listStatus: SchemaTypes.Query['listApplicationsStatus'] }
);


export const ListApplicationsStatusDocument = /*#__PURE__*/ gql`
    query listApplicationsStatus($filter: ApplicationFilteringRule) {
  listStatus: listApplicationsStatus(filter: $filter)
}
    `;

/**
 * __useListApplicationsStatusQuery__
 *
 * To run a query within a React component, call `useListApplicationsStatusQuery` and pass it any options that fit your needs.
 * When your component renders, `useListApplicationsStatusQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListApplicationsStatusQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListApplicationsStatusQuery(baseOptions?: Apollo.QueryHookOptions<ListApplicationsStatusQuery, ListApplicationsStatusQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListApplicationsStatusQuery, ListApplicationsStatusQueryVariables>(ListApplicationsStatusDocument, options);
      }
export function useListApplicationsStatusLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListApplicationsStatusQuery, ListApplicationsStatusQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListApplicationsStatusQuery, ListApplicationsStatusQueryVariables>(ListApplicationsStatusDocument, options);
        }
export type ListApplicationsStatusQueryHookResult = ReturnType<typeof useListApplicationsStatusQuery>;
export type ListApplicationsStatusLazyQueryHookResult = ReturnType<typeof useListApplicationsStatusLazyQuery>;
export type ListApplicationsStatusQueryResult = Apollo.QueryResult<ListApplicationsStatusQuery, ListApplicationsStatusQueryVariables>;
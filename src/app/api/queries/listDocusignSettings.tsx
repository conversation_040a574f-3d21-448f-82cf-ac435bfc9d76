import type * as SchemaTypes from '../types';

import type { DocusignSettingDataFragment } from '../fragments/DocusignSettingData';
import { gql } from '@apollo/client';
import { DocusignSettingDataFragmentDoc } from '../fragments/DocusignSettingData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListDocusignSettingsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListDocusignSettingsQuery = (
  { __typename: 'Query' }
  & { docusigns: (
    { __typename: 'PaginatedDocusignSettings' }
    & Pick<SchemaTypes.PaginatedDocusignSettings, 'count'>
    & { items: Array<(
      { __typename: 'DocusignSetting' }
      & DocusignSettingDataFragment
    )> }
  ) }
);


export const ListDocusignSettingsDocument = /*#__PURE__*/ gql`
    query listDocusignSettings($pagination: Pagination, $moduleId: ObjectID!) {
  docusigns: listDocusignSettings(pagination: $pagination, moduleId: $moduleId) {
    items {
      ...DocusignSettingData
    }
    count
  }
}
    ${DocusignSettingDataFragmentDoc}`;

/**
 * __useListDocusignSettingsQuery__
 *
 * To run a query within a React component, call `useListDocusignSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListDocusignSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListDocusignSettingsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useListDocusignSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListDocusignSettingsQuery, ListDocusignSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListDocusignSettingsQuery, ListDocusignSettingsQueryVariables>(ListDocusignSettingsDocument, options);
      }
export function useListDocusignSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListDocusignSettingsQuery, ListDocusignSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListDocusignSettingsQuery, ListDocusignSettingsQueryVariables>(ListDocusignSettingsDocument, options);
        }
export type ListDocusignSettingsQueryHookResult = ReturnType<typeof useListDocusignSettingsQuery>;
export type ListDocusignSettingsLazyQueryHookResult = ReturnType<typeof useListDocusignSettingsLazyQuery>;
export type ListDocusignSettingsQueryResult = Apollo.QueryResult<ListDocusignSettingsQuery, ListDocusignSettingsQueryVariables>;
import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetMonthOfImportOptionsQueryVariables = SchemaTypes.Exact<{
  dealerId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetMonthOfImportOptionsQuery = (
  { __typename: 'Query' }
  & { monthOfImportOptions: Array<(
    { __typename: 'MonthOfImportOption' }
    & Pick<SchemaTypes.MonthOfImportOption, 'value' | 'label'>
  )> }
);


export const GetMonthOfImportOptionsDocument = /*#__PURE__*/ gql`
    query getMonthOfImportOptions($dealerId: ObjectID!) {
  monthOfImportOptions: getMonthOfImportOptions(dealerId: $dealerId) {
    value
    label
  }
}
    `;

/**
 * __useGetMonthOfImportOptionsQuery__
 *
 * To run a query within a React component, call `useGetMonthOfImportOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMonthOfImportOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMonthOfImportOptionsQuery({
 *   variables: {
 *      dealerId: // value for 'dealerId'
 *   },
 * });
 */
export function useGetMonthOfImportOptionsQuery(baseOptions: Apollo.QueryHookOptions<GetMonthOfImportOptionsQuery, GetMonthOfImportOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMonthOfImportOptionsQuery, GetMonthOfImportOptionsQueryVariables>(GetMonthOfImportOptionsDocument, options);
      }
export function useGetMonthOfImportOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMonthOfImportOptionsQuery, GetMonthOfImportOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMonthOfImportOptionsQuery, GetMonthOfImportOptionsQueryVariables>(GetMonthOfImportOptionsDocument, options);
        }
export type GetMonthOfImportOptionsQueryHookResult = ReturnType<typeof useGetMonthOfImportOptionsQuery>;
export type GetMonthOfImportOptionsLazyQueryHookResult = ReturnType<typeof useGetMonthOfImportOptionsLazyQuery>;
export type GetMonthOfImportOptionsQueryResult = Apollo.QueryResult<GetMonthOfImportOptionsQuery, GetMonthOfImportOptionsQueryVariables>;
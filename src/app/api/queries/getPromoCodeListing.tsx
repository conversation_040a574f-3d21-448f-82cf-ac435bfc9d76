import type * as SchemaTypes from '../types';

import type { PromoCodeListingDataFragment } from '../fragments/PromoCodeListingData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { PromoCodeListingDataFragmentDoc } from '../fragments/PromoCodeListingData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetPromoCodeListingQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.PromoCodeSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.PromoCodeFilteringRule>;
}>;


export type GetPromoCodeListingQuery = (
  { __typename: 'Query' }
  & { promoCode: (
    { __typename: 'PaginatedPromoCode' }
    & Pick<SchemaTypes.PaginatedPromoCode, 'count'>
    & { items: Array<(
      { __typename: 'PromoCode' }
      & PromoCodeListingDataFragment
    )> }
  ) }
);


export const GetPromoCodeListingDocument = /*#__PURE__*/ gql`
    query getPromoCodeListing($pagination: Pagination, $sort: PromoCodeSortingRule, $filter: PromoCodeFilteringRule) {
  promoCode: getPromoCodeListing(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    count
    items {
      ...PromoCodeListingData
    }
  }
}
    ${PromoCodeListingDataFragmentDoc}
${PeriodDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useGetPromoCodeListingQuery__
 *
 * To run a query within a React component, call `useGetPromoCodeListingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPromoCodeListingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPromoCodeListingQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetPromoCodeListingQuery(baseOptions?: Apollo.QueryHookOptions<GetPromoCodeListingQuery, GetPromoCodeListingQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPromoCodeListingQuery, GetPromoCodeListingQueryVariables>(GetPromoCodeListingDocument, options);
      }
export function useGetPromoCodeListingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPromoCodeListingQuery, GetPromoCodeListingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPromoCodeListingQuery, GetPromoCodeListingQueryVariables>(GetPromoCodeListingDocument, options);
        }
export type GetPromoCodeListingQueryHookResult = ReturnType<typeof useGetPromoCodeListingQuery>;
export type GetPromoCodeListingLazyQueryHookResult = ReturnType<typeof useGetPromoCodeListingLazyQuery>;
export type GetPromoCodeListingQueryResult = Apollo.QueryResult<GetPromoCodeListingQuery, GetPromoCodeListingQueryVariables>;
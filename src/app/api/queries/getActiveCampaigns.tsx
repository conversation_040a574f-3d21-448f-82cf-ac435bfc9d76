import type * as SchemaTypes from '../types';

import type { CampaignDataFragment } from '../fragments/CampaignData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { CampaignDataFragmentDoc } from '../fragments/CampaignData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetActiveCampaignsQueryVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetActiveCampaignsQuery = (
  { __typename: 'Query' }
  & { campaigns: Array<(
    { __typename: 'Campaign' }
    & CampaignDataFragment
  )> }
);


export const GetActiveCampaignsDocument = /*#__PURE__*/ gql`
    query getActiveCampaigns($companyId: ObjectID!) {
  campaigns: getActiveCampaigns(companyId: $companyId) {
    ...CampaignData
  }
}
    ${CampaignDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useGetActiveCampaignsQuery__
 *
 * To run a query within a React component, call `useGetActiveCampaignsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetActiveCampaignsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetActiveCampaignsQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useGetActiveCampaignsQuery(baseOptions: Apollo.QueryHookOptions<GetActiveCampaignsQuery, GetActiveCampaignsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetActiveCampaignsQuery, GetActiveCampaignsQueryVariables>(GetActiveCampaignsDocument, options);
      }
export function useGetActiveCampaignsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetActiveCampaignsQuery, GetActiveCampaignsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetActiveCampaignsQuery, GetActiveCampaignsQueryVariables>(GetActiveCampaignsDocument, options);
        }
export type GetActiveCampaignsQueryHookResult = ReturnType<typeof useGetActiveCampaignsQuery>;
export type GetActiveCampaignsLazyQueryHookResult = ReturnType<typeof useGetActiveCampaignsLazyQuery>;
export type GetActiveCampaignsQueryResult = Apollo.QueryResult<GetActiveCampaignsQuery, GetActiveCampaignsQueryVariables>;
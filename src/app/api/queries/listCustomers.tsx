import type * as SchemaTypes from '../types';

import type { CustomerListSpecs_CorporateCustomer_Fragment, CustomerListSpecs_Guarantor_Fragment, CustomerListSpecs_LocalCustomer_Fragment } from '../fragments/CustomerListSpecs';
import type { LeadInCustomerList_ConfiguratorLead_Fragment, LeadInCustomerList_EventLead_Fragment, LeadInCustomerList_FinderLead_Fragment, LeadInCustomerList_LaunchpadLead_Fragment, LeadInCustomerList_MobilityLead_Fragment, LeadInCustomerList_StandardLead_Fragment } from '../fragments/LeadInCustomerList';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { CustomerListSpecsFragmentDoc } from '../fragments/CustomerListSpecs';
import { LeadInCustomerListFragmentDoc } from '../fragments/LeadInCustomerList';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListCustomersQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.CustomerSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.CustomerFilteringRule>;
}>;


export type ListCustomersQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedCustomers' }
    & Pick<SchemaTypes.PaginatedCustomers, 'count'>
    & { items: Array<(
      { __typename: 'CorporateCustomer' }
      & CustomerListSpecs_CorporateCustomer_Fragment
    ) | (
      { __typename: 'Guarantor' }
      & CustomerListSpecs_Guarantor_Fragment
    ) | (
      { __typename: 'LocalCustomer' }
      & CustomerListSpecs_LocalCustomer_Fragment
    )> }
  ) }
);


export const ListCustomersDocument = /*#__PURE__*/ gql`
    query listCustomers($pagination: Pagination!, $sort: CustomerSortingRule, $filter: CustomerFilteringRule) {
  list: listCustomers(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...CustomerListSpecs
    }
  }
}
    ${CustomerListSpecsFragmentDoc}
${LeadInCustomerListFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListCustomersQuery__
 *
 * To run a query within a React component, call `useListCustomersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListCustomersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListCustomersQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListCustomersQuery(baseOptions: Apollo.QueryHookOptions<ListCustomersQuery, ListCustomersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListCustomersQuery, ListCustomersQueryVariables>(ListCustomersDocument, options);
      }
export function useListCustomersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListCustomersQuery, ListCustomersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListCustomersQuery, ListCustomersQueryVariables>(ListCustomersDocument, options);
        }
export type ListCustomersQueryHookResult = ReturnType<typeof useListCustomersQuery>;
export type ListCustomersLazyQueryHookResult = ReturnType<typeof useListCustomersLazyQuery>;
export type ListCustomersQueryResult = Apollo.QueryResult<ListCustomersQuery, ListCustomersQueryVariables>;
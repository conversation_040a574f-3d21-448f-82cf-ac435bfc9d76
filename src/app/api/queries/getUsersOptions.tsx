import type * as SchemaTypes from '../types';

import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetUsersOptionsQueryVariables = SchemaTypes.Exact<{
  roleId: SchemaTypes.Scalars['ObjectID']['input'];
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.UserFilteringRule>;
}>;


export type GetUsersOptionsQuery = (
  { __typename: 'Query' }
  & { users: Array<(
    { __typename: 'User' }
    & UserPreviewDataFragment
  )> }
);


export const GetUsersOptionsDocument = /*#__PURE__*/ gql`
    query getUsersOptions($roleId: ObjectID!, $companyId: ObjectID, $dealerIds: [ObjectID!], $filter: UserFilteringRule) {
  users: listAvailableUsersByRole(
    roleId: $roleId
    companyId: $companyId
    dealerIds: $dealerIds
    filter: $filter
  ) {
    ...UserPreviewData
  }
}
    ${UserPreviewDataFragmentDoc}`;

/**
 * __useGetUsersOptionsQuery__
 *
 * To run a query within a React component, call `useGetUsersOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUsersOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUsersOptionsQuery({
 *   variables: {
 *      roleId: // value for 'roleId'
 *      companyId: // value for 'companyId'
 *      dealerIds: // value for 'dealerIds'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetUsersOptionsQuery(baseOptions: Apollo.QueryHookOptions<GetUsersOptionsQuery, GetUsersOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUsersOptionsQuery, GetUsersOptionsQueryVariables>(GetUsersOptionsDocument, options);
      }
export function useGetUsersOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUsersOptionsQuery, GetUsersOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUsersOptionsQuery, GetUsersOptionsQueryVariables>(GetUsersOptionsDocument, options);
        }
export type GetUsersOptionsQueryHookResult = ReturnType<typeof useGetUsersOptionsQuery>;
export type GetUsersOptionsLazyQueryHookResult = ReturnType<typeof useGetUsersOptionsLazyQuery>;
export type GetUsersOptionsQueryResult = Apollo.QueryResult<GetUsersOptionsQuery, GetUsersOptionsQueryVariables>;
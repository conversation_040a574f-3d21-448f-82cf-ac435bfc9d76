import type * as SchemaTypes from '../types';

import type { VariantConfiguratorListItemFragment } from '../fragments/VariantConfiguratorListItem';
import { gql } from '@apollo/client';
import { VariantConfiguratorListItemFragmentDoc } from '../fragments/VariantConfiguratorListItem';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListVariantConfiguratorsQueryVariables = SchemaTypes.Exact<{
  modelConfiguratorId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.VariantConfiguratorFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.VariantConfiguratorSortingRule>;
}>;


export type ListVariantConfiguratorsQuery = (
  { __typename: 'Query' }
  & { lists: (
    { __typename: 'PaginatedVariantConfigurator' }
    & Pick<SchemaTypes.PaginatedVariantConfigurator, 'count'>
    & { items: Array<(
      { __typename: 'VariantConfigurator' }
      & VariantConfiguratorListItemFragment
    )> }
  ) }
);


export const ListVariantConfiguratorsDocument = /*#__PURE__*/ gql`
    query listVariantConfigurators($modelConfiguratorId: ObjectID!, $pagination: Pagination, $filter: VariantConfiguratorFilteringRule, $sort: VariantConfiguratorSortingRule) {
  lists: listVariantConfigurators(
    modelConfiguratorId: $modelConfiguratorId
    pagination: $pagination
    filter: $filter
    sort: $sort
  ) {
    items {
      ...VariantConfiguratorListItem
    }
    count
  }
}
    ${VariantConfiguratorListItemFragmentDoc}`;

/**
 * __useListVariantConfiguratorsQuery__
 *
 * To run a query within a React component, call `useListVariantConfiguratorsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListVariantConfiguratorsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListVariantConfiguratorsQuery({
 *   variables: {
 *      modelConfiguratorId: // value for 'modelConfiguratorId'
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListVariantConfiguratorsQuery(baseOptions: Apollo.QueryHookOptions<ListVariantConfiguratorsQuery, ListVariantConfiguratorsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListVariantConfiguratorsQuery, ListVariantConfiguratorsQueryVariables>(ListVariantConfiguratorsDocument, options);
      }
export function useListVariantConfiguratorsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListVariantConfiguratorsQuery, ListVariantConfiguratorsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListVariantConfiguratorsQuery, ListVariantConfiguratorsQueryVariables>(ListVariantConfiguratorsDocument, options);
        }
export type ListVariantConfiguratorsQueryHookResult = ReturnType<typeof useListVariantConfiguratorsQuery>;
export type ListVariantConfiguratorsLazyQueryHookResult = ReturnType<typeof useListVariantConfiguratorsLazyQuery>;
export type ListVariantConfiguratorsQueryResult = Apollo.QueryResult<ListVariantConfiguratorsQuery, ListVariantConfiguratorsQueryVariables>;
import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchKycFieldsForLaunchPadApplicationQueryVariables = SchemaTypes.Exact<{
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
  configuration: SchemaTypes.LaunchPadApplicationConfiguration;
}>;


export type PrefetchKycFieldsForLaunchPadApplicationQuery = (
  { __typename: 'Query' }
  & { applicantKyc: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, corporateKyc: Array<(
    { __typename: 'KY<PERSON>ield' }
    & KycFieldSpecsFragment
  )>, guarantorKyc: Array<(
    { __typename: 'KY<PERSON>ield' }
    & KycFieldSpecsFragment
  )> }
);


export const PrefetchKycFieldsForLaunchPadApplicationDocument = /*#__PURE__*/ gql`
    query prefetchKYCFieldsForLaunchPadApplication($applicationModuleId: ObjectID!, $configuration: LaunchPadApplicationConfiguration!) {
  applicantKyc: prefetchKYCFieldsForLaunchPadApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "local"
  ) {
    ...KYCFieldSpecs
  }
  corporateKyc: prefetchKYCFieldsForLaunchPadApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "corporate"
  ) {
    ...KYCFieldSpecs
  }
  guarantorKyc: prefetchKYCFieldsForLaunchPadApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "guarantor"
  ) {
    ...KYCFieldSpecs
  }
}
    ${KycFieldSpecsFragmentDoc}`;

/**
 * __usePrefetchKycFieldsForLaunchPadApplicationQuery__
 *
 * To run a query within a React component, call `usePrefetchKycFieldsForLaunchPadApplicationQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchKycFieldsForLaunchPadApplicationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchKycFieldsForLaunchPadApplicationQuery({
 *   variables: {
 *      applicationModuleId: // value for 'applicationModuleId'
 *      configuration: // value for 'configuration'
 *   },
 * });
 */
export function usePrefetchKycFieldsForLaunchPadApplicationQuery(baseOptions: Apollo.QueryHookOptions<PrefetchKycFieldsForLaunchPadApplicationQuery, PrefetchKycFieldsForLaunchPadApplicationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchKycFieldsForLaunchPadApplicationQuery, PrefetchKycFieldsForLaunchPadApplicationQueryVariables>(PrefetchKycFieldsForLaunchPadApplicationDocument, options);
      }
export function usePrefetchKycFieldsForLaunchPadApplicationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchKycFieldsForLaunchPadApplicationQuery, PrefetchKycFieldsForLaunchPadApplicationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchKycFieldsForLaunchPadApplicationQuery, PrefetchKycFieldsForLaunchPadApplicationQueryVariables>(PrefetchKycFieldsForLaunchPadApplicationDocument, options);
        }
export type PrefetchKycFieldsForLaunchPadApplicationQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForLaunchPadApplicationQuery>;
export type PrefetchKycFieldsForLaunchPadApplicationLazyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForLaunchPadApplicationLazyQuery>;
export type PrefetchKycFieldsForLaunchPadApplicationQueryResult = Apollo.QueryResult<PrefetchKycFieldsForLaunchPadApplicationQuery, PrefetchKycFieldsForLaunchPadApplicationQueryVariables>;
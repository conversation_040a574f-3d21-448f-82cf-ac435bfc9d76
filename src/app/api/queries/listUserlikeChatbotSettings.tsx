import type * as SchemaTypes from '../types';

import type { UserlikeChatbotSettingsSpecFragment } from '../fragments/UserlikeChatbotSettingsSpec';
import { gql } from '@apollo/client';
import { UserlikeChatbotSettingsSpecFragmentDoc } from '../fragments/UserlikeChatbotSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListUserlikeChatbotSettingsQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type ListUserlikeChatbotSettingsQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedUserlikeChatbotSettings' }
    & Pick<SchemaTypes.PaginatedUserlikeChatbotSettings, 'count'>
    & { items: Array<(
      { __typename: 'UserlikeChatbotSetting' }
      & UserlikeChatbotSettingsSpecFragment
    )> }
  ) }
);


export const ListUserlikeChatbotSettingsDocument = /*#__PURE__*/ gql`
    query listUserlikeChatbotSettings($moduleId: ObjectID!, $pagination: Pagination) {
  settings: listUserlikeChatbotSettings(
    moduleId: $moduleId
    pagination: $pagination
  ) {
    count
    items {
      ...UserlikeChatbotSettingsSpec
    }
  }
}
    ${UserlikeChatbotSettingsSpecFragmentDoc}`;

/**
 * __useListUserlikeChatbotSettingsQuery__
 *
 * To run a query within a React component, call `useListUserlikeChatbotSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListUserlikeChatbotSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListUserlikeChatbotSettingsQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useListUserlikeChatbotSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListUserlikeChatbotSettingsQuery, ListUserlikeChatbotSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListUserlikeChatbotSettingsQuery, ListUserlikeChatbotSettingsQueryVariables>(ListUserlikeChatbotSettingsDocument, options);
      }
export function useListUserlikeChatbotSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListUserlikeChatbotSettingsQuery, ListUserlikeChatbotSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListUserlikeChatbotSettingsQuery, ListUserlikeChatbotSettingsQueryVariables>(ListUserlikeChatbotSettingsDocument, options);
        }
export type ListUserlikeChatbotSettingsQueryHookResult = ReturnType<typeof useListUserlikeChatbotSettingsQuery>;
export type ListUserlikeChatbotSettingsLazyQueryHookResult = ReturnType<typeof useListUserlikeChatbotSettingsLazyQuery>;
export type ListUserlikeChatbotSettingsQueryResult = Apollo.QueryResult<ListUserlikeChatbotSettingsQuery, ListUserlikeChatbotSettingsQueryVariables>;
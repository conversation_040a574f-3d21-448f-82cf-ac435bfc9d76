import type * as SchemaTypes from '../types';

import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocationsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocationFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LocationSortingRule>;
}>;


export type ListLocationsQuery = (
  { __typename: 'Query' }
  & { locations: (
    { __typename: 'PaginatedMobilityLocation' }
    & Pick<SchemaTypes.PaginatedMobilityLocation, 'count'>
    & { items: Array<(
      { __typename: 'MobilityLocation' }
      & MobilityLocationDataFragment
    )> }
  ) }
);


export const ListLocationsDocument = /*#__PURE__*/ gql`
    query listLocations($pagination: Pagination, $filter: LocationFilteringRule, $sort: LocationSortingRule) {
  locations: listLocations(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...MobilityLocationData
    }
  }
}
    ${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}`;

/**
 * __useListLocationsQuery__
 *
 * To run a query within a React component, call `useListLocationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocationsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListLocationsQuery(baseOptions?: Apollo.QueryHookOptions<ListLocationsQuery, ListLocationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocationsQuery, ListLocationsQueryVariables>(ListLocationsDocument, options);
      }
export function useListLocationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocationsQuery, ListLocationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocationsQuery, ListLocationsQueryVariables>(ListLocationsDocument, options);
        }
export type ListLocationsQueryHookResult = ReturnType<typeof useListLocationsQuery>;
export type ListLocationsLazyQueryHookResult = ReturnType<typeof useListLocationsLazyQuery>;
export type ListLocationsQueryResult = Apollo.QueryResult<ListLocationsQuery, ListLocationsQueryVariables>;
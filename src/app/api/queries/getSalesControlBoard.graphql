query getSalesControlBoard($filter: SalesControlBoardFilterPayload!) {
    salesControlBoard: getSalesControlBoard(filter: $filter) {
        lostDataType
        fiCommissions {
            salesConsultantName
            inHouseFinanceTarget
            inHouseFinanceMtd
            inHouseFinanceYtd
            inHouseFinance3MAvg
            inHouseInsuranceTarget
            inHouseInsuranceMtd
            inHouseInsuranceYtd
            inHouseInsurance3MAvg
        }

        weekFunnels {
            start
            end
            leadsCreated
            testDrives
            salesOffers
            orderIntakes
            retails
            leadToTestDriveRate
            testDriveToSalesOfferRate
            salesOfferToOrderIntakeRate
            orderIntakeToRetailRate
        }

        monthlyAverageTarget {
            orderIntakesMonthlyModelAverageTarget
            retailsMonthlyModelAverageTarget
            testDriveMonthlyModelAverageTarget
            salesOfferMonthlyModelAverageTarget
            leadsMonthlyModelAverageTarget
        }

        progressGoal {
            retailTargetMonth
            retailActualMonth
            retailMonthRate
            retailMonthDev
            retailTargetYtd
            retailActualYtd
            retailYtdRate
            retailYtdDev
            financeTarget
            financeActualRate
            insuranceTarget
            insuranceActualRate
        }

        performance {
            total {
                salesConsultantName
                totalLeadTarget
                totalLeadActual
                totalLeadDev
                totalOrderIntakeTarget
                totalOrderIntakeActual
                totalOrderIntakeDev
                totalRetailTarget
                totalRetailActual
                totalRetailDev
                totalSalesOfferTarget
                totalSalesOfferActual
                totalSalesOfferDev
                totalTestDriveTarget
                totalTestDriveActual
                totalTestDriveDev
                totalLio
                totalPi
                totalUlr
                totalOlr
            }
            model {
                modelName
                totalLeadTarget
                totalLeadActual
                totalLeadDev
                totalOrderIntakeTarget
                totalOrderIntakeActual
                totalOrderIntakeDev
                totalRetailTarget
                totalRetailActual
                totalRetailDev
                totalSalesOfferTarget
                totalSalesOfferActual
                totalSalesOfferDev
                totalTestDriveTarget
                totalTestDriveActual
                totalTestDriveDev
                totalLio
                totalPi
                totalUlr
                totalOlr
            }
            items {
                salesConsultantName
                modelName
                retail {
                    actual
                    target
                    dev
                }
                orderIntake {
                    actual
                    target
                    dev
                }
                salesOffer {
                    actual
                    target
                    dev
                }
                testDrive {
                    actual
                    target
                    dev
                }
                lead {
                    target
                    actual
                    dev
                    lio
                    olr
                    ulr
                    pi
                }
            }
        }
    }
}

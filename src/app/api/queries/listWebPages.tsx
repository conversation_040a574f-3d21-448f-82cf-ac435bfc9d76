import type * as SchemaTypes from '../types';

import type { WebPageListDataFragment } from '../fragments/WebPageListData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { WebPageListDataFragmentDoc } from '../fragments/WebPageListData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListWebPagesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.WebPageFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.WebPageSortingRule>;
}>;


export type ListWebPagesQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedWebPage' }
    & Pick<SchemaTypes.PaginatedWebPage, 'count'>
    & { items: Array<(
      { __typename: 'WebPage' }
      & WebPageListDataFragment
    )> }
  ) }
);


export const ListWebPagesDocument = /*#__PURE__*/ gql`
    query listWebPages($pagination: Pagination, $filter: WebPageFilteringRule, $sort: WebPageSortingRule) {
  list: listWebPages(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...WebPageListData
    }
  }
}
    ${WebPageListDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListWebPagesQuery__
 *
 * To run a query within a React component, call `useListWebPagesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListWebPagesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListWebPagesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListWebPagesQuery(baseOptions?: Apollo.QueryHookOptions<ListWebPagesQuery, ListWebPagesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListWebPagesQuery, ListWebPagesQueryVariables>(ListWebPagesDocument, options);
      }
export function useListWebPagesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListWebPagesQuery, ListWebPagesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListWebPagesQuery, ListWebPagesQueryVariables>(ListWebPagesDocument, options);
        }
export type ListWebPagesQueryHookResult = ReturnType<typeof useListWebPagesQuery>;
export type ListWebPagesLazyQueryHookResult = ReturnType<typeof useListWebPagesLazyQuery>;
export type ListWebPagesQueryResult = Apollo.QueryResult<ListWebPagesQuery, ListWebPagesQueryVariables>;
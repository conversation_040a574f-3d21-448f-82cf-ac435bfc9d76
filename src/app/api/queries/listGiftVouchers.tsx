import type * as SchemaTypes from '../types';

import type { ListGiftVoucherDataFragment } from '../fragments/ListGiftVoucherData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { ListGiftVoucherDataFragmentDoc } from '../fragments/ListGiftVoucherData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListGiftVouchersQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.GiftVoucherSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.GiftVoucherFilteringRule>;
}>;


export type ListGiftVouchersQuery = (
  { __typename: 'Query' }
  & { listGiftVouchers: (
    { __typename: 'PaginatedGiftVoucher' }
    & Pick<SchemaTypes.PaginatedGiftVoucher, 'count'>
    & { items: Array<(
      { __typename: 'GiftVoucher' }
      & ListGiftVoucherDataFragment
    )> }
  ) }
);


export const ListGiftVouchersDocument = /*#__PURE__*/ gql`
    query listGiftVouchers($pagination: Pagination, $sort: GiftVoucherSortingRule, $filter: GiftVoucherFilteringRule) {
  listGiftVouchers(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...ListGiftVoucherData
    }
  }
}
    ${ListGiftVoucherDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListGiftVouchersQuery__
 *
 * To run a query within a React component, call `useListGiftVouchersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListGiftVouchersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListGiftVouchersQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListGiftVouchersQuery(baseOptions?: Apollo.QueryHookOptions<ListGiftVouchersQuery, ListGiftVouchersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListGiftVouchersQuery, ListGiftVouchersQueryVariables>(ListGiftVouchersDocument, options);
      }
export function useListGiftVouchersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListGiftVouchersQuery, ListGiftVouchersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListGiftVouchersQuery, ListGiftVouchersQueryVariables>(ListGiftVouchersDocument, options);
        }
export type ListGiftVouchersQueryHookResult = ReturnType<typeof useListGiftVouchersQuery>;
export type ListGiftVouchersLazyQueryHookResult = ReturnType<typeof useListGiftVouchersLazyQuery>;
export type ListGiftVouchersQueryResult = Apollo.QueryResult<ListGiftVouchersQuery, ListGiftVouchersQueryVariables>;
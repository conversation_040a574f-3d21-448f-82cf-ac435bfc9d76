import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetPorschePaymentMethodsForSalesOfferQueryVariables = SchemaTypes.Exact<{
  token: SchemaTypes.Scalars['String']['input'];
}>;


export type GetPorschePaymentMethodsForSalesOfferQuery = (
  { __typename: 'Query' }
  & { result: Array<(
    { __typename: 'PorschePaymentMethods' }
    & Pick<SchemaTypes.PorschePaymentMethods, 'method'>
    & { types: Array<(
      { __typename: 'PorschePaymentType' }
      & Pick<SchemaTypes.PorschePaymentType, 'displayName' | 'type'>
    )> }
  )> }
);


export const GetPorschePaymentMethodsForSalesOfferDocument = /*#__PURE__*/ gql`
    query getPorschePaymentMethodsForSalesOffer($token: String!) {
  result: getPorschePaymentMethodsForSalesOffer(token: $token) {
    method
    types {
      displayName
      type
    }
  }
}
    `;

/**
 * __useGetPorschePaymentMethodsForSalesOfferQuery__
 *
 * To run a query within a React component, call `useGetPorschePaymentMethodsForSalesOfferQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPorschePaymentMethodsForSalesOfferQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPorschePaymentMethodsForSalesOfferQuery({
 *   variables: {
 *      token: // value for 'token'
 *   },
 * });
 */
export function useGetPorschePaymentMethodsForSalesOfferQuery(baseOptions: Apollo.QueryHookOptions<GetPorschePaymentMethodsForSalesOfferQuery, GetPorschePaymentMethodsForSalesOfferQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPorschePaymentMethodsForSalesOfferQuery, GetPorschePaymentMethodsForSalesOfferQueryVariables>(GetPorschePaymentMethodsForSalesOfferDocument, options);
      }
export function useGetPorschePaymentMethodsForSalesOfferLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPorschePaymentMethodsForSalesOfferQuery, GetPorschePaymentMethodsForSalesOfferQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPorschePaymentMethodsForSalesOfferQuery, GetPorschePaymentMethodsForSalesOfferQueryVariables>(GetPorschePaymentMethodsForSalesOfferDocument, options);
        }
export type GetPorschePaymentMethodsForSalesOfferQueryHookResult = ReturnType<typeof useGetPorschePaymentMethodsForSalesOfferQuery>;
export type GetPorschePaymentMethodsForSalesOfferLazyQueryHookResult = ReturnType<typeof useGetPorschePaymentMethodsForSalesOfferLazyQuery>;
export type GetPorschePaymentMethodsForSalesOfferQueryResult = Apollo.QueryResult<GetPorschePaymentMethodsForSalesOfferQuery, GetPorschePaymentMethodsForSalesOfferQueryVariables>;
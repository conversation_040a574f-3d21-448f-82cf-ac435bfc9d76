import type * as SchemaTypes from '../types';

import type { WebPageOptionDataFragment } from '../fragments/WebPageOptionData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { WebPageOptionDataFragmentDoc } from '../fragments/WebPageOptionData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetWebPageOptionsQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetWebPageOptionsQuery = (
  { __typename: 'Query' }
  & { options: Array<(
    { __typename: 'WebPage' }
    & WebPageOptionDataFragment
  )> }
);


export const GetWebPageOptionsDocument = /*#__PURE__*/ gql`
    query getWebPageOptions($moduleId: ObjectID!) {
  options: getWebPageOptions(moduleId: $moduleId) {
    ...WebPageOptionData
  }
}
    ${WebPageOptionDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useGetWebPageOptionsQuery__
 *
 * To run a query within a React component, call `useGetWebPageOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetWebPageOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetWebPageOptionsQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useGetWebPageOptionsQuery(baseOptions: Apollo.QueryHookOptions<GetWebPageOptionsQuery, GetWebPageOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetWebPageOptionsQuery, GetWebPageOptionsQueryVariables>(GetWebPageOptionsDocument, options);
      }
export function useGetWebPageOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetWebPageOptionsQuery, GetWebPageOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetWebPageOptionsQuery, GetWebPageOptionsQueryVariables>(GetWebPageOptionsDocument, options);
        }
export type GetWebPageOptionsQueryHookResult = ReturnType<typeof useGetWebPageOptionsQuery>;
export type GetWebPageOptionsLazyQueryHookResult = ReturnType<typeof useGetWebPageOptionsLazyQuery>;
export type GetWebPageOptionsQueryResult = Apollo.QueryResult<GetWebPageOptionsQuery, GetWebPageOptionsQueryVariables>;
import type * as SchemaTypes from '../types';

import type { MyInfoModuleSpecsFragment } from '../fragments/MyInfoModuleSpecs';
import type { MyInfoSettingSpecFragment } from '../fragments/MyInfoSettingSpec';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { MyInfoModuleSpecsFragmentDoc } from '../fragments/MyInfoModuleSpecs';
import { MyInfoSettingSpecFragmentDoc } from '../fragments/MyInfoSettingSpec';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListMyInfoModulesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModuleFilteringRule>;
}>;


export type ListMyInfoModulesQuery = (
  { __typename: 'Query' }
  & { modules: (
    { __typename: 'PaginatedMyInfoModules' }
    & Pick<SchemaTypes.PaginatedMyInfoModules, 'count'>
    & { items: Array<(
      { __typename: 'MyInfoModule' }
      & MyInfoModuleSpecsFragment
    )> }
  ) }
);


export const ListMyInfoModulesDocument = /*#__PURE__*/ gql`
    query listMyInfoModules($pagination: Pagination, $filter: ModuleFilteringRule) {
  modules: listMyInfoModules(pagination: $pagination, filter: $filter) {
    count
    items {
      ...MyInfoModuleSpecs
    }
  }
}
    ${MyInfoModuleSpecsFragmentDoc}
${MyInfoSettingSpecFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListMyInfoModulesQuery__
 *
 * To run a query within a React component, call `useListMyInfoModulesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListMyInfoModulesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListMyInfoModulesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListMyInfoModulesQuery(baseOptions?: Apollo.QueryHookOptions<ListMyInfoModulesQuery, ListMyInfoModulesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListMyInfoModulesQuery, ListMyInfoModulesQueryVariables>(ListMyInfoModulesDocument, options);
      }
export function useListMyInfoModulesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListMyInfoModulesQuery, ListMyInfoModulesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListMyInfoModulesQuery, ListMyInfoModulesQueryVariables>(ListMyInfoModulesDocument, options);
        }
export type ListMyInfoModulesQueryHookResult = ReturnType<typeof useListMyInfoModulesQuery>;
export type ListMyInfoModulesLazyQueryHookResult = ReturnType<typeof useListMyInfoModulesLazyQuery>;
export type ListMyInfoModulesQueryResult = Apollo.QueryResult<ListMyInfoModulesQuery, ListMyInfoModulesQueryVariables>;
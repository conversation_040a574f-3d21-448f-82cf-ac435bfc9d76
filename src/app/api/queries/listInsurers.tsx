import type * as SchemaTypes from '../types';

import type { InsurerListDataFragment } from '../fragments/InsurerListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { InsurerListDataFragmentDoc } from '../fragments/InsurerListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListInsurersQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.InsurerSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.InsurerFilteringRule>;
}>;


export type ListInsurersQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedInsurers' }
    & Pick<SchemaTypes.PaginatedInsurers, 'count'>
    & { items: Array<(
      { __typename: 'Insurer' }
      & InsurerListDataFragment
    )> }
  ) }
);


export const ListInsurersDocument = /*#__PURE__*/ gql`
    query listInsurers($pagination: Pagination, $sort: InsurerSortingRule, $filter: InsurerFilteringRule) {
  list: listInsurers(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...InsurerListData
    }
  }
}
    ${InsurerListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListInsurersQuery__
 *
 * To run a query within a React component, call `useListInsurersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListInsurersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListInsurersQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListInsurersQuery(baseOptions?: Apollo.QueryHookOptions<ListInsurersQuery, ListInsurersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListInsurersQuery, ListInsurersQueryVariables>(ListInsurersDocument, options);
      }
export function useListInsurersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListInsurersQuery, ListInsurersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListInsurersQuery, ListInsurersQueryVariables>(ListInsurersDocument, options);
        }
export type ListInsurersQueryHookResult = ReturnType<typeof useListInsurersQuery>;
export type ListInsurersLazyQueryHookResult = ReturnType<typeof useListInsurersLazyQuery>;
export type ListInsurersQueryResult = Apollo.QueryResult<ListInsurersQuery, ListInsurersQueryVariables>;
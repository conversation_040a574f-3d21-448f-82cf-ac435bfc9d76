import type * as SchemaTypes from '../types';

import type { LocalVariantProductionListSpecsFragment } from '../fragments/LocalVariantProductionListSpecs';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { LocalVariantProductionListSpecsFragmentDoc } from '../fragments/LocalVariantProductionListSpecs';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocalVariantsForSelectionQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocalVariantFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LocalVariantSortingRule>;
  bankModuleId: SchemaTypes.Scalars['ObjectID']['input'];
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  applicationModuleIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListLocalVariantsForSelectionQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLocalVariants' }
    & Pick<SchemaTypes.PaginatedLocalVariants, 'count'>
    & { items: Array<(
      { __typename: 'LocalVariant' }
      & LocalVariantProductionListSpecsFragment
    )> }
  ) }
);


export const ListLocalVariantsForSelectionDocument = /*#__PURE__*/ gql`
    query listLocalVariantsForSelection($filter: LocalVariantFilteringRule, $sort: LocalVariantSortingRule, $bankModuleId: ObjectID!, $dealerId: ObjectID, $applicationModuleIds: [ObjectID!]) {
  list: listLocalVariants(filter: $filter, sort: $sort) {
    count
    items {
      ...LocalVariantProductionListSpecs
    }
  }
}
    ${LocalVariantProductionListSpecsFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListLocalVariantsForSelectionQuery__
 *
 * To run a query within a React component, call `useListLocalVariantsForSelectionQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocalVariantsForSelectionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocalVariantsForSelectionQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *      bankModuleId: // value for 'bankModuleId'
 *      dealerId: // value for 'dealerId'
 *      applicationModuleIds: // value for 'applicationModuleIds'
 *   },
 * });
 */
export function useListLocalVariantsForSelectionQuery(baseOptions: Apollo.QueryHookOptions<ListLocalVariantsForSelectionQuery, ListLocalVariantsForSelectionQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocalVariantsForSelectionQuery, ListLocalVariantsForSelectionQueryVariables>(ListLocalVariantsForSelectionDocument, options);
      }
export function useListLocalVariantsForSelectionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocalVariantsForSelectionQuery, ListLocalVariantsForSelectionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocalVariantsForSelectionQuery, ListLocalVariantsForSelectionQueryVariables>(ListLocalVariantsForSelectionDocument, options);
        }
export type ListLocalVariantsForSelectionQueryHookResult = ReturnType<typeof useListLocalVariantsForSelectionQuery>;
export type ListLocalVariantsForSelectionLazyQueryHookResult = ReturnType<typeof useListLocalVariantsForSelectionLazyQuery>;
export type ListLocalVariantsForSelectionQueryResult = Apollo.QueryResult<ListLocalVariantsForSelectionQuery, ListLocalVariantsForSelectionQueryVariables>;
import type * as SchemaTypes from '../types';

import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetUsersOptionsForUserGroupQueryVariables = SchemaTypes.Exact<{
  userGroupId: SchemaTypes.Scalars['ObjectID']['input'];
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type GetUsersOptionsForUserGroupQuery = (
  { __typename: 'Query' }
  & { users: Array<(
    { __typename: 'User' }
    & UserPreviewDataFragment
  )> }
);


export const GetUsersOptionsForUserGroupDocument = /*#__PURE__*/ gql`
    query getUsersOptionsForUserGroup($userGroupId: ObjectID!, $companyId: ObjectID, $dealerIds: [ObjectID!]) {
  users: listAvailableUsersForUserGroup(
    userGroupId: $userGroupId
    companyId: $companyId
    dealerIds: $dealerIds
  ) {
    ...UserPreviewData
  }
}
    ${UserPreviewDataFragmentDoc}`;

/**
 * __useGetUsersOptionsForUserGroupQuery__
 *
 * To run a query within a React component, call `useGetUsersOptionsForUserGroupQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUsersOptionsForUserGroupQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUsersOptionsForUserGroupQuery({
 *   variables: {
 *      userGroupId: // value for 'userGroupId'
 *      companyId: // value for 'companyId'
 *      dealerIds: // value for 'dealerIds'
 *   },
 * });
 */
export function useGetUsersOptionsForUserGroupQuery(baseOptions: Apollo.QueryHookOptions<GetUsersOptionsForUserGroupQuery, GetUsersOptionsForUserGroupQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUsersOptionsForUserGroupQuery, GetUsersOptionsForUserGroupQueryVariables>(GetUsersOptionsForUserGroupDocument, options);
      }
export function useGetUsersOptionsForUserGroupLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUsersOptionsForUserGroupQuery, GetUsersOptionsForUserGroupQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUsersOptionsForUserGroupQuery, GetUsersOptionsForUserGroupQueryVariables>(GetUsersOptionsForUserGroupDocument, options);
        }
export type GetUsersOptionsForUserGroupQueryHookResult = ReturnType<typeof useGetUsersOptionsForUserGroupQuery>;
export type GetUsersOptionsForUserGroupLazyQueryHookResult = ReturnType<typeof useGetUsersOptionsForUserGroupLazyQuery>;
export type GetUsersOptionsForUserGroupQueryResult = Apollo.QueryResult<GetUsersOptionsForUserGroupQuery, GetUsersOptionsForUserGroupQueryVariables>;
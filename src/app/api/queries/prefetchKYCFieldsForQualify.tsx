import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchKycFieldsForQualifyQueryVariables = SchemaTypes.Exact<{
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type PrefetchKycFieldsForQualifyQuery = (
  { __typename: 'Query' }
  & { applicantKyc: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )> }
);


export const PrefetchKycFieldsForQualifyDocument = /*#__PURE__*/ gql`
    query prefetchKYCFieldsForQualify($applicationModuleId: ObjectID!) {
  applicantKyc: prefetchKYCFieldsForQualify(
    applicationModuleId: $applicationModuleId
  ) {
    ...KYCFieldSpecs
  }
}
    ${KycFieldSpecsFragmentDoc}`;

/**
 * __usePrefetchKycFieldsForQualifyQuery__
 *
 * To run a query within a React component, call `usePrefetchKycFieldsForQualifyQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchKycFieldsForQualifyQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchKycFieldsForQualifyQuery({
 *   variables: {
 *      applicationModuleId: // value for 'applicationModuleId'
 *   },
 * });
 */
export function usePrefetchKycFieldsForQualifyQuery(baseOptions: Apollo.QueryHookOptions<PrefetchKycFieldsForQualifyQuery, PrefetchKycFieldsForQualifyQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchKycFieldsForQualifyQuery, PrefetchKycFieldsForQualifyQueryVariables>(PrefetchKycFieldsForQualifyDocument, options);
      }
export function usePrefetchKycFieldsForQualifyLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchKycFieldsForQualifyQuery, PrefetchKycFieldsForQualifyQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchKycFieldsForQualifyQuery, PrefetchKycFieldsForQualifyQueryVariables>(PrefetchKycFieldsForQualifyDocument, options);
        }
export type PrefetchKycFieldsForQualifyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForQualifyQuery>;
export type PrefetchKycFieldsForQualifyLazyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForQualifyLazyQuery>;
export type PrefetchKycFieldsForQualifyQueryResult = Apollo.QueryResult<PrefetchKycFieldsForQualifyQuery, PrefetchKycFieldsForQualifyQueryVariables>;
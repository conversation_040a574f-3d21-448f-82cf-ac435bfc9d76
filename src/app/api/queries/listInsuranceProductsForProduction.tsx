import type * as SchemaTypes from '../types';

import type { ProductionInsuranceProductDetails_Eazy_Fragment, ProductionInsuranceProductDetails_ErgoLookupTable_Fragment } from '../fragments/ProductionInsuranceProductDetails';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { ProductionInsuranceProductDetailsFragmentDoc } from '../fragments/ProductionInsuranceProductDetails';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListInsuranceProductsForProductionQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.InsuranceProductFilteringRule>;
}>;


export type ListInsuranceProductsForProductionQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedInsuranceProducts' }
    & Pick<SchemaTypes.PaginatedInsuranceProducts, 'count'>
    & { items: Array<(
      { __typename: 'Eazy' }
      & ProductionInsuranceProductDetails_Eazy_Fragment
    ) | (
      { __typename: 'ErgoLookupTable' }
      & ProductionInsuranceProductDetails_ErgoLookupTable_Fragment
    )> }
  ) }
);


export const ListInsuranceProductsForProductionDocument = /*#__PURE__*/ gql`
    query listInsuranceProductsForProduction($filter: InsuranceProductFilteringRule) {
  list: listInsuranceProducts(filter: $filter) {
    count
    items {
      ...ProductionInsuranceProductDetails
    }
  }
}
    ${ProductionInsuranceProductDetailsFragmentDoc}
${PeriodDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListInsuranceProductsForProductionQuery__
 *
 * To run a query within a React component, call `useListInsuranceProductsForProductionQuery` and pass it any options that fit your needs.
 * When your component renders, `useListInsuranceProductsForProductionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListInsuranceProductsForProductionQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListInsuranceProductsForProductionQuery(baseOptions?: Apollo.QueryHookOptions<ListInsuranceProductsForProductionQuery, ListInsuranceProductsForProductionQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListInsuranceProductsForProductionQuery, ListInsuranceProductsForProductionQueryVariables>(ListInsuranceProductsForProductionDocument, options);
      }
export function useListInsuranceProductsForProductionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListInsuranceProductsForProductionQuery, ListInsuranceProductsForProductionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListInsuranceProductsForProductionQuery, ListInsuranceProductsForProductionQueryVariables>(ListInsuranceProductsForProductionDocument, options);
        }
export type ListInsuranceProductsForProductionQueryHookResult = ReturnType<typeof useListInsuranceProductsForProductionQuery>;
export type ListInsuranceProductsForProductionLazyQueryHookResult = ReturnType<typeof useListInsuranceProductsForProductionLazyQuery>;
export type ListInsuranceProductsForProductionQueryResult = Apollo.QueryResult<ListInsuranceProductsForProductionQuery, ListInsuranceProductsForProductionQueryVariables>;
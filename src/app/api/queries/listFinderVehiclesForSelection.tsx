import type * as SchemaTypes from '../types';

import type { FinderVehicleSelectionDataFragment } from '../fragments/FinderVehicleSelectionData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { FinderVehicleSelectionDataFragmentDoc } from '../fragments/FinderVehicleSelectionData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListFinderVehiclesForSelectionQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.FinderVehicleSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.FilterVehicleFilteringRule>;
  bankModuleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  applicationModuleIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListFinderVehiclesForSelectionQuery = (
  { __typename: 'Query' }
  & { vehicles: (
    { __typename: 'ListFinderVehiclesInfo' }
    & Pick<SchemaTypes.ListFinderVehiclesInfo, 'count'>
    & { items: Array<(
      { __typename: 'FinderVehicle' }
      & FinderVehicleSelectionDataFragment
    )>, filters?: SchemaTypes.Maybe<(
      { __typename: 'FinderVehicleFilters' }
      & Pick<SchemaTypes.FinderVehicleFilters, 'conditions' | 'modelCategories'>
      & { monthlyInstalments: Array<(
        { __typename: 'RangeAmount' }
        & Pick<SchemaTypes.RangeAmount, 'from' | 'to'>
      )> }
    )> }
  ) }
);


export const ListFinderVehiclesForSelectionDocument = /*#__PURE__*/ gql`
    query listFinderVehiclesForSelection($pagination: Pagination, $sort: FinderVehicleSortingRule, $filter: FilterVehicleFilteringRule, $bankModuleId: ObjectID, $applicationModuleIds: [ObjectID!], $dealerId: ObjectID) {
  vehicles: listFinderVehicles(
    pagination: $pagination
    sort: $sort
    filter: $filter
    dealerId: $dealerId
    applicationModuleIds: $applicationModuleIds
  ) {
    items {
      ...FinderVehicleSelectionData
    }
    count
    filters {
      conditions
      modelCategories
      monthlyInstalments {
        from
        to
      }
    }
  }
}
    ${FinderVehicleSelectionDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListFinderVehiclesForSelectionQuery__
 *
 * To run a query within a React component, call `useListFinderVehiclesForSelectionQuery` and pass it any options that fit your needs.
 * When your component renders, `useListFinderVehiclesForSelectionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListFinderVehiclesForSelectionQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *      bankModuleId: // value for 'bankModuleId'
 *      applicationModuleIds: // value for 'applicationModuleIds'
 *      dealerId: // value for 'dealerId'
 *   },
 * });
 */
export function useListFinderVehiclesForSelectionQuery(baseOptions?: Apollo.QueryHookOptions<ListFinderVehiclesForSelectionQuery, ListFinderVehiclesForSelectionQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListFinderVehiclesForSelectionQuery, ListFinderVehiclesForSelectionQueryVariables>(ListFinderVehiclesForSelectionDocument, options);
      }
export function useListFinderVehiclesForSelectionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListFinderVehiclesForSelectionQuery, ListFinderVehiclesForSelectionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListFinderVehiclesForSelectionQuery, ListFinderVehiclesForSelectionQueryVariables>(ListFinderVehiclesForSelectionDocument, options);
        }
export type ListFinderVehiclesForSelectionQueryHookResult = ReturnType<typeof useListFinderVehiclesForSelectionQuery>;
export type ListFinderVehiclesForSelectionLazyQueryHookResult = ReturnType<typeof useListFinderVehiclesForSelectionLazyQuery>;
export type ListFinderVehiclesForSelectionQueryResult = Apollo.QueryResult<ListFinderVehiclesForSelectionQuery, ListFinderVehiclesForSelectionQueryVariables>;
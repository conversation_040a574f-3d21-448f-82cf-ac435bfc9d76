import type * as SchemaTypes from '../types';

import type { AuditTrailDetailsFragment } from '../fragments/AuditTrailDetails';
import { gql } from '@apollo/client';
import { AuditTrailDetailsFragmentDoc } from '../fragments/AuditTrailDetails';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetStockAuditTrailsQueryVariables = SchemaTypes.Exact<{
  inventoryId: SchemaTypes.Scalars['ObjectID']['input'];
  stockId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type GetStockAuditTrailsQuery = (
  { __typename: 'Query' }
  & { result: (
    { __typename: 'PaginatedStockAuditTrails' }
    & Pick<SchemaTypes.PaginatedStockAuditTrails, 'count'>
    & { items: Array<(
      { __typename: 'AuditTrail' }
      & AuditTrailDetailsFragment
    )> }
  ) }
);


export const GetStockAuditTrailsDocument = /*#__PURE__*/ gql`
    query getStockAuditTrails($inventoryId: ObjectID!, $stockId: ObjectID!, $pagination: Pagination) {
  result: getStockAuditTrails(
    inventoryId: $inventoryId
    stockId: $stockId
    pagination: $pagination
  ) {
    count
    items {
      ...AuditTrailDetails
    }
  }
}
    ${AuditTrailDetailsFragmentDoc}`;

/**
 * __useGetStockAuditTrailsQuery__
 *
 * To run a query within a React component, call `useGetStockAuditTrailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetStockAuditTrailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetStockAuditTrailsQuery({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *      stockId: // value for 'stockId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useGetStockAuditTrailsQuery(baseOptions: Apollo.QueryHookOptions<GetStockAuditTrailsQuery, GetStockAuditTrailsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetStockAuditTrailsQuery, GetStockAuditTrailsQueryVariables>(GetStockAuditTrailsDocument, options);
      }
export function useGetStockAuditTrailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetStockAuditTrailsQuery, GetStockAuditTrailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetStockAuditTrailsQuery, GetStockAuditTrailsQueryVariables>(GetStockAuditTrailsDocument, options);
        }
export type GetStockAuditTrailsQueryHookResult = ReturnType<typeof useGetStockAuditTrailsQuery>;
export type GetStockAuditTrailsLazyQueryHookResult = ReturnType<typeof useGetStockAuditTrailsLazyQuery>;
export type GetStockAuditTrailsQueryResult = Apollo.QueryResult<GetStockAuditTrailsQuery, GetStockAuditTrailsQueryVariables>;
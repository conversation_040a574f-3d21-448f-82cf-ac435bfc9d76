import type * as SchemaTypes from '../types';

import type { ModelConfiguratorListDataFragment } from '../fragments/ModelConfiguratorListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { InventoryDetailsPublicData_ConfiguratorInventory_Fragment, InventoryDetailsPublicData_MobilityInventory_Fragment } from '../fragments/InventoryDetailsPublicData';
import type { StockInventorySpecs_ConfiguratorStockInventory_Fragment, StockInventorySpecs_MobilityStockInventory_Fragment } from '../fragments/StockInventorySpecs';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { ReferenceApplicationData_ConfiguratorApplication_Fragment, ReferenceApplicationData_EventApplication_Fragment, ReferenceApplicationData_FinderApplication_Fragment, ReferenceApplicationData_LaunchpadApplication_Fragment, ReferenceApplicationData_MobilityApplication_Fragment, ReferenceApplicationData_SalesOfferApplication_Fragment, ReferenceApplicationData_StandardApplication_Fragment, ReferenceDepositData_ApplicationAdyenDeposit_Fragment, ReferenceDepositData_ApplicationFiservDeposit_Fragment, ReferenceDepositData_ApplicationPayGateDeposit_Fragment, ReferenceDepositData_ApplicationPorscheDeposit_Fragment, ReferenceDepositData_ApplicationTtbDeposit_Fragment, ReferenceFinancingData_DefaultApplicationFinancing_Fragment, ReferenceFinancingData_NewZealandApplicationFinancing_Fragment, ReferenceFinancingData_SingaporeApplicationFinancing_Fragment, ReferenceInsuranceData_DefaultApplicationInsurancing_Fragment, ReferenceInsuranceData_NewZealandApplicationInsurancing_Fragment, ReferenceInsuranceData_SingaporeApplicationInsurancing_Fragment } from '../fragments/ReferenceApplicationData';
import type { VehicleSpecs_FinderVehicle_Fragment, VehicleSpecs_LocalMake_Fragment, VehicleSpecs_LocalModel_Fragment, VehicleSpecs_LocalVariant_Fragment } from '../fragments/VehicleSpecs';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import type { TradeInVehicleDataFragment } from '../fragments/TradeInVehicleData';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { StockBlockingPeriodDataFragment } from '../fragments/StockBlockingPeriod';
import type { ConfiguratorInventoryPublicSpecsFragment } from '../fragments/ConfiguratorInventoryPublicSpecs';
import type { MobilityInventoryPublicSpecsFragment } from '../fragments/MobilityInventoryPublicSpecs';
import type { MobilityModuleSpecsFragment } from '../fragments/MobilityModuleSpecs';
import type { CompanyInModuleOptionDataFragment } from '../fragments/CompanyInModuleOptionData';
import type { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragment } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { DealerTranslatedStringSettingDataFragment } from '../fragments/DealerTranslatedStringData';
import type { DealerBookingCodeSpecsFragment } from '../fragments/DealerBookingCodeSpecs';
import type { MobilitySigningSettingSpecsFragment } from '../fragments/MobilitySigningSettingSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { KycPresetsOptionsDataFragment } from '../fragments/KYCPresetsOptionsData';
import type { MobilityModuleEmailScenarioContentSpecsFragment } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import type { MobilityCustomerEmailContentDataFragment } from '../fragments/MobilityCustomerEmailContentData';
import type { MobilityEmailContentDataFragment } from '../fragments/MobilityEmailContentData';
import type { TranslatedTextDataFragment } from '../fragments/TranslationTextData';
import type { MobilityOperatorEmailContentDataFragment } from '../fragments/MobilityOperatorEmailContentData';
import type { MobilityHomeDeliveryDataFragment } from '../fragments/MobilityHomeDeliveryData';
import type { CounterSettingsSpecsFragment } from '../fragments/CounterSettingsSpecs';
import type { UploadFileFormDataFragment } from '../fragments/UploadFileFormData';
import { gql } from '@apollo/client';
import { ModelConfiguratorListDataFragmentDoc } from '../fragments/ModelConfiguratorListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { InventoryDetailsPublicDataFragmentDoc } from '../fragments/InventoryDetailsPublicData';
import { StockInventorySpecsFragmentDoc } from '../fragments/StockInventorySpecs';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { ReferenceApplicationDataFragmentDoc, ReferenceDepositDataFragmentDoc, ReferenceFinancingDataFragmentDoc, ReferenceInsuranceDataFragmentDoc } from '../fragments/ReferenceApplicationData';
import { VehicleSpecsFragmentDoc } from '../fragments/VehicleSpecs';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import { TradeInVehicleDataFragmentDoc } from '../fragments/TradeInVehicleData';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { StockBlockingPeriodDataFragmentDoc } from '../fragments/StockBlockingPeriod';
import { ConfiguratorInventoryPublicSpecsFragmentDoc } from '../fragments/ConfiguratorInventoryPublicSpecs';
import { MobilityInventoryPublicSpecsFragmentDoc } from '../fragments/MobilityInventoryPublicSpecs';
import { MobilityModuleSpecsFragmentDoc } from '../fragments/MobilityModuleSpecs';
import { CompanyInModuleOptionDataFragmentDoc } from '../fragments/CompanyInModuleOptionData';
import { VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc } from '../fragments/VehicleDataWithPorscheCodeIntegrationSettingSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { DealerTranslatedStringSettingDataFragmentDoc } from '../fragments/DealerTranslatedStringData';
import { DealerBookingCodeSpecsFragmentDoc } from '../fragments/DealerBookingCodeSpecs';
import { MobilitySigningSettingSpecsFragmentDoc } from '../fragments/MobilitySigningSettingSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { KycPresetsOptionsDataFragmentDoc } from '../fragments/KYCPresetsOptionsData';
import { MobilityModuleEmailScenarioContentSpecsFragmentDoc } from '../fragments/MobilityModuleEmailScenarioContentSpecs';
import { MobilityCustomerEmailContentDataFragmentDoc } from '../fragments/MobilityCustomerEmailContentData';
import { MobilityEmailContentDataFragmentDoc } from '../fragments/MobilityEmailContentData';
import { TranslatedTextDataFragmentDoc } from '../fragments/TranslationTextData';
import { MobilityOperatorEmailContentDataFragmentDoc } from '../fragments/MobilityOperatorEmailContentData';
import { MobilityHomeDeliveryDataFragmentDoc } from '../fragments/MobilityHomeDeliveryData';
import { CounterSettingsSpecsFragmentDoc } from '../fragments/CounterSettingsSpecs';
import { UploadFileFormDataFragmentDoc } from '../fragments/UploadFileFormData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListModelConfiguratorsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.ModelConfiguratorSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModelConfiguratorFilteringRule>;
}>;


export type ListModelConfiguratorsQuery = (
  { __typename: 'Query' }
  & { lists: (
    { __typename: 'PaginatedModelConfigurator' }
    & Pick<SchemaTypes.PaginatedModelConfigurator, 'count'>
    & { items: Array<(
      { __typename: 'ModelConfigurator' }
      & ModelConfiguratorListDataFragment
    )> }
  ) }
);


export const ListModelConfiguratorsDocument = /*#__PURE__*/ gql`
    query listModelConfigurators($pagination: Pagination, $sort: ModelConfiguratorSortingRule, $filter: ModelConfiguratorFilteringRule) {
  lists: listModelConfigurators(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    count
    items {
      ...ModelConfiguratorListData
    }
  }
}
    ${ModelConfiguratorListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${InventoryDetailsPublicDataFragmentDoc}
${StockInventorySpecsFragmentDoc}
${ApplicationStageDataFragmentDoc}
${ReferenceApplicationDataFragmentDoc}
${ReferenceDepositDataFragmentDoc}
${ReferenceFinancingDataFragmentDoc}
${ReferenceInsuranceDataFragmentDoc}
${VehicleSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}
${TradeInVehicleDataFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${PeriodDataFragmentDoc}
${StockBlockingPeriodDataFragmentDoc}
${ConfiguratorInventoryPublicSpecsFragmentDoc}
${MobilityInventoryPublicSpecsFragmentDoc}
${MobilityModuleSpecsFragmentDoc}
${CompanyInModuleOptionDataFragmentDoc}
${VehicleDataWithPorscheCodeIntegrationSettingSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${DealerTranslatedStringSettingDataFragmentDoc}
${DealerBookingCodeSpecsFragmentDoc}
${MobilitySigningSettingSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${KycPresetsOptionsDataFragmentDoc}
${MobilityModuleEmailScenarioContentSpecsFragmentDoc}
${MobilityCustomerEmailContentDataFragmentDoc}
${MobilityEmailContentDataFragmentDoc}
${TranslatedTextDataFragmentDoc}
${MobilityOperatorEmailContentDataFragmentDoc}
${MobilityHomeDeliveryDataFragmentDoc}
${CounterSettingsSpecsFragmentDoc}
${UploadFileFormDataFragmentDoc}`;

/**
 * __useListModelConfiguratorsQuery__
 *
 * To run a query within a React component, call `useListModelConfiguratorsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListModelConfiguratorsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListModelConfiguratorsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListModelConfiguratorsQuery(baseOptions?: Apollo.QueryHookOptions<ListModelConfiguratorsQuery, ListModelConfiguratorsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListModelConfiguratorsQuery, ListModelConfiguratorsQueryVariables>(ListModelConfiguratorsDocument, options);
      }
export function useListModelConfiguratorsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListModelConfiguratorsQuery, ListModelConfiguratorsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListModelConfiguratorsQuery, ListModelConfiguratorsQueryVariables>(ListModelConfiguratorsDocument, options);
        }
export type ListModelConfiguratorsQueryHookResult = ReturnType<typeof useListModelConfiguratorsQuery>;
export type ListModelConfiguratorsLazyQueryHookResult = ReturnType<typeof useListModelConfiguratorsLazyQuery>;
export type ListModelConfiguratorsQueryResult = Apollo.QueryResult<ListModelConfiguratorsQuery, ListModelConfiguratorsQueryVariables>;
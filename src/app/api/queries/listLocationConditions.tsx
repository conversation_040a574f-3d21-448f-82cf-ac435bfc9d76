import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocationConditionsQueryVariables = SchemaTypes.Exact<{
  locationId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListLocationConditionsQuery = (
  { __typename: 'Query' }
  & { list: SchemaTypes.Query['listLocationConditions'] }
);


export const ListLocationConditionsDocument = /*#__PURE__*/ gql`
    query listLocationConditions($locationId: ObjectID!) {
  list: listLocationConditions(locationId: $locationId)
}
    `;

/**
 * __useListLocationConditionsQuery__
 *
 * To run a query within a React component, call `useListLocationConditionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocationConditionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocationConditionsQuery({
 *   variables: {
 *      locationId: // value for 'locationId'
 *   },
 * });
 */
export function useListLocationConditionsQuery(baseOptions: Apollo.QueryHookOptions<ListLocationConditionsQuery, ListLocationConditionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocationConditionsQuery, ListLocationConditionsQueryVariables>(ListLocationConditionsDocument, options);
      }
export function useListLocationConditionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocationConditionsQuery, ListLocationConditionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocationConditionsQuery, ListLocationConditionsQueryVariables>(ListLocationConditionsDocument, options);
        }
export type ListLocationConditionsQueryHookResult = ReturnType<typeof useListLocationConditionsQuery>;
export type ListLocationConditionsLazyQueryHookResult = ReturnType<typeof useListLocationConditionsLazyQuery>;
export type ListLocationConditionsQueryResult = Apollo.QueryResult<ListLocationConditionsQuery, ListLocationConditionsQueryVariables>;
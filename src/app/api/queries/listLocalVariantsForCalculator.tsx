import type * as SchemaTypes from '../types';

import type { VehicleCalculatorSpecs_FinderVehicle_Fragment, VehicleCalculatorSpecs_LocalMake_Fragment, VehicleCalculatorSpecs_LocalModel_Fragment, VehicleCalculatorSpecs_LocalVariant_Fragment } from '../fragments/VehicleCalculatorSpecs';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { LocalVariantCalculatorSpecsFragment } from '../fragments/LocalVariantCalculatorSpecs';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelCalculatorSpecsFragment } from '../fragments/LocalModelCalculatorSpecs';
import type { LocalMakeCalculatorSpecsFragment } from '../fragments/LocalMakeCalculatorSpecs';
import type { FinderVehicleDataFragment } from '../fragments/FinderVehicleData';
import { gql } from '@apollo/client';
import { VehicleCalculatorSpecsFragmentDoc } from '../fragments/VehicleCalculatorSpecs';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { LocalVariantCalculatorSpecsFragmentDoc } from '../fragments/LocalVariantCalculatorSpecs';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelCalculatorSpecsFragmentDoc } from '../fragments/LocalModelCalculatorSpecs';
import { LocalMakeCalculatorSpecsFragmentDoc } from '../fragments/LocalMakeCalculatorSpecs';
import { FinderVehicleDataFragmentDoc } from '../fragments/FinderVehicleData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocalVariantsForCalculatorQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocalVariantFilteringRule>;
  bankModuleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  applicationModuleIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListLocalVariantsForCalculatorQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLocalVariants' }
    & Pick<SchemaTypes.PaginatedLocalVariants, 'count'>
    & { items: Array<(
      { __typename: 'LocalVariant' }
      & VehicleCalculatorSpecs_LocalVariant_Fragment
    )> }
  ) }
);


export const ListLocalVariantsForCalculatorDocument = /*#__PURE__*/ gql`
    query listLocalVariantsForCalculator($filter: LocalVariantFilteringRule, $bankModuleId: ObjectID, $applicationModuleIds: [ObjectID!], $dealerId: ObjectID) {
  list: listLocalVariants(filter: $filter) {
    count
    items {
      ...VehicleCalculatorSpecs
    }
  }
}
    ${VehicleCalculatorSpecsFragmentDoc}
${TranslatedStringDataFragmentDoc}
${LocalVariantCalculatorSpecsFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelCalculatorSpecsFragmentDoc}
${LocalMakeCalculatorSpecsFragmentDoc}
${FinderVehicleDataFragmentDoc}`;

/**
 * __useListLocalVariantsForCalculatorQuery__
 *
 * To run a query within a React component, call `useListLocalVariantsForCalculatorQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocalVariantsForCalculatorQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocalVariantsForCalculatorQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *      bankModuleId: // value for 'bankModuleId'
 *      applicationModuleIds: // value for 'applicationModuleIds'
 *      dealerId: // value for 'dealerId'
 *   },
 * });
 */
export function useListLocalVariantsForCalculatorQuery(baseOptions?: Apollo.QueryHookOptions<ListLocalVariantsForCalculatorQuery, ListLocalVariantsForCalculatorQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocalVariantsForCalculatorQuery, ListLocalVariantsForCalculatorQueryVariables>(ListLocalVariantsForCalculatorDocument, options);
      }
export function useListLocalVariantsForCalculatorLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocalVariantsForCalculatorQuery, ListLocalVariantsForCalculatorQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocalVariantsForCalculatorQuery, ListLocalVariantsForCalculatorQueryVariables>(ListLocalVariantsForCalculatorDocument, options);
        }
export type ListLocalVariantsForCalculatorQueryHookResult = ReturnType<typeof useListLocalVariantsForCalculatorQuery>;
export type ListLocalVariantsForCalculatorLazyQueryHookResult = ReturnType<typeof useListLocalVariantsForCalculatorLazyQuery>;
export type ListLocalVariantsForCalculatorQueryResult = Apollo.QueryResult<ListLocalVariantsForCalculatorQuery, ListLocalVariantsForCalculatorQueryVariables>;
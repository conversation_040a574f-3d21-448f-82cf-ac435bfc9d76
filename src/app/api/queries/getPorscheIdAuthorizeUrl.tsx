import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetPorscheIdAuthorizeUrlQueryVariables = SchemaTypes.Exact<{
  applicationId: SchemaTypes.Scalars['ObjectID']['input'];
  routerId: SchemaTypes.Scalars['ObjectID']['input'];
  endpointId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetPorscheIdAuthorizeUrlQuery = (
  { __typename: 'Query' }
  & { authorizeUrl: SchemaTypes.Query['getPorscheIdAuthorizeUrl'] }
);


export const GetPorscheIdAuthorizeUrlDocument = /*#__PURE__*/ gql`
    query getPorscheIdAuthorizeUrl($applicationId: ObjectID!, $routerId: ObjectID!, $endpointId: ObjectID!) {
  authorizeUrl: getPorscheIdAuthorizeUrl(
    applicationId: $applicationId
    routerId: $routerId
    endpointId: $endpointId
  )
}
    `;

/**
 * __useGetPorscheIdAuthorizeUrlQuery__
 *
 * To run a query within a React component, call `useGetPorscheIdAuthorizeUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPorscheIdAuthorizeUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPorscheIdAuthorizeUrlQuery({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      routerId: // value for 'routerId'
 *      endpointId: // value for 'endpointId'
 *   },
 * });
 */
export function useGetPorscheIdAuthorizeUrlQuery(baseOptions: Apollo.QueryHookOptions<GetPorscheIdAuthorizeUrlQuery, GetPorscheIdAuthorizeUrlQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPorscheIdAuthorizeUrlQuery, GetPorscheIdAuthorizeUrlQueryVariables>(GetPorscheIdAuthorizeUrlDocument, options);
      }
export function useGetPorscheIdAuthorizeUrlLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPorscheIdAuthorizeUrlQuery, GetPorscheIdAuthorizeUrlQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPorscheIdAuthorizeUrlQuery, GetPorscheIdAuthorizeUrlQueryVariables>(GetPorscheIdAuthorizeUrlDocument, options);
        }
export type GetPorscheIdAuthorizeUrlQueryHookResult = ReturnType<typeof useGetPorscheIdAuthorizeUrlQuery>;
export type GetPorscheIdAuthorizeUrlLazyQueryHookResult = ReturnType<typeof useGetPorscheIdAuthorizeUrlLazyQuery>;
export type GetPorscheIdAuthorizeUrlQueryResult = Apollo.QueryResult<GetPorscheIdAuthorizeUrlQuery, GetPorscheIdAuthorizeUrlQueryVariables>;
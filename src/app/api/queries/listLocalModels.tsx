import type * as SchemaTypes from '../types';

import type { LocalModelsListDataFragment } from '../fragments/LocalModelsListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { LocalModelsListDataFragmentDoc } from '../fragments/LocalModelsListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocalModelsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LocalModelSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocalModelFilteringRule>;
}>;


export type ListLocalModelsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLocalModels' }
    & Pick<SchemaTypes.PaginatedLocalModels, 'count'>
    & { items: Array<(
      { __typename: 'LocalModel' }
      & LocalModelsListDataFragment
    )> }
  ) }
);


export const ListLocalModelsDocument = /*#__PURE__*/ gql`
    query listLocalModels($pagination: Pagination, $sort: LocalModelSortingRule, $filter: LocalModelFilteringRule) {
  list: listLocalModels(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...LocalModelsListData
    }
  }
}
    ${LocalModelsListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListLocalModelsQuery__
 *
 * To run a query within a React component, call `useListLocalModelsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocalModelsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocalModelsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListLocalModelsQuery(baseOptions?: Apollo.QueryHookOptions<ListLocalModelsQuery, ListLocalModelsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocalModelsQuery, ListLocalModelsQueryVariables>(ListLocalModelsDocument, options);
      }
export function useListLocalModelsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocalModelsQuery, ListLocalModelsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocalModelsQuery, ListLocalModelsQueryVariables>(ListLocalModelsDocument, options);
        }
export type ListLocalModelsQueryHookResult = ReturnType<typeof useListLocalModelsQuery>;
export type ListLocalModelsLazyQueryHookResult = ReturnType<typeof useListLocalModelsLazyQuery>;
export type ListLocalModelsQueryResult = Apollo.QueryResult<ListLocalModelsQuery, ListLocalModelsQueryVariables>;
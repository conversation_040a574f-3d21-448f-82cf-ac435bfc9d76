import type * as SchemaTypes from '../types';

import type { UserGroupListDataFragment } from '../fragments/UserGroupListData';
import { gql } from '@apollo/client';
import { UserGroupListDataFragmentDoc } from '../fragments/UserGroupListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListAvailableUserGroupsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.UserGroupFilteringRule>;
}>;


export type ListAvailableUserGroupsQuery = (
  { __typename: 'Query' }
  & { page: (
    { __typename: 'PaginatedUserGroups' }
    & Pick<SchemaTypes.PaginatedUserGroups, 'count'>
    & { items: Array<(
      { __typename: 'UserGroup' }
      & UserGroupListDataFragment
    )> }
  ) }
);


export const ListAvailableUserGroupsDocument = /*#__PURE__*/ gql`
    query listAvailableUserGroups($pagination: Pagination, $filter: UserGroupFilteringRule) {
  page: listAvailableUserGroups(pagination: $pagination, filter: $filter) {
    count
    items {
      ...UserGroupListData
    }
  }
}
    ${UserGroupListDataFragmentDoc}`;

/**
 * __useListAvailableUserGroupsQuery__
 *
 * To run a query within a React component, call `useListAvailableUserGroupsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListAvailableUserGroupsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListAvailableUserGroupsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListAvailableUserGroupsQuery(baseOptions?: Apollo.QueryHookOptions<ListAvailableUserGroupsQuery, ListAvailableUserGroupsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListAvailableUserGroupsQuery, ListAvailableUserGroupsQueryVariables>(ListAvailableUserGroupsDocument, options);
      }
export function useListAvailableUserGroupsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListAvailableUserGroupsQuery, ListAvailableUserGroupsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListAvailableUserGroupsQuery, ListAvailableUserGroupsQueryVariables>(ListAvailableUserGroupsDocument, options);
        }
export type ListAvailableUserGroupsQueryHookResult = ReturnType<typeof useListAvailableUserGroupsQuery>;
export type ListAvailableUserGroupsLazyQueryHookResult = ReturnType<typeof useListAvailableUserGroupsLazyQuery>;
export type ListAvailableUserGroupsQueryResult = Apollo.QueryResult<ListAvailableUserGroupsQuery, ListAvailableUserGroupsQueryVariables>;
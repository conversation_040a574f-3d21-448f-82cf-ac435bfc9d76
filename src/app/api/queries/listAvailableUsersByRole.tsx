import type * as SchemaTypes from '../types';

import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListAvailableUsersByRoleQueryVariables = SchemaTypes.Exact<{
  roleId: SchemaTypes.Scalars['ObjectID']['input'];
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListAvailableUsersByRoleQuery = (
  { __typename: 'Query' }
  & { users: Array<(
    { __typename: 'User' }
    & UserPreviewDataFragment
  )> }
);


export const ListAvailableUsersByRoleDocument = /*#__PURE__*/ gql`
    query listAvailableUsersByRole($roleId: ObjectID!, $companyId: ObjectID, $dealerIds: [ObjectID!]) {
  users: listAvailableUsersByRole(
    roleId: $roleId
    companyId: $companyId
    dealerIds: $dealerIds
  ) {
    ...UserPreviewData
  }
}
    ${UserPreviewDataFragmentDoc}`;

/**
 * __useListAvailableUsersByRoleQuery__
 *
 * To run a query within a React component, call `useListAvailableUsersByRoleQuery` and pass it any options that fit your needs.
 * When your component renders, `useListAvailableUsersByRoleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListAvailableUsersByRoleQuery({
 *   variables: {
 *      roleId: // value for 'roleId'
 *      companyId: // value for 'companyId'
 *      dealerIds: // value for 'dealerIds'
 *   },
 * });
 */
export function useListAvailableUsersByRoleQuery(baseOptions: Apollo.QueryHookOptions<ListAvailableUsersByRoleQuery, ListAvailableUsersByRoleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListAvailableUsersByRoleQuery, ListAvailableUsersByRoleQueryVariables>(ListAvailableUsersByRoleDocument, options);
      }
export function useListAvailableUsersByRoleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListAvailableUsersByRoleQuery, ListAvailableUsersByRoleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListAvailableUsersByRoleQuery, ListAvailableUsersByRoleQueryVariables>(ListAvailableUsersByRoleDocument, options);
        }
export type ListAvailableUsersByRoleQueryHookResult = ReturnType<typeof useListAvailableUsersByRoleQuery>;
export type ListAvailableUsersByRoleLazyQueryHookResult = ReturnType<typeof useListAvailableUsersByRoleLazyQuery>;
export type ListAvailableUsersByRoleQueryResult = Apollo.QueryResult<ListAvailableUsersByRoleQuery, ListAvailableUsersByRoleQueryVariables>;
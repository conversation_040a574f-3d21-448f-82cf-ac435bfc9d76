import type * as SchemaTypes from '../types';

import type { UserGroupListDataFragment } from '../fragments/UserGroupListData';
import { gql } from '@apollo/client';
import { UserGroupListDataFragmentDoc } from '../fragments/UserGroupListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListUserGroupsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.UserGroupFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.UserGroupSortingRule>;
}>;


export type ListUserGroupsQuery = (
  { __typename: 'Query' }
  & { page: (
    { __typename: 'PaginatedUserGroups' }
    & Pick<SchemaTypes.PaginatedUserGroups, 'count'>
    & { items: Array<(
      { __typename: 'UserGroup' }
      & UserGroupListDataFragment
    )> }
  ) }
);


export const ListUserGroupsDocument = /*#__PURE__*/ gql`
    query listUserGroups($pagination: Pagination, $filter: UserGroupFilteringRule, $sort: UserGroupSortingRule) {
  page: listUserGroups(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...UserGroupListData
    }
  }
}
    ${UserGroupListDataFragmentDoc}`;

/**
 * __useListUserGroupsQuery__
 *
 * To run a query within a React component, call `useListUserGroupsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListUserGroupsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListUserGroupsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListUserGroupsQuery(baseOptions?: Apollo.QueryHookOptions<ListUserGroupsQuery, ListUserGroupsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListUserGroupsQuery, ListUserGroupsQueryVariables>(ListUserGroupsDocument, options);
      }
export function useListUserGroupsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListUserGroupsQuery, ListUserGroupsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListUserGroupsQuery, ListUserGroupsQueryVariables>(ListUserGroupsDocument, options);
        }
export type ListUserGroupsQueryHookResult = ReturnType<typeof useListUserGroupsQuery>;
export type ListUserGroupsLazyQueryHookResult = ReturnType<typeof useListUserGroupsLazyQuery>;
export type ListUserGroupsQueryResult = Apollo.QueryResult<ListUserGroupsQuery, ListUserGroupsQueryVariables>;
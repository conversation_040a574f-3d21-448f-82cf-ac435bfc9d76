import type * as SchemaTypes from '../types';

import type { LeadListData_ConfiguratorLead_Fragment, LeadListData_EventLead_Fragment, LeadListData_FinderLead_Fragment, LeadListData_LaunchpadLead_Fragment, LeadListData_MobilityLead_Fragment, LeadListData_StandardLead_Fragment } from '../fragments/LeadListData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { LeadListDataFragmentDoc } from '../fragments/LeadListData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLeadsQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LeadSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LeadFilteringRule>;
}>;


export type ListLeadsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLeads' }
    & Pick<SchemaTypes.PaginatedLeads, 'count'>
    & { items: Array<(
      { __typename: 'ConfiguratorLead' }
      & LeadListData_ConfiguratorLead_Fragment
    ) | (
      { __typename: 'EventLead' }
      & LeadListData_EventLead_Fragment
    ) | (
      { __typename: 'FinderLead' }
      & LeadListData_FinderLead_Fragment
    ) | (
      { __typename: 'LaunchpadLead' }
      & LeadListData_LaunchpadLead_Fragment
    ) | (
      { __typename: 'MobilityLead' }
      & LeadListData_MobilityLead_Fragment
    ) | (
      { __typename: 'StandardLead' }
      & LeadListData_StandardLead_Fragment
    )> }
  ) }
);


export const ListLeadsDocument = /*#__PURE__*/ gql`
    query listLeads($pagination: Pagination!, $sort: LeadSortingRule, $filter: LeadFilteringRule) {
  list: listLeads(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...LeadListData
    }
  }
}
    ${LeadListDataFragmentDoc}
${AuthorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListLeadsQuery__
 *
 * To run a query within a React component, call `useListLeadsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLeadsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLeadsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListLeadsQuery(baseOptions: Apollo.QueryHookOptions<ListLeadsQuery, ListLeadsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLeadsQuery, ListLeadsQueryVariables>(ListLeadsDocument, options);
      }
export function useListLeadsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLeadsQuery, ListLeadsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLeadsQuery, ListLeadsQueryVariables>(ListLeadsDocument, options);
        }
export type ListLeadsQueryHookResult = ReturnType<typeof useListLeadsQuery>;
export type ListLeadsLazyQueryHookResult = ReturnType<typeof useListLeadsLazyQuery>;
export type ListLeadsQueryResult = Apollo.QueryResult<ListLeadsQuery, ListLeadsQueryVariables>;
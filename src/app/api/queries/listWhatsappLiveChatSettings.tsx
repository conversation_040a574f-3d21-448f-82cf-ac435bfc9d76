import type * as SchemaTypes from '../types';

import type { WhatsappLiveChatSettingsSpecFragment } from '../fragments/WhatsappLiveChatSettingsSpec';
import { gql } from '@apollo/client';
import { WhatsappLiveChatSettingsSpecFragmentDoc } from '../fragments/WhatsappLiveChatSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListWhatsappLiveChatSettingsQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type ListWhatsappLiveChatSettingsQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedWhatsappLiveChatSettings' }
    & Pick<SchemaTypes.PaginatedWhatsappLiveChatSettings, 'count'>
    & { items: Array<(
      { __typename: 'WhatsappLiveChatSetting' }
      & WhatsappLiveChatSettingsSpecFragment
    )> }
  ) }
);


export const ListWhatsappLiveChatSettingsDocument = /*#__PURE__*/ gql`
    query listWhatsappLiveChatSettings($moduleId: ObjectID!, $pagination: Pagination) {
  settings: listWhatsappLiveChatSettings(
    moduleId: $moduleId
    pagination: $pagination
  ) {
    count
    items {
      ...WhatsappLiveChatSettingsSpec
    }
  }
}
    ${WhatsappLiveChatSettingsSpecFragmentDoc}`;

/**
 * __useListWhatsappLiveChatSettingsQuery__
 *
 * To run a query within a React component, call `useListWhatsappLiveChatSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListWhatsappLiveChatSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListWhatsappLiveChatSettingsQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useListWhatsappLiveChatSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListWhatsappLiveChatSettingsQuery, ListWhatsappLiveChatSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListWhatsappLiveChatSettingsQuery, ListWhatsappLiveChatSettingsQueryVariables>(ListWhatsappLiveChatSettingsDocument, options);
      }
export function useListWhatsappLiveChatSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListWhatsappLiveChatSettingsQuery, ListWhatsappLiveChatSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListWhatsappLiveChatSettingsQuery, ListWhatsappLiveChatSettingsQueryVariables>(ListWhatsappLiveChatSettingsDocument, options);
        }
export type ListWhatsappLiveChatSettingsQueryHookResult = ReturnType<typeof useListWhatsappLiveChatSettingsQuery>;
export type ListWhatsappLiveChatSettingsLazyQueryHookResult = ReturnType<typeof useListWhatsappLiveChatSettingsLazyQuery>;
export type ListWhatsappLiveChatSettingsQueryResult = Apollo.QueryResult<ListWhatsappLiveChatSettingsQuery, ListWhatsappLiveChatSettingsQueryVariables>;
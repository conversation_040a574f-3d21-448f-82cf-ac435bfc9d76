import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ValidateFileQueryVariables = SchemaTypes.Exact<{
  upload: SchemaTypes.Scalars['Upload']['input'];
  assetFieldSource: SchemaTypes.Scalars['String']['input'];
}>;


export type ValidateFileQuery = (
  { __typename: 'Query' }
  & { validateFile: SchemaTypes.Query['validateFile'] }
);


export const ValidateFileDocument = /*#__PURE__*/ gql`
    query validateFile($upload: Upload!, $assetFieldSource: String!) {
  validateFile: validateFile(upload: $upload, assetFieldSource: $assetFieldSource)
}
    `;

/**
 * __useValidateFileQuery__
 *
 * To run a query within a React component, call `useValidateFileQuery` and pass it any options that fit your needs.
 * When your component renders, `useValidateFileQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useValidateFileQuery({
 *   variables: {
 *      upload: // value for 'upload'
 *      assetFieldSource: // value for 'assetFieldSource'
 *   },
 * });
 */
export function useValidateFileQuery(baseOptions: Apollo.QueryHookOptions<ValidateFileQuery, ValidateFileQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ValidateFileQuery, ValidateFileQueryVariables>(ValidateFileDocument, options);
      }
export function useValidateFileLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ValidateFileQuery, ValidateFileQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ValidateFileQuery, ValidateFileQueryVariables>(ValidateFileDocument, options);
        }
export type ValidateFileQueryHookResult = ReturnType<typeof useValidateFileQuery>;
export type ValidateFileLazyQueryHookResult = ReturnType<typeof useValidateFileLazyQuery>;
export type ValidateFileQueryResult = Apollo.QueryResult<ValidateFileQuery, ValidateFileQueryVariables>;
query listInventoriesFilter($filter: InventoryFilteringRule) {
    listInventoriesFilter(filter: $filter) {
        variant {
            identifier
            name {
                ...TranslatedStringData
            }
        }

        model {
            identifier
            name {
                ...TranslatedStringData
            }
        }

        subModel {
            identifier
            name {
                ...TranslatedStringData
            }
        }

        ... on ConfiguratorInventory {
            module {
                id
                displayName
            }

            colorSetting {
                id
                name {
                    ...TranslatedStringData
                }
                code
            }

            trimSetting {
                id
                name {
                    ...TranslatedStringData
                }
                code
            }

            packageSetting {
                id
                packageName {
                    ...TranslatedStringData
                }
            }
        }

        ... on MobilityInventory {
            module {
                id
                displayName
            }
        }
    }
}

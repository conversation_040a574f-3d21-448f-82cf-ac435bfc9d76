import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListMobilityHomeDeliveriesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.HomeDeliveryFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.HomeDeliverySortingRule>;
}>;


export type ListMobilityHomeDeliveriesQuery = (
  { __typename: 'Query' }
  & { delivery: (
    { __typename: 'PaginatedMobilityHomeDelivery' }
    & Pick<SchemaTypes.PaginatedMobilityHomeDelivery, 'count'>
    & { items: Array<(
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'displayName' | 'id'>
      & { homeDelivery: (
        { __typename: 'MobilityHomeDelivery' }
        & Pick<SchemaTypes.MobilityHomeDelivery, 'isEnable' | 'id'>
      ) }
    )> }
  ) }
);


export const ListMobilityHomeDeliveriesDocument = /*#__PURE__*/ gql`
    query listMobilityHomeDeliveries($pagination: Pagination, $filter: HomeDeliveryFilteringRule, $sort: HomeDeliverySortingRule) {
  delivery: listMobilityHomeDeliveries(
    pagination: $pagination
    filter: $filter
    sort: $sort
  ) {
    count
    items {
      displayName
      id
      homeDelivery {
        isEnable
        id
      }
    }
  }
}
    `;

/**
 * __useListMobilityHomeDeliveriesQuery__
 *
 * To run a query within a React component, call `useListMobilityHomeDeliveriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListMobilityHomeDeliveriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListMobilityHomeDeliveriesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListMobilityHomeDeliveriesQuery(baseOptions?: Apollo.QueryHookOptions<ListMobilityHomeDeliveriesQuery, ListMobilityHomeDeliveriesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListMobilityHomeDeliveriesQuery, ListMobilityHomeDeliveriesQueryVariables>(ListMobilityHomeDeliveriesDocument, options);
      }
export function useListMobilityHomeDeliveriesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListMobilityHomeDeliveriesQuery, ListMobilityHomeDeliveriesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListMobilityHomeDeliveriesQuery, ListMobilityHomeDeliveriesQueryVariables>(ListMobilityHomeDeliveriesDocument, options);
        }
export type ListMobilityHomeDeliveriesQueryHookResult = ReturnType<typeof useListMobilityHomeDeliveriesQuery>;
export type ListMobilityHomeDeliveriesLazyQueryHookResult = ReturnType<typeof useListMobilityHomeDeliveriesLazyQuery>;
export type ListMobilityHomeDeliveriesQueryResult = Apollo.QueryResult<ListMobilityHomeDeliveriesQuery, ListMobilityHomeDeliveriesQueryVariables>;
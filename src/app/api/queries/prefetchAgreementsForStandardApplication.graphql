query prefetchAgreementsForStandardApplication(
    $applicationModuleId: ObjectID!
    $configuration: StandardApplicationConfiguration!
) {
    applicantAgreements: prefetchAgreementsForStandardApplication(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "local"
    ) {
        ...ApplicationAgreementData
    }

    corporateAgreements: prefetchAgreementsForStandardApplication(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "corporate"
    ) {
        ...ApplicationAgreementData
    }
}

import type * as SchemaTypes from '../types';

import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetTradeInQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetTradeInQuery = (
  { __typename: 'Query' }
  & { tradeIn?: SchemaTypes.Maybe<never> }
);


export const GetTradeInDocument = /*#__PURE__*/ gql`
    query getTradeIn($id: ObjectID!) {
  tradeIn: getTradeIn(id: $id) {
    id
    moduleId
    status
    identifier
    versioning {
      ...SimpleVersioningData
    }
  }
}
    ${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useGetTradeInQuery__
 *
 * To run a query within a React component, call `useGetTradeInQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTradeInQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTradeInQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetTradeInQuery(baseOptions: Apollo.QueryHookOptions<GetTradeInQuery, GetTradeInQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTradeInQuery, GetTradeInQueryVariables>(GetTradeInDocument, options);
      }
export function useGetTradeInLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTradeInQuery, GetTradeInQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTradeInQuery, GetTradeInQueryVariables>(GetTradeInDocument, options);
        }
export type GetTradeInQueryHookResult = ReturnType<typeof useGetTradeInQuery>;
export type GetTradeInLazyQueryHookResult = ReturnType<typeof useGetTradeInLazyQuery>;
export type GetTradeInQueryResult = Apollo.QueryResult<GetTradeInQuery, GetTradeInQueryVariables>;
import type * as SchemaTypes from '../types';

import type { WebCalcSettingDataFragment } from '../fragments/WebCalcSettingData';
import { gql } from '@apollo/client';
import { WebCalcSettingDataFragmentDoc } from '../fragments/WebCalcSettingData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListWebCalcSettingsQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type ListWebCalcSettingsQuery = (
  { __typename: 'Query' }
  & { result: (
    { __typename: 'PaginatedWebCalcSettings' }
    & Pick<SchemaTypes.PaginatedWebCalcSettings, 'count'>
    & { items: Array<(
      { __typename: 'WebCalcSetting' }
      & WebCalcSettingDataFragment
    )> }
  ) }
);


export const ListWebCalcSettingsDocument = /*#__PURE__*/ gql`
    query listWebCalcSettings($moduleId: ObjectID!, $pagination: Pagination) {
  result: listWebCalcSettings(moduleId: $moduleId, pagination: $pagination) {
    count
    items {
      ...WebCalcSettingData
    }
  }
}
    ${WebCalcSettingDataFragmentDoc}`;

/**
 * __useListWebCalcSettingsQuery__
 *
 * To run a query within a React component, call `useListWebCalcSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListWebCalcSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListWebCalcSettingsQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useListWebCalcSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListWebCalcSettingsQuery, ListWebCalcSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListWebCalcSettingsQuery, ListWebCalcSettingsQueryVariables>(ListWebCalcSettingsDocument, options);
      }
export function useListWebCalcSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListWebCalcSettingsQuery, ListWebCalcSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListWebCalcSettingsQuery, ListWebCalcSettingsQueryVariables>(ListWebCalcSettingsDocument, options);
        }
export type ListWebCalcSettingsQueryHookResult = ReturnType<typeof useListWebCalcSettingsQuery>;
export type ListWebCalcSettingsLazyQueryHookResult = ReturnType<typeof useListWebCalcSettingsLazyQuery>;
export type ListWebCalcSettingsQueryResult = Apollo.QueryResult<ListWebCalcSettingsQuery, ListWebCalcSettingsQueryVariables>;
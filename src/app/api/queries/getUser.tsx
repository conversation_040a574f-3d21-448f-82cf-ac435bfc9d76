import type * as SchemaTypes from '../types';

import type { UserSpecsFragment } from '../fragments/UserSpecs';
import type { PermissionSpecsFragment } from '../fragments/PermissionSpecs';
import type { RoleSpecsFragment } from '../fragments/RoleSpecs';
import type { UserAvatarSpecsFragment } from '../fragments/UserAvatarSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { UserSpecsFragmentDoc } from '../fragments/UserSpecs';
import { PermissionSpecsFragmentDoc } from '../fragments/PermissionSpecs';
import { RoleSpecsFragmentDoc } from '../fragments/RoleSpecs';
import { UserAvatarSpecsFragmentDoc } from '../fragments/UserAvatarSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetUserQueryVariables = SchemaTypes.Exact<{
  userId: SchemaTypes.Scalars['ObjectID']['input'];
  filter?: SchemaTypes.InputMaybe<SchemaTypes.UserFilteringRule>;
}>;


export type GetUserQuery = (
  { __typename: 'Query' }
  & { user?: SchemaTypes.Maybe<(
    { __typename: 'User' }
    & UserSpecsFragment
  )> }
);


export const GetUserDocument = /*#__PURE__*/ gql`
    query getUser($userId: ObjectID!, $filter: UserFilteringRule) {
  user: getUser(userId: $userId, filter: $filter) {
    ...UserSpecs
  }
}
    ${UserSpecsFragmentDoc}
${PermissionSpecsFragmentDoc}
${RoleSpecsFragmentDoc}
${UserAvatarSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useGetUserQuery__
 *
 * To run a query within a React component, call `useGetUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserQuery({
 *   variables: {
 *      userId: // value for 'userId'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetUserQuery(baseOptions: Apollo.QueryHookOptions<GetUserQuery, GetUserQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
      }
export function useGetUserLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUserQuery, GetUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
        }
export type GetUserQueryHookResult = ReturnType<typeof useGetUserQuery>;
export type GetUserLazyQueryHookResult = ReturnType<typeof useGetUserLazyQuery>;
export type GetUserQueryResult = Apollo.QueryResult<GetUserQuery, GetUserQueryVariables>;
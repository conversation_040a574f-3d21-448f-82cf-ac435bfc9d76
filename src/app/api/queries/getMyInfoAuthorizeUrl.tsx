import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetMyInfoAuthorizeUrlQueryVariables = SchemaTypes.Exact<{
  applicationId: SchemaTypes.Scalars['ObjectID']['input'];
  routerId: SchemaTypes.Scalars['ObjectID']['input'];
  endpointId: SchemaTypes.Scalars['ObjectID']['input'];
  customerKind: SchemaTypes.CustomerKind;
  withTradeIn?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
  withTestDrive?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
}>;


export type GetMyInfoAuthorizeUrlQuery = (
  { __typename: 'Query' }
  & { authorizeUrl: SchemaTypes.Query['getMyInfoAuthorizeUrl'] }
);


export const GetMyInfoAuthorizeUrlDocument = /*#__PURE__*/ gql`
    query getMyInfoAuthorizeUrl($applicationId: ObjectID!, $routerId: ObjectID!, $endpointId: ObjectID!, $customerKind: CustomerKind!, $withTradeIn: Boolean, $withTestDrive: Boolean) {
  authorizeUrl: getMyInfoAuthorizeUrl(
    applicationId: $applicationId
    routerId: $routerId
    endpointId: $endpointId
    customerKind: $customerKind
    withTradeIn: $withTradeIn
    withTestDrive: $withTestDrive
  )
}
    `;

/**
 * __useGetMyInfoAuthorizeUrlQuery__
 *
 * To run a query within a React component, call `useGetMyInfoAuthorizeUrlQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMyInfoAuthorizeUrlQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMyInfoAuthorizeUrlQuery({
 *   variables: {
 *      applicationId: // value for 'applicationId'
 *      routerId: // value for 'routerId'
 *      endpointId: // value for 'endpointId'
 *      customerKind: // value for 'customerKind'
 *      withTradeIn: // value for 'withTradeIn'
 *      withTestDrive: // value for 'withTestDrive'
 *   },
 * });
 */
export function useGetMyInfoAuthorizeUrlQuery(baseOptions: Apollo.QueryHookOptions<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>(GetMyInfoAuthorizeUrlDocument, options);
      }
export function useGetMyInfoAuthorizeUrlLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>(GetMyInfoAuthorizeUrlDocument, options);
        }
export type GetMyInfoAuthorizeUrlQueryHookResult = ReturnType<typeof useGetMyInfoAuthorizeUrlQuery>;
export type GetMyInfoAuthorizeUrlLazyQueryHookResult = ReturnType<typeof useGetMyInfoAuthorizeUrlLazyQuery>;
export type GetMyInfoAuthorizeUrlQueryResult = Apollo.QueryResult<GetMyInfoAuthorizeUrlQuery, GetMyInfoAuthorizeUrlQueryVariables>;
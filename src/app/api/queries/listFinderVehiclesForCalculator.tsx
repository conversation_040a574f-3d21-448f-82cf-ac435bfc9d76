import type * as SchemaTypes from '../types';

import type { FinderVehicleCalculatorDataFragment } from '../fragments/FinderVehicleCalculatorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { FinderVehicleCalculatorDataFragmentDoc } from '../fragments/FinderVehicleCalculatorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListFinderVehiclesForCalculatorQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.FinderVehicleSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.FilterVehicleFilteringRule>;
  applicationModuleIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListFinderVehiclesForCalculatorQuery = (
  { __typename: 'Query' }
  & { vehicles: (
    { __typename: 'ListFinderVehiclesInfo' }
    & { items: Array<(
      { __typename: 'FinderVehicle' }
      & FinderVehicleCalculatorDataFragment
    )> }
  ) }
);


export const ListFinderVehiclesForCalculatorDocument = /*#__PURE__*/ gql`
    query listFinderVehiclesForCalculator($pagination: Pagination, $sort: FinderVehicleSortingRule, $filter: FilterVehicleFilteringRule, $applicationModuleIds: [ObjectID!], $dealerId: ObjectID) {
  vehicles: listFinderVehicles(
    pagination: $pagination
    sort: $sort
    filter: $filter
    dealerId: $dealerId
    applicationModuleIds: $applicationModuleIds
  ) {
    items {
      ...FinderVehicleCalculatorData
    }
  }
}
    ${FinderVehicleCalculatorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListFinderVehiclesForCalculatorQuery__
 *
 * To run a query within a React component, call `useListFinderVehiclesForCalculatorQuery` and pass it any options that fit your needs.
 * When your component renders, `useListFinderVehiclesForCalculatorQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListFinderVehiclesForCalculatorQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *      applicationModuleIds: // value for 'applicationModuleIds'
 *      dealerId: // value for 'dealerId'
 *   },
 * });
 */
export function useListFinderVehiclesForCalculatorQuery(baseOptions?: Apollo.QueryHookOptions<ListFinderVehiclesForCalculatorQuery, ListFinderVehiclesForCalculatorQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListFinderVehiclesForCalculatorQuery, ListFinderVehiclesForCalculatorQueryVariables>(ListFinderVehiclesForCalculatorDocument, options);
      }
export function useListFinderVehiclesForCalculatorLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListFinderVehiclesForCalculatorQuery, ListFinderVehiclesForCalculatorQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListFinderVehiclesForCalculatorQuery, ListFinderVehiclesForCalculatorQueryVariables>(ListFinderVehiclesForCalculatorDocument, options);
        }
export type ListFinderVehiclesForCalculatorQueryHookResult = ReturnType<typeof useListFinderVehiclesForCalculatorQuery>;
export type ListFinderVehiclesForCalculatorLazyQueryHookResult = ReturnType<typeof useListFinderVehiclesForCalculatorLazyQuery>;
export type ListFinderVehiclesForCalculatorQueryResult = Apollo.QueryResult<ListFinderVehiclesForCalculatorQuery, ListFinderVehiclesForCalculatorQueryVariables>;
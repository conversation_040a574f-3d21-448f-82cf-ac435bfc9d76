import type * as SchemaTypes from '../types';

import type { AdyenPaymentSettingsSpecFragment } from '../fragments/AdyenPaymentSettingsSpec';
import { gql } from '@apollo/client';
import { AdyenPaymentSettingsSpecFragmentDoc } from '../fragments/AdyenPaymentSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListAdyenPaymentSettingsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListAdyenPaymentSettingsQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedAdyenPaymentSettings' }
    & Pick<SchemaTypes.PaginatedAdyenPaymentSettings, 'count'>
    & { items: Array<(
      { __typename: 'AdyenPaymentSetting' }
      & AdyenPaymentSettingsSpecFragment
    )> }
  ) }
);


export const ListAdyenPaymentSettingsDocument = /*#__PURE__*/ gql`
    query listAdyenPaymentSettings($pagination: Pagination, $moduleId: ObjectID!) {
  settings: listAdyenPaymentSettings(pagination: $pagination, moduleId: $moduleId) {
    count
    items {
      ...AdyenPaymentSettingsSpec
    }
  }
}
    ${AdyenPaymentSettingsSpecFragmentDoc}`;

/**
 * __useListAdyenPaymentSettingsQuery__
 *
 * To run a query within a React component, call `useListAdyenPaymentSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListAdyenPaymentSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListAdyenPaymentSettingsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useListAdyenPaymentSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListAdyenPaymentSettingsQuery, ListAdyenPaymentSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListAdyenPaymentSettingsQuery, ListAdyenPaymentSettingsQueryVariables>(ListAdyenPaymentSettingsDocument, options);
      }
export function useListAdyenPaymentSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListAdyenPaymentSettingsQuery, ListAdyenPaymentSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListAdyenPaymentSettingsQuery, ListAdyenPaymentSettingsQueryVariables>(ListAdyenPaymentSettingsDocument, options);
        }
export type ListAdyenPaymentSettingsQueryHookResult = ReturnType<typeof useListAdyenPaymentSettingsQuery>;
export type ListAdyenPaymentSettingsLazyQueryHookResult = ReturnType<typeof useListAdyenPaymentSettingsLazyQuery>;
export type ListAdyenPaymentSettingsQueryResult = Apollo.QueryResult<ListAdyenPaymentSettingsQuery, ListAdyenPaymentSettingsQueryVariables>;
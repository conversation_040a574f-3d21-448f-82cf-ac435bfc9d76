import type * as SchemaTypes from '../types';

import type { RoleListDataFragment } from '../fragments/RoleListData';
import { gql } from '@apollo/client';
import { RoleListDataFragmentDoc } from '../fragments/RoleListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListRolesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.RoleFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.RoleSortingRule>;
}>;


export type ListRolesQuery = (
  { __typename: 'Query' }
  & { page: (
    { __typename: 'PaginatedRoles' }
    & Pick<SchemaTypes.PaginatedRoles, 'count'>
    & { items: Array<(
      { __typename: 'Role' }
      & RoleListDataFragment
    )> }
  ) }
);


export const ListRolesDocument = /*#__PURE__*/ gql`
    query listRoles($pagination: Pagination, $filter: RoleFilteringRule, $sort: RoleSortingRule) {
  page: listRoles(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...RoleListData
    }
  }
}
    ${RoleListDataFragmentDoc}`;

/**
 * __useListRolesQuery__
 *
 * To run a query within a React component, call `useListRolesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListRolesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListRolesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListRolesQuery(baseOptions?: Apollo.QueryHookOptions<ListRolesQuery, ListRolesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListRolesQuery, ListRolesQueryVariables>(ListRolesDocument, options);
      }
export function useListRolesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListRolesQuery, ListRolesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListRolesQuery, ListRolesQueryVariables>(ListRolesDocument, options);
        }
export type ListRolesQueryHookResult = ReturnType<typeof useListRolesQuery>;
export type ListRolesLazyQueryHookResult = ReturnType<typeof useListRolesLazyQuery>;
export type ListRolesQueryResult = Apollo.QueryResult<ListRolesQuery, ListRolesQueryVariables>;
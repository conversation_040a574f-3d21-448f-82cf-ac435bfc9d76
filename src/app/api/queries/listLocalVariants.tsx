import type * as SchemaTypes from '../types';

import type { LocalVariantsListDataFragment } from '../fragments/LocalVariantsListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { LocalVariantsListDataFragmentDoc } from '../fragments/LocalVariantsListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocalVariantsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LocalVariantSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocalVariantFilteringRule>;
}>;


export type ListLocalVariantsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLocalVariants' }
    & Pick<SchemaTypes.PaginatedLocalVariants, 'count'>
    & { items: Array<(
      { __typename: 'LocalVariant' }
      & LocalVariantsListDataFragment
    )> }
  ) }
);


export const ListLocalVariantsDocument = /*#__PURE__*/ gql`
    query listLocalVariants($pagination: Pagination, $sort: LocalVariantSortingRule, $filter: LocalVariantFilteringRule) {
  list: listLocalVariants(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...LocalVariantsListData
    }
  }
}
    ${LocalVariantsListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListLocalVariantsQuery__
 *
 * To run a query within a React component, call `useListLocalVariantsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocalVariantsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocalVariantsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListLocalVariantsQuery(baseOptions?: Apollo.QueryHookOptions<ListLocalVariantsQuery, ListLocalVariantsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocalVariantsQuery, ListLocalVariantsQueryVariables>(ListLocalVariantsDocument, options);
      }
export function useListLocalVariantsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocalVariantsQuery, ListLocalVariantsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocalVariantsQuery, ListLocalVariantsQueryVariables>(ListLocalVariantsDocument, options);
        }
export type ListLocalVariantsQueryHookResult = ReturnType<typeof useListLocalVariantsQuery>;
export type ListLocalVariantsLazyQueryHookResult = ReturnType<typeof useListLocalVariantsLazyQuery>;
export type ListLocalVariantsQueryResult = Apollo.QueryResult<ListLocalVariantsQuery, ListLocalVariantsQueryVariables>;
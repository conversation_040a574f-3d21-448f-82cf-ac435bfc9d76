import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchKycFieldsForEventQueryVariables = SchemaTypes.Exact<{
  eventId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  urlSlug?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
  configuration: SchemaTypes.EventApplicationConfigurationPayload;
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  eventModuleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type PrefetchKycFieldsForEventQuery = (
  { __typename: 'Query' }
  & { corporateKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, applicantKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, guarantorKYC: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )> }
);


export const PrefetchKycFieldsForEventDocument = /*#__PURE__*/ gql`
    query prefetchKYCFieldsForEvent($eventId: ObjectID, $urlSlug: String, $configuration: EventApplicationConfigurationPayload!, $dealerId: ObjectID, $eventModuleId: ObjectID) {
  corporateKYC: prefetchKYCFieldsForEvent(
    eventId: $eventId
    urlSlug: $urlSlug
    configuration: $configuration
    customerKind: "corporate"
    dealerId: $dealerId
    eventModuleId: $eventModuleId
  ) {
    ...KYCFieldSpecs
  }
  applicantKYC: prefetchKYCFieldsForEvent(
    eventId: $eventId
    urlSlug: $urlSlug
    configuration: $configuration
    customerKind: "local"
    dealerId: $dealerId
    eventModuleId: $eventModuleId
  ) {
    ...KYCFieldSpecs
  }
  guarantorKYC: prefetchKYCFieldsForEvent(
    eventId: $eventId
    urlSlug: $urlSlug
    configuration: $configuration
    customerKind: "guarantor"
    dealerId: $dealerId
    eventModuleId: $eventModuleId
  ) {
    ...KYCFieldSpecs
  }
}
    ${KycFieldSpecsFragmentDoc}`;

/**
 * __usePrefetchKycFieldsForEventQuery__
 *
 * To run a query within a React component, call `usePrefetchKycFieldsForEventQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchKycFieldsForEventQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchKycFieldsForEventQuery({
 *   variables: {
 *      eventId: // value for 'eventId'
 *      urlSlug: // value for 'urlSlug'
 *      configuration: // value for 'configuration'
 *      dealerId: // value for 'dealerId'
 *      eventModuleId: // value for 'eventModuleId'
 *   },
 * });
 */
export function usePrefetchKycFieldsForEventQuery(baseOptions: Apollo.QueryHookOptions<PrefetchKycFieldsForEventQuery, PrefetchKycFieldsForEventQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchKycFieldsForEventQuery, PrefetchKycFieldsForEventQueryVariables>(PrefetchKycFieldsForEventDocument, options);
      }
export function usePrefetchKycFieldsForEventLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchKycFieldsForEventQuery, PrefetchKycFieldsForEventQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchKycFieldsForEventQuery, PrefetchKycFieldsForEventQueryVariables>(PrefetchKycFieldsForEventDocument, options);
        }
export type PrefetchKycFieldsForEventQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForEventQuery>;
export type PrefetchKycFieldsForEventLazyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForEventLazyQuery>;
export type PrefetchKycFieldsForEventQueryResult = Apollo.QueryResult<PrefetchKycFieldsForEventQuery, PrefetchKycFieldsForEventQueryVariables>;
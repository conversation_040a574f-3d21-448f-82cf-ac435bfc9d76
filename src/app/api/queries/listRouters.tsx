import type * as SchemaTypes from '../types';

import type { RouterListDataFragment } from '../fragments/RouterListData';
import { gql } from '@apollo/client';
import { RouterListDataFragmentDoc } from '../fragments/RouterListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListRoutersQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.RouterFilteringRule>;
}>;


export type ListRoutersQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedRouters' }
    & Pick<SchemaTypes.PaginatedRouters, 'count'>
    & { items: Array<(
      { __typename: 'Router' }
      & RouterListDataFragment
    )> }
  ) }
);


export const ListRoutersDocument = /*#__PURE__*/ gql`
    query listRouters($pagination: Pagination!, $filter: RouterFilteringRule) {
  list: listRouters(pagination: $pagination, filter: $filter) {
    count
    items {
      ...RouterListData
    }
  }
}
    ${RouterListDataFragmentDoc}`;

/**
 * __useListRoutersQuery__
 *
 * To run a query within a React component, call `useListRoutersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListRoutersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListRoutersQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListRoutersQuery(baseOptions: Apollo.QueryHookOptions<ListRoutersQuery, ListRoutersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListRoutersQuery, ListRoutersQueryVariables>(ListRoutersDocument, options);
      }
export function useListRoutersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListRoutersQuery, ListRoutersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListRoutersQuery, ListRoutersQueryVariables>(ListRoutersDocument, options);
        }
export type ListRoutersQueryHookResult = ReturnType<typeof useListRoutersQuery>;
export type ListRoutersLazyQueryHookResult = ReturnType<typeof useListRoutersLazyQuery>;
export type ListRoutersQueryResult = Apollo.QueryResult<ListRoutersQuery, ListRoutersQueryVariables>;
import type * as SchemaTypes from '../types';

import type { FiservPaymentSettingsSpecFragment } from '../fragments/FiservPaymentSettingsSpec';
import { gql } from '@apollo/client';
import { FiservPaymentSettingsSpecFragmentDoc } from '../fragments/FiservPaymentSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListFiservPaymentSettingsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListFiservPaymentSettingsQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedFiservPaymentSettings' }
    & Pick<SchemaTypes.PaginatedFiservPaymentSettings, 'count'>
    & { items: Array<(
      { __typename: 'FiservPaymentSetting' }
      & FiservPaymentSettingsSpecFragment
    )> }
  ) }
);


export const ListFiservPaymentSettingsDocument = /*#__PURE__*/ gql`
    query listFiservPaymentSettings($pagination: Pagination, $moduleId: ObjectID!) {
  settings: listFiservPaymentSettings(
    pagination: $pagination
    moduleId: $moduleId
  ) {
    count
    items {
      ...FiservPaymentSettingsSpec
    }
  }
}
    ${FiservPaymentSettingsSpecFragmentDoc}`;

/**
 * __useListFiservPaymentSettingsQuery__
 *
 * To run a query within a React component, call `useListFiservPaymentSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListFiservPaymentSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListFiservPaymentSettingsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useListFiservPaymentSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListFiservPaymentSettingsQuery, ListFiservPaymentSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListFiservPaymentSettingsQuery, ListFiservPaymentSettingsQueryVariables>(ListFiservPaymentSettingsDocument, options);
      }
export function useListFiservPaymentSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListFiservPaymentSettingsQuery, ListFiservPaymentSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListFiservPaymentSettingsQuery, ListFiservPaymentSettingsQueryVariables>(ListFiservPaymentSettingsDocument, options);
        }
export type ListFiservPaymentSettingsQueryHookResult = ReturnType<typeof useListFiservPaymentSettingsQuery>;
export type ListFiservPaymentSettingsLazyQueryHookResult = ReturnType<typeof useListFiservPaymentSettingsLazyQuery>;
export type ListFiservPaymentSettingsQueryResult = Apollo.QueryResult<ListFiservPaymentSettingsQuery, ListFiservPaymentSettingsQueryVariables>;
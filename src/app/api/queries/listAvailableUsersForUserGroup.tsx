import type * as SchemaTypes from '../types';

import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListAvailableUsersForUserGroupQueryVariables = SchemaTypes.Exact<{
  userGroupId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListAvailableUsersForUserGroupQuery = (
  { __typename: 'Query' }
  & { users: Array<(
    { __typename: 'User' }
    & UserPreviewDataFragment
  )> }
);


export const ListAvailableUsersForUserGroupDocument = /*#__PURE__*/ gql`
    query listAvailableUsersForUserGroup($userGroupId: ObjectID!) {
  users: listAvailableUsersForUserGroup(userGroupId: $userGroupId) {
    ...UserPreviewData
  }
}
    ${UserPreviewDataFragmentDoc}`;

/**
 * __useListAvailableUsersForUserGroupQuery__
 *
 * To run a query within a React component, call `useListAvailableUsersForUserGroupQuery` and pass it any options that fit your needs.
 * When your component renders, `useListAvailableUsersForUserGroupQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListAvailableUsersForUserGroupQuery({
 *   variables: {
 *      userGroupId: // value for 'userGroupId'
 *   },
 * });
 */
export function useListAvailableUsersForUserGroupQuery(baseOptions: Apollo.QueryHookOptions<ListAvailableUsersForUserGroupQuery, ListAvailableUsersForUserGroupQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListAvailableUsersForUserGroupQuery, ListAvailableUsersForUserGroupQueryVariables>(ListAvailableUsersForUserGroupDocument, options);
      }
export function useListAvailableUsersForUserGroupLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListAvailableUsersForUserGroupQuery, ListAvailableUsersForUserGroupQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListAvailableUsersForUserGroupQuery, ListAvailableUsersForUserGroupQueryVariables>(ListAvailableUsersForUserGroupDocument, options);
        }
export type ListAvailableUsersForUserGroupQueryHookResult = ReturnType<typeof useListAvailableUsersForUserGroupQuery>;
export type ListAvailableUsersForUserGroupLazyQueryHookResult = ReturnType<typeof useListAvailableUsersForUserGroupLazyQuery>;
export type ListAvailableUsersForUserGroupQueryResult = Apollo.QueryResult<ListAvailableUsersForUserGroupQuery, ListAvailableUsersForUserGroupQueryVariables>;
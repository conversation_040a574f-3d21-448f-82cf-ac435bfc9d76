import type * as SchemaTypes from '../types';

import type { AuditTrailDetailsFragment } from '../fragments/AuditTrailDetails';
import { gql } from '@apollo/client';
import { AuditTrailDetailsFragmentDoc } from '../fragments/AuditTrailDetails';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetTradeInAuditTrailsQueryVariables = SchemaTypes.Exact<{
  tradeInId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type GetTradeInAuditTrailsQuery = (
  { __typename: 'Query' }
  & { result: (
    { __typename: 'PaginatedAuditTrails' }
    & Pick<SchemaTypes.PaginatedAuditTrails, 'count'>
    & { items: Array<(
      { __typename: 'AuditTrail' }
      & AuditTrailDetailsFragment
    )> }
  ) }
);


export const GetTradeInAuditTrailsDocument = /*#__PURE__*/ gql`
    query getTradeInAuditTrails($tradeInId: ObjectID!, $pagination: Pagination) {
  result: getTradeInAuditTrails(tradeInId: $tradeInId, pagination: $pagination) {
    count
    items {
      ...AuditTrailDetails
    }
  }
}
    ${AuditTrailDetailsFragmentDoc}`;

/**
 * __useGetTradeInAuditTrailsQuery__
 *
 * To run a query within a React component, call `useGetTradeInAuditTrailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTradeInAuditTrailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTradeInAuditTrailsQuery({
 *   variables: {
 *      tradeInId: // value for 'tradeInId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useGetTradeInAuditTrailsQuery(baseOptions: Apollo.QueryHookOptions<GetTradeInAuditTrailsQuery, GetTradeInAuditTrailsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTradeInAuditTrailsQuery, GetTradeInAuditTrailsQueryVariables>(GetTradeInAuditTrailsDocument, options);
      }
export function useGetTradeInAuditTrailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTradeInAuditTrailsQuery, GetTradeInAuditTrailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTradeInAuditTrailsQuery, GetTradeInAuditTrailsQueryVariables>(GetTradeInAuditTrailsDocument, options);
        }
export type GetTradeInAuditTrailsQueryHookResult = ReturnType<typeof useGetTradeInAuditTrailsQuery>;
export type GetTradeInAuditTrailsLazyQueryHookResult = ReturnType<typeof useGetTradeInAuditTrailsLazyQuery>;
export type GetTradeInAuditTrailsQueryResult = Apollo.QueryResult<GetTradeInAuditTrailsQuery, GetTradeInAuditTrailsQueryVariables>;
import type * as SchemaTypes from '../types';

import type { ModuleWithScenarios_AdyenPaymentModule_Fragment, ModuleWithScenarios_AppointmentModule_Fragment, ModuleWithScenarios_AutoplayModule_Fragment, ModuleWithScenarios_BankModule_Fragment, ModuleWithScenarios_BasicSigningModule_Fragment, ModuleWithScenarios_CapModule_Fragment, ModuleWithScenarios_ConfiguratorModule_Fragment, ModuleWithScenarios_ConsentsAndDeclarationsModule_Fragment, ModuleWithScenarios_CtsModule_Fragment, ModuleWithScenarios_DocusignModule_Fragment, ModuleWithScenarios_EventApplicationModule_Fragment, ModuleWithScenarios_FinderApplicationPrivateModule_Fragment, ModuleWithScenarios_FinderApplicationPublicModule_Fragment, ModuleWithScenarios_FinderVehicleManagementModule_Fragment, ModuleWithScenarios_FiservPaymentModule_Fragment, ModuleWithScenarios_GiftVoucherModule_Fragment, ModuleWithScenarios_InsuranceModule_Fragment, ModuleWithScenarios_LabelsModule_Fragment, ModuleWithScenarios_LaunchPadModule_Fragment, ModuleWithScenarios_LocalCustomerManagementModule_Fragment, ModuleWithScenarios_MaintenanceModule_Fragment, ModuleWithScenarios_MarketingModule_Fragment, ModuleWithScenarios_MobilityModule_Fragment, ModuleWithScenarios_MyInfoModule_Fragment, ModuleWithScenarios_NamirialSigningModule_Fragment, ModuleWithScenarios_OidcModule_Fragment, ModuleWithScenarios_PayGatePaymentModule_Fragment, ModuleWithScenarios_PorscheIdModule_Fragment, ModuleWithScenarios_PorscheMasterDataModule_Fragment, ModuleWithScenarios_PorschePaymentModule_Fragment, ModuleWithScenarios_PorscheRetainModule_Fragment, ModuleWithScenarios_PromoCodeModule_Fragment, ModuleWithScenarios_SalesControlBoardModule_Fragment, ModuleWithScenarios_SalesOfferModule_Fragment, ModuleWithScenarios_SimpleVehicleManagementModule_Fragment, ModuleWithScenarios_StandardApplicationModule_Fragment, ModuleWithScenarios_TradeInModule_Fragment, ModuleWithScenarios_TtbPaymentModule_Fragment, ModuleWithScenarios_UserlikeChatbotModule_Fragment, ModuleWithScenarios_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModuleWithScenarios_VisitAppointmentModule_Fragment, ModuleWithScenarios_WebsiteModule_Fragment, ModuleWithScenarios_WhatsappLiveChatModule_Fragment } from '../fragments/ModuleWithScenarios';
import { gql } from '@apollo/client';
import { ModuleWithScenariosFragmentDoc } from '../fragments/ModuleWithScenarios';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetModulesWithScenariosQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModuleFilteringRule>;
}>;


export type GetModulesWithScenariosQuery = (
  { __typename: 'Query' }
  & { modules: (
    { __typename: 'PaginatedModules' }
    & { items: Array<(
      { __typename: 'AdyenPaymentModule' }
      & ModuleWithScenarios_AdyenPaymentModule_Fragment
    ) | (
      { __typename: 'AppointmentModule' }
      & ModuleWithScenarios_AppointmentModule_Fragment
    ) | (
      { __typename: 'AutoplayModule' }
      & ModuleWithScenarios_AutoplayModule_Fragment
    ) | (
      { __typename: 'BankModule' }
      & ModuleWithScenarios_BankModule_Fragment
    ) | (
      { __typename: 'BasicSigningModule' }
      & ModuleWithScenarios_BasicSigningModule_Fragment
    ) | (
      { __typename: 'CapModule' }
      & ModuleWithScenarios_CapModule_Fragment
    ) | (
      { __typename: 'ConfiguratorModule' }
      & ModuleWithScenarios_ConfiguratorModule_Fragment
    ) | (
      { __typename: 'ConsentsAndDeclarationsModule' }
      & ModuleWithScenarios_ConsentsAndDeclarationsModule_Fragment
    ) | (
      { __typename: 'CtsModule' }
      & ModuleWithScenarios_CtsModule_Fragment
    ) | (
      { __typename: 'DocusignModule' }
      & ModuleWithScenarios_DocusignModule_Fragment
    ) | (
      { __typename: 'EventApplicationModule' }
      & ModuleWithScenarios_EventApplicationModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPrivateModule' }
      & ModuleWithScenarios_FinderApplicationPrivateModule_Fragment
    ) | (
      { __typename: 'FinderApplicationPublicModule' }
      & ModuleWithScenarios_FinderApplicationPublicModule_Fragment
    ) | (
      { __typename: 'FinderVehicleManagementModule' }
      & ModuleWithScenarios_FinderVehicleManagementModule_Fragment
    ) | (
      { __typename: 'FiservPaymentModule' }
      & ModuleWithScenarios_FiservPaymentModule_Fragment
    ) | (
      { __typename: 'GiftVoucherModule' }
      & ModuleWithScenarios_GiftVoucherModule_Fragment
    ) | (
      { __typename: 'InsuranceModule' }
      & ModuleWithScenarios_InsuranceModule_Fragment
    ) | (
      { __typename: 'LabelsModule' }
      & ModuleWithScenarios_LabelsModule_Fragment
    ) | (
      { __typename: 'LaunchPadModule' }
      & ModuleWithScenarios_LaunchPadModule_Fragment
    ) | (
      { __typename: 'LocalCustomerManagementModule' }
      & ModuleWithScenarios_LocalCustomerManagementModule_Fragment
    ) | (
      { __typename: 'MaintenanceModule' }
      & ModuleWithScenarios_MaintenanceModule_Fragment
    ) | (
      { __typename: 'MarketingModule' }
      & ModuleWithScenarios_MarketingModule_Fragment
    ) | (
      { __typename: 'MobilityModule' }
      & ModuleWithScenarios_MobilityModule_Fragment
    ) | (
      { __typename: 'MyInfoModule' }
      & ModuleWithScenarios_MyInfoModule_Fragment
    ) | (
      { __typename: 'NamirialSigningModule' }
      & ModuleWithScenarios_NamirialSigningModule_Fragment
    ) | (
      { __typename: 'OIDCModule' }
      & ModuleWithScenarios_OidcModule_Fragment
    ) | (
      { __typename: 'PayGatePaymentModule' }
      & ModuleWithScenarios_PayGatePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheIdModule' }
      & ModuleWithScenarios_PorscheIdModule_Fragment
    ) | (
      { __typename: 'PorscheMasterDataModule' }
      & ModuleWithScenarios_PorscheMasterDataModule_Fragment
    ) | (
      { __typename: 'PorschePaymentModule' }
      & ModuleWithScenarios_PorschePaymentModule_Fragment
    ) | (
      { __typename: 'PorscheRetainModule' }
      & ModuleWithScenarios_PorscheRetainModule_Fragment
    ) | (
      { __typename: 'PromoCodeModule' }
      & ModuleWithScenarios_PromoCodeModule_Fragment
    ) | (
      { __typename: 'SalesControlBoardModule' }
      & ModuleWithScenarios_SalesControlBoardModule_Fragment
    ) | (
      { __typename: 'SalesOfferModule' }
      & ModuleWithScenarios_SalesOfferModule_Fragment
    ) | (
      { __typename: 'SimpleVehicleManagementModule' }
      & ModuleWithScenarios_SimpleVehicleManagementModule_Fragment
    ) | (
      { __typename: 'StandardApplicationModule' }
      & ModuleWithScenarios_StandardApplicationModule_Fragment
    ) | (
      { __typename: 'TradeInModule' }
      & ModuleWithScenarios_TradeInModule_Fragment
    ) | (
      { __typename: 'TtbPaymentModule' }
      & ModuleWithScenarios_TtbPaymentModule_Fragment
    ) | (
      { __typename: 'UserlikeChatbotModule' }
      & ModuleWithScenarios_UserlikeChatbotModule_Fragment
    ) | (
      { __typename: 'VehicleDataWithPorscheCodeIntegrationModule' }
      & ModuleWithScenarios_VehicleDataWithPorscheCodeIntegrationModule_Fragment
    ) | (
      { __typename: 'VisitAppointmentModule' }
      & ModuleWithScenarios_VisitAppointmentModule_Fragment
    ) | (
      { __typename: 'WebsiteModule' }
      & ModuleWithScenarios_WebsiteModule_Fragment
    ) | (
      { __typename: 'WhatsappLiveChatModule' }
      & ModuleWithScenarios_WhatsappLiveChatModule_Fragment
    )> }
  ) }
);


export const GetModulesWithScenariosDocument = /*#__PURE__*/ gql`
    query getModulesWithScenarios($filter: ModuleFilteringRule) {
  modules: listModules(filter: $filter) {
    items {
      ...ModuleWithScenarios
    }
  }
}
    ${ModuleWithScenariosFragmentDoc}`;

/**
 * __useGetModulesWithScenariosQuery__
 *
 * To run a query within a React component, call `useGetModulesWithScenariosQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetModulesWithScenariosQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetModulesWithScenariosQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetModulesWithScenariosQuery(baseOptions?: Apollo.QueryHookOptions<GetModulesWithScenariosQuery, GetModulesWithScenariosQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetModulesWithScenariosQuery, GetModulesWithScenariosQueryVariables>(GetModulesWithScenariosDocument, options);
      }
export function useGetModulesWithScenariosLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetModulesWithScenariosQuery, GetModulesWithScenariosQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetModulesWithScenariosQuery, GetModulesWithScenariosQueryVariables>(GetModulesWithScenariosDocument, options);
        }
export type GetModulesWithScenariosQueryHookResult = ReturnType<typeof useGetModulesWithScenariosQuery>;
export type GetModulesWithScenariosLazyQueryHookResult = ReturnType<typeof useGetModulesWithScenariosLazyQuery>;
export type GetModulesWithScenariosQueryResult = Apollo.QueryResult<GetModulesWithScenariosQuery, GetModulesWithScenariosQueryVariables>;
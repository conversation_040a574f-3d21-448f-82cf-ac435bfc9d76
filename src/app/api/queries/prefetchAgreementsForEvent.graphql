query prefetchAgreementsForEvent(
    $eventId: ObjectID
    $urlSlug: String
    $configuration: EventApplicationConfigurationPayload!
    $dealerId: ObjectID
    $eventModuleId: ObjectID
) {
    applicantAgreements: prefetchAgreementsForEvent(
        eventId: $eventId
        urlSlug: $urlSlug
        configuration: $configuration
        customerKind: "local"
        dealerId: $dealerId
        eventModuleId: $eventModuleId
    ) {
        ...ApplicationAgreementData
    }

    corporateAgreements: prefetchAgreementsForEvent(
        eventId: $eventId
        urlSlug: $urlSlug
        configuration: $configuration
        customerKind: "corporate"
        dealerId: $dealerId
        eventModuleId: $eventModuleId
    ) {
        ...ApplicationAgreementData
    }
}

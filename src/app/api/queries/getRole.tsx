import type * as SchemaTypes from '../types';

import type { RoleSpecsFragment } from '../fragments/RoleSpecs';
import type { UserAvatarSpecsFragment } from '../fragments/UserAvatarSpecs';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { RoleSpecsFragmentDoc } from '../fragments/RoleSpecs';
import { UserAvatarSpecsFragmentDoc } from '../fragments/UserAvatarSpecs';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetRoleQueryVariables = SchemaTypes.Exact<{
  roleId: SchemaTypes.Scalars['ObjectID']['input'];
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type GetRoleQuery = (
  { __typename: 'Query' }
  & { role?: SchemaTypes.Maybe<(
    { __typename: 'Role' }
    & RoleSpecsFragment
  )> }
);


export const GetRoleDocument = /*#__PURE__*/ gql`
    query getRole($roleId: ObjectID!, $companyId: ObjectID, $dealerIds: [ObjectID!]) {
  role: getRole(id: $roleId, companyId: $companyId, dealerIds: $dealerIds) {
    ...RoleSpecs
  }
}
    ${RoleSpecsFragmentDoc}
${UserAvatarSpecsFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useGetRoleQuery__
 *
 * To run a query within a React component, call `useGetRoleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRoleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRoleQuery({
 *   variables: {
 *      roleId: // value for 'roleId'
 *      companyId: // value for 'companyId'
 *      dealerIds: // value for 'dealerIds'
 *   },
 * });
 */
export function useGetRoleQuery(baseOptions: Apollo.QueryHookOptions<GetRoleQuery, GetRoleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRoleQuery, GetRoleQueryVariables>(GetRoleDocument, options);
      }
export function useGetRoleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRoleQuery, GetRoleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRoleQuery, GetRoleQueryVariables>(GetRoleDocument, options);
        }
export type GetRoleQueryHookResult = ReturnType<typeof useGetRoleQuery>;
export type GetRoleLazyQueryHookResult = ReturnType<typeof useGetRoleLazyQuery>;
export type GetRoleQueryResult = Apollo.QueryResult<GetRoleQuery, GetRoleQueryVariables>;
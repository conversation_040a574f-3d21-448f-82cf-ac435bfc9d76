import type * as SchemaTypes from '../types';

import type { WebPageDataFragment } from '../fragments/WebPageData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { WebsiteModulePublicSpecsFragment } from '../fragments/WebsiteModulePublicSpecs';
import type { CompanyWebpagePublicSpecFragment } from '../fragments/CompanyWebpagePublicSpec';
import type { EdmEmailFooterPublicDataFragment } from '../fragments/EdmEmailFooterPublicData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { EdmSocialMediaDataFragment } from '../fragments/EdmSocialMediaData';
import type { WebPageBlockData_ColumnWebPageBlock_Fragment, WebPageBlockData_CustomWebPageBlock_Fragment, WebPageBlockData_ImageWebPageBlock_Fragment, WebPageBlockData_TextCarouselWebPageBlock_Fragment, WebPageBlockData_TextImageWebPageBlock_Fragment } from '../fragments/WebPageBlockData';
import type { ColumnWebPageBlockDataFragment } from '../fragments/ColumnWebPageBlockData';
import type { ColumnBlockDataFragment } from '../fragments/ColumnBlockData';
import type { TextImageWebPageBlockDataFragment } from '../fragments/TextImageWebPageBlockData';
import type { WebPageButtonDataFragment } from '../fragments/WebPageButtonData';
import type { ImageWebPageBlockDataFragment } from '../fragments/ImageWebPageBlockData';
import type { ImageDescriptionWebPageBlockDataFragment } from '../fragments/ImageDescriptionWebPageBlockData';
import type { CustomWebPageBlockDataFragment } from '../fragments/CustomWebPageBlockData';
import type { TextCarouselWebPageBlockDataFragment } from '../fragments/TextCarouselWebPageBlockData';
import { gql } from '@apollo/client';
import { WebPageDataFragmentDoc } from '../fragments/WebPageData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { WebsiteModulePublicSpecsFragmentDoc } from '../fragments/WebsiteModulePublicSpecs';
import { CompanyWebpagePublicSpecFragmentDoc } from '../fragments/CompanyWebpagePublicSpec';
import { EdmEmailFooterPublicDataFragmentDoc } from '../fragments/EdmEmailFooterPublicData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { EdmSocialMediaDataFragmentDoc } from '../fragments/EdmSocialMediaData';
import { WebPageBlockDataFragmentDoc } from '../fragments/WebPageBlockData';
import { ColumnWebPageBlockDataFragmentDoc } from '../fragments/ColumnWebPageBlockData';
import { ColumnBlockDataFragmentDoc } from '../fragments/ColumnBlockData';
import { TextImageWebPageBlockDataFragmentDoc } from '../fragments/TextImageWebPageBlockData';
import { WebPageButtonDataFragmentDoc } from '../fragments/WebPageButtonData';
import { ImageWebPageBlockDataFragmentDoc } from '../fragments/ImageWebPageBlockData';
import { ImageDescriptionWebPageBlockDataFragmentDoc } from '../fragments/ImageDescriptionWebPageBlockData';
import { CustomWebPageBlockDataFragmentDoc } from '../fragments/CustomWebPageBlockData';
import { TextCarouselWebPageBlockDataFragmentDoc } from '../fragments/TextCarouselWebPageBlockData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetWebPageQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
  productionOnly?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['Boolean']['input']>;
}>;


export type GetWebPageQuery = (
  { __typename: 'Query' }
  & { webpage: (
    { __typename: 'WebPage' }
    & WebPageDataFragment
  ) }
);


export const GetWebPageDocument = /*#__PURE__*/ gql`
    query getWebPage($id: ObjectID!, $productionOnly: Boolean) {
  webpage: getWebPage(id: $id, productionOnly: $productionOnly) {
    ...WebPageData
  }
}
    ${WebPageDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${WebsiteModulePublicSpecsFragmentDoc}
${CompanyWebpagePublicSpecFragmentDoc}
${EdmEmailFooterPublicDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${EdmSocialMediaDataFragmentDoc}
${WebPageBlockDataFragmentDoc}
${ColumnWebPageBlockDataFragmentDoc}
${ColumnBlockDataFragmentDoc}
${TextImageWebPageBlockDataFragmentDoc}
${WebPageButtonDataFragmentDoc}
${ImageWebPageBlockDataFragmentDoc}
${ImageDescriptionWebPageBlockDataFragmentDoc}
${CustomWebPageBlockDataFragmentDoc}
${TextCarouselWebPageBlockDataFragmentDoc}`;

/**
 * __useGetWebPageQuery__
 *
 * To run a query within a React component, call `useGetWebPageQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetWebPageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetWebPageQuery({
 *   variables: {
 *      id: // value for 'id'
 *      productionOnly: // value for 'productionOnly'
 *   },
 * });
 */
export function useGetWebPageQuery(baseOptions: Apollo.QueryHookOptions<GetWebPageQuery, GetWebPageQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetWebPageQuery, GetWebPageQueryVariables>(GetWebPageDocument, options);
      }
export function useGetWebPageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetWebPageQuery, GetWebPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetWebPageQuery, GetWebPageQueryVariables>(GetWebPageDocument, options);
        }
export type GetWebPageQueryHookResult = ReturnType<typeof useGetWebPageQuery>;
export type GetWebPageLazyQueryHookResult = ReturnType<typeof useGetWebPageLazyQuery>;
export type GetWebPageQueryResult = Apollo.QueryResult<GetWebPageQuery, GetWebPageQueryVariables>;
import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchKycFieldsForContactQueryVariables = SchemaTypes.Exact<{
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type PrefetchKycFieldsForContactQuery = (
  { __typename: 'Query' }
  & { applicantKyc: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )> }
);


export const PrefetchKycFieldsForContactDocument = /*#__PURE__*/ gql`
    query prefetchKYCFieldsForContact($applicationModuleId: ObjectID!) {
  applicantKyc: prefetchKYCFieldsForContact(
    applicationModuleId: $applicationModuleId
  ) {
    ...KYCFieldSpecs
  }
}
    ${KycFieldSpecsFragmentDoc}`;

/**
 * __usePrefetchKycFieldsForContactQuery__
 *
 * To run a query within a React component, call `usePrefetchKycFieldsForContactQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchKycFieldsForContactQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchKycFieldsForContactQuery({
 *   variables: {
 *      applicationModuleId: // value for 'applicationModuleId'
 *   },
 * });
 */
export function usePrefetchKycFieldsForContactQuery(baseOptions: Apollo.QueryHookOptions<PrefetchKycFieldsForContactQuery, PrefetchKycFieldsForContactQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchKycFieldsForContactQuery, PrefetchKycFieldsForContactQueryVariables>(PrefetchKycFieldsForContactDocument, options);
      }
export function usePrefetchKycFieldsForContactLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchKycFieldsForContactQuery, PrefetchKycFieldsForContactQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchKycFieldsForContactQuery, PrefetchKycFieldsForContactQueryVariables>(PrefetchKycFieldsForContactDocument, options);
        }
export type PrefetchKycFieldsForContactQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForContactQuery>;
export type PrefetchKycFieldsForContactLazyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForContactLazyQuery>;
export type PrefetchKycFieldsForContactQueryResult = Apollo.QueryResult<PrefetchKycFieldsForContactQuery, PrefetchKycFieldsForContactQueryVariables>;
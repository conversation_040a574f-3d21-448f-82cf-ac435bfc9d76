import type * as SchemaTypes from '../types';

import type { NearbyDealersDataFragment } from '../fragments/NearbyDealersData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import { gql } from '@apollo/client';
import { NearbyDealersDataFragmentDoc } from '../fragments/NearbyDealersData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetNearbyDealersQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.NearbyDealerFilteringRule>;
}>;


export type GetNearbyDealersQuery = (
  { __typename: 'Query' }
  & { getNearbyDealers?: SchemaTypes.Maybe<Array<SchemaTypes.Maybe<(
    { __typename: 'Dealer' }
    & NearbyDealersDataFragment
  )>>> }
);


export const GetNearbyDealersDocument = /*#__PURE__*/ gql`
    query getNearbyDealers($filter: NearbyDealerFilteringRule) {
  getNearbyDealers(filter: $filter) {
    ...NearbyDealersData
  }
}
    ${NearbyDealersDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}`;

/**
 * __useGetNearbyDealersQuery__
 *
 * To run a query within a React component, call `useGetNearbyDealersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetNearbyDealersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetNearbyDealersQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetNearbyDealersQuery(baseOptions?: Apollo.QueryHookOptions<GetNearbyDealersQuery, GetNearbyDealersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetNearbyDealersQuery, GetNearbyDealersQueryVariables>(GetNearbyDealersDocument, options);
      }
export function useGetNearbyDealersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetNearbyDealersQuery, GetNearbyDealersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetNearbyDealersQuery, GetNearbyDealersQueryVariables>(GetNearbyDealersDocument, options);
        }
export type GetNearbyDealersQueryHookResult = ReturnType<typeof useGetNearbyDealersQuery>;
export type GetNearbyDealersLazyQueryHookResult = ReturnType<typeof useGetNearbyDealersLazyQuery>;
export type GetNearbyDealersQueryResult = Apollo.QueryResult<GetNearbyDealersQuery, GetNearbyDealersQueryVariables>;
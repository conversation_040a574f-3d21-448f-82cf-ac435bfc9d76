import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListInventoriesFilterQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.InventoryFilteringRule>;
}>;


export type ListInventoriesFilterQuery = (
  { __typename: 'Query' }
  & { listInventoriesFilter: Array<(
    { __typename: 'ConfiguratorInventory' }
    & { module: (
      { __typename: 'ConfiguratorModule' }
      & Pick<SchemaTypes.ConfiguratorModule, 'id' | 'displayName'>
    ), colorSetting: (
      { __typename: 'ColorAndTrimSettings' }
      & Pick<SchemaTypes.ColorAndTrimSettings, 'id' | 'code'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ), trimSetting: (
      { __typename: 'ColorAndTrimSettings' }
      & Pick<SchemaTypes.ColorAndTrimSettings, 'id' | 'code'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ), packageSetting?: SchemaTypes.Maybe<(
      { __typename: 'PackageSettings' }
      & Pick<SchemaTypes.PackageSettings, 'id'>
      & { packageName: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    )>, variant: (
      { __typename: 'LocalVariant' }
      & Pick<SchemaTypes.LocalVariant, 'identifier'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ), model: (
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'identifier'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ), subModel?: SchemaTypes.Maybe<(
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'identifier'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    )> }
  ) | (
    { __typename: 'MobilityInventory' }
    & { module: (
      { __typename: 'MobilityModule' }
      & Pick<SchemaTypes.MobilityModule, 'id' | 'displayName'>
    ), variant: (
      { __typename: 'LocalVariant' }
      & Pick<SchemaTypes.LocalVariant, 'identifier'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ), model: (
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'identifier'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    ), subModel?: SchemaTypes.Maybe<(
      { __typename: 'LocalModel' }
      & Pick<SchemaTypes.LocalModel, 'identifier'>
      & { name: (
        { __typename: 'TranslatedString' }
        & TranslatedStringDataFragment
      ) }
    )> }
  )> }
);


export const ListInventoriesFilterDocument = /*#__PURE__*/ gql`
    query listInventoriesFilter($filter: InventoryFilteringRule) {
  listInventoriesFilter(filter: $filter) {
    variant {
      identifier
      name {
        ...TranslatedStringData
      }
    }
    model {
      identifier
      name {
        ...TranslatedStringData
      }
    }
    subModel {
      identifier
      name {
        ...TranslatedStringData
      }
    }
    ... on ConfiguratorInventory {
      module {
        id
        displayName
      }
      colorSetting {
        id
        name {
          ...TranslatedStringData
        }
        code
      }
      trimSetting {
        id
        name {
          ...TranslatedStringData
        }
        code
      }
      packageSetting {
        id
        packageName {
          ...TranslatedStringData
        }
      }
    }
    ... on MobilityInventory {
      module {
        id
        displayName
      }
    }
  }
}
    ${TranslatedStringDataFragmentDoc}`;

/**
 * __useListInventoriesFilterQuery__
 *
 * To run a query within a React component, call `useListInventoriesFilterQuery` and pass it any options that fit your needs.
 * When your component renders, `useListInventoriesFilterQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListInventoriesFilterQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListInventoriesFilterQuery(baseOptions?: Apollo.QueryHookOptions<ListInventoriesFilterQuery, ListInventoriesFilterQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListInventoriesFilterQuery, ListInventoriesFilterQueryVariables>(ListInventoriesFilterDocument, options);
      }
export function useListInventoriesFilterLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListInventoriesFilterQuery, ListInventoriesFilterQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListInventoriesFilterQuery, ListInventoriesFilterQueryVariables>(ListInventoriesFilterDocument, options);
        }
export type ListInventoriesFilterQueryHookResult = ReturnType<typeof useListInventoriesFilterQuery>;
export type ListInventoriesFilterLazyQueryHookResult = ReturnType<typeof useListInventoriesFilterLazyQuery>;
export type ListInventoriesFilterQueryResult = Apollo.QueryResult<ListInventoriesFilterQuery, ListInventoriesFilterQueryVariables>;
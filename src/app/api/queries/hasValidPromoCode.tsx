import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type HasValidPromoCodeQueryVariables = SchemaTypes.Exact<{
  moduleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  variantId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type HasValidPromoCodeQuery = (
  { __typename: 'Query' }
  & Pick<SchemaTypes.Query, 'hasValidPromoCode'>
);


export const HasValidPromoCodeDocument = /*#__PURE__*/ gql`
    query hasValidPromoCode($moduleId: ObjectID, $dealerId: ObjectID, $variantId: ObjectID, $applicationModuleId: ObjectID!) {
  hasValidPromoCode(
    moduleId: $moduleId
    dealerId: $dealerId
    variantId: $variantId
    applicationModuleId: $applicationModuleId
  )
}
    `;

/**
 * __useHasValidPromoCodeQuery__
 *
 * To run a query within a React component, call `useHasValidPromoCodeQuery` and pass it any options that fit your needs.
 * When your component renders, `useHasValidPromoCodeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useHasValidPromoCodeQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      dealerId: // value for 'dealerId'
 *      variantId: // value for 'variantId'
 *      applicationModuleId: // value for 'applicationModuleId'
 *   },
 * });
 */
export function useHasValidPromoCodeQuery(baseOptions: Apollo.QueryHookOptions<HasValidPromoCodeQuery, HasValidPromoCodeQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<HasValidPromoCodeQuery, HasValidPromoCodeQueryVariables>(HasValidPromoCodeDocument, options);
      }
export function useHasValidPromoCodeLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<HasValidPromoCodeQuery, HasValidPromoCodeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<HasValidPromoCodeQuery, HasValidPromoCodeQueryVariables>(HasValidPromoCodeDocument, options);
        }
export type HasValidPromoCodeQueryHookResult = ReturnType<typeof useHasValidPromoCodeQuery>;
export type HasValidPromoCodeLazyQueryHookResult = ReturnType<typeof useHasValidPromoCodeLazyQuery>;
export type HasValidPromoCodeQueryResult = Apollo.QueryResult<HasValidPromoCodeQuery, HasValidPromoCodeQueryVariables>;
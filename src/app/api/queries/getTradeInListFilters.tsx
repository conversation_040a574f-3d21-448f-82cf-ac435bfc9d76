import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetTradeInListFiltersQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.TradeInFilteringRule>;
}>;


export type GetTradeInListFiltersQuery = (
  { __typename: 'Query' }
  & { getTradeInListFilters: (
    { __typename: 'TradeInListFilter' }
    & Pick<SchemaTypes.TradeInListFilter, 'statuses'>
  ) }
);


export const GetTradeInListFiltersDocument = /*#__PURE__*/ gql`
    query getTradeInListFilters($filter: TradeInFilteringRule) {
  getTradeInListFilters: getTradeInListFilters(filter: $filter) {
    statuses
  }
}
    `;

/**
 * __useGetTradeInListFiltersQuery__
 *
 * To run a query within a React component, call `useGetTradeInListFiltersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTradeInListFiltersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTradeInListFiltersQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetTradeInListFiltersQuery(baseOptions?: Apollo.QueryHookOptions<GetTradeInListFiltersQuery, GetTradeInListFiltersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTradeInListFiltersQuery, GetTradeInListFiltersQueryVariables>(GetTradeInListFiltersDocument, options);
      }
export function useGetTradeInListFiltersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTradeInListFiltersQuery, GetTradeInListFiltersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTradeInListFiltersQuery, GetTradeInListFiltersQueryVariables>(GetTradeInListFiltersDocument, options);
        }
export type GetTradeInListFiltersQueryHookResult = ReturnType<typeof useGetTradeInListFiltersQuery>;
export type GetTradeInListFiltersLazyQueryHookResult = ReturnType<typeof useGetTradeInListFiltersLazyQuery>;
export type GetTradeInListFiltersQueryResult = Apollo.QueryResult<GetTradeInListFiltersQuery, GetTradeInListFiltersQueryVariables>;
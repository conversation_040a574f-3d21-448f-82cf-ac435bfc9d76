import type * as SchemaTypes from '../types';

import type { MobilityStockPublicList_ConfiguratorStockInventory_Fragment, MobilityStockPublicList_MobilityStockInventory_Fragment } from '../fragments/MobilityStockPublicList';
import type { StockBlockingPeriodDataFragment } from '../fragments/StockBlockingPeriod';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import { gql } from '@apollo/client';
import { MobilityStockPublicListFragmentDoc } from '../fragments/MobilityStockPublicList';
import { StockBlockingPeriodDataFragmentDoc } from '../fragments/StockBlockingPeriod';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListMobilityStocksQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.StockSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.StockFilteringRule>;
}>;


export type ListMobilityStocksQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedStockInventory' }
    & Pick<SchemaTypes.PaginatedStockInventory, 'count'>
    & { items: Array<(
      { __typename: 'ConfiguratorStockInventory' }
      & MobilityStockPublicList_ConfiguratorStockInventory_Fragment
    ) | (
      { __typename: 'MobilityStockInventory' }
      & MobilityStockPublicList_MobilityStockInventory_Fragment
    )> }
  ) }
);


export const ListMobilityStocksDocument = /*#__PURE__*/ gql`
    query listMobilityStocks($pagination: Pagination, $sort: StockSortingRule, $filter: StockFilteringRule) {
  list: listStockInventories(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    count
    items {
      ...MobilityStockPublicList
    }
  }
}
    ${MobilityStockPublicListFragmentDoc}
${StockBlockingPeriodDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${PeriodDataFragmentDoc}`;

/**
 * __useListMobilityStocksQuery__
 *
 * To run a query within a React component, call `useListMobilityStocksQuery` and pass it any options that fit your needs.
 * When your component renders, `useListMobilityStocksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListMobilityStocksQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListMobilityStocksQuery(baseOptions?: Apollo.QueryHookOptions<ListMobilityStocksQuery, ListMobilityStocksQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListMobilityStocksQuery, ListMobilityStocksQueryVariables>(ListMobilityStocksDocument, options);
      }
export function useListMobilityStocksLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListMobilityStocksQuery, ListMobilityStocksQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListMobilityStocksQuery, ListMobilityStocksQueryVariables>(ListMobilityStocksDocument, options);
        }
export type ListMobilityStocksQueryHookResult = ReturnType<typeof useListMobilityStocksQuery>;
export type ListMobilityStocksLazyQueryHookResult = ReturnType<typeof useListMobilityStocksLazyQuery>;
export type ListMobilityStocksQueryResult = Apollo.QueryResult<ListMobilityStocksQuery, ListMobilityStocksQueryVariables>;
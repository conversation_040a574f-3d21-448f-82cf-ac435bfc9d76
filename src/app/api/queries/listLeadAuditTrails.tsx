import type * as SchemaTypes from '../types';

import type { AuditTrailDetailsFragment } from '../fragments/AuditTrailDetails';
import { gql } from '@apollo/client';
import { AuditTrailDetailsFragmentDoc } from '../fragments/AuditTrailDetails';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLeadAuditTrailsQueryVariables = SchemaTypes.Exact<{
  leadId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type ListLeadAuditTrailsQuery = (
  { __typename: 'Query' }
  & { result: (
    { __typename: 'PaginatedAuditTrails' }
    & Pick<SchemaTypes.PaginatedAuditTrails, 'count'>
    & { items: Array<(
      { __typename: 'AuditTrail' }
      & AuditTrailDetailsFragment
    )> }
  ) }
);


export const ListLeadAuditTrailsDocument = /*#__PURE__*/ gql`
    query listLeadAuditTrails($leadId: ObjectID!, $pagination: Pagination) {
  result: listLeadAuditTrails(leadId: $leadId, pagination: $pagination) {
    count
    items {
      ...AuditTrailDetails
    }
  }
}
    ${AuditTrailDetailsFragmentDoc}`;

/**
 * __useListLeadAuditTrailsQuery__
 *
 * To run a query within a React component, call `useListLeadAuditTrailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLeadAuditTrailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLeadAuditTrailsQuery({
 *   variables: {
 *      leadId: // value for 'leadId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useListLeadAuditTrailsQuery(baseOptions: Apollo.QueryHookOptions<ListLeadAuditTrailsQuery, ListLeadAuditTrailsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLeadAuditTrailsQuery, ListLeadAuditTrailsQueryVariables>(ListLeadAuditTrailsDocument, options);
      }
export function useListLeadAuditTrailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLeadAuditTrailsQuery, ListLeadAuditTrailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLeadAuditTrailsQuery, ListLeadAuditTrailsQueryVariables>(ListLeadAuditTrailsDocument, options);
        }
export type ListLeadAuditTrailsQueryHookResult = ReturnType<typeof useListLeadAuditTrailsQuery>;
export type ListLeadAuditTrailsLazyQueryHookResult = ReturnType<typeof useListLeadAuditTrailsLazyQuery>;
export type ListLeadAuditTrailsQueryResult = Apollo.QueryResult<ListLeadAuditTrailsQuery, ListLeadAuditTrailsQueryVariables>;
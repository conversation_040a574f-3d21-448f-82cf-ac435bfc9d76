import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListBankOptionsQueryVariables = SchemaTypes.Exact<{
  sort?: SchemaTypes.InputMaybe<SchemaTypes.BankSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.BankOptionFilteringRule>;
}>;


export type ListBankOptionsQuery = (
  { __typename: 'Query' }
  & { list: Array<(
    { __typename: 'BankOption' }
    & Pick<SchemaTypes.BankOption, 'id' | 'displayName' | 'allowVariantCodeMapping'>
  )> }
);


export const ListBankOptionsDocument = /*#__PURE__*/ gql`
    query listBankOptions($sort: BankSortingRule, $filter: BankOptionFilteringRule) {
  list: listBankOptions(sort: $sort, filter: $filter) {
    id
    displayName
    allowVariantCodeMapping
  }
}
    `;

/**
 * __useListBankOptionsQuery__
 *
 * To run a query within a React component, call `useListBankOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListBankOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListBankOptionsQuery({
 *   variables: {
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListBankOptionsQuery(baseOptions?: Apollo.QueryHookOptions<ListBankOptionsQuery, ListBankOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListBankOptionsQuery, ListBankOptionsQueryVariables>(ListBankOptionsDocument, options);
      }
export function useListBankOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListBankOptionsQuery, ListBankOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListBankOptionsQuery, ListBankOptionsQueryVariables>(ListBankOptionsDocument, options);
        }
export type ListBankOptionsQueryHookResult = ReturnType<typeof useListBankOptionsQuery>;
export type ListBankOptionsLazyQueryHookResult = ReturnType<typeof useListBankOptionsLazyQuery>;
export type ListBankOptionsQueryResult = Apollo.QueryResult<ListBankOptionsQuery, ListBankOptionsQueryVariables>;
import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListEventFiltersQueryVariables = SchemaTypes.Exact<{
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  moduleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  dealerIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
  productionOnly: SchemaTypes.Scalars['Boolean']['input'];
}>;


export type ListEventFiltersQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'EventFilters' }
    & Pick<SchemaTypes.EventFilters, 'mediums' | 'privateAccesses' | 'scenarios' | 'moduleCount'>
  ) }
);


export const ListEventFiltersDocument = /*#__PURE__*/ gql`
    query listEventFilters($companyId: ObjectID, $moduleId: ObjectID, $dealerIds: [ObjectID!], $productionOnly: Boolean!) {
  list: listEventFilters(
    companyId: $companyId
    moduleId: $moduleId
    dealerIds: $dealerIds
    productionOnly: $productionOnly
  ) {
    mediums
    privateAccesses
    scenarios
    moduleCount
  }
}
    `;

/**
 * __useListEventFiltersQuery__
 *
 * To run a query within a React component, call `useListEventFiltersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListEventFiltersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListEventFiltersQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *      moduleId: // value for 'moduleId'
 *      dealerIds: // value for 'dealerIds'
 *      productionOnly: // value for 'productionOnly'
 *   },
 * });
 */
export function useListEventFiltersQuery(baseOptions: Apollo.QueryHookOptions<ListEventFiltersQuery, ListEventFiltersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListEventFiltersQuery, ListEventFiltersQueryVariables>(ListEventFiltersDocument, options);
      }
export function useListEventFiltersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListEventFiltersQuery, ListEventFiltersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListEventFiltersQuery, ListEventFiltersQueryVariables>(ListEventFiltersDocument, options);
        }
export type ListEventFiltersQueryHookResult = ReturnType<typeof useListEventFiltersQuery>;
export type ListEventFiltersLazyQueryHookResult = ReturnType<typeof useListEventFiltersLazyQuery>;
export type ListEventFiltersQueryResult = Apollo.QueryResult<ListEventFiltersQuery, ListEventFiltersQueryVariables>;
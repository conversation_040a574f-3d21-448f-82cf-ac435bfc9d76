import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetPorschePaymentMethodsQueryVariables = SchemaTypes.Exact<{
  token: SchemaTypes.Scalars['String']['input'];
}>;


export type GetPorschePaymentMethodsQuery = (
  { __typename: 'Query' }
  & { result: Array<(
    { __typename: 'PorschePaymentMethods' }
    & Pick<SchemaTypes.PorschePaymentMethods, 'method'>
    & { types: Array<(
      { __typename: 'PorschePaymentType' }
      & Pick<SchemaTypes.PorschePaymentType, 'displayName' | 'type'>
    )> }
  )> }
);


export const GetPorschePaymentMethodsDocument = /*#__PURE__*/ gql`
    query getPorschePaymentMethods($token: String!) {
  result: getPorschePaymentMethods(token: $token) {
    method
    types {
      displayName
      type
    }
  }
}
    `;

/**
 * __useGetPorschePaymentMethodsQuery__
 *
 * To run a query within a React component, call `useGetPorschePaymentMethodsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPorschePaymentMethodsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPorschePaymentMethodsQuery({
 *   variables: {
 *      token: // value for 'token'
 *   },
 * });
 */
export function useGetPorschePaymentMethodsQuery(baseOptions: Apollo.QueryHookOptions<GetPorschePaymentMethodsQuery, GetPorschePaymentMethodsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPorschePaymentMethodsQuery, GetPorschePaymentMethodsQueryVariables>(GetPorschePaymentMethodsDocument, options);
      }
export function useGetPorschePaymentMethodsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPorschePaymentMethodsQuery, GetPorschePaymentMethodsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPorschePaymentMethodsQuery, GetPorschePaymentMethodsQueryVariables>(GetPorschePaymentMethodsDocument, options);
        }
export type GetPorschePaymentMethodsQueryHookResult = ReturnType<typeof useGetPorschePaymentMethodsQuery>;
export type GetPorschePaymentMethodsLazyQueryHookResult = ReturnType<typeof useGetPorschePaymentMethodsLazyQuery>;
export type GetPorschePaymentMethodsQueryResult = Apollo.QueryResult<GetPorschePaymentMethodsQuery, GetPorschePaymentMethodsQueryVariables>;
import type * as SchemaTypes from '../types';

import type { StockPublicData_ConfiguratorStockInventory_Fragment, StockPublicData_MobilityStockInventory_Fragment } from '../fragments/StockPublicData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { StockBlockingPeriodDataFragment } from '../fragments/StockBlockingPeriod';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { CompanyPublicSpecsFragment } from '../fragments/CompanyPublicSpecs';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import { gql } from '@apollo/client';
import { StockPublicDataFragmentDoc } from '../fragments/StockPublicData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { StockBlockingPeriodDataFragmentDoc } from '../fragments/StockBlockingPeriod';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { CompanyPublicSpecsFragmentDoc } from '../fragments/CompanyPublicSpecs';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetPublicStockQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
  omitApplicationId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  token?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
}>;


export type GetPublicStockQuery = (
  { __typename: 'Query' }
  & { stock?: SchemaTypes.Maybe<(
    { __typename: 'ConfiguratorStockInventory' }
    & StockPublicData_ConfiguratorStockInventory_Fragment
  ) | (
    { __typename: 'MobilityStockInventory' }
    & StockPublicData_MobilityStockInventory_Fragment
  )> }
);


export const GetPublicStockDocument = /*#__PURE__*/ gql`
    query getPublicStock($id: ObjectID!, $omitApplicationId: ObjectID, $token: String) {
  stock: getStock(
    id: $id
    productionOnly: true
    omitApplicationId: $omitApplicationId
    token: $token
  ) {
    ...StockPublicData
  }
}
    ${StockPublicDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${StockBlockingPeriodDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${CompanyPublicSpecsFragmentDoc}
${PeriodDataFragmentDoc}`;

/**
 * __useGetPublicStockQuery__
 *
 * To run a query within a React component, call `useGetPublicStockQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPublicStockQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPublicStockQuery({
 *   variables: {
 *      id: // value for 'id'
 *      omitApplicationId: // value for 'omitApplicationId'
 *      token: // value for 'token'
 *   },
 * });
 */
export function useGetPublicStockQuery(baseOptions: Apollo.QueryHookOptions<GetPublicStockQuery, GetPublicStockQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPublicStockQuery, GetPublicStockQueryVariables>(GetPublicStockDocument, options);
      }
export function useGetPublicStockLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPublicStockQuery, GetPublicStockQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPublicStockQuery, GetPublicStockQueryVariables>(GetPublicStockDocument, options);
        }
export type GetPublicStockQueryHookResult = ReturnType<typeof useGetPublicStockQuery>;
export type GetPublicStockLazyQueryHookResult = ReturnType<typeof useGetPublicStockLazyQuery>;
export type GetPublicStockQueryResult = Apollo.QueryResult<GetPublicStockQuery, GetPublicStockQueryVariables>;
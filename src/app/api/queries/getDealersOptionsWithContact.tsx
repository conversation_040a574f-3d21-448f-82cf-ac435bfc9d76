import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetDealersOptionsWithContactQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.DealerFilteringRule>;
}>;


export type GetDealersOptionsWithContactQuery = (
  { __typename: 'Query' }
  & { dealers: Array<(
    { __typename: 'ListDealerOptionWithContact' }
    & Pick<SchemaTypes.ListDealerOptionWithContact, 'id' | 'displayName' | 'companyId' | 'ppsr' | 'estFee' | 'coe' | 'limitFeature'>
    & { legalName: (
      { __typename: 'TranslatedString' }
      & TranslatedStringDataFragment
    ), contact: (
      { __typename: 'DealerContact' }
      & DealerContactFragmentFragment
    ), integrationDetails: (
      { __typename: 'BasicDealerIntegrationDetails' }
      & Pick<SchemaTypes.BasicDealerIntegrationDetails, 'dealerCode' | 'partnerNumber' | 'assortment'>
    ) }
  )> }
);


export const GetDealersOptionsWithContactDocument = /*#__PURE__*/ gql`
    query getDealersOptionsWithContact($filter: DealerFilteringRule) {
  dealers: listDealerOptionsWithContact(filter: $filter) {
    id
    displayName
    legalName {
      ...TranslatedStringData
    }
    contact {
      ...DealerContactFragment
    }
    companyId
    ppsr
    estFee
    coe
    integrationDetails {
      dealerCode
      partnerNumber
      assortment
    }
    limitFeature
  }
}
    ${TranslatedStringDataFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}`;

/**
 * __useGetDealersOptionsWithContactQuery__
 *
 * To run a query within a React component, call `useGetDealersOptionsWithContactQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDealersOptionsWithContactQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDealersOptionsWithContactQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetDealersOptionsWithContactQuery(baseOptions?: Apollo.QueryHookOptions<GetDealersOptionsWithContactQuery, GetDealersOptionsWithContactQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetDealersOptionsWithContactQuery, GetDealersOptionsWithContactQueryVariables>(GetDealersOptionsWithContactDocument, options);
      }
export function useGetDealersOptionsWithContactLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetDealersOptionsWithContactQuery, GetDealersOptionsWithContactQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetDealersOptionsWithContactQuery, GetDealersOptionsWithContactQueryVariables>(GetDealersOptionsWithContactDocument, options);
        }
export type GetDealersOptionsWithContactQueryHookResult = ReturnType<typeof useGetDealersOptionsWithContactQuery>;
export type GetDealersOptionsWithContactLazyQueryHookResult = ReturnType<typeof useGetDealersOptionsWithContactLazyQuery>;
export type GetDealersOptionsWithContactQueryResult = Apollo.QueryResult<GetDealersOptionsWithContactQuery, GetDealersOptionsWithContactQueryVariables>;
import type * as SchemaTypes from '../types';

import type { VehicleListData_FinderVehicle_Fragment, VehicleListData_LocalMake_Fragment, VehicleListData_LocalModel_Fragment, VehicleListData_LocalVariant_Fragment } from '../fragments/VehicleListData';
import type { LocalVariantsListDataFragment } from '../fragments/LocalVariantsListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { LocalModelsListDataFragment } from '../fragments/LocalModelsListData';
import type { LocalMakesListDataFragment } from '../fragments/LocalMakesListData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { FinderVehiclesListDataFragment } from '../fragments/FinderVehiclesListData';
import { gql } from '@apollo/client';
import { VehicleListDataFragmentDoc } from '../fragments/VehicleListData';
import { LocalVariantsListDataFragmentDoc } from '../fragments/LocalVariantsListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { LocalModelsListDataFragmentDoc } from '../fragments/LocalModelsListData';
import { LocalMakesListDataFragmentDoc } from '../fragments/LocalMakesListData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { FinderVehiclesListDataFragmentDoc } from '../fragments/FinderVehiclesListData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListVehiclesQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
}>;


export type ListVehiclesQuery = (
  { __typename: 'Query' }
  & { page: (
    { __typename: 'PaginatedVehicles' }
    & Pick<SchemaTypes.PaginatedVehicles, 'count'>
    & { items: Array<(
      { __typename: 'FinderVehicle' }
      & VehicleListData_FinderVehicle_Fragment
    ) | (
      { __typename: 'LocalMake' }
      & VehicleListData_LocalMake_Fragment
    ) | (
      { __typename: 'LocalModel' }
      & VehicleListData_LocalModel_Fragment
    ) | (
      { __typename: 'LocalVariant' }
      & VehicleListData_LocalVariant_Fragment
    )> }
  ) }
);


export const ListVehiclesDocument = /*#__PURE__*/ gql`
    query listVehicles($pagination: Pagination!) {
  page: listVehicles(pagination: $pagination) {
    count
    items {
      ...VehicleListData
    }
  }
}
    ${VehicleListDataFragmentDoc}
${LocalVariantsListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${LocalModelsListDataFragmentDoc}
${LocalMakesListDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${FinderVehiclesListDataFragmentDoc}`;

/**
 * __useListVehiclesQuery__
 *
 * To run a query within a React component, call `useListVehiclesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListVehiclesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListVehiclesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useListVehiclesQuery(baseOptions: Apollo.QueryHookOptions<ListVehiclesQuery, ListVehiclesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListVehiclesQuery, ListVehiclesQueryVariables>(ListVehiclesDocument, options);
      }
export function useListVehiclesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListVehiclesQuery, ListVehiclesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListVehiclesQuery, ListVehiclesQueryVariables>(ListVehiclesDocument, options);
        }
export type ListVehiclesQueryHookResult = ReturnType<typeof useListVehiclesQuery>;
export type ListVehiclesLazyQueryHookResult = ReturnType<typeof useListVehiclesLazyQuery>;
export type ListVehiclesQueryResult = Apollo.QueryResult<ListVehiclesQuery, ListVehiclesQueryVariables>;
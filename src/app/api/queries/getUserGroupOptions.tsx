import type * as SchemaTypes from '../types';

import type { UserGroupOptionsDataFragment } from '../fragments/UserGroupOptionsData';
import { gql } from '@apollo/client';
import { UserGroupOptionsDataFragmentDoc } from '../fragments/UserGroupOptionsData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetUserGroupOptionsQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.UserGroupFilteringRule>;
}>;


export type GetUserGroupOptionsQuery = (
  { __typename: 'Query' }
  & { userGroups: (
    { __typename: 'PaginatedUserGroups' }
    & { items: Array<(
      { __typename: 'UserGroup' }
      & UserGroupOptionsDataFragment
    )> }
  ) }
);


export const GetUserGroupOptionsDocument = /*#__PURE__*/ gql`
    query getUserGroupOptions($filter: UserGroupFilteringRule) {
  userGroups: listAvailableUserGroups(filter: $filter) {
    items {
      ...UserGroupOptionsData
    }
  }
}
    ${UserGroupOptionsDataFragmentDoc}`;

/**
 * __useGetUserGroupOptionsQuery__
 *
 * To run a query within a React component, call `useGetUserGroupOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserGroupOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserGroupOptionsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetUserGroupOptionsQuery(baseOptions?: Apollo.QueryHookOptions<GetUserGroupOptionsQuery, GetUserGroupOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUserGroupOptionsQuery, GetUserGroupOptionsQueryVariables>(GetUserGroupOptionsDocument, options);
      }
export function useGetUserGroupOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUserGroupOptionsQuery, GetUserGroupOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUserGroupOptionsQuery, GetUserGroupOptionsQueryVariables>(GetUserGroupOptionsDocument, options);
        }
export type GetUserGroupOptionsQueryHookResult = ReturnType<typeof useGetUserGroupOptionsQuery>;
export type GetUserGroupOptionsLazyQueryHookResult = ReturnType<typeof useGetUserGroupOptionsLazyQuery>;
export type GetUserGroupOptionsQueryResult = Apollo.QueryResult<GetUserGroupOptionsQuery, GetUserGroupOptionsQueryVariables>;
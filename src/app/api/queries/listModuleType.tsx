import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListModuleTypeQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ModuleFilteringRule>;
}>;


export type ListModuleTypeQuery = (
  { __typename: 'Query' }
  & { types: SchemaTypes.Query['listModuleType'] }
);


export const ListModuleTypeDocument = /*#__PURE__*/ gql`
    query listModuleType($filter: ModuleFilteringRule) {
  types: listModuleType(filter: $filter)
}
    `;

/**
 * __useListModuleTypeQuery__
 *
 * To run a query within a React component, call `useListModuleTypeQuery` and pass it any options that fit your needs.
 * When your component renders, `useListModuleTypeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListModuleTypeQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListModuleTypeQuery(baseOptions?: Apollo.QueryHookOptions<ListModuleTypeQuery, ListModuleTypeQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListModuleTypeQuery, ListModuleTypeQueryVariables>(ListModuleTypeDocument, options);
      }
export function useListModuleTypeLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListModuleTypeQuery, ListModuleTypeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListModuleTypeQuery, ListModuleTypeQueryVariables>(ListModuleTypeDocument, options);
        }
export type ListModuleTypeQueryHookResult = ReturnType<typeof useListModuleTypeQuery>;
export type ListModuleTypeLazyQueryHookResult = ReturnType<typeof useListModuleTypeLazyQuery>;
export type ListModuleTypeQueryResult = Apollo.QueryResult<ListModuleTypeQuery, ListModuleTypeQueryVariables>;
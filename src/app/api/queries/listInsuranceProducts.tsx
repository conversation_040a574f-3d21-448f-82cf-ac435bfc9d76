import type * as SchemaTypes from '../types';

import type { InsuranceProductListData_Eazy_Fragment, InsuranceProductListData_ErgoLookupTable_Fragment } from '../fragments/InsuranceProductListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { ModulesCompanyTimezoneData_AdyenPaymentModule_Fragment, ModulesCompanyTimezoneData_AppointmentModule_Fragment, ModulesCompanyTimezoneData_AutoplayModule_Fragment, ModulesCompanyTimezoneData_BankModule_Fragment, ModulesCompanyTimezoneData_BasicSigningModule_Fragment, ModulesCompanyTimezoneData_CapModule_Fragment, ModulesCompanyTimezoneData_ConfiguratorModule_Fragment, ModulesCompanyTimezoneData_ConsentsAndDeclarationsModule_Fragment, ModulesCompanyTimezoneData_CtsModule_Fragment, ModulesCompanyTimezoneData_DocusignModule_Fragment, ModulesCompanyTimezoneData_EventApplicationModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPrivateModule_Fragment, ModulesCompanyTimezoneData_FinderApplicationPublicModule_Fragment, ModulesCompanyTimezoneData_FinderVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_FiservPaymentModule_Fragment, ModulesCompanyTimezoneData_GiftVoucherModule_Fragment, ModulesCompanyTimezoneData_InsuranceModule_Fragment, ModulesCompanyTimezoneData_LabelsModule_Fragment, ModulesCompanyTimezoneData_LaunchPadModule_Fragment, ModulesCompanyTimezoneData_LocalCustomerManagementModule_Fragment, ModulesCompanyTimezoneData_MaintenanceModule_Fragment, ModulesCompanyTimezoneData_MarketingModule_Fragment, ModulesCompanyTimezoneData_MobilityModule_Fragment, ModulesCompanyTimezoneData_MyInfoModule_Fragment, ModulesCompanyTimezoneData_NamirialSigningModule_Fragment, ModulesCompanyTimezoneData_OidcModule_Fragment, ModulesCompanyTimezoneData_PayGatePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheIdModule_Fragment, ModulesCompanyTimezoneData_PorscheMasterDataModule_Fragment, ModulesCompanyTimezoneData_PorschePaymentModule_Fragment, ModulesCompanyTimezoneData_PorscheRetainModule_Fragment, ModulesCompanyTimezoneData_PromoCodeModule_Fragment, ModulesCompanyTimezoneData_SalesControlBoardModule_Fragment, ModulesCompanyTimezoneData_SalesOfferModule_Fragment, ModulesCompanyTimezoneData_SimpleVehicleManagementModule_Fragment, ModulesCompanyTimezoneData_StandardApplicationModule_Fragment, ModulesCompanyTimezoneData_TradeInModule_Fragment, ModulesCompanyTimezoneData_TtbPaymentModule_Fragment, ModulesCompanyTimezoneData_UserlikeChatbotModule_Fragment, ModulesCompanyTimezoneData_VehicleDataWithPorscheCodeIntegrationModule_Fragment, ModulesCompanyTimezoneData_VisitAppointmentModule_Fragment, ModulesCompanyTimezoneData_WebsiteModule_Fragment, ModulesCompanyTimezoneData_WhatsappLiveChatModule_Fragment } from '../fragments/ModulesCompanyTimezoneData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { VehicleReferenceParametersDataFragment } from '../fragments/VehicleReferenceParametersData';
import type { ErgoLookupTableSettingsDetailsFragment } from '../fragments/ErgoLookupTableSettingDetails';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { InsuranceProductListDataFragmentDoc } from '../fragments/InsuranceProductListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { ModulesCompanyTimezoneDataFragmentDoc } from '../fragments/ModulesCompanyTimezoneData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { VehicleReferenceParametersDataFragmentDoc } from '../fragments/VehicleReferenceParametersData';
import { ErgoLookupTableSettingsDetailsFragmentDoc } from '../fragments/ErgoLookupTableSettingDetails';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListInsuranceProductsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.InsuranceProductSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.InsuranceProductFilteringRule>;
}>;


export type ListInsuranceProductsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedInsuranceProducts' }
    & Pick<SchemaTypes.PaginatedInsuranceProducts, 'count'>
    & { items: Array<(
      { __typename: 'Eazy' }
      & InsuranceProductListData_Eazy_Fragment
    ) | (
      { __typename: 'ErgoLookupTable' }
      & InsuranceProductListData_ErgoLookupTable_Fragment
    )> }
  ) }
);


export const ListInsuranceProductsDocument = /*#__PURE__*/ gql`
    query listInsuranceProducts($pagination: Pagination, $sort: InsuranceProductSortingRule, $filter: InsuranceProductFilteringRule) {
  list: listInsuranceProducts(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    count
    items {
      ...InsuranceProductListData
    }
  }
}
    ${InsuranceProductListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${ModulesCompanyTimezoneDataFragmentDoc}
${PeriodDataFragmentDoc}
${VehicleReferenceParametersDataFragmentDoc}
${ErgoLookupTableSettingsDetailsFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListInsuranceProductsQuery__
 *
 * To run a query within a React component, call `useListInsuranceProductsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListInsuranceProductsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListInsuranceProductsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListInsuranceProductsQuery(baseOptions?: Apollo.QueryHookOptions<ListInsuranceProductsQuery, ListInsuranceProductsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListInsuranceProductsQuery, ListInsuranceProductsQueryVariables>(ListInsuranceProductsDocument, options);
      }
export function useListInsuranceProductsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListInsuranceProductsQuery, ListInsuranceProductsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListInsuranceProductsQuery, ListInsuranceProductsQueryVariables>(ListInsuranceProductsDocument, options);
        }
export type ListInsuranceProductsQueryHookResult = ReturnType<typeof useListInsuranceProductsQuery>;
export type ListInsuranceProductsLazyQueryHookResult = ReturnType<typeof useListInsuranceProductsLazyQuery>;
export type ListInsuranceProductsQueryResult = Apollo.QueryResult<ListInsuranceProductsQuery, ListInsuranceProductsQueryVariables>;
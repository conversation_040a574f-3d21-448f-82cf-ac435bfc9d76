import type * as SchemaTypes from '../types';

import type { ApplicationAgreementData_CheckboxApplicationAgreement_Fragment, ApplicationAgreementData_GroupApplicationAgreement_Fragment, ApplicationAgreementData_MarketingApplicationAgreement_Fragment, ApplicationAgreementData_TextApplicationAgreement_Fragment } from '../fragments/ApplicationAgreementData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import type { MarketingPlatformSpecsFragment } from '../fragments/MarketingPlatformSpecs';
import type { MarketingPlatformsAgreedSpecsFragment } from '../fragments/MarketingPlatformsAgreedSpecs';
import { gql } from '@apollo/client';
import { ApplicationAgreementDataFragmentDoc } from '../fragments/ApplicationAgreementData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import { MarketingPlatformSpecsFragmentDoc } from '../fragments/MarketingPlatformSpecs';
import { MarketingPlatformsAgreedSpecsFragmentDoc } from '../fragments/MarketingPlatformsAgreedSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchAgreementsForLaunchPadQueryVariables = SchemaTypes.Exact<{
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
  configuration: SchemaTypes.LaunchPadApplicationConfiguration;
}>;


export type PrefetchAgreementsForLaunchPadQuery = (
  { __typename: 'Query' }
  & { applicantAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )>, corporateAgreements: Array<(
    { __typename: 'CheckboxApplicationAgreement' }
    & ApplicationAgreementData_CheckboxApplicationAgreement_Fragment
  ) | (
    { __typename: 'GroupApplicationAgreement' }
    & ApplicationAgreementData_GroupApplicationAgreement_Fragment
  ) | (
    { __typename: 'MarketingApplicationAgreement' }
    & ApplicationAgreementData_MarketingApplicationAgreement_Fragment
  ) | (
    { __typename: 'TextApplicationAgreement' }
    & ApplicationAgreementData_TextApplicationAgreement_Fragment
  )> }
);


export const PrefetchAgreementsForLaunchPadDocument = /*#__PURE__*/ gql`
    query prefetchAgreementsForLaunchPad($applicationModuleId: ObjectID!, $configuration: LaunchPadApplicationConfiguration!) {
  applicantAgreements: prefetchAgreementsForLaunchPad(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "local"
  ) {
    ...ApplicationAgreementData
  }
  corporateAgreements: prefetchAgreementsForLaunchPad(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "corporate"
  ) {
    ...ApplicationAgreementData
  }
}
    ${ApplicationAgreementDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}
${MarketingPlatformSpecsFragmentDoc}
${MarketingPlatformsAgreedSpecsFragmentDoc}`;

/**
 * __usePrefetchAgreementsForLaunchPadQuery__
 *
 * To run a query within a React component, call `usePrefetchAgreementsForLaunchPadQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchAgreementsForLaunchPadQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchAgreementsForLaunchPadQuery({
 *   variables: {
 *      applicationModuleId: // value for 'applicationModuleId'
 *      configuration: // value for 'configuration'
 *   },
 * });
 */
export function usePrefetchAgreementsForLaunchPadQuery(baseOptions: Apollo.QueryHookOptions<PrefetchAgreementsForLaunchPadQuery, PrefetchAgreementsForLaunchPadQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchAgreementsForLaunchPadQuery, PrefetchAgreementsForLaunchPadQueryVariables>(PrefetchAgreementsForLaunchPadDocument, options);
      }
export function usePrefetchAgreementsForLaunchPadLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchAgreementsForLaunchPadQuery, PrefetchAgreementsForLaunchPadQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchAgreementsForLaunchPadQuery, PrefetchAgreementsForLaunchPadQueryVariables>(PrefetchAgreementsForLaunchPadDocument, options);
        }
export type PrefetchAgreementsForLaunchPadQueryHookResult = ReturnType<typeof usePrefetchAgreementsForLaunchPadQuery>;
export type PrefetchAgreementsForLaunchPadLazyQueryHookResult = ReturnType<typeof usePrefetchAgreementsForLaunchPadLazyQuery>;
export type PrefetchAgreementsForLaunchPadQueryResult = Apollo.QueryResult<PrefetchAgreementsForLaunchPadQuery, PrefetchAgreementsForLaunchPadQueryVariables>;
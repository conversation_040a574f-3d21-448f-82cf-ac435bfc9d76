import type * as SchemaTypes from '../types';

import type { VehicleListData_FinderVehicle_Fragment, VehicleListData_LocalMake_Fragment, VehicleListData_LocalModel_Fragment, VehicleListData_LocalVariant_Fragment } from '../fragments/VehicleListData';
import type { LocalVariantsListDataFragment } from '../fragments/LocalVariantsListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { LocalModelsListDataFragment } from '../fragments/LocalModelsListData';
import type { LocalMakesListDataFragment } from '../fragments/LocalMakesListData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { FinderVehiclesListDataFragment } from '../fragments/FinderVehiclesListData';
import { gql } from '@apollo/client';
import { VehicleListDataFragmentDoc } from '../fragments/VehicleListData';
import { LocalVariantsListDataFragmentDoc } from '../fragments/LocalVariantsListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { LocalModelsListDataFragmentDoc } from '../fragments/LocalModelsListData';
import { LocalMakesListDataFragmentDoc } from '../fragments/LocalMakesListData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { FinderVehiclesListDataFragmentDoc } from '../fragments/FinderVehiclesListData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetVariantsOfAllConfiguratorsQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetVariantsOfAllConfiguratorsQuery = (
  { __typename: 'Query' }
  & { vehicles: Array<(
    { __typename: 'FinderVehicle' }
    & VehicleListData_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleListData_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleListData_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleListData_LocalVariant_Fragment
  )> }
);


export const GetVariantsOfAllConfiguratorsDocument = /*#__PURE__*/ gql`
    query getVariantsOfAllConfigurators($moduleId: ObjectID!) {
  vehicles: getVariantsOfAllConfigurators(moduleId: $moduleId) {
    ...VehicleListData
  }
}
    ${VehicleListDataFragmentDoc}
${LocalVariantsListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${LocalModelsListDataFragmentDoc}
${LocalMakesListDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${FinderVehiclesListDataFragmentDoc}`;

/**
 * __useGetVariantsOfAllConfiguratorsQuery__
 *
 * To run a query within a React component, call `useGetVariantsOfAllConfiguratorsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVariantsOfAllConfiguratorsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVariantsOfAllConfiguratorsQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useGetVariantsOfAllConfiguratorsQuery(baseOptions: Apollo.QueryHookOptions<GetVariantsOfAllConfiguratorsQuery, GetVariantsOfAllConfiguratorsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVariantsOfAllConfiguratorsQuery, GetVariantsOfAllConfiguratorsQueryVariables>(GetVariantsOfAllConfiguratorsDocument, options);
      }
export function useGetVariantsOfAllConfiguratorsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVariantsOfAllConfiguratorsQuery, GetVariantsOfAllConfiguratorsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVariantsOfAllConfiguratorsQuery, GetVariantsOfAllConfiguratorsQueryVariables>(GetVariantsOfAllConfiguratorsDocument, options);
        }
export type GetVariantsOfAllConfiguratorsQueryHookResult = ReturnType<typeof useGetVariantsOfAllConfiguratorsQuery>;
export type GetVariantsOfAllConfiguratorsLazyQueryHookResult = ReturnType<typeof useGetVariantsOfAllConfiguratorsLazyQuery>;
export type GetVariantsOfAllConfiguratorsQueryResult = Apollo.QueryResult<GetVariantsOfAllConfiguratorsQuery, GetVariantsOfAllConfiguratorsQueryVariables>;
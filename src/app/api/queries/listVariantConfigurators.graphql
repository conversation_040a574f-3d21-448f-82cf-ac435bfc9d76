query listVariantConfigurators(
    $modelConfiguratorId: ObjectID!
    $pagination: Pagination
    $filter: VariantConfiguratorFilteringRule
    $sort: VariantConfiguratorSortingRule
) {
    lists: listVariantConfigurators(
        modelConfiguratorId: $modelConfiguratorId
        pagination: $pagination
        filter: $filter
        sort: $sort
    ) {
        items {
            ...VariantConfiguratorListItem
        }
        count
    }
}
import type * as SchemaTypes from '../types';

import type { PorschePaymentSettingsSpecFragment } from '../fragments/PorschePaymentSettingsSpec';
import { gql } from '@apollo/client';
import { PorschePaymentSettingsSpecFragmentDoc } from '../fragments/PorschePaymentSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListPorschePaymentSettingsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListPorschePaymentSettingsQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedPorschePaymentSettings' }
    & Pick<SchemaTypes.PaginatedPorschePaymentSettings, 'count'>
    & { items: Array<(
      { __typename: 'PorschePaymentSetting' }
      & PorschePaymentSettingsSpecFragment
    )> }
  ) }
);


export const ListPorschePaymentSettingsDocument = /*#__PURE__*/ gql`
    query listPorschePaymentSettings($pagination: Pagination, $moduleId: ObjectID!) {
  settings: listPorschePaymentSettings(
    pagination: $pagination
    moduleId: $moduleId
  ) {
    count
    items {
      ...PorschePaymentSettingsSpec
    }
  }
}
    ${PorschePaymentSettingsSpecFragmentDoc}`;

/**
 * __useListPorschePaymentSettingsQuery__
 *
 * To run a query within a React component, call `useListPorschePaymentSettingsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListPorschePaymentSettingsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListPorschePaymentSettingsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useListPorschePaymentSettingsQuery(baseOptions: Apollo.QueryHookOptions<ListPorschePaymentSettingsQuery, ListPorschePaymentSettingsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListPorschePaymentSettingsQuery, ListPorschePaymentSettingsQueryVariables>(ListPorschePaymentSettingsDocument, options);
      }
export function useListPorschePaymentSettingsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListPorschePaymentSettingsQuery, ListPorschePaymentSettingsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListPorschePaymentSettingsQuery, ListPorschePaymentSettingsQueryVariables>(ListPorschePaymentSettingsDocument, options);
        }
export type ListPorschePaymentSettingsQueryHookResult = ReturnType<typeof useListPorschePaymentSettingsQuery>;
export type ListPorschePaymentSettingsLazyQueryHookResult = ReturnType<typeof useListPorschePaymentSettingsLazyQuery>;
export type ListPorschePaymentSettingsQueryResult = Apollo.QueryResult<ListPorschePaymentSettingsQuery, ListPorschePaymentSettingsQueryVariables>;
query listFinderVehiclesForSelection(
    $pagination: Pagination
    $sort: FinderVehicleSortingRule
    $filter: FilterVehicleFilteringRule
    $bankModuleId: ObjectID
    $applicationModuleIds: [ObjectID!]
    $dealerId: ObjectID
) {
    vehicles: listFinderVehicles(pagination: $pagination, sort: $sort, filter: $filter, dealerId: $dealerId, applicationModuleIds: $applicationModuleIds) {
        items {
            ...FinderVehicleSelectionData
        }
        count
        filters {
            conditions
            modelCategories
            monthlyInstalments {
                from
                to
            }
        }
    }
}

import type * as SchemaTypes from '../types';

import type { CampaignDataFragment } from '../fragments/CampaignData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import { gql } from '@apollo/client';
import { CampaignDataFragmentDoc } from '../fragments/CampaignData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListCampaignsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.CampaignSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.CampaignFilteringRule>;
}>;


export type ListCampaignsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedCampaign' }
    & Pick<SchemaTypes.PaginatedCampaign, 'count'>
    & { items: Array<(
      { __typename: 'Campaign' }
      & CampaignDataFragment
    )> }
  ) }
);


export const ListCampaignsDocument = /*#__PURE__*/ gql`
    query listCampaigns($pagination: Pagination, $sort: CampaignSortingRule, $filter: CampaignFilteringRule) {
  list: listCampaigns(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...CampaignData
    }
  }
}
    ${CampaignDataFragmentDoc}
${TranslatedStringDataFragmentDoc}`;

/**
 * __useListCampaignsQuery__
 *
 * To run a query within a React component, call `useListCampaignsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListCampaignsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListCampaignsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListCampaignsQuery(baseOptions?: Apollo.QueryHookOptions<ListCampaignsQuery, ListCampaignsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListCampaignsQuery, ListCampaignsQueryVariables>(ListCampaignsDocument, options);
      }
export function useListCampaignsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListCampaignsQuery, ListCampaignsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListCampaignsQuery, ListCampaignsQueryVariables>(ListCampaignsDocument, options);
        }
export type ListCampaignsQueryHookResult = ReturnType<typeof useListCampaignsQuery>;
export type ListCampaignsLazyQueryHookResult = ReturnType<typeof useListCampaignsLazyQuery>;
export type ListCampaignsQueryResult = Apollo.QueryResult<ListCampaignsQuery, ListCampaignsQueryVariables>;
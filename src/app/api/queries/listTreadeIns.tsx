import type * as SchemaTypes from '../types';

import type { TradeInModuleSpecsFragment } from '../fragments/TradeInModuleSpecs';
import type { TradeInSettingSpecFragment } from '../fragments/TradeInSetting';
import type { SimpleVersioningDataFragment } from '../fragments/SimpleVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { TradeInModuleSpecsFragmentDoc } from '../fragments/TradeInModuleSpecs';
import { TradeInSettingSpecFragmentDoc } from '../fragments/TradeInSetting';
import { SimpleVersioningDataFragmentDoc } from '../fragments/SimpleVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListTradeInsQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.TradeInFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.TradeInSortingRule>;
}>;


export type ListTradeInsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedTradeIns' }
    & Pick<SchemaTypes.PaginatedTradeIns, 'count'>
    & { items: Array<never> }
  ) }
);


export const ListTradeInsDocument = /*#__PURE__*/ gql`
    query listTradeIns($pagination: Pagination!, $filter: TradeInFilteringRule, $sort: TradeInSortingRule) {
  list: listTradeIns(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      id
      moduleId
      module {
        ...TradeInModuleSpecs
      }
      status
      identifier
      versioning {
        ...SimpleVersioningData
      }
    }
  }
}
    ${TradeInModuleSpecsFragmentDoc}
${TradeInSettingSpecFragmentDoc}
${SimpleVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListTradeInsQuery__
 *
 * To run a query within a React component, call `useListTradeInsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListTradeInsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListTradeInsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListTradeInsQuery(baseOptions: Apollo.QueryHookOptions<ListTradeInsQuery, ListTradeInsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListTradeInsQuery, ListTradeInsQueryVariables>(ListTradeInsDocument, options);
      }
export function useListTradeInsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListTradeInsQuery, ListTradeInsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListTradeInsQuery, ListTradeInsQueryVariables>(ListTradeInsDocument, options);
        }
export type ListTradeInsQueryHookResult = ReturnType<typeof useListTradeInsQuery>;
export type ListTradeInsLazyQueryHookResult = ReturnType<typeof useListTradeInsLazyQuery>;
export type ListTradeInsQueryResult = Apollo.QueryResult<ListTradeInsQuery, ListTradeInsQueryVariables>;
import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type RetrieveBlockedAppointmentTimeSlotQueryVariables = SchemaTypes.Exact<{
  moduleId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  eventId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type RetrieveBlockedAppointmentTimeSlotQuery = (
  { __typename: 'Query' }
  & Pick<SchemaTypes.Query, 'retrieveBlockedAppointmentTimeSlot'>
);


export const RetrieveBlockedAppointmentTimeSlotDocument = /*#__PURE__*/ gql`
    query retrieveBlockedAppointmentTimeSlot($moduleId: ObjectID, $eventId: ObjectID) {
  retrieveBlockedAppointmentTimeSlot(moduleId: $moduleId, eventId: $eventId)
}
    `;

/**
 * __useRetrieveBlockedAppointmentTimeSlotQuery__
 *
 * To run a query within a React component, call `useRetrieveBlockedAppointmentTimeSlotQuery` and pass it any options that fit your needs.
 * When your component renders, `useRetrieveBlockedAppointmentTimeSlotQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useRetrieveBlockedAppointmentTimeSlotQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *      eventId: // value for 'eventId'
 *   },
 * });
 */
export function useRetrieveBlockedAppointmentTimeSlotQuery(baseOptions?: Apollo.QueryHookOptions<RetrieveBlockedAppointmentTimeSlotQuery, RetrieveBlockedAppointmentTimeSlotQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<RetrieveBlockedAppointmentTimeSlotQuery, RetrieveBlockedAppointmentTimeSlotQueryVariables>(RetrieveBlockedAppointmentTimeSlotDocument, options);
      }
export function useRetrieveBlockedAppointmentTimeSlotLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<RetrieveBlockedAppointmentTimeSlotQuery, RetrieveBlockedAppointmentTimeSlotQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<RetrieveBlockedAppointmentTimeSlotQuery, RetrieveBlockedAppointmentTimeSlotQueryVariables>(RetrieveBlockedAppointmentTimeSlotDocument, options);
        }
export type RetrieveBlockedAppointmentTimeSlotQueryHookResult = ReturnType<typeof useRetrieveBlockedAppointmentTimeSlotQuery>;
export type RetrieveBlockedAppointmentTimeSlotLazyQueryHookResult = ReturnType<typeof useRetrieveBlockedAppointmentTimeSlotLazyQuery>;
export type RetrieveBlockedAppointmentTimeSlotQueryResult = Apollo.QueryResult<RetrieveBlockedAppointmentTimeSlotQuery, RetrieveBlockedAppointmentTimeSlotQueryVariables>;
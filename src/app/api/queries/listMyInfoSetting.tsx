import type * as SchemaTypes from '../types';

import type { MyInfoSettingsListFragment } from '../fragments/MyInfoSettingsList';
import { gql } from '@apollo/client';
import { MyInfoSettingsListFragmentDoc } from '../fragments/MyInfoSettingsList';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListMyInfoSettingQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type ListMyInfoSettingQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedMyInfoSettings' }
    & Pick<SchemaTypes.PaginatedMyInfoSettings, 'count'>
    & { items: Array<(
      { __typename: 'MyInfoSetting' }
      & MyInfoSettingsListFragment
    )> }
  ) }
);


export const ListMyInfoSettingDocument = /*#__PURE__*/ gql`
    query listMyInfoSetting($pagination: Pagination, $moduleId: ObjectID!) {
  settings: listMyInfoSetting(pagination: $pagination, moduleId: $moduleId) {
    count
    items {
      ...MyInfoSettingsList
    }
  }
}
    ${MyInfoSettingsListFragmentDoc}`;

/**
 * __useListMyInfoSettingQuery__
 *
 * To run a query within a React component, call `useListMyInfoSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useListMyInfoSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListMyInfoSettingQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useListMyInfoSettingQuery(baseOptions: Apollo.QueryHookOptions<ListMyInfoSettingQuery, ListMyInfoSettingQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListMyInfoSettingQuery, ListMyInfoSettingQueryVariables>(ListMyInfoSettingDocument, options);
      }
export function useListMyInfoSettingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListMyInfoSettingQuery, ListMyInfoSettingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListMyInfoSettingQuery, ListMyInfoSettingQueryVariables>(ListMyInfoSettingDocument, options);
        }
export type ListMyInfoSettingQueryHookResult = ReturnType<typeof useListMyInfoSettingQuery>;
export type ListMyInfoSettingLazyQueryHookResult = ReturnType<typeof useListMyInfoSettingLazyQuery>;
export type ListMyInfoSettingQueryResult = Apollo.QueryResult<ListMyInfoSettingQuery, ListMyInfoSettingQueryVariables>;
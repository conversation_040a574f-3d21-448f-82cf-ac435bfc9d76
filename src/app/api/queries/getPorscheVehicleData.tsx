import type * as SchemaTypes from '../types';

import type { PorscheVehicleDataSpecsFragment, PorscheVehicleDataFeatureSpecsFragment, PorscheVehicleImagesSpecsFragment } from '../fragments/PorscheVehicleDataSpecs';
import { gql } from '@apollo/client';
import { PorscheVehicleDataSpecsFragmentDoc, PorscheVehicleDataFeatureSpecsFragmentDoc, PorscheVehicleImagesSpecsFragmentDoc } from '../fragments/PorscheVehicleDataSpecs';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetPorscheVehicleDataQueryVariables = SchemaTypes.Exact<{
  porscheCode: SchemaTypes.Scalars['String']['input'];
  salesOfferModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetPorscheVehicleDataQuery = (
  { __typename: 'Query' }
  & { getPorscheVehicleData?: SchemaTypes.Maybe<(
    { __typename: 'PorscheVehicleData' }
    & PorscheVehicleDataSpecsFragment
  )> }
);


export const GetPorscheVehicleDataDocument = /*#__PURE__*/ gql`
    query getPorscheVehicleData($porscheCode: String!, $salesOfferModuleId: ObjectID!) {
  getPorscheVehicleData(
    porscheCode: $porscheCode
    salesOfferModuleId: $salesOfferModuleId
  ) {
    ...PorscheVehicleDataSpecs
  }
}
    ${PorscheVehicleDataSpecsFragmentDoc}
${PorscheVehicleDataFeatureSpecsFragmentDoc}
${PorscheVehicleImagesSpecsFragmentDoc}`;

/**
 * __useGetPorscheVehicleDataQuery__
 *
 * To run a query within a React component, call `useGetPorscheVehicleDataQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPorscheVehicleDataQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPorscheVehicleDataQuery({
 *   variables: {
 *      porscheCode: // value for 'porscheCode'
 *      salesOfferModuleId: // value for 'salesOfferModuleId'
 *   },
 * });
 */
export function useGetPorscheVehicleDataQuery(baseOptions: Apollo.QueryHookOptions<GetPorscheVehicleDataQuery, GetPorscheVehicleDataQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPorscheVehicleDataQuery, GetPorscheVehicleDataQueryVariables>(GetPorscheVehicleDataDocument, options);
      }
export function useGetPorscheVehicleDataLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPorscheVehicleDataQuery, GetPorscheVehicleDataQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPorscheVehicleDataQuery, GetPorscheVehicleDataQueryVariables>(GetPorscheVehicleDataDocument, options);
        }
export type GetPorscheVehicleDataQueryHookResult = ReturnType<typeof useGetPorscheVehicleDataQuery>;
export type GetPorscheVehicleDataLazyQueryHookResult = ReturnType<typeof useGetPorscheVehicleDataLazyQuery>;
export type GetPorscheVehicleDataQueryResult = Apollo.QueryResult<GetPorscheVehicleDataQuery, GetPorscheVehicleDataQueryVariables>;
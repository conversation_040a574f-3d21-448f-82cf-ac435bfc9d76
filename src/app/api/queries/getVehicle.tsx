import type * as SchemaTypes from '../types';

import type { VehicleWithPermissionsSpecs_FinderVehicle_Fragment, VehicleWithPermissionsSpecs_LocalMake_Fragment, VehicleWithPermissionsSpecs_LocalModel_Fragment, VehicleWithPermissionsSpecs_LocalVariant_Fragment } from '../fragments/VehicleWithPermissionsSpecs';
import type { LocalVariantWithPermissionsSpecsFragment } from '../fragments/LocalVariantWithPermissionsSpecs';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { LocalModelWithPermissionsSpecsFragment } from '../fragments/LocalModelWithPermissionsSpecs';
import type { LocalMakeWithPermissionsSpecsFragment } from '../fragments/LocalMakeWithPermissionsSpecs';
import type { FinderVehicleWithPermissionsSpecsFragment } from '../fragments/FinderVehicleWithPermissionsSpecs';
import type { FinderVehicleSpecsFragment } from '../fragments/FinderVehicleSpecs';
import type { FullListingValueFragment, FormattedDateDataFragment, LocalizedStringDataFragment, LocalizedValueDataFragment, NumberUnitDataFragment } from '../fragments/finderListing.fragment';
import { gql } from '@apollo/client';
import { VehicleWithPermissionsSpecsFragmentDoc } from '../fragments/VehicleWithPermissionsSpecs';
import { LocalVariantWithPermissionsSpecsFragmentDoc } from '../fragments/LocalVariantWithPermissionsSpecs';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { LocalModelWithPermissionsSpecsFragmentDoc } from '../fragments/LocalModelWithPermissionsSpecs';
import { LocalMakeWithPermissionsSpecsFragmentDoc } from '../fragments/LocalMakeWithPermissionsSpecs';
import { FinderVehicleWithPermissionsSpecsFragmentDoc } from '../fragments/FinderVehicleWithPermissionsSpecs';
import { FinderVehicleSpecsFragmentDoc } from '../fragments/FinderVehicleSpecs';
import { FullListingValueFragmentDoc, FormattedDateDataFragmentDoc, LocalizedStringDataFragmentDoc, LocalizedValueDataFragmentDoc, NumberUnitDataFragmentDoc } from '../fragments/finderListing.fragment';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetVehicleQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetVehicleQuery = (
  { __typename: 'Query' }
  & { vehicle?: SchemaTypes.Maybe<(
    { __typename: 'FinderVehicle' }
    & VehicleWithPermissionsSpecs_FinderVehicle_Fragment
  ) | (
    { __typename: 'LocalMake' }
    & VehicleWithPermissionsSpecs_LocalMake_Fragment
  ) | (
    { __typename: 'LocalModel' }
    & VehicleWithPermissionsSpecs_LocalModel_Fragment
  ) | (
    { __typename: 'LocalVariant' }
    & VehicleWithPermissionsSpecs_LocalVariant_Fragment
  )> }
);


export const GetVehicleDocument = /*#__PURE__*/ gql`
    query getVehicle($id: ObjectID!) {
  vehicle: getVehicle(id: $id) {
    ...VehicleWithPermissionsSpecs
  }
}
    ${VehicleWithPermissionsSpecsFragmentDoc}
${LocalVariantWithPermissionsSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${TranslatedStringDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${LocalModelWithPermissionsSpecsFragmentDoc}
${LocalMakeWithPermissionsSpecsFragmentDoc}
${FinderVehicleWithPermissionsSpecsFragmentDoc}
${FinderVehicleSpecsFragmentDoc}
${FullListingValueFragmentDoc}
${FormattedDateDataFragmentDoc}
${LocalizedStringDataFragmentDoc}
${LocalizedValueDataFragmentDoc}
${NumberUnitDataFragmentDoc}`;

/**
 * __useGetVehicleQuery__
 *
 * To run a query within a React component, call `useGetVehicleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVehicleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVehicleQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetVehicleQuery(baseOptions: Apollo.QueryHookOptions<GetVehicleQuery, GetVehicleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVehicleQuery, GetVehicleQueryVariables>(GetVehicleDocument, options);
      }
export function useGetVehicleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVehicleQuery, GetVehicleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVehicleQuery, GetVehicleQueryVariables>(GetVehicleDocument, options);
        }
export type GetVehicleQueryHookResult = ReturnType<typeof useGetVehicleQuery>;
export type GetVehicleLazyQueryHookResult = ReturnType<typeof useGetVehicleLazyQuery>;
export type GetVehicleQueryResult = Apollo.QueryResult<GetVehicleQuery, GetVehicleQueryVariables>;
import type * as SchemaTypes from '../types';

import type { CompanySelectionItemFragment } from '../fragments/CompanySelectionItem';
import { gql } from '@apollo/client';
import { CompanySelectionItemFragmentDoc } from '../fragments/CompanySelectionItem';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListCompaniesForSelectionQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.CompanyFilteringRule>;
}>;


export type ListCompaniesForSelectionQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedCompanyOptions' }
    & Pick<SchemaTypes.PaginatedCompanyOptions, 'count'>
    & { items: Array<(
      { __typename: 'CompanyOptions' }
      & CompanySelectionItemFragment
    )> }
  ) }
);


export const ListCompaniesForSelectionDocument = /*#__PURE__*/ gql`
    query listCompaniesForSelection($filter: CompanyFilteringRule) {
  list: listCompaniesForSelection(filter: $filter) {
    count
    items {
      ...CompanySelectionItem
    }
  }
}
    ${CompanySelectionItemFragmentDoc}`;

/**
 * __useListCompaniesForSelectionQuery__
 *
 * To run a query within a React component, call `useListCompaniesForSelectionQuery` and pass it any options that fit your needs.
 * When your component renders, `useListCompaniesForSelectionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListCompaniesForSelectionQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListCompaniesForSelectionQuery(baseOptions?: Apollo.QueryHookOptions<ListCompaniesForSelectionQuery, ListCompaniesForSelectionQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListCompaniesForSelectionQuery, ListCompaniesForSelectionQueryVariables>(ListCompaniesForSelectionDocument, options);
      }
export function useListCompaniesForSelectionLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListCompaniesForSelectionQuery, ListCompaniesForSelectionQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListCompaniesForSelectionQuery, ListCompaniesForSelectionQueryVariables>(ListCompaniesForSelectionDocument, options);
        }
export type ListCompaniesForSelectionQueryHookResult = ReturnType<typeof useListCompaniesForSelectionQuery>;
export type ListCompaniesForSelectionLazyQueryHookResult = ReturnType<typeof useListCompaniesForSelectionLazyQuery>;
export type ListCompaniesForSelectionQueryResult = Apollo.QueryResult<ListCompaniesForSelectionQuery, ListCompaniesForSelectionQueryVariables>;
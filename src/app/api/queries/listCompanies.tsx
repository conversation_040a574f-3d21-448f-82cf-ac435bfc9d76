import type * as SchemaTypes from '../types';

import type { CompanyListDataFragment } from '../fragments/CompanyListData';
import { gql } from '@apollo/client';
import { CompanyListDataFragmentDoc } from '../fragments/CompanyListData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListCompaniesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.CompanySortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.CompanyFilteringRule>;
}>;


export type ListCompaniesQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedCompanies' }
    & Pick<SchemaTypes.PaginatedCompanies, 'count'>
    & { items: Array<(
      { __typename: 'Company' }
      & CompanyListDataFragment
    )> }
  ) }
);


export const ListCompaniesDocument = /*#__PURE__*/ gql`
    query listCompanies($pagination: Pagination, $sort: CompanySortingRule, $filter: CompanyFilteringRule) {
  list: listCompanies(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...CompanyListData
    }
  }
}
    ${CompanyListDataFragmentDoc}`;

/**
 * __useListCompaniesQuery__
 *
 * To run a query within a React component, call `useListCompaniesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListCompaniesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListCompaniesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListCompaniesQuery(baseOptions?: Apollo.QueryHookOptions<ListCompaniesQuery, ListCompaniesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListCompaniesQuery, ListCompaniesQueryVariables>(ListCompaniesDocument, options);
      }
export function useListCompaniesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListCompaniesQuery, ListCompaniesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListCompaniesQuery, ListCompaniesQueryVariables>(ListCompaniesDocument, options);
        }
export type ListCompaniesQueryHookResult = ReturnType<typeof useListCompaniesQuery>;
export type ListCompaniesLazyQueryHookResult = ReturnType<typeof useListCompaniesLazyQuery>;
export type ListCompaniesQueryResult = Apollo.QueryResult<ListCompaniesQuery, ListCompaniesQueryVariables>;
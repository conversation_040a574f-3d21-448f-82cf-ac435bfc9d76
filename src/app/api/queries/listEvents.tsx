import type * as SchemaTypes from '../types';

import type { EventListDataFragment } from '../fragments/EventListData';
import { gql } from '@apollo/client';
import { EventListDataFragmentDoc } from '../fragments/EventListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListEventsQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.EventSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.EventFilteringRule>;
}>;


export type ListEventsQuery = (
  { __typename: 'Query' }
  & { lists: (
    { __typename: 'PaginatedEvent' }
    & Pick<SchemaTypes.PaginatedEvent, 'count'>
    & { items: Array<(
      { __typename: 'Event' }
      & EventListDataFragment
    )> }
  ) }
);


export const ListEventsDocument = /*#__PURE__*/ gql`
    query listEvents($pagination: Pagination, $sort: EventSortingRule, $filter: EventFilteringRule) {
  lists: listEvents(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...EventListData
    }
  }
}
    ${EventListDataFragmentDoc}`;

/**
 * __useListEventsQuery__
 *
 * To run a query within a React component, call `useListEventsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListEventsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListEventsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListEventsQuery(baseOptions?: Apollo.QueryHookOptions<ListEventsQuery, ListEventsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListEventsQuery, ListEventsQueryVariables>(ListEventsDocument, options);
      }
export function useListEventsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListEventsQuery, ListEventsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListEventsQuery, ListEventsQueryVariables>(ListEventsDocument, options);
        }
export type ListEventsQueryHookResult = ReturnType<typeof useListEventsQuery>;
export type ListEventsLazyQueryHookResult = ReturnType<typeof useListEventsLazyQuery>;
export type ListEventsQueryResult = Apollo.QueryResult<ListEventsQuery, ListEventsQueryVariables>;
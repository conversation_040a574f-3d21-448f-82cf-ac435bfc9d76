import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchKycFieldsForLaunchPadAppointmentQueryVariables = SchemaTypes.Exact<{
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type PrefetchKycFieldsForLaunchPadAppointmentQuery = (
  { __typename: 'Query' }
  & { applicantKyc: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )> }
);


export const PrefetchKycFieldsForLaunchPadAppointmentDocument = /*#__PURE__*/ gql`
    query prefetchKYCFieldsForLaunchPadAppointment($applicationModuleId: ObjectID!) {
  applicantKyc: prefetchKYCFieldsForLaunchPadAppointment(
    applicationModuleId: $applicationModuleId
  ) {
    ...KYCFieldSpecs
  }
}
    ${KycFieldSpecsFragmentDoc}`;

/**
 * __usePrefetchKycFieldsForLaunchPadAppointmentQuery__
 *
 * To run a query within a React component, call `usePrefetchKycFieldsForLaunchPadAppointmentQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchKycFieldsForLaunchPadAppointmentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchKycFieldsForLaunchPadAppointmentQuery({
 *   variables: {
 *      applicationModuleId: // value for 'applicationModuleId'
 *   },
 * });
 */
export function usePrefetchKycFieldsForLaunchPadAppointmentQuery(baseOptions: Apollo.QueryHookOptions<PrefetchKycFieldsForLaunchPadAppointmentQuery, PrefetchKycFieldsForLaunchPadAppointmentQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchKycFieldsForLaunchPadAppointmentQuery, PrefetchKycFieldsForLaunchPadAppointmentQueryVariables>(PrefetchKycFieldsForLaunchPadAppointmentDocument, options);
      }
export function usePrefetchKycFieldsForLaunchPadAppointmentLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchKycFieldsForLaunchPadAppointmentQuery, PrefetchKycFieldsForLaunchPadAppointmentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchKycFieldsForLaunchPadAppointmentQuery, PrefetchKycFieldsForLaunchPadAppointmentQueryVariables>(PrefetchKycFieldsForLaunchPadAppointmentDocument, options);
        }
export type PrefetchKycFieldsForLaunchPadAppointmentQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForLaunchPadAppointmentQuery>;
export type PrefetchKycFieldsForLaunchPadAppointmentLazyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForLaunchPadAppointmentLazyQuery>;
export type PrefetchKycFieldsForLaunchPadAppointmentQueryResult = Apollo.QueryResult<PrefetchKycFieldsForLaunchPadAppointmentQuery, PrefetchKycFieldsForLaunchPadAppointmentQueryVariables>;
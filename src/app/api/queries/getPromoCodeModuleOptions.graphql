query getPromoCodeModuleOptions($filter: ModuleFilteringRule) {
    modules: listModules(filter: $filter) {
        items {
            id
            displayName

            ... on StandardApplicationModule {
                promoCodeModuleId
            }

            ... on FinderApplicationPublicModule {
                promoCodeModuleId
                bankModuleId
                vehicleModuleId
                finderVehicleConditions
            }

            ... on FinderApplicationPrivateModule {
                promoCodeModuleId
            }

            ... on MobilityModule {
                promoCodeModuleId
            }

            ... on ConfiguratorModule {
                promoCodeModuleId
            }
        }
    }
}

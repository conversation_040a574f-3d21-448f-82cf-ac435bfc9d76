query prefetchKYCFieldsForStandardApplication(
    $applicationModuleId: ObjectID!
    $configuration: StandardApplicationConfiguration!
) {
    applicantKyc: prefetchKYCFieldsForStandardApplication(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "local"
        placement: "kyc"
    ) {
        ...KYCFieldSpecs
    }

    corporateKyc: prefetchKYCFieldsForStandardApplication(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "corporate"
        placement: "kyc"
    ) {
        ...KYCFieldSpecs
    }

    guarantorKyc: prefetchKYCFieldsForStandardApplication(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "guarantor"
        placement: "kyc"
    ) {
        ...KYCFieldSpecs
    }

    shareKyc: prefetchKYCFieldsForStandardApplication(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "local"
        placement: "share"
    ) {
        ...KYCFieldSpecs
    }
}

query prefetchAgreementsForLaunchPad(
    $applicationModuleId: ObjectID!
    $configuration: LaunchPadApplicationConfiguration!
) {
    applicantAgreements: prefetchAgreementsForLaunchPad(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "local"
    ) {
        ...ApplicationAgreementData
    }

    corporateAgreements: prefetchAgreementsForLaunchPad(
        applicationModuleId: $applicationModuleId
        configuration: $configuration
        customerKind: "corporate"
    ) {
        ...ApplicationAgreementData
    }
}

import type * as SchemaTypes from '../types';

import type { PorschePaymentSettingsSpecFragment } from '../fragments/PorschePaymentSettingsSpec';
import { gql } from '@apollo/client';
import { PorschePaymentSettingsSpecFragmentDoc } from '../fragments/PorschePaymentSettingsSpec';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetSettingOptionsForPorschePaymentModuleQueryVariables = SchemaTypes.Exact<{
  moduleId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetSettingOptionsForPorschePaymentModuleQuery = (
  { __typename: 'Query' }
  & { settings: (
    { __typename: 'PaginatedPorschePaymentSettings' }
    & { items: Array<(
      { __typename: 'PorschePaymentSetting' }
      & PorschePaymentSettingsSpecFragment
    )> }
  ) }
);


export const GetSettingOptionsForPorschePaymentModuleDocument = /*#__PURE__*/ gql`
    query getSettingOptionsForPorschePaymentModule($moduleId: ObjectID!) {
  settings: listPorschePaymentSettings(moduleId: $moduleId) {
    items {
      ...PorschePaymentSettingsSpec
    }
  }
}
    ${PorschePaymentSettingsSpecFragmentDoc}`;

/**
 * __useGetSettingOptionsForPorschePaymentModuleQuery__
 *
 * To run a query within a React component, call `useGetSettingOptionsForPorschePaymentModuleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSettingOptionsForPorschePaymentModuleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSettingOptionsForPorschePaymentModuleQuery({
 *   variables: {
 *      moduleId: // value for 'moduleId'
 *   },
 * });
 */
export function useGetSettingOptionsForPorschePaymentModuleQuery(baseOptions: Apollo.QueryHookOptions<GetSettingOptionsForPorschePaymentModuleQuery, GetSettingOptionsForPorschePaymentModuleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSettingOptionsForPorschePaymentModuleQuery, GetSettingOptionsForPorschePaymentModuleQueryVariables>(GetSettingOptionsForPorschePaymentModuleDocument, options);
      }
export function useGetSettingOptionsForPorschePaymentModuleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSettingOptionsForPorschePaymentModuleQuery, GetSettingOptionsForPorschePaymentModuleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSettingOptionsForPorschePaymentModuleQuery, GetSettingOptionsForPorschePaymentModuleQueryVariables>(GetSettingOptionsForPorschePaymentModuleDocument, options);
        }
export type GetSettingOptionsForPorschePaymentModuleQueryHookResult = ReturnType<typeof useGetSettingOptionsForPorschePaymentModuleQuery>;
export type GetSettingOptionsForPorschePaymentModuleLazyQueryHookResult = ReturnType<typeof useGetSettingOptionsForPorschePaymentModuleLazyQuery>;
export type GetSettingOptionsForPorschePaymentModuleQueryResult = Apollo.QueryResult<GetSettingOptionsForPorschePaymentModuleQuery, GetSettingOptionsForPorschePaymentModuleQueryVariables>;
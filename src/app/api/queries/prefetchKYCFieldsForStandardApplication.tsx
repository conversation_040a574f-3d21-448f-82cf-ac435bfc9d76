import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from '../fragments/KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from '../fragments/KYCFieldSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type PrefetchKycFieldsForStandardApplicationQueryVariables = SchemaTypes.Exact<{
  applicationModuleId: SchemaTypes.Scalars['ObjectID']['input'];
  configuration: SchemaTypes.StandardApplicationConfiguration;
}>;


export type PrefetchKycFieldsForStandardApplicationQuery = (
  { __typename: 'Query' }
  & { applicantKyc: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, corporateKyc: Array<(
    { __typename: 'KY<PERSON>ield' }
    & KycFieldSpecsFragment
  )>, guarantorKyc: Array<(
    { __typename: 'KY<PERSON>ield' }
    & KycFieldSpecsFragment
  )>, shareKyc: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )> }
);


export const PrefetchKycFieldsForStandardApplicationDocument = /*#__PURE__*/ gql`
    query prefetchKYCFieldsForStandardApplication($applicationModuleId: ObjectID!, $configuration: StandardApplicationConfiguration!) {
  applicantKyc: prefetchKYCFieldsForStandardApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "local"
    placement: "kyc"
  ) {
    ...KYCFieldSpecs
  }
  corporateKyc: prefetchKYCFieldsForStandardApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "corporate"
    placement: "kyc"
  ) {
    ...KYCFieldSpecs
  }
  guarantorKyc: prefetchKYCFieldsForStandardApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "guarantor"
    placement: "kyc"
  ) {
    ...KYCFieldSpecs
  }
  shareKyc: prefetchKYCFieldsForStandardApplication(
    applicationModuleId: $applicationModuleId
    configuration: $configuration
    customerKind: "local"
    placement: "share"
  ) {
    ...KYCFieldSpecs
  }
}
    ${KycFieldSpecsFragmentDoc}`;

/**
 * __usePrefetchKycFieldsForStandardApplicationQuery__
 *
 * To run a query within a React component, call `usePrefetchKycFieldsForStandardApplicationQuery` and pass it any options that fit your needs.
 * When your component renders, `usePrefetchKycFieldsForStandardApplicationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePrefetchKycFieldsForStandardApplicationQuery({
 *   variables: {
 *      applicationModuleId: // value for 'applicationModuleId'
 *      configuration: // value for 'configuration'
 *   },
 * });
 */
export function usePrefetchKycFieldsForStandardApplicationQuery(baseOptions: Apollo.QueryHookOptions<PrefetchKycFieldsForStandardApplicationQuery, PrefetchKycFieldsForStandardApplicationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PrefetchKycFieldsForStandardApplicationQuery, PrefetchKycFieldsForStandardApplicationQueryVariables>(PrefetchKycFieldsForStandardApplicationDocument, options);
      }
export function usePrefetchKycFieldsForStandardApplicationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PrefetchKycFieldsForStandardApplicationQuery, PrefetchKycFieldsForStandardApplicationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PrefetchKycFieldsForStandardApplicationQuery, PrefetchKycFieldsForStandardApplicationQueryVariables>(PrefetchKycFieldsForStandardApplicationDocument, options);
        }
export type PrefetchKycFieldsForStandardApplicationQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForStandardApplicationQuery>;
export type PrefetchKycFieldsForStandardApplicationLazyQueryHookResult = ReturnType<typeof usePrefetchKycFieldsForStandardApplicationLazyQuery>;
export type PrefetchKycFieldsForStandardApplicationQueryResult = Apollo.QueryResult<PrefetchKycFieldsForStandardApplicationQuery, PrefetchKycFieldsForStandardApplicationQueryVariables>;
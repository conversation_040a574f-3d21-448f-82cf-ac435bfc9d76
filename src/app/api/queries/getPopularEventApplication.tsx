import type * as SchemaTypes from '../types';

import type { StatisticNameCounterFragment } from '../fragments/statisticNameCounter';
import type { StatisticNameCountResultFragment } from '../fragments/statisticNameCountResult';
import { gql } from '@apollo/client';
import { StatisticNameCounterFragmentDoc } from '../fragments/statisticNameCounter';
import { StatisticNameCountResultFragmentDoc } from '../fragments/statisticNameCountResult';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetPopularEventApplicationQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.StatisticFilterPayload>;
}>;


export type GetPopularEventApplicationQuery = (
  { __typename: 'Query' }
  & { metrics?: SchemaTypes.Maybe<(
    { __typename: 'StatisticMonthlyResult' }
    & StatisticNameCounterFragment
  )> }
);


export const GetPopularEventApplicationDocument = /*#__PURE__*/ gql`
    query getPopularEventApplication($filter: StatisticFilterPayload) {
  metrics: getPopularEvent(filter: $filter) {
    ...statisticNameCounter
  }
}
    ${StatisticNameCounterFragmentDoc}
${StatisticNameCountResultFragmentDoc}`;

/**
 * __useGetPopularEventApplicationQuery__
 *
 * To run a query within a React component, call `useGetPopularEventApplicationQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPopularEventApplicationQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPopularEventApplicationQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetPopularEventApplicationQuery(baseOptions?: Apollo.QueryHookOptions<GetPopularEventApplicationQuery, GetPopularEventApplicationQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPopularEventApplicationQuery, GetPopularEventApplicationQueryVariables>(GetPopularEventApplicationDocument, options);
      }
export function useGetPopularEventApplicationLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPopularEventApplicationQuery, GetPopularEventApplicationQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPopularEventApplicationQuery, GetPopularEventApplicationQueryVariables>(GetPopularEventApplicationDocument, options);
        }
export type GetPopularEventApplicationQueryHookResult = ReturnType<typeof useGetPopularEventApplicationQuery>;
export type GetPopularEventApplicationLazyQueryHookResult = ReturnType<typeof useGetPopularEventApplicationLazyQuery>;
export type GetPopularEventApplicationQueryResult = Apollo.QueryResult<GetPopularEventApplicationQuery, GetPopularEventApplicationQueryVariables>;
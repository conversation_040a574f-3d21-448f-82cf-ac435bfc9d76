import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type RetrieveNamespacesQueryVariables = SchemaTypes.Exact<{ [key: string]: never; }>;


export type RetrieveNamespacesQuery = (
  { __typename: 'Query' }
  & { namespaces: SchemaTypes.Query['retrieveNamespaces'] }
);


export const RetrieveNamespacesDocument = /*#__PURE__*/ gql`
    query retrieveNamespaces {
  namespaces: retrieveNamespaces
}
    `;

/**
 * __useRetrieveNamespacesQuery__
 *
 * To run a query within a React component, call `useRetrieveNamespacesQuery` and pass it any options that fit your needs.
 * When your component renders, `useRetrieveNamespacesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useRetrieveNamespacesQuery({
 *   variables: {
 *   },
 * });
 */
export function useRetrieveNamespacesQuery(baseOptions?: Apollo.QueryHookOptions<RetrieveNamespacesQuery, RetrieveNamespacesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<RetrieveNamespacesQuery, RetrieveNamespacesQueryVariables>(RetrieveNamespacesDocument, options);
      }
export function useRetrieveNamespacesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<RetrieveNamespacesQuery, RetrieveNamespacesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<RetrieveNamespacesQuery, RetrieveNamespacesQueryVariables>(RetrieveNamespacesDocument, options);
        }
export type RetrieveNamespacesQueryHookResult = ReturnType<typeof useRetrieveNamespacesQuery>;
export type RetrieveNamespacesLazyQueryHookResult = ReturnType<typeof useRetrieveNamespacesLazyQuery>;
export type RetrieveNamespacesQueryResult = Apollo.QueryResult<RetrieveNamespacesQuery, RetrieveNamespacesQueryVariables>;
import type * as SchemaTypes from '../types';

import type { VehiclesListByParametersSpecsFragment } from '../fragments/VehiclesListByParameters';
import type { LocalVariantSpecsFragment } from '../fragments/LocalVariantSpecs';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { LocalModelSpecsFragment } from '../fragments/LocalModelSpecs';
import type { LocalMakeSpecsFragment } from '../fragments/LocalMakeSpecs';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { FinderVehicleCalculatorDataFragment } from '../fragments/FinderVehicleCalculatorData';
import { gql } from '@apollo/client';
import { VehiclesListByParametersSpecsFragmentDoc } from '../fragments/VehiclesListByParameters';
import { LocalVariantSpecsFragmentDoc } from '../fragments/LocalVariantSpecs';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { LocalModelSpecsFragmentDoc } from '../fragments/LocalModelSpecs';
import { LocalMakeSpecsFragmentDoc } from '../fragments/LocalMakeSpecs';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { FinderVehicleCalculatorDataFragmentDoc } from '../fragments/FinderVehicleCalculatorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListVehiclesWithParametersQueryVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
  referenceParameters: SchemaTypes.VehicleReferenceParametersPayload;
  variantSuiteIds?: SchemaTypes.InputMaybe<Array<SchemaTypes.Scalars['ObjectID']['input']> | SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type ListVehiclesWithParametersQuery = (
  { __typename: 'Query' }
  & { listVehiclesWithParameters: (
    { __typename: 'VehiclesListByParameters' }
    & VehiclesListByParametersSpecsFragment
  ) }
);


export const ListVehiclesWithParametersDocument = /*#__PURE__*/ gql`
    query listVehiclesWithParameters($companyId: ObjectID!, $referenceParameters: VehicleReferenceParametersPayload!, $variantSuiteIds: [ObjectID!]) {
  listVehiclesWithParameters(
    companyId: $companyId
    referenceParameters: $referenceParameters
    variantSuiteIds: $variantSuiteIds
  ) {
    ...VehiclesListByParametersSpecs
  }
}
    ${VehiclesListByParametersSpecsFragmentDoc}
${LocalVariantSpecsFragmentDoc}
${TranslatedStringDataFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${LocalModelSpecsFragmentDoc}
${LocalMakeSpecsFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}
${FinderVehicleCalculatorDataFragmentDoc}`;

/**
 * __useListVehiclesWithParametersQuery__
 *
 * To run a query within a React component, call `useListVehiclesWithParametersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListVehiclesWithParametersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListVehiclesWithParametersQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *      referenceParameters: // value for 'referenceParameters'
 *      variantSuiteIds: // value for 'variantSuiteIds'
 *   },
 * });
 */
export function useListVehiclesWithParametersQuery(baseOptions: Apollo.QueryHookOptions<ListVehiclesWithParametersQuery, ListVehiclesWithParametersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListVehiclesWithParametersQuery, ListVehiclesWithParametersQueryVariables>(ListVehiclesWithParametersDocument, options);
      }
export function useListVehiclesWithParametersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListVehiclesWithParametersQuery, ListVehiclesWithParametersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListVehiclesWithParametersQuery, ListVehiclesWithParametersQueryVariables>(ListVehiclesWithParametersDocument, options);
        }
export type ListVehiclesWithParametersQueryHookResult = ReturnType<typeof useListVehiclesWithParametersQuery>;
export type ListVehiclesWithParametersLazyQueryHookResult = ReturnType<typeof useListVehiclesWithParametersLazyQuery>;
export type ListVehiclesWithParametersQueryResult = Apollo.QueryResult<ListVehiclesWithParametersQuery, ListVehiclesWithParametersQueryVariables>;
import type * as SchemaTypes from '../types';

import type { DealerListDataFragment } from '../fragments/DealerListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { DealerContactFragmentFragment } from '../fragments/DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from '../fragments/DealerSocialMediaFragment';
import type { UploadFileWithPreviewFormDataFragment } from '../fragments/UploadFileWithPreviewFormData';
import type { DealerIntegrationDetailsFragmentFragment } from '../fragments/DealerIntegrationDetailsFragment';
import { gql } from '@apollo/client';
import { DealerListDataFragmentDoc } from '../fragments/DealerListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { DealerContactFragmentFragmentDoc } from '../fragments/DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from '../fragments/DealerSocialMediaFragment';
import { UploadFileWithPreviewFormDataFragmentDoc } from '../fragments/UploadFileWithPreviewFormData';
import { DealerIntegrationDetailsFragmentFragmentDoc } from '../fragments/DealerIntegrationDetailsFragment';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListDealersQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.DealerFilteringRule>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.DealerSortingRule>;
}>;


export type ListDealersQuery = (
  { __typename: 'Query' }
  & { page: (
    { __typename: 'PaginatedDealers' }
    & Pick<SchemaTypes.PaginatedDealers, 'count'>
    & { items: Array<(
      { __typename: 'Dealer' }
      & DealerListDataFragment
    )> }
  ) }
);


export const ListDealersDocument = /*#__PURE__*/ gql`
    query listDealers($pagination: Pagination, $filter: DealerFilteringRule, $sort: DealerSortingRule) {
  page: listDealers(pagination: $pagination, filter: $filter, sort: $sort) {
    count
    items {
      ...DealerListData
    }
  }
}
    ${DealerListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${DealerContactFragmentFragmentDoc}
${DealerSocialMediaFragmentFragmentDoc}
${UploadFileWithPreviewFormDataFragmentDoc}
${DealerIntegrationDetailsFragmentFragmentDoc}`;

/**
 * __useListDealersQuery__
 *
 * To run a query within a React component, call `useListDealersQuery` and pass it any options that fit your needs.
 * When your component renders, `useListDealersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListDealersQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      filter: // value for 'filter'
 *      sort: // value for 'sort'
 *   },
 * });
 */
export function useListDealersQuery(baseOptions?: Apollo.QueryHookOptions<ListDealersQuery, ListDealersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListDealersQuery, ListDealersQueryVariables>(ListDealersDocument, options);
      }
export function useListDealersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListDealersQuery, ListDealersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListDealersQuery, ListDealersQueryVariables>(ListDealersDocument, options);
        }
export type ListDealersQueryHookResult = ReturnType<typeof useListDealersQuery>;
export type ListDealersLazyQueryHookResult = ReturnType<typeof useListDealersLazyQuery>;
export type ListDealersQueryResult = Apollo.QueryResult<ListDealersQuery, ListDealersQueryVariables>;
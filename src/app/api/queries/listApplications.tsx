import type * as SchemaTypes from '../types';

import type { ApplicationListData_ConfiguratorApplication_Fragment, ApplicationListData_EventApplication_Fragment, ApplicationListData_FinderApplication_Fragment, ApplicationListData_LaunchpadApplication_Fragment, ApplicationListData_MobilityApplication_Fragment, ApplicationListData_SalesOfferApplication_Fragment, ApplicationListData_StandardApplication_Fragment } from '../fragments/ApplicationListData';
import type { ApplicationStageData_ConfiguratorApplication_Fragment, ApplicationStageData_EventApplication_Fragment, ApplicationStageData_FinderApplication_Fragment, ApplicationStageData_LaunchpadApplication_Fragment, ApplicationStageData_MobilityApplication_Fragment, ApplicationStageData_SalesOfferApplication_Fragment, ApplicationStageData_StandardApplication_Fragment } from '../fragments/ApplicationStageData';
import type { ApplicationStageUserData_ConfiguratorApplication_Fragment, ApplicationStageUserData_EventApplication_Fragment, ApplicationStageUserData_FinderApplication_Fragment, ApplicationStageUserData_LaunchpadApplication_Fragment, ApplicationStageUserData_MobilityApplication_Fragment, ApplicationStageUserData_SalesOfferApplication_Fragment, ApplicationStageUserData_StandardApplication_Fragment } from '../fragments/ApplicationStageUserData';
import type { UsersOptionsDataFragment } from '../fragments/UsersOptionsData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { PeriodDataFragment } from '../fragments/PeriodData';
import type { MobilityBookingLocationHomeDataFragment } from '../fragments/MobilityBookingLocationHomeData';
import type { MobilityBookingLocationPickupDataFragment } from '../fragments/MobilityBookingLocationPickupData';
import type { CustomerSpecs_CorporateCustomer_Fragment, CustomerSpecs_Guarantor_Fragment, CustomerSpecs_LocalCustomer_Fragment } from '../fragments/CustomerSpecs';
import type { LocalCustomerDataFragment } from '../fragments/LocalCustomerData';
import type { LocalCustomerFieldData_LocalCustomerArrayStringField_Fragment, LocalCustomerFieldData_LocalCustomerDateField_Fragment, LocalCustomerFieldData_LocalCustomerDrivingLicenseField_Fragment, LocalCustomerFieldData_LocalCustomerNumberField_Fragment, LocalCustomerFieldData_LocalCustomerPhoneField_Fragment, LocalCustomerFieldData_LocalCustomerReferenceDetailSetField_Fragment, LocalCustomerFieldData_LocalCustomerSalaryTransferredBankSetField_Fragment, LocalCustomerFieldData_LocalCustomerStringDescriptionField_Fragment, LocalCustomerFieldData_LocalCustomerStringField_Fragment, LocalCustomerFieldData_LocalCustomerUaeIdentitySetField_Fragment, LocalCustomerFieldData_LocalCustomerUploadsField_Fragment, LocalCustomerFieldData_LocalCustomerVerifiedPhoneField_Fragment } from '../fragments/LocalCustomerFieldData';
import type { CorporateCustomerDataFragment } from '../fragments/CorporateCustomerData';
import type { GuarantorDataFragment } from '../fragments/GuarantorData';
import type { TradeInVehicleDataFragment } from '../fragments/TradeInVehicleData';
import { gql } from '@apollo/client';
import { ApplicationListDataFragmentDoc } from '../fragments/ApplicationListData';
import { ApplicationStageDataFragmentDoc } from '../fragments/ApplicationStageData';
import { ApplicationStageUserDataFragmentDoc } from '../fragments/ApplicationStageUserData';
import { UsersOptionsDataFragmentDoc } from '../fragments/UsersOptionsData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { PeriodDataFragmentDoc } from '../fragments/PeriodData';
import { MobilityBookingLocationHomeDataFragmentDoc } from '../fragments/MobilityBookingLocationHomeData';
import { MobilityBookingLocationPickupDataFragmentDoc } from '../fragments/MobilityBookingLocationPickupData';
import { CustomerSpecsFragmentDoc } from '../fragments/CustomerSpecs';
import { LocalCustomerDataFragmentDoc } from '../fragments/LocalCustomerData';
import { LocalCustomerFieldDataFragmentDoc } from '../fragments/LocalCustomerFieldData';
import { CorporateCustomerDataFragmentDoc } from '../fragments/CorporateCustomerData';
import { GuarantorDataFragmentDoc } from '../fragments/GuarantorData';
import { TradeInVehicleDataFragmentDoc } from '../fragments/TradeInVehicleData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListApplicationsQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.ApplicationSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ApplicationFilteringRule>;
}>;


export type ListApplicationsQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedApplications' }
    & Pick<SchemaTypes.PaginatedApplications, 'count'>
    & { items: Array<(
      { __typename: 'ConfiguratorApplication' }
      & ApplicationListData_ConfiguratorApplication_Fragment
    ) | (
      { __typename: 'EventApplication' }
      & ApplicationListData_EventApplication_Fragment
    ) | (
      { __typename: 'FinderApplication' }
      & ApplicationListData_FinderApplication_Fragment
    ) | (
      { __typename: 'LaunchpadApplication' }
      & ApplicationListData_LaunchpadApplication_Fragment
    ) | (
      { __typename: 'MobilityApplication' }
      & ApplicationListData_MobilityApplication_Fragment
    ) | (
      { __typename: 'SalesOfferApplication' }
      & ApplicationListData_SalesOfferApplication_Fragment
    ) | (
      { __typename: 'StandardApplication' }
      & ApplicationListData_StandardApplication_Fragment
    )> }
  ) }
);


export const ListApplicationsDocument = /*#__PURE__*/ gql`
    query listApplications($pagination: Pagination!, $sort: ApplicationSortingRule, $filter: ApplicationFilteringRule) {
  list: listApplications(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...ApplicationListData
    }
  }
}
    ${ApplicationListDataFragmentDoc}
${ApplicationStageDataFragmentDoc}
${ApplicationStageUserDataFragmentDoc}
${UsersOptionsDataFragmentDoc}
${AuthorDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${PeriodDataFragmentDoc}
${MobilityBookingLocationHomeDataFragmentDoc}
${MobilityBookingLocationPickupDataFragmentDoc}
${CustomerSpecsFragmentDoc}
${LocalCustomerDataFragmentDoc}
${LocalCustomerFieldDataFragmentDoc}
${CorporateCustomerDataFragmentDoc}
${GuarantorDataFragmentDoc}
${TradeInVehicleDataFragmentDoc}`;

/**
 * __useListApplicationsQuery__
 *
 * To run a query within a React component, call `useListApplicationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListApplicationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListApplicationsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListApplicationsQuery(baseOptions: Apollo.QueryHookOptions<ListApplicationsQuery, ListApplicationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListApplicationsQuery, ListApplicationsQueryVariables>(ListApplicationsDocument, options);
      }
export function useListApplicationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListApplicationsQuery, ListApplicationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListApplicationsQuery, ListApplicationsQueryVariables>(ListApplicationsDocument, options);
        }
export type ListApplicationsQueryHookResult = ReturnType<typeof useListApplicationsQuery>;
export type ListApplicationsLazyQueryHookResult = ReturnType<typeof useListApplicationsLazyQuery>;
export type ListApplicationsQueryResult = Apollo.QueryResult<ListApplicationsQuery, ListApplicationsQueryVariables>;
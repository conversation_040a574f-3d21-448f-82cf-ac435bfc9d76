import type * as SchemaTypes from '../types';

import type { LanguagePackListDataFragment } from '../fragments/LanguagePackListData';
import { gql } from '@apollo/client';
import { LanguagePackListDataFragmentDoc } from '../fragments/LanguagePackListData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ListLanguagePacksQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LanguagePackSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LanguagePackFilteringRule>;
}>;


export type ListLanguagePacksQuery = (
  { __typename: 'Query' }
  & { languages: (
    { __typename: 'PaginatedLanguagePacks' }
    & Pick<SchemaTypes.PaginatedLanguagePacks, 'count'>
    & { items: Array<(
      { __typename: 'LanguagePack' }
      & LanguagePackListDataFragment
    )> }
  ) }
);


export const ListLanguagePacksDocument = /*#__PURE__*/ gql`
    query listLanguagePacks($pagination: Pagination!, $sort: LanguagePackSortingRule, $filter: LanguagePackFilteringRule) {
  languages: listLanguagePacks(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    count
    items {
      ...LanguagePackListData
    }
  }
}
    ${LanguagePackListDataFragmentDoc}`;

/**
 * __useListLanguagePacksQuery__
 *
 * To run a query within a React component, call `useListLanguagePacksQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLanguagePacksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLanguagePacksQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListLanguagePacksQuery(baseOptions: Apollo.QueryHookOptions<ListLanguagePacksQuery, ListLanguagePacksQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLanguagePacksQuery, ListLanguagePacksQueryVariables>(ListLanguagePacksDocument, options);
      }
export function useListLanguagePacksLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLanguagePacksQuery, ListLanguagePacksQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLanguagePacksQuery, ListLanguagePacksQueryVariables>(ListLanguagePacksDocument, options);
        }
export type ListLanguagePacksQueryHookResult = ReturnType<typeof useListLanguagePacksQuery>;
export type ListLanguagePacksLazyQueryHookResult = ReturnType<typeof useListLanguagePacksLazyQuery>;
export type ListLanguagePacksQueryResult = Apollo.QueryResult<ListLanguagePacksQuery, ListLanguagePacksQueryVariables>;
import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetSalesControlBoardFilterOptionsQueryVariables = SchemaTypes.Exact<{
  dealerId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetSalesControlBoardFilterOptionsQuery = (
  { __typename: 'Query' }
  & { getSalesControlBoardFilterOptions: (
    { __typename: 'SalesControlBoardFilterOptions' }
    & { vehicleModelOptions: Array<(
      { __typename: 'VehicleModelOption' }
      & Pick<SchemaTypes.VehicleModelOption, 'value' | 'label'>
    )>, salesConsultantOptions: Array<(
      { __typename: 'SalesConsultantOption' }
      & Pick<SchemaTypes.SalesConsultantOption, 'value' | 'label'>
    )> }
  ) }
);


export const GetSalesControlBoardFilterOptionsDocument = /*#__PURE__*/ gql`
    query getSalesControlBoardFilterOptions($dealerId: ObjectID!) {
  getSalesControlBoardFilterOptions(dealerId: $dealerId) {
    vehicleModelOptions {
      value
      label
    }
    salesConsultantOptions {
      value
      label
    }
  }
}
    `;

/**
 * __useGetSalesControlBoardFilterOptionsQuery__
 *
 * To run a query within a React component, call `useGetSalesControlBoardFilterOptionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSalesControlBoardFilterOptionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSalesControlBoardFilterOptionsQuery({
 *   variables: {
 *      dealerId: // value for 'dealerId'
 *   },
 * });
 */
export function useGetSalesControlBoardFilterOptionsQuery(baseOptions: Apollo.QueryHookOptions<GetSalesControlBoardFilterOptionsQuery, GetSalesControlBoardFilterOptionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSalesControlBoardFilterOptionsQuery, GetSalesControlBoardFilterOptionsQueryVariables>(GetSalesControlBoardFilterOptionsDocument, options);
      }
export function useGetSalesControlBoardFilterOptionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSalesControlBoardFilterOptionsQuery, GetSalesControlBoardFilterOptionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSalesControlBoardFilterOptionsQuery, GetSalesControlBoardFilterOptionsQueryVariables>(GetSalesControlBoardFilterOptionsDocument, options);
        }
export type GetSalesControlBoardFilterOptionsQueryHookResult = ReturnType<typeof useGetSalesControlBoardFilterOptionsQuery>;
export type GetSalesControlBoardFilterOptionsLazyQueryHookResult = ReturnType<typeof useGetSalesControlBoardFilterOptionsLazyQuery>;
export type GetSalesControlBoardFilterOptionsQueryResult = Apollo.QueryResult<GetSalesControlBoardFilterOptionsQuery, GetSalesControlBoardFilterOptionsQueryVariables>;
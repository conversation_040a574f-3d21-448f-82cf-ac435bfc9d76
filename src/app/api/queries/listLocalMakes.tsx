import type * as SchemaTypes from '../types';

import type { LocalMakesListDataFragment } from '../fragments/LocalMakesListData';
import type { TranslatedStringDataFragment } from '../fragments/TranslatedStringData';
import type { AdvancedVersioningDataFragment } from '../fragments/AdvancedVersioningData';
import type { AuthorData_CorporateCustomer_Fragment, AuthorData_ExternalBank_Fragment, AuthorData_Guarantor_Fragment, AuthorData_LocalCustomer_Fragment, AuthorData_PorscheRetain_Fragment, AuthorData_Salesforce_Fragment, AuthorData_SystemBank_Fragment, AuthorData_User_Fragment } from '../fragments/AuthorData';
import { gql } from '@apollo/client';
import { LocalMakesListDataFragmentDoc } from '../fragments/LocalMakesListData';
import { TranslatedStringDataFragmentDoc } from '../fragments/TranslatedStringData';
import { AdvancedVersioningDataFragmentDoc } from '../fragments/AdvancedVersioningData';
import { AuthorDataFragmentDoc } from '../fragments/AuthorData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListLocalMakesQueryVariables = SchemaTypes.Exact<{
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.LocalMakeSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.LocalMakeFilteringRule>;
}>;


export type ListLocalMakesQuery = (
  { __typename: 'Query' }
  & { list: (
    { __typename: 'PaginatedLocalMakes' }
    & Pick<SchemaTypes.PaginatedLocalMakes, 'count'>
    & { items: Array<(
      { __typename: 'LocalMake' }
      & LocalMakesListDataFragment
    )> }
  ) }
);


export const ListLocalMakesDocument = /*#__PURE__*/ gql`
    query listLocalMakes($pagination: Pagination, $sort: LocalMakeSortingRule, $filter: LocalMakeFilteringRule) {
  list: listLocalMakes(pagination: $pagination, sort: $sort, filter: $filter) {
    count
    items {
      ...LocalMakesListData
    }
  }
}
    ${LocalMakesListDataFragmentDoc}
${TranslatedStringDataFragmentDoc}
${AdvancedVersioningDataFragmentDoc}
${AuthorDataFragmentDoc}`;

/**
 * __useListLocalMakesQuery__
 *
 * To run a query within a React component, call `useListLocalMakesQuery` and pass it any options that fit your needs.
 * When your component renders, `useListLocalMakesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListLocalMakesQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListLocalMakesQuery(baseOptions?: Apollo.QueryHookOptions<ListLocalMakesQuery, ListLocalMakesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListLocalMakesQuery, ListLocalMakesQueryVariables>(ListLocalMakesDocument, options);
      }
export function useListLocalMakesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListLocalMakesQuery, ListLocalMakesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListLocalMakesQuery, ListLocalMakesQueryVariables>(ListLocalMakesDocument, options);
        }
export type ListLocalMakesQueryHookResult = ReturnType<typeof useListLocalMakesQuery>;
export type ListLocalMakesLazyQueryHookResult = ReturnType<typeof useListLocalMakesLazyQuery>;
export type ListLocalMakesQueryResult = Apollo.QueryResult<ListLocalMakesQuery, ListLocalMakesQueryVariables>;
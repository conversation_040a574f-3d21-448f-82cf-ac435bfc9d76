query prefetchKYCFieldsForEvent(
    $eventId: ObjectID
    $urlSlug: String
    $configuration: EventApplicationConfigurationPayload!
    $dealerId: ObjectID
    $eventModuleId: ObjectID
) {
    corporateKYC: prefetchKYCFieldsForEvent(
        eventId: $eventId
        urlSlug: $urlSlug
        configuration: $configuration
        customerKind: "corporate"
        dealerId: $dealerId
        eventModuleId: $eventModuleId
    ) {
        ...KYCFieldSpecs
    }

    applicantKYC: prefetchKYCFieldsForEvent(
        eventId: $eventId
        urlSlug: $urlSlug
        configuration: $configuration
        customerKind: "local"
        dealerId: $dealerId
        eventModuleId: $eventModuleId
    ) {
        ...KYCFieldSpecs
    }

    guarantorKYC: prefetchKYCFieldsForEvent(
        eventId: $eventId
        urlSlug: $urlSlug
        configuration: $configuration
        customerKind: "guarantor"
        dealerId: $dealerId
        eventModuleId: $eventModuleId
    ) {
        ...KYCFieldSpecs
    }
}

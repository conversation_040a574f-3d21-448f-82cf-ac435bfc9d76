import type * as SchemaTypes from '../types';

import type { ConditionDetailSpecsFragment } from '../fragments/ConditionDetailSpecs';
import { gql } from '@apollo/client';
import { ConditionDetailSpecsFragmentDoc } from '../fragments/ConditionDetailSpecs';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetConditionDetailQueryVariables = SchemaTypes.Exact<{
  id: SchemaTypes.Scalars['ObjectID']['input'];
  kind: SchemaTypes.ConditionType;
}>;


export type GetConditionDetailQuery = (
  { __typename: 'Query' }
  & { conditionDetail: (
    { __typename: 'ConditionDetail' }
    & ConditionDetailSpecsFragment
  ) }
);


export const GetConditionDetailDocument = /*#__PURE__*/ gql`
    query getConditionDetail($id: ObjectID!, $kind: ConditionType!) {
  conditionDetail: getConditionDetail(id: $id, kind: $kind) {
    ...ConditionDetailSpecs
  }
}
    ${ConditionDetailSpecsFragmentDoc}`;

/**
 * __useGetConditionDetailQuery__
 *
 * To run a query within a React component, call `useGetConditionDetailQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetConditionDetailQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetConditionDetailQuery({
 *   variables: {
 *      id: // value for 'id'
 *      kind: // value for 'kind'
 *   },
 * });
 */
export function useGetConditionDetailQuery(baseOptions: Apollo.QueryHookOptions<GetConditionDetailQuery, GetConditionDetailQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetConditionDetailQuery, GetConditionDetailQueryVariables>(GetConditionDetailDocument, options);
      }
export function useGetConditionDetailLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetConditionDetailQuery, GetConditionDetailQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetConditionDetailQuery, GetConditionDetailQueryVariables>(GetConditionDetailDocument, options);
        }
export type GetConditionDetailQueryHookResult = ReturnType<typeof useGetConditionDetailQuery>;
export type GetConditionDetailLazyQueryHookResult = ReturnType<typeof useGetConditionDetailLazyQuery>;
export type GetConditionDetailQueryResult = Apollo.QueryResult<GetConditionDetailQuery, GetConditionDetailQueryVariables>;
# Be careful with using fragment
# As it reach the graphql depth limit
# Especially for journey or application fragments
# You can't find it in log, because it will just return {} and response code 500

# To prevent that happen again,
# If you need to use all of the data
# Get it using separately if possible

query retrieveLink($id: String!) {
    retrieveLink(id: $id) {
        __typename

        ... on ResetPasswordLink {
            token
            verified
            email
        }

        ... on CreateNewUserLink {
            token
            verified
        }

        ... on MyInfoCallbackLink {
            linkId
            applicationJourney {
                token

                application {
                    ... on StandardApplication {
                        withCustomerDevice
                    }
                    ... on ConfiguratorApplication {
                        withCustomerDevice
                    }
                    ... on EventApplication {
                        withCustomerDevice
                    }
                    ... on FinderApplication {
                        vehicle {
                            ... on FinderVehicle {
                                listing {
                                    id
                                }
                            }
                        }
                        withCustomerDevice
                    }
                }
            }

            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }
        }

        ... on NamirialSigningLink {
            token
            path
        }

        ... on AdyenRedirectionLink {
            token
            path
        }

        ... on ConfiguratorApplicationLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            application<PERSON>ourney {
                token

                application {
                    ...ApplicationStageData

                    ... on ConfiguratorApplication {
                        draftFlow {
                            isReceived
                        }
                    }
                }
            }

            urlIdentifier
            modelConfiguratorId
            variantConfiguratorId
        }

        ... on StandardApplicationLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            applicationJourney {
                token
            }
        }

        ... on EventApplicationLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            applicationJourney {
                token
            }
        }

        ... on FinderApplicationLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            applicationJourney {
                token

                application {
                    ... on FinderApplication {
                        vehicle {
                            ... on FinderVehicle {
                                listing {
                                    id
                                }
                            }
                        }
                    }
                }
            }
        }

        ... on VerifyEmailUpdateLink {
            token
            email
            userId
        }

        ... on ProceedWithCustomerLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            secret
            applicationKind
        }

        ... on PorschePaymentRedirectionLink {
            token
            path
        }

        ... on FiservPaymentRedirectionLink {
            token
            path
        }

        ... on PayGatePaymentRedirectionLink {
            token
            path
        }

        ... on TtbPaymentRedirectionLink {
            token
            path
        }

        ... on MobilityApplicationAmendmentLink {
            token
            path
            application {
                ...MobilityApplicationExpired
            }
        }

        ... on MobilityApplicationCancellationLink {
            token
            path
            application {
                ...MobilityApplicationExpired
            }
        }

        ... on TestDriveProcessRedirectionLink {
            applicationJourney {
                token
                application {
                    versioning {
                        suiteId
                    }

                    ... on EventApplication {
                        event {
                            urlSlug
                        }
                        router {
                            id
                            pathname
                            hostname
                        }

                        endpoint {
                            id
                            pathname
                        }
                    }

                    ... on FinderApplication {
                        router {
                            id
                            pathname
                            hostname
                        }

                        endpoint {
                            id
                            pathname
                        }

                        vehicle {
                            ...FinderVehicleSpecs
                        }
                    }

                    ... on StandardApplication {
                        router {
                            id
                            pathname
                            hostname
                        }

                        endpoint {
                            id
                            pathname
                        }
                    }

                    ... on LaunchpadApplication {
                        router {
                            id
                            pathname
                            hostname
                        }

                        endpoint {
                            id
                            pathname
                        }
                    }

                    ... on ConfiguratorApplication {
                        router {
                            id
                            pathname
                            hostname
                        }

                        endpoint {
                            id
                            pathname
                        }
                    }
                }
            }
        }

        ... on CTSFinderRedirectionLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            type
            vehicleId

            applicationJourney {
                token

                application {
                    ... on FinderApplication {
                        vehicle {
                            ... on FinderVehicle {
                                listing {
                                    id
                                }
                            }
                        }
                    }
                }
            }

            ctsSetting {
                ...CtsModuleSettingData
            }
        }

        ... on GiftVoucherAdyenRedirectionLink {
            token
            path
        }

        ... on GiftVoucherTtbPaymentRedirectionLink {
            token
            path
        }

        ... on GiftVoucherPorschePaymentRedirectionLink {
            token
            path
        }

        ... on GiftVoucherPayGatePaymentRedirectionLink {
            token
            path
        }

        ... on GiftVoucherFiservPaymentRedirectionLink {
            token
            path
        }

        ... on ApplyNewRedirectionLink {
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            applicationJourney {
                token
                application {
                    ... on StandardApplication {
                        vehicleId
                    }

                    ... on FinderApplication {
                        vehicle {
                            ... on FinderVehicle {
                                listing {
                                    id
                                }
                            }
                        }
                    }

                    ... on ConfiguratorApplication {
                        configurator {
                            modelConfigurator {
                                ... on ModelConfigurator {
                                    urlIdentifier
                                }
                            }
                        }
                    }
                }
            }
        }

        ... on PorscheIdCallbackLink {
            linkId
            applicationJourney {
                token

                application {
                    ... on ConfiguratorApplication {
                        withCustomerDevice
                    }
                    ... on EventApplication {
                        withCustomerDevice
                    }
                    ... on FinderApplication {
                        vehicle {
                            ... on FinderVehicle {
                                listing {
                                    id
                                }
                            }
                        }
                        withCustomerDevice
                    }
                }
            }

            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }
        }

        ... on SendSalesOfferLink {
            linkId
            secret
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            featureKinds
            
            isVsaSigningForSalesManager
        }

        ... on SalesOfferNamirialSigningLink {
            token
            router {
                id
                pathname
                hostname
            }

            endpoint {
                id
                pathname
            }

            isVsaSigningForSalesManager
        }
    }
}

query listFinderVehiclesForCalculator(
    $pagination: Pagination
    $sort: FinderVehicleSortingRule
    $filter: FilterVehicleFilteringRule
    $applicationModuleIds: [ObjectID!]
    $dealerId: ObjectID
) {
    vehicles: listFinderVehicles(pagination: $pagination, sort: $sort, filter: $filter, dealerId: $dealerId, applicationModuleIds: $applicationModuleIds) {
        items {
            ...FinderVehicleCalculatorData
        }
    }
}

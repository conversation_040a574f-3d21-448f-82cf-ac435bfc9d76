import type * as SchemaTypes from '../types';

import type { RouterListWithEndpointsDataFragment } from '../fragments/RouterListWithEndpoints';
import { gql } from '@apollo/client';
import { RouterListWithEndpointsDataFragmentDoc } from '../fragments/RouterListWithEndpoints';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetRouterEndpointListQueryVariables = SchemaTypes.Exact<{
  companyId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type GetRouterEndpointListQuery = (
  { __typename: 'Query' }
  & { list: Array<(
    { __typename: 'Router' }
    & RouterListWithEndpointsDataFragment
  )> }
);


export const GetRouterEndpointListDocument = /*#__PURE__*/ gql`
    query getRouterEndpointList($companyId: ObjectID!) {
  list: getRouterEndpointList(companyId: $companyId) {
    ...RouterListWithEndpointsData
  }
}
    ${RouterListWithEndpointsDataFragmentDoc}`;

/**
 * __useGetRouterEndpointListQuery__
 *
 * To run a query within a React component, call `useGetRouterEndpointListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRouterEndpointListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRouterEndpointListQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useGetRouterEndpointListQuery(baseOptions: Apollo.QueryHookOptions<GetRouterEndpointListQuery, GetRouterEndpointListQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRouterEndpointListQuery, GetRouterEndpointListQueryVariables>(GetRouterEndpointListDocument, options);
      }
export function useGetRouterEndpointListLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRouterEndpointListQuery, GetRouterEndpointListQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRouterEndpointListQuery, GetRouterEndpointListQueryVariables>(GetRouterEndpointListDocument, options);
        }
export type GetRouterEndpointListQueryHookResult = ReturnType<typeof useGetRouterEndpointListQuery>;
export type GetRouterEndpointListLazyQueryHookResult = ReturnType<typeof useGetRouterEndpointListLazyQuery>;
export type GetRouterEndpointListQueryResult = Apollo.QueryResult<GetRouterEndpointListQuery, GetRouterEndpointListQueryVariables>;
import type * as SchemaTypes from '../types';

import type { ApplicationListEndpointContextDataFragment } from '../fragments/ApplicationListEndpointContextData';
import { gql } from '@apollo/client';
import { ApplicationListEndpointContextDataFragmentDoc } from '../fragments/ApplicationListEndpointContextData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetReferenceApplicationListEndpointsQueryVariables = SchemaTypes.Exact<{
  filter: SchemaTypes.ReferenceApplicationListEndpointFilter;
}>;


export type GetReferenceApplicationListEndpointsQuery = (
  { __typename: 'Query' }
  & { endpoints: Array<(
    { __typename: 'ApplicationListEndpoint' }
    & ApplicationListEndpointContextDataFragment
  )> }
);


export const GetReferenceApplicationListEndpointsDocument = /*#__PURE__*/ gql`
    query getReferenceApplicationListEndpoints($filter: ReferenceApplicationListEndpointFilter!) {
  endpoints: getReferenceApplicationListEndpoints(filter: $filter) {
    ...ApplicationListEndpointContextData
  }
}
    ${ApplicationListEndpointContextDataFragmentDoc}`;

/**
 * __useGetReferenceApplicationListEndpointsQuery__
 *
 * To run a query within a React component, call `useGetReferenceApplicationListEndpointsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetReferenceApplicationListEndpointsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetReferenceApplicationListEndpointsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useGetReferenceApplicationListEndpointsQuery(baseOptions: Apollo.QueryHookOptions<GetReferenceApplicationListEndpointsQuery, GetReferenceApplicationListEndpointsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetReferenceApplicationListEndpointsQuery, GetReferenceApplicationListEndpointsQueryVariables>(GetReferenceApplicationListEndpointsDocument, options);
      }
export function useGetReferenceApplicationListEndpointsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetReferenceApplicationListEndpointsQuery, GetReferenceApplicationListEndpointsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetReferenceApplicationListEndpointsQuery, GetReferenceApplicationListEndpointsQueryVariables>(GetReferenceApplicationListEndpointsDocument, options);
        }
export type GetReferenceApplicationListEndpointsQueryHookResult = ReturnType<typeof useGetReferenceApplicationListEndpointsQuery>;
export type GetReferenceApplicationListEndpointsLazyQueryHookResult = ReturnType<typeof useGetReferenceApplicationListEndpointsLazyQuery>;
export type GetReferenceApplicationListEndpointsQueryResult = Apollo.QueryResult<GetReferenceApplicationListEndpointsQuery, GetReferenceApplicationListEndpointsQueryVariables>;
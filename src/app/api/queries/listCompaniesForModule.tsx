import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListCompaniesForModuleQueryVariables = SchemaTypes.Exact<{ [key: string]: never; }>;


export type ListCompaniesForModuleQuery = (
  { __typename: 'Query' }
  & { companies: (
    { __typename: 'PaginatedCompanies' }
    & { items: Array<(
      { __typename: 'Company' }
      & Pick<SchemaTypes.Company, 'id' | 'displayName'>
    )> }
  ) }
);


export const ListCompaniesForModuleDocument = /*#__PURE__*/ gql`
    query listCompaniesForModule {
  companies: listCompanies(filter: {withModuleCreation: true}) {
    items {
      id
      displayName
    }
  }
}
    `;

/**
 * __useListCompaniesForModuleQuery__
 *
 * To run a query within a React component, call `useListCompaniesForModuleQuery` and pass it any options that fit your needs.
 * When your component renders, `useListCompaniesForModuleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListCompaniesForModuleQuery({
 *   variables: {
 *   },
 * });
 */
export function useListCompaniesForModuleQuery(baseOptions?: Apollo.QueryHookOptions<ListCompaniesForModuleQuery, ListCompaniesForModuleQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListCompaniesForModuleQuery, ListCompaniesForModuleQueryVariables>(ListCompaniesForModuleDocument, options);
      }
export function useListCompaniesForModuleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListCompaniesForModuleQuery, ListCompaniesForModuleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListCompaniesForModuleQuery, ListCompaniesForModuleQueryVariables>(ListCompaniesForModuleDocument, options);
        }
export type ListCompaniesForModuleQueryHookResult = ReturnType<typeof useListCompaniesForModuleQuery>;
export type ListCompaniesForModuleLazyQueryHookResult = ReturnType<typeof useListCompaniesForModuleLazyQuery>;
export type ListCompaniesForModuleQueryResult = Apollo.QueryResult<ListCompaniesForModuleQuery, ListCompaniesForModuleQueryVariables>;
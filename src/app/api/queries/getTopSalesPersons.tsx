import type * as SchemaTypes from '../types';

import type { StatisticNameAmountNumberFragment } from '../fragments/statisticNameAmountNumber';
import type { StatisticNameAmountResultFragment } from '../fragments/statisticNameAmountResult';
import { gql } from '@apollo/client';
import { StatisticNameAmountNumberFragmentDoc } from '../fragments/statisticNameAmountNumber';
import { StatisticNameAmountResultFragmentDoc } from '../fragments/statisticNameAmountResult';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type GetTopSalesPersonsQueryVariables = SchemaTypes.Exact<{
  filter?: SchemaTypes.InputMaybe<SchemaTypes.StatisticFilterPayload>;
  sortParam?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['String']['input']>;
}>;


export type GetTopSalesPersonsQuery = (
  { __typename: 'Query' }
  & { metrics?: SchemaTypes.Maybe<(
    { __typename: 'StatisticMonthlyResult' }
    & StatisticNameAmountNumberFragment
  )> }
);


export const GetTopSalesPersonsDocument = /*#__PURE__*/ gql`
    query getTopSalesPersons($filter: StatisticFilterPayload, $sortParam: String) {
  metrics: getTopSalesPersons(filter: $filter, sortParam: $sortParam) {
    ...statisticNameAmountNumber
  }
}
    ${StatisticNameAmountNumberFragmentDoc}
${StatisticNameAmountResultFragmentDoc}`;

/**
 * __useGetTopSalesPersonsQuery__
 *
 * To run a query within a React component, call `useGetTopSalesPersonsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTopSalesPersonsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTopSalesPersonsQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *      sortParam: // value for 'sortParam'
 *   },
 * });
 */
export function useGetTopSalesPersonsQuery(baseOptions?: Apollo.QueryHookOptions<GetTopSalesPersonsQuery, GetTopSalesPersonsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTopSalesPersonsQuery, GetTopSalesPersonsQueryVariables>(GetTopSalesPersonsDocument, options);
      }
export function useGetTopSalesPersonsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTopSalesPersonsQuery, GetTopSalesPersonsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTopSalesPersonsQuery, GetTopSalesPersonsQueryVariables>(GetTopSalesPersonsDocument, options);
        }
export type GetTopSalesPersonsQueryHookResult = ReturnType<typeof useGetTopSalesPersonsQuery>;
export type GetTopSalesPersonsLazyQueryHookResult = ReturnType<typeof useGetTopSalesPersonsLazyQuery>;
export type GetTopSalesPersonsQueryResult = Apollo.QueryResult<GetTopSalesPersonsQuery, GetTopSalesPersonsQueryVariables>;
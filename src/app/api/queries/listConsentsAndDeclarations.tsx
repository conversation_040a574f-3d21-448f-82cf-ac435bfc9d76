import type * as SchemaTypes from '../types';

import type { ConsentsAndDeclarationsListData_CheckboxConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsListData_GroupConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsListData_MarketingConsentsAndDeclarations_Fragment, ConsentsAndDeclarationsListData_TextConsentsAndDeclarations_Fragment } from '../fragments/ConsentsAndDeclarationsListData';
import type { TranslatedStringSpecsFragment } from '../fragments/TranslatedStringSpecs';
import type { ConditionSpecs_ApplicationModuleCondition_Fragment, ConditionSpecs_BankCondition_Fragment, ConditionSpecs_ContextualCondition_Fragment, ConditionSpecs_DealerCondition_Fragment, ConditionSpecs_GiftVoucherCondition_Fragment, ConditionSpecs_InsurerCondition_Fragment, ConditionSpecs_LocationCondition_Fragment, ConditionSpecs_LogicCondition_Fragment, ConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/ConditionSpecs';
import type { BaseConditionSpecs_ApplicationModuleCondition_Fragment, BaseConditionSpecs_BankCondition_Fragment, BaseConditionSpecs_ContextualCondition_Fragment, BaseConditionSpecs_DealerCondition_Fragment, BaseConditionSpecs_GiftVoucherCondition_Fragment, BaseConditionSpecs_InsurerCondition_Fragment, BaseConditionSpecs_LocationCondition_Fragment, BaseConditionSpecs_LogicCondition_Fragment, BaseConditionSpecs_SalesOfferAgreementsCondition_Fragment } from '../fragments/BaseConditionSpecs';
import type { MobilityLocationDataFragment } from '../fragments/MobilityLocationData';
import type { UserPreviewDataFragment } from '../fragments/UserPreviewData';
import { gql } from '@apollo/client';
import { ConsentsAndDeclarationsListDataFragmentDoc } from '../fragments/ConsentsAndDeclarationsListData';
import { TranslatedStringSpecsFragmentDoc } from '../fragments/TranslatedStringSpecs';
import { ConditionSpecsFragmentDoc } from '../fragments/ConditionSpecs';
import { BaseConditionSpecsFragmentDoc } from '../fragments/BaseConditionSpecs';
import { MobilityLocationDataFragmentDoc } from '../fragments/MobilityLocationData';
import { UserPreviewDataFragmentDoc } from '../fragments/UserPreviewData';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListConsentsAndDeclarationsQueryVariables = SchemaTypes.Exact<{
  pagination: SchemaTypes.Pagination;
  sort?: SchemaTypes.InputMaybe<SchemaTypes.ConsentsAndDeclarationsSortingRule>;
  filter?: SchemaTypes.InputMaybe<SchemaTypes.ConsentsAndDeclarationsFilteringRule>;
}>;


export type ListConsentsAndDeclarationsQuery = (
  { __typename: 'Query' }
  & { consentsAndDeclarations: (
    { __typename: 'PaginatedConsentsAndDeclarations' }
    & Pick<SchemaTypes.PaginatedConsentsAndDeclarations, 'count'>
    & { items: Array<(
      { __typename: 'CheckboxConsentsAndDeclarations' }
      & ConsentsAndDeclarationsListData_CheckboxConsentsAndDeclarations_Fragment
    ) | (
      { __typename: 'GroupConsentsAndDeclarations' }
      & ConsentsAndDeclarationsListData_GroupConsentsAndDeclarations_Fragment
    ) | (
      { __typename: 'MarketingConsentsAndDeclarations' }
      & ConsentsAndDeclarationsListData_MarketingConsentsAndDeclarations_Fragment
    ) | (
      { __typename: 'TextConsentsAndDeclarations' }
      & ConsentsAndDeclarationsListData_TextConsentsAndDeclarations_Fragment
    )> }
  ) }
);


export const ListConsentsAndDeclarationsDocument = /*#__PURE__*/ gql`
    query listConsentsAndDeclarations($pagination: Pagination!, $sort: ConsentsAndDeclarationsSortingRule, $filter: ConsentsAndDeclarationsFilteringRule) {
  consentsAndDeclarations: listConsentsAndDeclarations(
    pagination: $pagination
    sort: $sort
    filter: $filter
  ) {
    count
    items {
      ...ConsentsAndDeclarationsListData
    }
  }
}
    ${ConsentsAndDeclarationsListDataFragmentDoc}
${TranslatedStringSpecsFragmentDoc}
${ConditionSpecsFragmentDoc}
${BaseConditionSpecsFragmentDoc}
${MobilityLocationDataFragmentDoc}
${UserPreviewDataFragmentDoc}`;

/**
 * __useListConsentsAndDeclarationsQuery__
 *
 * To run a query within a React component, call `useListConsentsAndDeclarationsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListConsentsAndDeclarationsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListConsentsAndDeclarationsQuery({
 *   variables: {
 *      pagination: // value for 'pagination'
 *      sort: // value for 'sort'
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useListConsentsAndDeclarationsQuery(baseOptions: Apollo.QueryHookOptions<ListConsentsAndDeclarationsQuery, ListConsentsAndDeclarationsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListConsentsAndDeclarationsQuery, ListConsentsAndDeclarationsQueryVariables>(ListConsentsAndDeclarationsDocument, options);
      }
export function useListConsentsAndDeclarationsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListConsentsAndDeclarationsQuery, ListConsentsAndDeclarationsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListConsentsAndDeclarationsQuery, ListConsentsAndDeclarationsQueryVariables>(ListConsentsAndDeclarationsDocument, options);
        }
export type ListConsentsAndDeclarationsQueryHookResult = ReturnType<typeof useListConsentsAndDeclarationsQuery>;
export type ListConsentsAndDeclarationsLazyQueryHookResult = ReturnType<typeof useListConsentsAndDeclarationsLazyQuery>;
export type ListConsentsAndDeclarationsQueryResult = Apollo.QueryResult<ListConsentsAndDeclarationsQuery, ListConsentsAndDeclarationsQueryVariables>;
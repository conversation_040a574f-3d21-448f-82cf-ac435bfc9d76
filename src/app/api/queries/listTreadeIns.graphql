query listTradeIns($pagination: Pagination!, $filter: TradeInFilteringRule, $sort: TradeInSortingRule, ) {
    list: listTradeIns(pagination: $pagination, filter: $filter, sort: $sort) {
        count
        items {
            id
            moduleId

            module {
                ...TradeInModuleSpecs
            }
            status
            identifier
            versioning {
                ...SimpleVersioningData
            }
        }
    }
}

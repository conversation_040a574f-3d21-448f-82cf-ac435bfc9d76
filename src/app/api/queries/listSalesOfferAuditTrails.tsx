import type * as SchemaTypes from '../types';

import type { AuditTrailDetailsFragment } from '../fragments/AuditTrailDetails';
import { gql } from '@apollo/client';
import { AuditTrailDetailsFragmentDoc } from '../fragments/AuditTrailDetails';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type ListSalesOfferAuditTrailsQueryVariables = SchemaTypes.Exact<{
  salesOfferId: SchemaTypes.Scalars['ObjectID']['input'];
  pagination?: SchemaTypes.InputMaybe<SchemaTypes.Pagination>;
}>;


export type ListSalesOfferAuditTrailsQuery = (
  { __typename: 'Query' }
  & { result: (
    { __typename: 'PaginatedAuditTrails' }
    & Pick<SchemaTypes.PaginatedAuditTrails, 'count'>
    & { items: Array<(
      { __typename: 'AuditTrail' }
      & AuditTrailDetailsFragment
    )> }
  ) }
);


export const ListSalesOfferAuditTrailsDocument = /*#__PURE__*/ gql`
    query listSalesOfferAuditTrails($salesOfferId: ObjectID!, $pagination: Pagination) {
  result: listSalesOfferAuditTrails(
    salesOfferId: $salesOfferId
    pagination: $pagination
  ) {
    count
    items {
      ...AuditTrailDetails
    }
  }
}
    ${AuditTrailDetailsFragmentDoc}`;

/**
 * __useListSalesOfferAuditTrailsQuery__
 *
 * To run a query within a React component, call `useListSalesOfferAuditTrailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useListSalesOfferAuditTrailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useListSalesOfferAuditTrailsQuery({
 *   variables: {
 *      salesOfferId: // value for 'salesOfferId'
 *      pagination: // value for 'pagination'
 *   },
 * });
 */
export function useListSalesOfferAuditTrailsQuery(baseOptions: Apollo.QueryHookOptions<ListSalesOfferAuditTrailsQuery, ListSalesOfferAuditTrailsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ListSalesOfferAuditTrailsQuery, ListSalesOfferAuditTrailsQueryVariables>(ListSalesOfferAuditTrailsDocument, options);
      }
export function useListSalesOfferAuditTrailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ListSalesOfferAuditTrailsQuery, ListSalesOfferAuditTrailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ListSalesOfferAuditTrailsQuery, ListSalesOfferAuditTrailsQueryVariables>(ListSalesOfferAuditTrailsDocument, options);
        }
export type ListSalesOfferAuditTrailsQueryHookResult = ReturnType<typeof useListSalesOfferAuditTrailsQuery>;
export type ListSalesOfferAuditTrailsLazyQueryHookResult = ReturnType<typeof useListSalesOfferAuditTrailsLazyQuery>;
export type ListSalesOfferAuditTrailsQueryResult = Apollo.QueryResult<ListSalesOfferAuditTrailsQuery, ListSalesOfferAuditTrailsQueryVariables>;
import dayjs from 'dayjs';
import saveAs from 'file-saver';

const exportDealerTemplate = async (token: string | null, companyId?: string | null) => {
    const headers = {
        'Content-Type': 'application/json',
        'X-Timezone': dayjs.tz.guess(),
        Authorization: `Bearer ${token}`,
    };

    const url = companyId ? `/api/export/dealer?companyId=${companyId}` : '/api/export/dealer';
    const response = await fetch(url, { headers });

    if (response?.ok) {
        const blob = await response.blob();
        saveAs(blob, 'Dealers_Template.xlsx');

        return true;
    }

    return false;
};

export default exportDealerTemplate;

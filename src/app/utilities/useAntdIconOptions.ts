import * as Icon from '@ant-design/icons';
import { useMemo } from 'react';

const useAntdIconOptions = () =>
    useMemo(() => {
        const antdIconList = Object.entries(Icon).map(obj => obj[0]);
        antdIconList.splice(antdIconList.length - 4, 4);

        const antdIconOptions = antdIconList.map(list => ({
            label: list,
            value: list,
        }));

        return antdIconOptions;
    }, []);

export default useAntdIconOptions;

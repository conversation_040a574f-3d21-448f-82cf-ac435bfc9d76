import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AddressAutofillType } from '../api/types';

const useAddressAutofillOptions = () => {
    const { t } = useTranslation('common');

    return useMemo(() => {
        const addressAutofillOptions = [
            {
                value: AddressAutofillType.None,
                label: t('common:options.addressAutofillType.none'),
            },
            {
                value: AddressAutofillType.Partial,
                label: t('common:options.addressAutofillType.partial'),
            },
            {
                value: AddressAutofillType.PostalCode,
                label: t('common:options.addressAutofillType.postalCode'),
            },
        ];

        return addressAutofillOptions;
    }, [t]);
};

export default useAddressAutofillOptions;

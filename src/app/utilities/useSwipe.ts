import { useCallback, useState } from 'react';

type SwipeDirection = 'left' | 'right' | 'up' | 'down';

type SwipeCallback = (direction: SwipeDirection) => void;

const useSwipe = (onSwipe: SwipeCallback, minSwipeDistance = 50) => {
    const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
    const [touchEnd, setTouchEnd] = useState<{ x: number; y: number } | null>(null);

    const onTouchStart = useCallback((e: React.TouchEvent) => {
        setTouchEnd(null); // Reset on new touch
        setTouchStart({
            x: e.targetTouches[0].clientX,
            y: e.targetTouches[0].clientY,
        });
    }, []);

    const onTouchMove = useCallback((e: React.TouchEvent) => {
        setTouchEnd({
            x: e.targetTouches[0].clientX,
            y: e.targetTouches[0].clientY,
        });
    }, []);

    const onTouchEnd = useCallback(() => {
        if (!touchStart || !touchEnd) {
            return;
        }

        const deltaX = touchEnd.x - touchStart.x;
        const deltaY = touchEnd.y - touchStart.y;

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // Horizontal swipe
            if (deltaX > minSwipeDistance) {
                onSwipe('right');
            } else if (deltaX < -minSwipeDistance) {
                onSwipe('left');
            }
        } else if (deltaY > minSwipeDistance) {
            onSwipe('down');
        } else if (deltaY < -minSwipeDistance) {
            onSwipe('up');
        }
    }, [touchStart, touchEnd, onSwipe, minSwipeDistance]);

    return {
        onTouchStart,
        onTouchMove,
        onTouchEnd,
    };
};

export default useSwipe;

import { useEffect } from 'react';
import { useLocation } from 'react-router';
import urlJoin from 'url-join';
import { PathScriptSpecsFragment } from '../api';
import { useRuntimeConfig } from '../runtimeConfig';

const insertScript = (dataScript: string, nonce?: string | null) => {
    // Create a temporary DOM element to parse the script content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = dataScript;

    // Extract script elements from the parsed content
    const scripts = tempDiv.getElementsByTagName('script');
    const noscripts = tempDiv.getElementsByTagName('noscript');
    const addedScripts = [];
    for (let i = 0; i < scripts.length; i++) {
        const script = document.createElement('script');
        if (scripts[i].src) {
            // If the script has a src attribute, set it
            script.src = scripts[i].src;
        } else {
            // Otherwise, set the inline script content
            script.innerHTML = scripts[i].innerHTML;
        }
        if (nonce) {
            script.setAttribute('nonce', nonce);
        }
        addedScripts.push(script);
        document.body.appendChild(script);
    }
    for (let i = 0; i < noscripts.length; i++) {
        const noscript = document.createElement('noscript');
        noscript.innerHTML = noscripts[i].innerHTML;
        addedScripts.push(noscript);
        document.body.appendChild(noscript);
    }

    return addedScripts;
};

const usePathScriptObserver = (specs: Pick<PathScriptSpecsFragment, 'script' | 'pathname'>[], prefix: string = '/') => {
    const location = useLocation();
    const { router } = useRuntimeConfig();
    useEffect(() => {
        const pathScripts = specs.filter(
            pathScript => pathScript.pathname === '*' || urlJoin(prefix, pathScript.pathname) === location.pathname
        );
        if (pathScripts.length === 0) {
            return () => {};
        }

        const scripts = pathScripts.flatMap(pathScript => insertScript(pathScript.script, router.nonce));

        return () => {
            for (const script of scripts) {
                document.body.removeChild(script);
            }
        };
    }, [location.pathname, prefix, router.nonce]);
};
export default usePathScriptObserver;

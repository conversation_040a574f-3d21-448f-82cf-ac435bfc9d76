import { isEmpty } from 'lodash/fp';
import { useCallback } from 'react';
import { TranslatedTextDataFragment } from '../api/fragments/TranslationTextData';
import useTranslatedString from './useTranslatedString';

export type TranslatedString = (
    value: TranslatedTextDataFragment,
    params?: { [key: string]: string | number }
) => string;

const useDealerTranslatedString = (preferredDealerId: string) => {
    const translate = useTranslatedString();

    return useCallback<TranslatedString>(
        (value, params) => {
            if (!value) {
                // something is wrong here, not value given at all
                return '';
            }

            const { defaultValue, overrides = [] } = value;

            const override = overrides.find(({ dealerId }) => dealerId === preferredDealerId);
            const defaultTranslatedValue = translate(defaultValue, params);

            if (override) {
                const translatedValue = translate(override.value, params);
                if (!isEmpty(translatedValue)) {
                    return translatedValue;
                }
            }

            return defaultTranslatedValue;
        },
        [preferredDealerId, translate]
    );
};

export default useDealerTranslatedString;

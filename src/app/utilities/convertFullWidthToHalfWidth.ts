/**
 * Converts Japanese full-width (zenkaku) characters to half-width (hankaku) characters
 * This utility is used across different address autocomplete components to ensure
 * consistent display formatting for Japanese address data.
 */

/**
 * Converts full-width characters to half-width characters for Japanese text
 * @param text - The text to convert
 * @returns The converted text with half-width characters
 */
export const convertFullWidthToHalfWidth = (text: string): string => {
    if (!text) {
        return text;
    }

    return (
        text
            // Full-width numbers to half-width numbers
            .replace(/[０-９]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
            // Full-width letters to half-width letters
            .replace(/[Ａ-Ｚａ-ｚ]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))
            // Full-width punctuation to half-width punctuation
            .replace(/\u3000/g, ' ') // Full-width space (U+3000)
            .replace(/（/g, '(') // Full-width opening parenthesis
            .replace(/）/g, ')') // Full-width closing parenthesis
            .replace(/－/g, '-') // Full-width hyphen
            .replace(/．/g, '.') // Full-width period
            .replace(/，/g, ',') // Full-width comma
            .replace(/：/g, ':') // Full-width colon
            .replace(/；/g, ';') // Full-width semicolon
            .replace(/！/g, '!') // Full-width exclamation mark
            .replace(/？/g, '?') // Full-width question mark
            .replace(/／/g, '/') // Full-width slash
            .replace(/＼/g, '\\') // Full-width backslash
    );
};

export default convertFullWidthToHalfWidth;

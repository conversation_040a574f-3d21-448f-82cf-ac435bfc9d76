import { parsePhoneNumberFromString, isValidNumber } from 'libphonenumber-js';
import { isEmpty } from 'lodash/fp';
import type { KycFieldSpecsFragment } from '../../api/fragments/KYCFieldSpecs';
import { LocalCustomerFieldKey, LocalCustomerFieldSource, TradeInVehiclePayload } from '../../api/types';
import type { SearchedCustomer } from '../../components/cap/searchCustomersAndLeads/useReducer';
import { emailRegex } from '../validators';
import type { KYCPresetFormFields } from './index';

const SEARCHED_CUSTOMER_TO_KYC_MAPPING: Record<keyof SearchedCustomer, LocalCustomerFieldKey | undefined> = {
    email: LocalCustomerFieldKey.Email,
    phone: LocalCustomerFieldKey.Phone,
    firstName: LocalCustomerFieldKey.FirstName,
    lastName: LocalCustomerFieldKey.LastName,
    firstNameJapan: LocalCustomerFieldKey.FirstNameJapan,
    lastNameJapan: LocalCustomerFieldKey.LastNameJapan,
    vin: undefined, // VIN should go to tradeInVehicle, not KYC fields
    porscheId: undefined, // Don't map PorscheID
};

interface GetKYCDataFromSearchedCustomerOptions {
    /**
     * Optional callback to track which fields were auto-filled.
     * Useful for implementing reset functionality that only affects auto-filled fields.
     */
    onFieldFilled?: (fieldKey: LocalCustomerFieldKey) => void;
}

interface FormValues {
    customer: {
        fields: KYCPresetFormFields;
    };
    tradeInVehicle: TradeInVehiclePayload[];
}

/**
 * Maps searched customer data to appropriate form fields with validation and formatting.
 *
 * This function:
 * - Validates data before auto-filling (email regex, phone parsing)
 * - Handles different field types (string, phone with prefix/value structure)
 * - Maps VIN to tradeInVehicle array instead of KYC fields
 * - Optionally tracks which fields are being auto-filled for reset operations
 * - Preserves existing field structure and validation rules
 * - Skips fields that don't exist in the current KYC configuration
 *
 * Phone handling:
 * - Attempts to parse international phone numbers using libphonenumber-js
 * - Falls back to preserving existing country prefix if parsing fails
 * - Stores as {prefix: number, value: string} structure
 *
 * @param searchedCustomer - Customer data retrieved from search (CAP, etc.)
 * @param kycFields - Available KYC field specifications for validation
 * @param formValues - Current form values structure to be updated
 * @param options - Optional configuration for tracking and callbacks
 * @returns Updated form values object with auto-filled data
 */
export default function getKYCDataFromSearchedCustomer(
    searchedCustomer: SearchedCustomer,
    kycFields: KycFieldSpecsFragment[],
    formValues: FormValues,
    options: GetKYCDataFromSearchedCustomerOptions = {}
): FormValues {
    const { customer, tradeInVehicle } = formValues;
    const { fields } = customer;
    const { onFieldFilled } = options;

    const hasKycField = (key: LocalCustomerFieldKey): boolean => kycFields.some(kycField => kycField.key === key);

    (Object.keys(searchedCustomer) as Array<keyof SearchedCustomer>).forEach(key => {
        const kycField = SEARCHED_CUSTOMER_TO_KYC_MAPPING[key];
        const value = searchedCustomer[key];

        // Only process if value exists and the corresponding KYC field is available
        if (value && kycField && hasKycField(kycField)) {
            switch (kycField) {
                case LocalCustomerFieldKey.Phone: {
                    const phoneNumber = parsePhoneNumberFromString(String(value));
                    if (phoneNumber && isValidNumber(String(value))) {
                        fields[kycField] = {
                            value: {
                                prefix: phoneNumber.countryCallingCode
                                    ? Number(phoneNumber.countryCallingCode)
                                    : fields[LocalCustomerFieldKey.Phone]?.value?.prefix,
                                value: phoneNumber.nationalNumber,
                            },
                            source: LocalCustomerFieldSource.UserInput,
                        };
                        onFieldFilled?.(kycField);
                    } else {
                        fields[kycField] = {
                            value: {
                                prefix: fields[LocalCustomerFieldKey.Phone]?.value?.prefix,
                                value: String(value),
                            },
                            source: LocalCustomerFieldSource.UserInput,
                        };
                        onFieldFilled?.(kycField);
                    }
                    break;
                }

                case LocalCustomerFieldKey.Email: {
                    // Only set email if it's valid using emailRegex
                    if (emailRegex.test(String(value))) {
                        fields[kycField] = {
                            value: value as string,
                            source: LocalCustomerFieldSource.UserInput,
                        };
                        onFieldFilled?.(kycField);
                    }
                    break;
                }

                default: {
                    // For all other fields, set the value directly
                    fields[kycField] = {
                        value: value as string,
                        source: LocalCustomerFieldSource.UserInput,
                    };
                    onFieldFilled?.(kycField);
                }
            }
        }
    });

    // Handle VIN mapping to tradeInVehicle if VIN exists and tradeInVehicle array has items
    const updatedTradeInVehicle = [...tradeInVehicle];

    if (searchedCustomer.vin) {
        updatedTradeInVehicle[0] = {
            ...updatedTradeInVehicle[0],
            vin: searchedCustomer.vin,
            isSelected: true,
            source: LocalCustomerFieldSource.UserInput,
        };
    }

    return {
        customer: {
            fields,
        },
        // prevent VIN field from being hidden if tradeInVehicle is empty
        ...(!isEmpty(updatedTradeInVehicle) && { tradeInVehicle: updatedTradeInVehicle }),
    };
}

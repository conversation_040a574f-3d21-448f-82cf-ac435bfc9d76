/**
 * Downloads an image or video from a URL and converts it to a File object
 * @param url - The URL of the image or video to download
 * @param filename - The filename to use for the File object
 * @param sizeLimitInMb - The maximum file size limit in MB (default: 5MB)
 * @returns Promise<File> - The downloaded image as a File object
 */
export const downloadImageOrVideoAsFile = async (
    url: string,
    filename: string,
    sizeLimitInMb: number = 5
): Promise<File> => {
    try {
        // eslint-disable-next-line no-new
        new URL(url);
    } catch {
        throw new Error(`Invalid URL format: ${url}`);
    }

    try {
        const response = await fetch(url, {
            mode: 'cors',
            headers: {
                Accept: 'image/*,video/*',
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to download image or video: ${response.status} ${response.statusText}`);
        }

        // Check content type
        const contentType = response.headers.get('content-type');
        if (!contentType?.startsWith('image/') && !contentType?.startsWith('video/')) {
            throw new Error(`Response is not an image or video: ${contentType}`);
        }

        // Check file size (limit to specified MB)
        const contentLength = response.headers.get('content-length');
        if (contentLength && parseInt(contentLength, 10) > sizeLimitInMb * 1024 * 1024) {
            throw new Error(`Image or video too large: ${contentLength} bytes`);
        }

        const blob = await response.blob();

        const file = new File([blob], filename, {
            type: blob.type,
            lastModified: Date.now(),
        });

        return file;
    } catch (error) {
        console.error('Error downloading image or video as file:', error);
        throw new Error(
            `Failed to download image or video from ${url}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
    }
};

/**
 * Downloads multiple images or videos from URLs and converts them to File objects
 * @param downloads - Array of {url, filename} objects
 * @param sizeLimitInMb - The maximum file size limit in MB (default: 5MB)
 * @returns Promise<File[]> - Array of downloaded images or videos as File objects
 */
export const downloadImagesOrVideosAsFiles = async (
    downloads: Array<{ url: string; filename: string }>,
    sizeLimitInMb: number = 5
): Promise<File[]> => {
    const downloadPromises = downloads.map(({ url, filename }) =>
        downloadImageOrVideoAsFile(url, filename, sizeLimitInMb)
    );

    try {
        return await Promise.all(downloadPromises);
    } catch (error) {
        console.error('Error downloading multiple images or videos:', error);
        throw error;
    }
};

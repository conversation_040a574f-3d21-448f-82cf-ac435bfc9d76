import { Navigate, useLocation } from 'react-router-dom';
import { useAccount } from '../components/contexts/AccountContextManager';

interface ProtectedRouteProps {
    element: React.ReactElement;
}

const ProtectedRoute = ({ element }: ProtectedRouteProps) => {
    const user = useAccount(true);
    const location = useLocation();

    if (!user) {
        return (
            <Navigate
                state={{ nextPage: location.pathname, queryParams: window.location.search }}
                to={{ pathname: '/auth/signIn' }}
            />
        );
    }

    return element;
};

export default ProtectedRoute;

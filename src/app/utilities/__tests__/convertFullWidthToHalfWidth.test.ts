/**
 * Unit tests for convertFullWidthToHalfWidth utility
 * Tests the shared utility function used across address autocomplete components
 */

import { convertFullWidthToHalfWidth } from '../convertFullWidthToHalfWidth';

describe('convertFullWidthToHalfWidth utility', () => {
    it('should convert full-width numbers to half-width numbers', () => {
        expect(convertFullWidthToHalfWidth('０１２３４５６７８９')).toBe('0123456789');
        expect(convertFullWidthToHalfWidth('２９')).toBe('29');
        expect(convertFullWidthToHalfWidth('１２３')).toBe('123');
        expect(convertFullWidthToHalfWidth('５０１')).toBe('501');
    });

    it('should convert full-width letters to half-width letters', () => {
        expect(convertFullWidthToHalfWidth('ＡＢＣＤＥＦＧ')).toBe('ABCDEFG');
        expect(convertFullWidthToHalfWidth('ａｂｃｄｅｆｇ')).toBe('abcdefg');
        expect(convertFullWidthToHalfWidth('ビルＡ棟')).toBe('ビルA棟');
        expect(convertFullWidthToHalfWidth('Ｆ階')).toBe('F階');
    });

    it('should convert full-width punctuation to half-width punctuation', () => {
        expect(convertFullWidthToHalfWidth('（２９階）')).toBe('(29階)');
        expect(convertFullWidthToHalfWidth('１－２－３')).toBe('1-2-3');
        expect(convertFullWidthToHalfWidth('住所．番地')).toBe('住所.番地');
        expect(convertFullWidthToHalfWidth('東京，大阪')).toBe('東京,大阪');
        expect(convertFullWidthToHalfWidth('時間：９時')).toBe('時間:9時');
        expect(convertFullWidthToHalfWidth('注意；重要')).toBe('注意;重要');
        expect(convertFullWidthToHalfWidth('緊急！連絡')).toBe('緊急!連絡');
        expect(convertFullWidthToHalfWidth('質問？回答')).toBe('質問?回答');
        expect(convertFullWidthToHalfWidth('パス／ディレクトリ')).toBe('パス/ディレクトリ');
        expect(convertFullWidthToHalfWidth('バックスラッシュ＼テスト')).toBe('バックスラッシュ\\テスト');
    });

    it('should convert full-width spaces to half-width spaces', () => {
        // Using Unicode escape for full-width space to avoid linting issues
        expect(convertFullWidthToHalfWidth('東京\u3000都')).toBe('東京 都');
        expect(convertFullWidthToHalfWidth('渋谷\u3000区')).toBe('渋谷 区');
    });

    it('should handle mixed content correctly', () => {
        expect(convertFullWidthToHalfWidth('１２３番地')).toBe('123番地');
        expect(convertFullWidthToHalfWidth('アパート５０１号室')).toBe('アパート501号室');
        expect(convertFullWidthToHalfWidth('マンション（２９階）')).toBe('マンション(29階)');
        expect(convertFullWidthToHalfWidth('ビル１０Ｆ')).toBe('ビル10F');
    });

    it('should preserve half-width characters and other content', () => {
        expect(convertFullWidthToHalfWidth('123番地')).toBe('123番地');
        expect(convertFullWidthToHalfWidth('(29階)')).toBe('(29階)');
        expect(convertFullWidthToHalfWidth('アパート501号室')).toBe('アパート501号室');
        expect(convertFullWidthToHalfWidth('普通の文字列')).toBe('普通の文字列');
    });

    it('should handle empty and null inputs', () => {
        expect(convertFullWidthToHalfWidth('')).toBe('');
        expect(convertFullWidthToHalfWidth(null as any)).toBe(null);
        expect(convertFullWidthToHalfWidth(undefined as any)).toBe(undefined);
    });

    it('should handle complex real-world address examples', () => {
        // Real-world examples that might come from Japan postal API
        expect(convertFullWidthToHalfWidth('東京都渋谷区（２９階）')).toBe('東京都渋谷区(29階)');
        expect(convertFullWidthToHalfWidth('大阪市中央区１－２－３')).toBe('大阪市中央区1-2-3');
        expect(convertFullWidthToHalfWidth('横浜市西区みなとみらい２－１－１')).toBe('横浜市西区みなとみらい2-1-1');
        expect(convertFullWidthToHalfWidth('名古屋市中区栄３－１５－３３（１８階）')).toBe(
            '名古屋市中区栄3-15-33(18階)'
        );
        // Additional complex examples with various full-width characters
        expect(convertFullWidthToHalfWidth('ビルＡ棟\u3000２０１号室')).toBe('ビルA棟 201号室');
        expect(convertFullWidthToHalfWidth('住所：東京都．渋谷区１－１－１')).toBe('住所:東京都.渋谷区1-1-1');
        expect(convertFullWidthToHalfWidth('緊急連絡先！０３－１２３４－５６７８')).toBe('緊急連絡先!03-1234-5678');
        expect(convertFullWidthToHalfWidth('パス／ディレクトリ＼ファイル')).toBe('パス/ディレクトリ\\ファイル');
    });

    it('should work correctly for address components only (not city/region names)', () => {
        // Test scenarios for address parts that should be converted
        const addressWithBuilding = '１－２－３（２９階）';
        const expectedAddressValue = '1-2-3(29階)';

        expect(convertFullWidthToHalfWidth(addressWithBuilding)).toBe(expectedAddressValue);

        // Test building and room information
        const buildingInfo = 'ビルＡ棟\u3000５０１号室';
        const expectedBuildingValue = 'ビルA棟 501号室';

        expect(convertFullWidthToHalfWidth(buildingInfo)).toBe(expectedBuildingValue);

        // Test that city/region names would be preserved in their original form
        // (Note: In practice, these wouldn't be passed to this function anymore)
        const cityName = '渋谷区';
        expect(convertFullWidthToHalfWidth(cityName)).toBe('渋谷区'); // No change expected for pure kanji
    });
});

import { useMemo } from 'react';
import { useGetActiveCampaignsQuery } from '../api/queries/getActiveCampaigns';
import useTranslatedString from './useTranslatedString';

type CampaignOption = {
    value: string;
    label: string;
    campaignId: string;
};

const useCapCampaignIdOptions = (companyId?: string): CampaignOption[] => {
    const translateString = useTranslatedString();

    const { data } = useGetActiveCampaignsQuery({
        variables: {
            companyId: companyId!,
        },
        skip: !companyId,
        fetchPolicy: 'cache-and-network',
    });

    const campaignOptions = useMemo(() => {
        if (!data?.campaigns) {
            return [];
        }

        return data.campaigns.map(campaign => ({
            value: campaign.id,
            label: `${translateString(campaign.description)} (${campaign.campaignId})`,
            campaignId: campaign.campaignId,
        }));
    }, [data?.campaigns, translateString]);

    return campaignOptions;
};

export default useCapCampaignIdOptions;

import { ApplicationStage, ModuleType, type Company, type CounterSettings, type LaunchPadModule } from '../documents';
import getDatabaseContext from '../getDatabaseContext';
import { increaseCompanyCounter, parseCounterPrefix } from './counters';

const getLeadIdentifier = async (company: Company, counter: CounterSettings) => {
    // get the index on the counter
    // the index is based on the raw prefix to ensure global appliance
    const index = await increaseCompanyCounter(company._id, counter.prefix, counter.method, company.timeZone);

    return [
        // first part is the computed prefix
        parseCounterPrefix(counter.prefix, company.timeZone),
        // second part is the allocated index with leading zeros
        index.toString().padStart(counter.padding + 1, '0'),
    ].join('');
};

// eslint-disable-next-line import/prefer-default-export
export const getLeadIdentifierFromLaunchpadModule = async (
    applicationStage: ApplicationStage | null,
    launchpadModule: LaunchPadModule
): Promise<string> => {
    const { collections } = await getDatabaseContext();

    // validate the launchpad module type
    if (launchpadModule._type !== ModuleType.LaunchPadModule) {
        throw new Error('Invalid module type, expected LaunchPadModule');
    }

    // we fetch the company ID to identify the timezone to follow
    const company = await collections.companies.findOne({ _id: launchpadModule.companyId });

    switch (applicationStage) {
        case ApplicationStage.Lead: {
            return getLeadIdentifier(company, launchpadModule.leadCounter);
        }

        case ApplicationStage.Appointment: {
            return getLeadIdentifier(company, launchpadModule.appointmentCounter);
        }

        default: {
            throw new Error(`Lead identifier not implemented for application stage: ${applicationStage}`);
        }
    }
};

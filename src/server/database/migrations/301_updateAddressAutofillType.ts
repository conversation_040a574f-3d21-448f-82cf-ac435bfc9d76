import { AddressAutofillType } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '301_updateAddressAutofillType',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db.collection('companies').updateMany(
            {
                addressAutofill: true,
            },
            {
                $set: {
                    addressAutofill: AddressAutofillType.Partial,
                },
            }
        );

        await db.collection('companies').updateMany(
            {
                addressAutofill: { $ne: AddressAutofillType.Partial },
            },
            {
                $set: {
                    addressAutofill: AddressAutofillType.None,
                },
            }
        );
    },
};

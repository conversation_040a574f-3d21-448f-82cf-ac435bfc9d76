import { ModuleType } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '291_insertHasTradeInRequestOnLaunchpad',

    async up({ collections }: DatabaseContext): Promise<void> {
        await collections.modules.updateMany(
            { _type: ModuleType.LaunchPadModule },
            { $set: { hasTradeInRequest: false } }
        );
    },
};

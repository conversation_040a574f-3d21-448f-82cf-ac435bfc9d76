import { ObjectId } from 'mongodb';
import type { AuditTrail } from '../documents/AuditTrail/auditTrail';
import { LeadStatus } from '../documents/Lead/shared';
import { AuthorKind, type Author } from '../documents/Versioning';
import type { DatabaseContext } from '../getDatabaseContext';
import { createLeadMergeAuditTrail } from '../helpers/leads';

export default {
    identifier: '296_addLeadMergeAuditTrails',
    async up({ collections }: DatabaseContext): Promise<void> {
        const sourceLeads = await collections.leads
            .find({
                $and: [{ mergedToLeadSuiteId: { $exists: true } }, { mergedToLeadSuiteId: { $ne: null } }],
            })
            .toArray();

        if (sourceLeads.length === 0) {
            return;
        }

        const updateSourceLeadOperations = [];

        const auditTrails = [];

        for await (const sourceLead of sourceLeads) {
            const targetLead = await collections.leads.findOne({
                '_versioning.suiteId': sourceLead.mergedToLeadSuiteId,
                '_versioning.isLatest': true,
                isDraft: false,
            });

            if (!targetLead) {
                continue; // Skip if target lead is not found
            }

            // get latest audit trail for the source lead to get the date and author
            const latestSourceLeadAuditTrail = await collections.auditTrails.findOne(
                { leadSuiteId: sourceLead._versioning.suiteId, leadId: sourceLead._id },
                { sort: { _date: -1 } }
            );

            const date = latestSourceLeadAuditTrail?._date || new Date();
            const datePlusOneSecond = new Date(date.getTime() + 1000); // new audit trail should be after the latest one

            const author: Author =
                latestSourceLeadAuditTrail &&
                'author' in latestSourceLeadAuditTrail &&
                latestSourceLeadAuditTrail.author?.kind === AuthorKind.User
                    ? latestSourceLeadAuditTrail.author
                    : {
                          kind: AuthorKind.System,
                      };

            // Source lead log
            auditTrails.push(createLeadMergeAuditTrail('source', sourceLead, targetLead, datePlusOneSecond, author));

            // Target lead log
            auditTrails.push(createLeadMergeAuditTrail('target', sourceLead, targetLead, datePlusOneSecond, author));

            updateSourceLeadOperations.push({
                updateOne: {
                    filter: { _id: sourceLead._id },
                    update: {
                        $set: {
                            status: LeadStatus.Merged,
                            isLead: false, // reset to false since this is no longer a lead
                        },
                    },
                },
            });

            if (!sourceLead.identifier) {
                // Since we can't access source lead that has no identifier (Contact ID) after the merge
                // we need to retrieve all audit trails of the source lead and duplicate them to the target lead
                const sourceLeadAuditTrails = await collections.auditTrails
                    .find({ leadSuiteId: sourceLead._versioning.suiteId })
                    .toArray();

                const newAuditTrails: AuditTrail[] = sourceLeadAuditTrails.map(auditTrail => ({
                    ...auditTrail,
                    _id: new ObjectId(),
                    leadId: targetLead._id,
                    leadSuiteId: targetLead._versioning.suiteId,
                }));

                if (newAuditTrails.length) {
                    auditTrails.push(...newAuditTrails);
                }
            }
        }

        if (updateSourceLeadOperations.length > 0) {
            await collections.leads.bulkWrite(updateSourceLeadOperations);
        }

        if (auditTrails.length > 0) {
            await collections.auditTrails.insertMany(auditTrails);
        }
    },
};

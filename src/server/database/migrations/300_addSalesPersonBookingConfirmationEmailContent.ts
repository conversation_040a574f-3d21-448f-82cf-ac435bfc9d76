/* eslint-disable max-len */
import { Module, ModuleType } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

const getDefaultItemInput = (defaultValue: string | boolean) => ({
    defaultValue: {
        defaultValue,
        overrides: [],
    },
    overrides: [],
});

export const getDefaultEmailContentInput = (subject: string, introTitle: string, contentText: string) => ({
    subject: getDefaultItemInput(subject),
    introTitle: getDefaultItemInput(introTitle),
    contentText: getDefaultItemInput(contentText),
    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
});

export default {
    identifier: '300_addSalesPersonBookingConfirmationEmailContent',
    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        // Add salesPersonBookingConfirmation to AppointmentModule
        await db.collection<Module>('modules').updateMany(
            {
                _type: ModuleType.AppointmentModule,
                $or: [
                    { 'emailContents.salesPerson.bookingConfirmation': { $exists: false } },
                    { 'emailContents.salesPerson.bookingConfirmation': { $eq: null } },
                ],
            },
            [
                {
                    $set: {
                        'emailContents.salesPerson': {
                            $mergeObjects: [
                                '$emailContents.salesPerson',
                                {
                                    bookingConfirmation: getDefaultEmailContentInput(
                                        '{{companyName}}: Test Drive {{vehicleName}} {{inventoryID}} Confirmed: {{customerName}}',
                                        "Customer's Test Drive Confirmed",
                                        'Test Drive Booking has been confirmed. Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**'
                                    ),
                                },
                            ],
                        },
                    },
                },
            ]
        );

        // Add salesPersonBookingConfirmation to VisitAppointmentModule
        await db.collection<Module>('modules').updateMany(
            {
                _type: ModuleType.VisitAppointmentModule,
                $or: [
                    { 'emailContents.salesPerson.bookingConfirmation': { $exists: false } },
                    { 'emailContents.salesPerson.bookingConfirmation': { $eq: null } },
                ],
            },
            [
                {
                    $set: {
                        'emailContents.salesPerson': {
                            $mergeObjects: [
                                '$emailContents.salesPerson',
                                {
                                    bookingConfirmation: getDefaultEmailContentInput(
                                        '{{companyName}}: Showroom Visit {{vehicleName}} {{inventoryID}} Confirmed: {{customerName}}',
                                        "Customer's Showroom Visit Confirmed",
                                        'Showroom Visit appointment has been confirmed.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**'
                                    ),
                                },
                            ],
                        },
                    },
                },
            ]
        );
    },
};

import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '306_addVehicleIndexesForFilteringLocalVariants',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db.collection('vehicles').createIndex({
            _kind: 1,
            isDeleted: 1,
            '_versioning.isLatest': 1,
            moduleId: 1,
        });

        await db.collection('vehicles').createIndex({
            '_versioning.suiteId': 1,
            _kind: 1,
            isDeleted: 1,
            '_versioning.isLatest': 1,
        });

        await db.collection('modules').createIndex({
            'dealerVehicles.dealerId': 1,
        });

        await db.collection('modules').createIndex({
            _id: 1,
            'dealerVehicles.dealerId': 1,
        });
    },
};

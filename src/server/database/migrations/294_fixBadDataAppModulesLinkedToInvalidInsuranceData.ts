import { AnyBulkWriteOperation } from 'mongodb';
import {
    type Company,
    type ConfiguratorModule,
    type FinderApplicationPrivateModule,
    type FinderApplicationPublicModule,
    type Module,
    ModuleType,
    type SalesOfferModule,
    type StandardApplicationModule,
} from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '294_fixBadDataAppModulesLinkedToInvalidInsuranceData',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const moduleCollection = db.collection<Module>('modules');
        const companies = await db
            .collection<Company>('companies')
            .find({
                countryCode: 'SG',
            })
            .toArray();

        const appModuleTypes = [
            ModuleType.StandardApplicationModule,
            ModuleType.ConfiguratorModule,
            ModuleType.FinderApplicationPublicModule,
            ModuleType.FinderApplicationPrivateModule,
            ModuleType.SalesOfferModule,
        ];

        const applicationModules = await moduleCollection
            .find({
                _type: {
                    $in: appModuleTypes,
                },
                companyId: { $in: companies.map(company => company._id) },
                insuranceModuleId: { $exists: true, $ne: null },
            })
            .toArray();

        const operations = (
            await Promise.all(
                applicationModules.map(
                    async (
                        applicationModule:
                            | StandardApplicationModule
                            | ConfiguratorModule
                            | FinderApplicationPublicModule
                            | FinderApplicationPrivateModule
                            | SalesOfferModule
                    ): Promise<AnyBulkWriteOperation<Module> | null> => {
                        const insuranceModule = await moduleCollection.findOne({
                            _id: applicationModule.insuranceModuleId,
                            companyId: applicationModule.companyId,
                        });

                        if (insuranceModule) {
                            return null;
                        }

                        return {
                            updateOne: {
                                filter: { _id: applicationModule._id },
                                update: {
                                    $set: { insuranceModuleId: null, dealerInsuranceProducts: [], insurerIds: [] },
                                },
                            },
                        };
                    }
                )
            )
        ).filter(Boolean);

        if (operations.length > 0) {
            await moduleCollection.bulkWrite(operations);
        }
    },
};

import { ObjectId, type AnyBulkWriteOperation } from 'mongodb';
import type { Lead, Module } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '290_addCompanyIdToLeads',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const modules = await db
            .collection<Module>('modules')
            .find({}, { projection: { _id: 1, companyId: 1 } })
            .toArray();

        const moduleCompanyMap = new Map<string, ObjectId>();
        modules.forEach(module => {
            moduleCompanyMap.set(module._id.toString(), module.companyId);
        });

        const leads = await db
            .collection<Lead>('leads')
            .find({ companyId: { $exists: false } }, { projection: { _id: 1, moduleId: 1 } })
            .toArray();

        const bulkOps: Array<AnyBulkWriteOperation<Lead>> = leads
            .filter(lead => {
                const moduleId = lead.moduleId?.toString();

                return moduleId && moduleCompanyMap.has(moduleId);
            })
            .map(lead => {
                const moduleId = lead.moduleId.toString();
                const companyId = moduleCompanyMap.get(moduleId);

                return {
                    updateOne: {
                        filter: { _id: lead._id },
                        update: { $set: { companyId } },
                    },
                };
            });

        if (bulkOps.length > 0) {
            await db.collection<Lead>('leads').bulkWrite(bulkOps);
        }
    },
};

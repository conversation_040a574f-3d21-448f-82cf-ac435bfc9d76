import { AspectRatio } from '../documents/shared';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '306_updateLcfBannerMobileRatio',

    async up({ collections }: DatabaseContext): Promise<void> {
        // Get banner IDs from events
        const bannerIds = await collections.events.distinct('bannerId', {
            $and: [{ bannerId: { $exists: true } }, { bannerId: { $ne: null } }],
        });

        if (bannerIds.length === 0) {
            return;
        }

        await collections.banners.updateMany(
            {
                _id: { $in: bannerIds },
            },
            {
                $set: {
                    mobileRatio: AspectRatio.Square, // default ratio is 1:1
                },
            }
        );
    },
};

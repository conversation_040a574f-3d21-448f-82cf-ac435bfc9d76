import type { DatabaseContext } from '../getDatabaseContext';

const getSet = path => ({
    [path]: {
        $map: {
            input: `$${path}`,
            as: 'document',
            in: {
                $mergeObjects: [
                    '$$document',
                    {
                        createdAt: {
                            $cond: {
                                if: { $not: ['$$document.createdAt'] },
                                then: '$$document.lastUpdatedAt',
                                else: '$$document.createdAt',
                            },
                        },
                    },
                ],
            },
        },
    },
});
export default {
    identifier: '290_addCreatedByForSalesOfferDocuments',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const set = {
            ...getSet('mainDetails.documents'),
            ...getSet('vehicle.documents'),
            ...getSet('deposit.documents'),
            ...getSet('finance.documents'),
            ...getSet('insurance.documents'),
            ...getSet('vsa.documents'),
        };

        await db.collection('salesOffers').updateMany({}, [
            {
                $set: set,
            },
        ]);
    },
};

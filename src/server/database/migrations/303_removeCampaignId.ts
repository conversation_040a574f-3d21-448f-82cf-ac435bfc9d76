import { ObjectId, type AnyBulkWriteOperation, type MatchKeysAndValues } from 'mongodb';
import { getSimpleVersioningBySystemForCreation } from '../../utils/versioning';
import type { Campaign } from '../documents/Campaign';
import type { Event } from '../documents/Event';
import type {
    StandardApplicationModule,
    FinderApplicationPrivateModule,
    FinderApplicationPublicModule,
    ConfiguratorModule,
} from '../documents/modules';
import type { DatabaseContext } from '../getDatabaseContext';

const DEFAULT_CAMPAIGN_DESCRIPTION = 'C@P';

type ModuleWithCapCampaignId = Omit<
    StandardApplicationModule | FinderApplicationPrivateModule | FinderApplicationPublicModule | ConfiguratorModule,
    'leadCampaignId'
> & {
    leadCampaignId?: string | null;
};

type EventWithModule = Omit<Event, 'utmParametersSettings'> & {
    module?: { companyId: ObjectId };
    utmParametersSettings?: {
        defaultValue: { capCampaignId?: string | null };
        overrides: { capCampaignId?: string | null; utmUrl: string }[];
    };
};

export default {
    identifier: '303_removeCampaignId',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        // Step 1: Collect all unique campaign IDs grouped by company
        const companyCampaignMap = new Map<string, Set<string>>();

        const modulesWithCampaigns = await db
            .collection('modules')
            .find<ModuleWithCapCampaignId>({
                leadCampaignId: { $exists: true, $type: 'string' },
                companyId: { $exists: true },
            })
            .toArray();

        for (const module of modulesWithCampaigns) {
            if (module.leadCampaignId && module.companyId) {
                const companyIdStr = module.companyId.toString();
                if (!companyCampaignMap.has(companyIdStr)) {
                    companyCampaignMap.set(companyIdStr, new Set());
                }

                companyCampaignMap.get(companyIdStr)!.add(module.leadCampaignId);
            }
        }

        // Collect campaign IDs from events (both defaultValue and overrides)
        const eventsWithCampaigns = await db
            .collection('events')
            .aggregate<EventWithModule>([
                {
                    $match: {
                        $and: [
                            {
                                $or: [
                                    {
                                        'utmParametersSettings.defaultValue.capCampaignId': {
                                            $exists: true,
                                            $type: 'string',
                                        },
                                    },
                                    {
                                        'utmParametersSettings.overrides': {
                                            $elemMatch: {
                                                capCampaignId: { $exists: true, $type: 'string' },
                                            },
                                        },
                                    },
                                ],
                            },
                            {
                                moduleId: { $exists: true },
                            },
                        ],
                    },
                },
                {
                    $lookup: {
                        from: 'modules',
                        localField: 'moduleId',
                        foreignField: '_id',
                        as: 'module',
                        pipeline: [
                            {
                                $project: { companyId: 1 },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: '$module',
                        preserveNullAndEmptyArrays: false,
                    },
                },
            ])
            .toArray();

        for (const event of eventsWithCampaigns) {
            if (event.module?.companyId) {
                const companyIdStr = event.module.companyId.toString();
                if (!companyCampaignMap.has(companyIdStr)) {
                    companyCampaignMap.set(companyIdStr, new Set());
                }

                if (event.utmParametersSettings?.defaultValue?.capCampaignId) {
                    companyCampaignMap.get(companyIdStr)!.add(event.utmParametersSettings.defaultValue.capCampaignId);
                }

                if (event.utmParametersSettings?.overrides) {
                    for (const override of event.utmParametersSettings.overrides) {
                        if (override.capCampaignId) {
                            companyCampaignMap.get(companyIdStr)!.add(override.capCampaignId);
                        }
                    }
                }
            }
        }

        // Step 2: Create Campaign documents and build mapping
        const campaignIdToObjectIdMap = new Map<string, ObjectId>();
        const campaignsToInsert: Campaign[] = [];
        const now = new Date();

        for (const [companyIdStr, campaignIds] of companyCampaignMap) {
            const companyId = new ObjectId(companyIdStr);

            for (const campaignId of campaignIds) {
                const campaignObjectId = new ObjectId();
                campaignIdToObjectIdMap.set(campaignId, campaignObjectId);

                const campaign: Campaign = {
                    _id: campaignObjectId,
                    companyId,
                    campaignId,
                    description: {
                        defaultValue: DEFAULT_CAMPAIGN_DESCRIPTION,
                        overrides: [],
                    },
                    isActive: true,
                    isDeleted: false,
                    _versioning: getSimpleVersioningBySystemForCreation(now),
                };

                campaignsToInsert.push(campaign);
            }
        }

        if (campaignsToInsert.length > 0) {
            await db.collection('campaigns').insertMany(campaignsToInsert);
        }

        // Step 3: Update modules with Campaign ObjectIds
        const moduleUpdates: AnyBulkWriteOperation[] = [];
        for (const module of modulesWithCampaigns) {
            if (module.leadCampaignId) {
                const campaignObjectId = campaignIdToObjectIdMap.get(module.leadCampaignId);

                moduleUpdates.push({
                    updateOne: {
                        filter: { _id: module._id },
                        update: {
                            $set: {
                                leadCampaignId: campaignObjectId || null,
                            },
                        },
                    },
                });
            }
        }

        if (moduleUpdates.length > 0) {
            await db.collection('modules').bulkWrite(moduleUpdates);
        }

        // Step 4: Update events with Campaign ObjectIds (both defaultValue and overrides)
        const eventUpdates: AnyBulkWriteOperation<Event>[] = [];
        for (const event of eventsWithCampaigns) {
            const updateFields: MatchKeysAndValues<Event> = {};
            let hasUpdates = false;

            // Handle default value update
            if (event.utmParametersSettings?.defaultValue?.capCampaignId) {
                const campaignObjectId = campaignIdToObjectIdMap.get(
                    event.utmParametersSettings.defaultValue.capCampaignId
                );
                updateFields['utmParametersSettings.defaultValue.capCampaignId'] = campaignObjectId || null;
                hasUpdates = true;
            }

            // Handle overrides update
            if (event.utmParametersSettings?.overrides) {
                const hasStringCampaignIds = event.utmParametersSettings.overrides.some(
                    override => override.capCampaignId && typeof override.capCampaignId === 'string'
                );

                if (hasStringCampaignIds) {
                    const updatedOverrides = event.utmParametersSettings.overrides.map(override => {
                        if (override.capCampaignId && typeof override.capCampaignId === 'string') {
                            const campaignObjectId = campaignIdToObjectIdMap.get(override.capCampaignId);

                            return {
                                ...override,
                                capCampaignId: campaignObjectId || null,
                            };
                        }

                        return override;
                    });
                    updateFields['utmParametersSettings.overrides'] = updatedOverrides;
                    hasUpdates = true;
                }
            }

            if (hasUpdates) {
                eventUpdates.push({
                    updateOne: {
                        filter: { _id: event._id },
                        update: {
                            $set: updateFields,
                        },
                    },
                });
            }
        }

        if (eventUpdates.length > 0) {
            await db.collection<Event>('events').bulkWrite(eventUpdates);
        }
    },
};

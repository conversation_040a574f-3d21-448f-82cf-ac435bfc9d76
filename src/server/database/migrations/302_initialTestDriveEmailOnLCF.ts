import type { Event } from '../documents/Event';
import type { AppointmentModule, EventApplicationModule } from '../documents/modules';
import { DatabaseContext } from '../getDatabaseContext';

type ExtendedEvent = Event & { module: EventApplicationModule; appointmentModule: AppointmentModule };

export default {
    identifier: '302_initialTestDriveEmailOnLCF',
    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const events = await db
            .collection<ExtendedEvent>('events')
            .aggregate<ExtendedEvent>([
                { $match: {} },
                {
                    $lookup: {
                        from: 'modules',
                        localField: 'moduleId',
                        foreignField: '_id',
                        as: 'module',
                    },
                },
                {
                    $unwind: {
                        path: '$module',
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $lookup: {
                        from: 'modules',
                        localField: 'module.appointmentModuleId',
                        foreignField: '_id',
                        as: 'appointmentModule',
                    },
                },
                {
                    $unwind: {
                        path: '$appointmentModule',
                        preserveNullAndEmptyArrays: false,
                    },
                },
            ])
            .toArray();

        const promises = events.map(event => {
            const getDefaultTranslationValue = (value: string) => ({
                defaultValue: {
                    defaultValue: value,
                    overrides: [],
                },
                overrides: [],
            });

            const currentSubmitOrderEmail = event.emailContents?.submitOrder
                ? event.emailContents.submitOrder
                : {
                      subject: getDefaultTranslationValue(''),
                      introTitle: getDefaultTranslationValue(''),
                      contentText: getDefaultTranslationValue(''),
                  };

            if (event.appointmentModule) {
                const {
                    emailContents: { customer: customerTestDriveEmail },
                } = event.appointmentModule;

                return {
                    updateOne: {
                        filter: { _id: event._id },
                        update: {
                            $set: {
                                'emailContents.submitOrder': currentSubmitOrderEmail,
                                'emailContents.testDrive.customer': {
                                    submitConfirmation: customerTestDriveEmail.submitConfirmation,
                                    bookingAmendment: customerTestDriveEmail.bookingAmendment,
                                    bookingCancellation: customerTestDriveEmail.bookingCancellation,
                                    bookingConfirmation: customerTestDriveEmail.bookingConfirmation,
                                    completeTestDriveWithoutProcess:
                                        customerTestDriveEmail.completeTestDriveWithoutProcess,
                                    endTestDriveWithProcess: customerTestDriveEmail.endTestDriveWithProcess,
                                },
                            },
                        },
                    },
                };
            }

            return {
                updateOne: {
                    filter: { _id: event._id },
                    update: {
                        $set: {
                            'emailContents.submitOrder': currentSubmitOrderEmail,
                            'emailContents.testDrive.customer': {
                                submitConfirmation: {
                                    subject: getDefaultTranslationValue('{{companyName}}: Test Drive Submitted'),
                                    introTitle: getDefaultTranslationValue(
                                        'You will be contacted shortly to confirm the Test Drive'
                                    ),
                                    contentText: getDefaultTranslationValue(
                                        'Please note Test Drive is subject to availability of the model and the vehicle. Booking will be cancelled if Vehicle is purchased.'
                                    ),
                                    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                                },
                                bookingAmendment: {
                                    subject: getDefaultTranslationValue('{{companyName}}: Test Drive Booking Updated'),
                                    introTitle: getDefaultTranslationValue('Your updated Test Drive Booking'),
                                    contentText: getDefaultTranslationValue(
                                        'A change in Test Drive Booking details has been made. Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.<br><br>**Appointment Date: {{apptDate}}**<br><br>**Appointment Time: {{apptTime}}**'
                                    ),
                                    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                                },
                                bookingCancellation: {
                                    subject: getDefaultTranslationValue(
                                        '{{companyName}}: Application ID {{identifier}} Cancelled'
                                    ),
                                    introTitle: getDefaultTranslationValue('Your Test Drive has been cancelled'),
                                    contentText: getDefaultTranslationValue(
                                        'Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}.'
                                    ),
                                    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                                },
                                bookingConfirmation: {
                                    subject: getDefaultTranslationValue('{{companyName}}: Test Drive Confirmed'),
                                    introTitle: getDefaultTranslationValue('Your Test Drive is confirmed'),
                                    contentText: getDefaultTranslationValue(
                                        'Please note Test Drive is subject to availability of the vehicle. Booking will be cancelled if Vehicle is purchased.'
                                    ),
                                    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                                },
                                completeTestDriveWithoutProcess: {
                                    subject: getDefaultTranslationValue('{{companyName}}: Test Drive Completed'),
                                    introTitle: getDefaultTranslationValue(
                                        'Enjoyed your Test Drive car and like to own it?'
                                    ),
                                    contentText: getDefaultTranslationValue(
                                        'Reserve the car via this link: <url>{{link}}</url>'
                                    ),
                                    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                                },
                                endTestDriveWithProcess: {
                                    subject: getDefaultTranslationValue('{{companyName}}: Test Drive Ended'),
                                    introTitle: getDefaultTranslationValue(
                                        'Enjoyed your Test Drive car and like to own it?'
                                    ),
                                    contentText: getDefaultTranslationValue(
                                        'Reserve the car via this link: <url>{{link}}</url>'
                                    ),
                                    isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                                },
                            },
                        },
                    },
                },
            };
        });

        if (promises.length > 0) {
            await db.collection<Event>('events').bulkWrite(promises);
        }
    },
};

import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '290_initialEventThankyouPageContent',

    async up({ encrypted: { db } }: DatabaseContext): Promise<void> {
        await db.collection('events').updateMany(
            {},
            {
                $set: {
                    hasCustomiseThankYouPage: false,
                    thankYouPageContent: {
                        introTitle: {
                            defaultValue: {
                                defaultValue: '',
                                overrides: [],
                            },
                            overrides: [],
                        },
                        contentText: {
                            defaultValue: {
                                defaultValue: 'Our sales consultant will be in touch with you shortly.',
                                overrides: [],
                            },
                            overrides: [],
                        },
                        isCustomRedirectionButton: false,
                        redirectButton: {
                            title: {
                                defaultValue: {
                                    defaultValue: 'Continue Browsing',
                                    overrides: [],
                                },
                                overrides: [],
                            },
                            url: 'https://www.porsche.com/singapore/en/models/911/',
                        },
                    },
                },
            }
        );
    },
};

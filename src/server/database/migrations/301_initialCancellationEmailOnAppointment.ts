import { ModuleType } from '../documents/modules';
import { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '301_initialCancellationEmailOnAppointment',
    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        const getDefaultTranslationValue = (value: string) => ({
            defaultValue: {
                defaultValue: value,
                overrides: [],
            },
            overrides: [],
        });

        await db.collection('modules').updateMany(
            { _type: ModuleType.AppointmentModule },
            {
                $set: {
                    'emailContents.customer.bookingCancellation': {
                        subject: getDefaultTranslationValue('{companyName}}: Test Drive {{identifier}} Cancelled'),
                        introTitle: getDefaultTranslationValue('Your Test Drive has been cancelled'),
                        contentText: getDefaultTranslationValue(
                            'Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}.'
                        ),
                        isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                    },
                    'emailContents.salesPerson.bookingCancellation': {
                        subject: getDefaultTranslationValue(
                            '{{companyName}}: Test Drive ID {{identifier}}: Cancelled: {{customerName}}'
                        ),
                        introTitle: getDefaultTranslationValue(
                            '{{initiatorName}} has cancelled Test Drive ID: {{identifier}}'
                        ),
                        contentText: getDefaultTranslationValue(
                            'You can access this test drive using the following link: <url>{{link}}</url>'
                        ),
                        isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                    },
                },
            }
        );

        await db.collection('modules').updateMany(
            { _type: ModuleType.VisitAppointmentModule },
            {
                $set: {
                    'emailContents.customer.bookingCancellation': {
                        subject: getDefaultTranslationValue('{{companyName}}: Showroom Visit {{identifier}} Cancelled'),
                        introTitle: getDefaultTranslationValue('Your Showroom Visit has been cancelled'),
                        contentText: getDefaultTranslationValue(
                            'Should you have any queries, please contact {{assignee.name}} at {{assignee.phone}}.'
                        ),
                        isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                    },
                    'emailContents.salesPerson.bookingCancellation': {
                        subject: getDefaultTranslationValue(
                            '{{companyName}}: Showroom Visit ID {{identifier}}: Cancelled: {{customerName}}'
                        ),
                        introTitle: getDefaultTranslationValue(
                            '{{initiatorName}} has cancelled Showroom Visit ID: {{identifier}}'
                        ),
                        contentText: getDefaultTranslationValue(
                            'You can access this Showroom Visit using the following link: <url>{{link}}</url>'
                        ),
                        isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
                    },
                },
            }
        );
    },
};

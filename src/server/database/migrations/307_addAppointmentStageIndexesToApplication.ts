import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '307_addAppointmentStageIndexesToApplication',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db.collection('applications').createIndex({
            '_versioning.isLatest': 1,
            isDraft: 1,
            'appointmentStage.status': 1,
        });

        await db.collection('applications').createIndex({
            '_versioning.isLatest': 1,
            isDraft: 1,
            'appointmentStage.appointmentModuleId': 1,
            'appointmentStage.status': 1,
            'appointmentStage.appointmentReservedExpiryDate': 1,
            'appointmentStage.bookingTimeSlot.slot': 1,
        });

        await db.collection('applications').createIndex({
            '_versioning.isLatest': 1,
            isDraft: 1,
            eventId: 1,
            'appointmentStage.appointmentModuleId': 1,
            'appointmentStage.status': 1,
            'appointmentStage.appointmentReservedExpiryDate': 1,
            'appointmentStage.bookingTimeSlot.slot': 1,
        });

        await db.collection('applications').createIndex({
            '_versioning.isLatest': 1,
            isDraft: 1,
            'visitAppointmentStage.visitAppointmentModuleId': 1,
            'visitAppointmentStage.status': 1,
            'visitAppointmentStage.appointmentReservedExpiryDate': 1,
            'visitAppointmentStage.bookingTimeSlot.slot': 1,
        });
    },
};

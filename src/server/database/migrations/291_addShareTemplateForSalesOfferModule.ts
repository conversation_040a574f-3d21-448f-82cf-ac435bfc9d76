import { DealerTranslatedStringSetting, ModuleType, SalesOfferEmailContents, SalesOfferModule } from '../documents';
import { DatabaseContext } from '../getDatabaseContext';

type OldSalesOfferModule = Omit<SalesOfferModule, 'emailContents'> & {
    emailContents: {
        combinedTemplate: SalesOfferEmailContents;
        singlePreOfferTemplate: SalesOfferEmailContents;
        salesOfferTemplate: SalesOfferEmailContents;
    };
};

const defaultShareSubject: DealerTranslatedStringSetting = {
    defaultValue: {
        defaultValue: `{{ documentName }} - {{ vehicleModel }} Information`,
        overrides: [],
    },
    overrides: [],
};

const defaultShareIntroTitle: DealerTranslatedStringSetting = {
    defaultValue: { defaultValue: 'Document for Your Reference', overrides: [] },
    overrides: [],
};

const defaultShareContentText: DealerTranslatedStringSetting = {
    defaultValue: {
        defaultValue: `Hello {{ firstName }} {{ lastName }},
Please find the attached {{ documentName }} related to the {{ vehicleModelVariant }} for your records and reference.`,
        overrides: [],
    },
    overrides: [],
};

export default {
    identifier: '291_addShareTemplateForSalesOfferModule',

    async up({ regular: { db } }: DatabaseContext): Promise<void> {
        await db.collection<OldSalesOfferModule>('modules').updateMany(
            { _type: ModuleType.SalesOfferModule },
            {
                $set: {
                    'emailContents.shareTemplate': {
                        introTitle: defaultShareIntroTitle,
                        subject: defaultShareSubject,
                        contentText: defaultShareContentText,
                    },
                },
            }
        );
    },
};

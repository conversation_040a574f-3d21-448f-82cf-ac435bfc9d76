import type { ObjectId } from 'mongodb';
import type { TranslatedString } from './LanguagePack';
import type { SimpleVersioning } from './Versioning';

export type Campaign = {
    /* ID */
    _id: ObjectId;

    /* Company ID */
    companyId: ObjectId;

    /* Campaign identifier for C@P integration */
    campaignId: string;

    /* Campaign description */
    description: TranslatedString;

    /* Whether this campaign is active */
    isActive: boolean;

    /* Versioning */
    _versioning: SimpleVersioning;

    /* Soft delete */
    isDeleted: boolean;
};

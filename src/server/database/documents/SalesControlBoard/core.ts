import { ObjectId } from 'mongodb';
import { SalesControlBoardDataType } from './enums';

/**
 * save by
 * 1. sales Consultant
 * :: e.g: 8 consultants
 *
 * 2. modelId
 * :: e.g: 5 models
 *
 * in the end of import should have 40 documents
 */

export type SalesControlBoardCore<TSource extends SalesControlBoardDataType> = {
    _id: ObjectId;

    source: TSource;

    importDate: string; // '2025-07'
    dealerId: ObjectId;
    salesConsultantId: ObjectId;
    modelId: ObjectId;
};

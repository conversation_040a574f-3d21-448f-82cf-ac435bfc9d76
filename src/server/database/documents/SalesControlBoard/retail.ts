import type { SalesControlBoardCore } from './core';
import { SalesControlBoardDataType } from './enums';
import type { SalesControlBoardWeeklyCount } from './typings';

export type SalesControlBoardRetail = SalesControlBoardCore<SalesControlBoardDataType.Retails> & {
    ytd: {
        actual: number;
        appliedFinance: number;
        appliedInsurance: number;
    };
    month: {
        actual: number;
        appliedFinance: number;
        appliedInsurance: number;
    };
    threeMonths: {
        actual: number;
        appliedFinance: number;
        appliedInsurance: number;
    };

    weeks: Array<SalesControlBoardWeeklyCount>; // ordered actual retails by week
};

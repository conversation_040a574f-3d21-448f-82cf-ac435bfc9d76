import type { SalesControlBoardCore } from './core';
import { SalesControlBoardDataType } from './enums';
import type { SalesControlBoardWeeklyCount } from './typings';

export type SalesControlBoardOrderIntake = SalesControlBoardCore<SalesControlBoardDataType.OrderIntakes> & {
    ytd: {
        actual: number;
    };
    month: {
        actual: number;
    };
    weeks: Array<SalesControlBoardWeeklyCount>; // ordered actual retails by week
};

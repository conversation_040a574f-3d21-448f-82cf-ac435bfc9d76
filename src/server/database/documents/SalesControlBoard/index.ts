import type { SalesControlBoardLead } from './lead';
import type { SalesControlBoardOrderIntake } from './orderIntake';
import type { SalesControlBoardRetail } from './retail';

export type * from './core';
export * from './enums';
export type * from './lead';
export type * from './orderIntake';
export type * from './retail';
export type * from './typings';

export type SalesControlBoard = SalesControlBoardRetail | SalesControlBoardOrderIntake | SalesControlBoardLead;

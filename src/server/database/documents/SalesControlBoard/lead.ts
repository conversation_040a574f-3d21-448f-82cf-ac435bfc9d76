import type { SalesControlBoardCore } from './core';
import { SalesControlBoardDataType } from './enums';
import type { SalesControlBoardWeeklyCount } from './typings';

export type SalesControlBoardLead = SalesControlBoardCore<SalesControlBoardDataType.Leads> & {
    ytd: {
        actual: number;
        lio?: number;
        ulr?: LeadRatioTuple;
        olr?: LeadRatioTuple;
        pi?: number;
    };
    month: {
        actual: number;
        lio?: number;
        ulr?: LeadRatioTuple;
        olr?: LeadRatioTuple;
        pi?: number;
    };

    weeks: Array<SalesControlBoardWeeklyCount>; // ordered actual leads by week
};

export type LeadRatioTuple = {
    numerator: number;
    denominator: number;
};

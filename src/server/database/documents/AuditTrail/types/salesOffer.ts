import type { ObjectId } from 'mongodb';
import { SalesOfferFeatureKind } from '../../SalesOffer';
import type { Author } from '../../Versioning';
import { type AuditTrailCore, AuditTrailKind } from '../core';
import type { KnownChange } from './shared/change';

type SalesOfferAuditTrailCore<TKind extends AuditTrailKind> = AuditTrailCore<TKind> & {
    // sales offer ID
    salesOfferId: ObjectId;
    author: Author;
};

export type SalesOfferPorscheCodeRetrievedAuditTrail =
    SalesOfferAuditTrailCore<AuditTrailKind.SalesOfferPorscheCodeRetrieved> & {
        porscheCode: string;
    };

export type SalesOfferFeatureSentAuditTrail = SalesOfferAuditTrailCore<AuditTrailKind.SalesOfferFeatureSent> & {
    featureKind: SalesOfferFeatureKind;
};

export type SalesOfferDocumentSignedAuditTrail = SalesOfferAuditTrailCore<AuditTrailKind.SalesOfferDocumentSigned> & {
    featureKind: SalesOfferFeatureKind;
};

export type SalesOfferUpdatedAuditTrail = SalesOfferAuditTrailCore<AuditTrailKind.SalesOfferUpdated> & {
    tabKey: string;
};

export type SalesOfferAmendmentsAuditTrail = SalesOfferAuditTrailCore<AuditTrailKind.SalesOfferAmendments> & {
    tabKey: string;
    changes: KnownChange[];
};

export type SalesOfferAuditTrail =
    | SalesOfferPorscheCodeRetrievedAuditTrail
    | SalesOfferFeatureSentAuditTrail
    | SalesOfferDocumentSignedAuditTrail
    | SalesOfferUpdatedAuditTrail
    | SalesOfferAmendmentsAuditTrail;

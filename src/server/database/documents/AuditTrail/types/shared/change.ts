import type {
    ApplicationValueSetting,
    DealerOption,
    MobilityBookingLocation,
    MonthlyInstalment,
} from '../../../Applications';
import type { StringDescription } from '../../../Customer';
import type { StockBlockingPeriod } from '../../../Inventory';
import type { FittedOptions } from '../../../SalesOffer';
import type { UploadedFileWithPreview } from '../../../UploadedFile';
import type { Period, Phone } from '../../../shared';

export enum ChangeKind {
    String = 'string',
    Boolean = 'boolean',
    Number = 'number',
    Percent = 'percent',
    Period = 'period',
    Date = 'date',
    DateTime = 'datetime',
    Year = 'year',
    Amount = 'amount',
    Currency = 'currency',
    CompoundValue = 'compoundValue',
    MonthlyInstalment = 'monthlyInstalment',
    DealerOption = 'dealerOption',
    Phone = 'phone',
    StringDescription = 'stringDescription',
    UploadedFileWithPreview = 'uploadedFileWithPreview',

    MobilityLocation = 'location',

    // on inventory stock
    BlockPeriod = 'blockPeriod',

    // sales offer
    LocalFittedOptions = 'localFittedOptions',
}
export type Change<TKind extends ChangeKind, TValue> = {
    key: string;
    before: TValue;
    after: TValue;
    kind: TKind;
};

type StringChange = Change<ChangeKind.String, string>;
type BooleanChange = Change<ChangeKind.Boolean, boolean>;
type NumberChange = Change<ChangeKind.Number, number>;
type PercentChange = Change<ChangeKind.Percent, number>;
type AmountChange = Change<ChangeKind.Amount, number>;
type CurrencyChange = Change<ChangeKind.Currency, number>;
type PeriodChange = Change<ChangeKind.Period, Period>;
type MobilityLocationChange = Change<ChangeKind.MobilityLocation, MobilityBookingLocation>;
type DateChange = Change<ChangeKind.Date, Date>;
type DateTimeChange = Change<ChangeKind.DateTime, Date>;
type YearChange = Change<ChangeKind.Year, Date>;
type CompoundValueChange = Change<ChangeKind.CompoundValue, ApplicationValueSetting>;
type MonthlyInstalmentChange = Change<ChangeKind.MonthlyInstalment, MonthlyInstalment[]>;
type DealerOptionChange = Change<ChangeKind.DealerOption, DealerOption[]>;
type PhoneChange = Change<ChangeKind.Phone, Phone>;
type StringDescriptionChange = Change<ChangeKind.StringDescription, StringDescription>;
type UploadedFileWithPreviewChange = Change<ChangeKind.UploadedFileWithPreview, UploadedFileWithPreview[]>;
type BlockPeriodChange = Change<ChangeKind.BlockPeriod, StockBlockingPeriod[]>;
type LocalFittedOptionsChange = Change<ChangeKind.LocalFittedOptions, FittedOptions[]>;

export type KnownChange =
    | StringChange
    | BooleanChange
    | NumberChange
    | PercentChange
    | AmountChange
    | CurrencyChange
    | PeriodChange
    | MobilityLocationChange
    | DateChange
    | DateTimeChange
    | YearChange
    | CompoundValueChange
    | MonthlyInstalmentChange
    | DealerOptionChange
    | PhoneChange
    | StringDescriptionChange
    | UploadedFileWithPreviewChange
    | BlockPeriodChange
    | LocalFittedOptionsChange;

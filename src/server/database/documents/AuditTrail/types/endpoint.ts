import { ObjectId } from 'mongodb';
import type { Author } from '../../Versioning';
import { AuditTrailCore, AuditTrailKind } from '../core';

export type EndpointReplacedAuditTrail = AuditTrailCore<AuditTrailKind.EndpointReplaced> & {
    companyId: ObjectId;
    // The deleted endpoint
    deletedRouterId: ObjectId;
    deletedEndpointId: ObjectId;
    // The replacement endpoint
    replacementRouterId: ObjectId;
    replacementEndpointId: ObjectId;
    // Author of the replacement
    author: Author;
};

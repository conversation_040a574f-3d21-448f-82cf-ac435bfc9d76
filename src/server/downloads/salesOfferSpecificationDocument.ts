import * as Sentry from '@sentry/node';
import { Request<PERSON>and<PERSON> } from 'express';
import { isNil } from 'lodash/fp';
import { getFileStream } from '../core/storage';
import { ExternalLinkKind } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';

type Param = {
    secret: string;
};

const download: RequestHandler<Param, unknown, unknown> = async (req, res, next) => {
    if (req.method !== 'GET') {
        // The app.get() function is automatically called for the HTTP HEAD method
        // in addition to the GET method if app.head() was not called for the path before app.get().
        // https://expressjs.com/en/api.html#app.METHOD
        res.status(200).send();
    }

    const { secret } = req.params;

    const { collections } = await getDatabaseContext();

    const link = await collections.externalLinks.findOne({ secret });

    if (isNil(link) || link._kind !== ExternalLinkKind.SalesOfferSpecificationDocumentDownload) {
        res.status(404).send();

        return;
    }

    const salesOffer = await collections.salesOffers.findOne({ _id: link.data.salesOfferId });

    if (isNil(salesOffer)) {
        res.status(404).send();

        return;
    }
    const document = salesOffer.vehicle.specificationDocument;
    if (isNil(document)) {
        res.status(404).send('No specification document found for this sales offer');

        return;
    }

    try {
        const source = await getFileStream(document);
        if (!source) {
            res.status(404).send('Document not found');
        }

        res.set({
            'Content-Disposition': `attachment; filename="${document.filename}"; filename*="${document.filename}"`,
            'Content-Type': 'application/pdf',
        });
        source.pipe(res);
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('specificationDocument', 'vehicle');
            scope.setLevel('fatal');
            scope.setContext('vehicle', salesOffer);
            Sentry.captureException(error);
        });

        next(error);
    }
};

export default download;

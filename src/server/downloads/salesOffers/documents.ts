import * as Sentry from '@sentry/node';
import { RequestHandler } from 'express';
import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getFileStream } from '../../core/storage';
import { ExternalLinkKind } from '../../database/documents';
import getDatabaseContext from '../../database/getDatabaseContext';
import { getSalesOfferDocuments } from '../../schema/resolvers/mutations/salesOffers/downloadSalesOfferDocument';

type Param = {
    secret: string;
};

const cleanup = async (linkId: ObjectId, deleteOnFetch: boolean) => {
    if (deleteOnFetch) {
        const { collections } = await getDatabaseContext();
        collections.externalLinks.findOneAndDelete({ _id: linkId });
    }
};

const download: RequestHandler<Param, unknown, unknown> = async (req, res, next) => {
    if (req.method !== 'GET') {
        // The app.get() function is automatically called for the HTTP HEAD method
        // in addition to the GET method if app.head() was not called for the path before app.get().
        // https://expressjs.com/en/api.html#app.METHOD
        res.status(200).send();

        return;
    }

    const { secret } = req.params;

    const { collections } = await getDatabaseContext();

    const link = await collections.externalLinks.findOne({ secret });

    if (isNil(link) || link._kind !== ExternalLinkKind.SalesOfferDocumentDownload) {
        res.status(404).send();

        return;
    }

    const {
        data: { salesOfferId, fileId, kind },
        deleteOnFetch,
    } = link;

    const salesOffer = await collections.salesOffers.findOne({ _id: salesOfferId });

    if (isNil(salesOffer)) {
        res.status(404).send();

        return;
    }

    const documents = getSalesOfferDocuments(kind, salesOffer);
    const document = documents.find(({ _id }) => _id.equals(fileId));

    try {
        const source = await getFileStream(document);
        res.setHeader(
            'Content-Disposition',
            `attachment; filename="${document.filename}"; filename*="${document.filename}"`
        );

        source.pipe(res);

        await cleanup(link._id, deleteOnFetch);
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('download', 'salesOffer');
            scope.setLevel('fatal');
            scope.setContext('salesOffer', salesOffer);
            Sentry.captureException(error);
        });

        next(error);
    }
};

export default download;

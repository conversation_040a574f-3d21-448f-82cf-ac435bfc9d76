import type { JobOptions } from 'bull';
import { QueueHandler } from './QueueHandler';

// Create a singleton instance
let mainQueueHandler: QueueHandler<any> | null = null;

export const setMainQueueHandler = (handler: QueueHandler<any>) => {
    mainQueueHandler = handler;
};

export const addToMainQueue = async (message: any, options?: JobOptions) => {
    if (!mainQueueHandler) {
        throw new Error('Main queue handler not initialized');
    }

    return mainQueueHandler.add(message, options);
};

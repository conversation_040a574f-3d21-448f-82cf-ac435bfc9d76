import dayjs from 'dayjs';
import { i18n } from 'i18next';
import { last } from 'lodash';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../../core/storage';
import { ApplicationKind, ApplicationStage } from '../../../database/documents/Applications/core';
import type {
    ConfiguratorApplication,
    EventApplication,
    FinderApplication,
    LaunchpadApplication,
    SalesOfferApplication,
    StandardApplication,
} from '../../../database/documents/Applications/kinds';
import {
    type CalendarEventEmailHistory,
    CalendarEventType,
    EmailHistoryType,
} from '../../../database/documents/CalendarEvent';
import type { Customer } from '../../../database/documents/Customer';
import type { User } from '../../../database/documents/User';
import { VehicleKind, type FinderVehicle, type LocalVariant } from '../../../database/documents/Vehicle/types';
import {
    type AppointmentModule,
    type LocalCustomerManagementModule,
    ModuleType,
    type StandardApplicationModule,
    type VisitAppointmentModule,
} from '../../../database/documents/modules';
import getDatabaseContext from '../../../database/getDatabaseContext';
import {
    getCustomerEmail,
    getCustomerFullName,
    getCustomerFullNameWithTitle,
} from '../../../database/helpers/customers';
import { getKYCPresetsForCustomerModule } from '../../../database/helpers/kyc';
import { type EmailContext } from '../../../database/helpers/settings';
import {
    AudienceMessage,
    sendCancellationEmailToBank,
    sendCustomerCancelConfirmation,
    sendSalespersonCancelConfirmation,
} from '../../../emails';
import createCompanySMTPTransports from '../../../emails/transporters/createCompanySMTPTransports';
import createLoaders, { type Loaders } from '../../../loaders';
import {
    AppointmentModuleAsset,
    BankIntegrationProvider,
    StandardApplicationModuleAsset,
    VisitAppointmentModuleAsset,
} from '../../../schema/resolvers/enums';
import { CalendarEventService } from '../../../services/CalendarEventService';
import { getApplicationIdentifier } from '../../../utils/application';
import createI18nInstance from '../../../utils/createI18nInstance';
import { getFormattedDateOnly, getTimeZoneOffset } from '../../../utils/date';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import { getAppointmentEmailContent } from '../appointmentTestDrive/shared';
import { canSendEmailToCustomer } from '../sendApplicationSubmissionMail/shared';
import { generateCancellationInvitation, getAdditionalCalendarEmails } from '../shared/calendarInvitationHandler';
import checkIsBankViewable from '../shared/checkIsBankViewable';
import getApplicationAssignee from '../shared/getApplicationAssignee';
import getApplicationContext from '../shared/getApplicationContext';
import getApplicationDetailsLink from '../shared/getApplicationDetailsLink';
import { getStandardApplicationEmailContent } from '../shared/getStandardApplicationEmailContents';
import { getVisitAppointmentEmailContent } from '../showroomVisitAppointment/shared';

type Application =
    | ConfiguratorApplication
    | StandardApplication
    | FinderApplication
    | EventApplication
    | SalesOfferApplication
    | LaunchpadApplication;

const canSendCancelEmailToSalesperson = (stage: ApplicationStage) => stage !== ApplicationStage.Appointment;
const getCancellationEmailContentByStage = async ({
    application,
    loaders,
    i18n,
    emailContext,
    vehicle,
    dealerId,
    customer,
    customerModule,
    audience,
    assignee,
    link,
    stage,
}: {
    application: Application;
    loaders: Loaders;
    i18n: i18n;
    emailContext: EmailContext;
    vehicle: LocalVariant | FinderVehicle;
    dealerId: ObjectId;
    customer: Customer;
    customerModule: LocalCustomerManagementModule;
    audience: AudienceMessage;
    assignee?: User;
    link: string;
    stage: ApplicationStage;
}) => {
    switch (stage) {
        case ApplicationStage.Appointment: {
            const appointmentModule = (await loaders.moduleById.load(
                application.appointmentStage?.appointmentModuleId
            )) as AppointmentModule;

            return getAppointmentEmailContent({
                application,
                i18n,
                loaders,
                appointmentModule,
                appointmentAsset:
                    audience === AudienceMessage.Salesperson
                        ? AppointmentModuleAsset.SalesPersonBookingCancellation
                        : AppointmentModuleAsset.CustomerBookingCancellation,
                emailContext,
                audience,
                vehicle,
                dealerId,
                customer,
                customerModule,
                assignee,
                link,
            });
        }

        case ApplicationStage.VisitAppointment: {
            const visitAppointmentModule = (await loaders.moduleById.load(
                application.visitAppointmentStage?.visitAppointmentModuleId
            )) as VisitAppointmentModule;

            return getVisitAppointmentEmailContent({
                application,
                i18n,
                visitAppointmentModule,
                visitAppointmentAsset:
                    audience === AudienceMessage.Salesperson
                        ? VisitAppointmentModuleAsset.SalesPersonBookingCancellation
                        : VisitAppointmentModuleAsset.CustomerBookingCancellation,
                emailContext,
                vehicle,
                dealerId,
                customer,
                customerModule,
                assignee,
                link,
            });
        }

        default: {
            throw new Error(`Application stage ${stage} is not implemented`);
        }
    }
};

const sendGenericCancellationEmail = async (application: Application, stage: ApplicationStage) => {
    const { collections } = await getDatabaseContext();
    const loaders = createLoaders();

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'calculators', 'variant']);

    const { t } = i18n;

    const context = await getApplicationContext<LocalVariant | FinderVehicle>(application._id, stage);
    const { customer, company, variant, financeProduct, bank, dealer, customerModule, lead } = context;

    const assignee = await getApplicationAssignee(application, lead, stage);

    // load mail context
    const emailContext = await getCompanyEmailContext(company);
    const edmEmailFooterContext = await getEdmEmailFooterContext({ company });
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const defaultIntroImage =
        variant._kind === VehicleKind.FinderVehicle
            ? last(variant.listing.vehicle.images.edges[0].node.variants).url
            : await getUrlForUpload(variant?.images?.[0]);

    const transporter = await createCompanySMTPTransports(company);
    const link = await getApplicationDetailsLink(application, lead, assignee, stage);
    const identifier = getApplicationIdentifier(application, lead, stage);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, customer._kind);

    const customerFullName = getCustomerFullNameWithTitle(t, customer, company, kycPresets);
    const isBankViewable = context.bank
        ? await checkIsBankViewable(variant._versioning.suiteId, application.moduleId)
        : false;

    const hasTestDriveProcess = !!context.journey.testDriveSetting;
    if (
        application.kind !== ApplicationKind.Standard &&
        (canSendCancelEmailToSalesperson(stage) ||
            // Salesperson need to cancel calendar event for Test Drive
            ((application.kind === ApplicationKind.Event || application.kind === ApplicationKind.Launchpad) &&
                (stage === ApplicationStage.Appointment || stage === ApplicationStage.VisitAppointment)))
    ) {
        const subject = t('emails:salesPersonCancelConfirmation.subject', {
            companyName: emailContext.companyName,
            identifier,
            customerName: customerFullName,
        });

        let calendarAttachment = null;
        let calendarInvitation = null;
        if (
            company.shouldSendCalendarInvite &&
            (application.kind === ApplicationKind.Event || application.kind === ApplicationKind.Launchpad) &&
            (stage === ApplicationStage.Appointment || stage === ApplicationStage.VisitAppointment)
        ) {
            // Calculate appointment date and time for template replacement
            const appointmentSlot =
                stage === ApplicationStage.Appointment
                    ? application.appointmentStage?.bookingTimeSlot?.slot || new Date()
                    : application.visitAppointmentStage?.bookingTimeSlot?.slot || new Date();

            const appointmentDate = getFormattedDateOnly(t, appointmentSlot, company?.timeZone);
            const appointmentTime = `${dayjs(appointmentSlot)
                .tz(company.timeZone)
                .format('hh:mm A')} ${getTimeZoneOffset(t, company?.timeZone)}`;

            calendarInvitation = await generateCancellationInvitation({
                emailSubject: subject,
                contentText: t('emails:salesPersonCancelConfirmation.calendarCancellationText', {
                    customerName: customerFullName,
                    defaultValue: 'This appointment has been cancelled.',
                }),
                appointmentSlot,
                dealer,
                company,
                application,
                eventType:
                    stage === ApplicationStage.Appointment
                        ? CalendarEventType.TestDrive
                        : CalendarEventType.ShowroomVisit,
                assignee,
                appointmentDate,
                appointmentTime,
            });

            if (calendarInvitation) {
                calendarAttachment = {
                    content: Buffer.from(calendarInvitation.icsContent, 'utf-8'),
                    filename: calendarInvitation.filename,
                    contentType: 'text/calendar; method=CANCEL',
                };
            }
        }

        // Prepare recipients array including assignee and additional calendar emails
        const recipients = [{ name: assignee.displayName, address: assignee.email }];

        // Add additional calendar recipients if calendar invite is enabled
        if (company.shouldSendCalendarInvite && calendarAttachment) {
            const additionalEmails = getAdditionalCalendarEmails(assignee);
            if (additionalEmails.length > 0) {
                recipients.push(...additionalEmails.map(email => ({ name: assignee.displayName, address: email })));
            }
        }

        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } = [
            ApplicationStage.Appointment,
            ApplicationStage.VisitAppointment,
        ].includes(stage)
            ? await getCancellationEmailContentByStage({
                  application,
                  i18n,
                  loaders,
                  emailContext,
                  vehicle: variant,
                  dealerId: dealer._id,
                  customer,
                  customerModule,
                  audience: AudienceMessage.Salesperson,
                  assignee,
                  link,
                  stage,
              })
            : {
                  introImageUrl: defaultIntroImage,
                  emailSubject: subject,
                  introTitle: null,
                  contentText: null,
                  isSummaryVehicleVisible: true,
              };

        // Send email and handle calendar event storage
        try {
            await sendSalespersonCancelConfirmation(
                {
                    i18n,
                    data: {
                        ...context,
                        assignee,
                        emailContext,
                        edmEmailFooterContext,
                        dealerEmailContext,
                        introImageUrl,
                        introTitle,
                        contentText,
                        link,
                        stage,
                        isBankViewable,
                        hasTestDriveProcess,
                        isSummaryVehicleVisible,
                    },
                    to: recipients,
                    subject: emailSubject,
                    attachments: calendarAttachment ? [calendarAttachment] : undefined,
                },
                transporter,
                emailContext.sender
            );

            // Store cancelled calendar event with email history if calendar invitation was generated
            if (calendarInvitation?.calendarEvent) {
                const emailHistory: CalendarEventEmailHistory = {
                    emailType: EmailHistoryType.Cancellation,
                    sentAt: new Date(),
                    success: true,
                    recipientType: 'salesperson',
                    subject,
                };

                const calendarEventWithHistory = CalendarEventService.addEmailHistory(
                    calendarInvitation.calendarEvent,
                    emailHistory
                );

                await collections.calendarEvents.replaceOne(
                    { _id: calendarInvitation.calendarEvent._id },
                    calendarEventWithHistory
                );
            }
        } catch (error) {
            // Store failed email attempt in calendar event history if calendar invitation was generated
            if (calendarInvitation?.calendarEvent) {
                const emailHistory: CalendarEventEmailHistory = {
                    emailType: EmailHistoryType.Cancellation,
                    sentAt: new Date(),
                    success: false,
                    recipientType: 'salesperson',
                    subject,
                };

                const calendarEventWithHistory = CalendarEventService.addEmailHistory(
                    calendarInvitation.calendarEvent,
                    emailHistory
                );

                await collections.calendarEvents.replaceOne(
                    { _id: calendarInvitation.calendarEvent._id },
                    calendarEventWithHistory
                );
            }

            throw error;
        }
    }

    if (application.kind === ApplicationKind.Standard && canSendCancelEmailToSalesperson(stage)) {
        const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
            _id: application.moduleId,
            _type: ModuleType.StandardApplicationModule,
        });
        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
            await getStandardApplicationEmailContent({
                i18n,
                standardApplicationModule,
                standardApplicationAsset: StandardApplicationModuleAsset.SalesPersonCancelled,
                emailContext,
                dealer,
                customer,
                customerModule,
                user: assignee,
                link,
                identifier,
                lead,
            });

        await sendSalespersonCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,

                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introTitle,
                    contentText,
                    introImageUrl: introImageUrl ?? defaultIntroImage,
                    isSummaryVehicleVisible,

                    link,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,
                },
                to: { name: assignee.displayName, address: assignee.email },
                subject: emailSubject,
            },
            transporter,
            emailContext.sender
        );
    }

    if (
        application.kind === ApplicationKind.Standard &&
        canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)
    ) {
        const standardApplicationModule = await collections.modules.findOne<StandardApplicationModule>({
            _id: application.moduleId,
            _type: ModuleType.StandardApplicationModule,
        });

        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } =
            await getStandardApplicationEmailContent({
                i18n,
                standardApplicationModule,
                standardApplicationAsset: StandardApplicationModuleAsset.CustomerCancelled,
                emailContext,
                dealer,
                customer,
                customerModule,
                user: assignee,
                link,
                identifier,
                lead,
            });
        await sendCustomerCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,

                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introTitle,
                    contentText,
                    isSummaryVehicleVisible,
                    introImageUrl: introImageUrl ?? defaultIntroImage,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,
                },
                to: {
                    name: getCustomerFullName(t, customer, company, kycPresets),
                    address: getCustomerEmail(t, customer),
                },
                subject:
                    application.kind === ApplicationKind.Standard
                        ? emailSubject
                        : t('emails:customerCancelConfirmation.subject', {
                              companyName: emailContext.companyName,
                              identifier,
                          }),
            },
            transporter,
            emailContext.sender
        );
    }

    // send the email to customer
    if (
        application.kind !== ApplicationKind.Standard &&
        canSendEmailToCustomer(application, context.bank, null, context.journey, false, customer, t)
    ) {
        const { introImageUrl, emailSubject, introTitle, contentText, isSummaryVehicleVisible } = [
            ApplicationStage.Appointment,
            ApplicationStage.VisitAppointment,
        ].includes(stage)
            ? await getCancellationEmailContentByStage({
                  application,
                  i18n,
                  loaders,
                  emailContext,
                  vehicle: variant,
                  dealerId: dealer._id,
                  customer,
                  customerModule,
                  audience: AudienceMessage.Customer,
                  assignee,
                  link,
                  stage,
              })
            : {
                  introImageUrl: defaultIntroImage,
                  emailSubject: t('emails:customerCancelConfirmation.subject', {
                      companyName: emailContext.companyName,
                      identifier,
                  }),
                  introTitle: null,
                  contentText: null,
                  isSummaryVehicleVisible: true,
              };

        await sendCustomerCancelConfirmation(
            {
                i18n,
                data: {
                    ...context,
                    assignee,
                    introTitle,
                    contentText,
                    isSummaryVehicleVisible,
                    emailContext,
                    edmEmailFooterContext,
                    dealerEmailContext,
                    introImageUrl,
                    stage,
                    isBankViewable,
                    hasTestDriveProcess,
                    isAppointment: stage === ApplicationStage.Appointment,
                },
                to: {
                    name: getCustomerFullName(t, customer, company, kycPresets),
                    address: getCustomerEmail(t, customer),
                },
                subject: emailSubject,
            },
            transporter,
            emailContext.sender
        );
    }

    if (stage === ApplicationStage.Financing && bank.integration.provider === BankIntegrationProvider.Email) {
        await sendCancellationEmailToBank(
            {
                i18n,
                data: {
                    emailContext,
                    bank,
                    application,
                    variant,
                    financeProduct,
                    assignee,
                    module: context.applicationModule,
                    dealerId: dealer._id,
                },
                subject: t('emails:bankCancellation.subject', {
                    companyName: emailContext.companyName,
                    identifier: application.financingStage?.identifier,
                    customerName: customerFullName,
                }),
                to: { name: bank.displayName, address: bank.integration.email },
            },
            transporter,
            emailContext.sender
        );
    }
};

export default sendGenericCancellationEmail;

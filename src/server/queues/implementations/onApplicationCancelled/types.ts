import { ObjectId } from 'mongodb';
import { ApplicationStage } from '../../../database/documents/Applications/core';
import type { DbsWebhookPayload } from '../../../integrations/banks/dbs/types';

export enum ApplicationCancelSource {
    UOB = 'uob',
    User = 'user',
    DBS = 'dbs',
    Customer = 'customer',
}

export type OnUserApplicationCancelledMessage = {
    source: ApplicationCancelSource.User;
    applicationId: ObjectId;
    stage: ApplicationStage;
    userId: ObjectId;
};

export type OnCustomerBookingCancelledMessage = {
    source: ApplicationCancelSource.Customer;
    applicationId: ObjectId;
    applicantId: ObjectId;
};

export type OnUobIntegrationApplicationCancelledMessage = {
    source: ApplicationCancelSource.UOB;
    bankId: ObjectId;
    applicationId: ObjectId;
    reference: string;
    subDescription: string | null | undefined;
};

export type OnDbsApplicationCancelledMessage = {
    source: ApplicationCancelSource.DBS;
    bankId: ObjectId;
    applicationId: ObjectId;
    partnerCode: string;
    payload: DbsWebhookPayload;
};

export type OnApplicationCancelledMessage =
    | OnUserApplicationCancelledMessage
    | OnUobIntegrationApplicationCancelledMessage
    | OnDbsApplicationCancelledMessage
    | OnCustomerBookingCancelledMessage;

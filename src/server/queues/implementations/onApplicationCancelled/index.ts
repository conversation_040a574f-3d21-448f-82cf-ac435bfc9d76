import { Document } from 'bson';
import { Job } from 'bull';
import { isEmpty, isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import {
    Application,
    ApplicationCancelledAuditTrail,
    ApplicationJourneySubmissionKind,
    ApplicationKind,
    ApplicationStage,
    ApplicationStatus,
    AuditTrailKind,
    AuthorKind,
    BankIntegrationProvider,
    InventoryKind,
    SettingId,
} from '../../../database/documents';
import getDatabaseContext from '../../../database/getDatabaseContext';
import hlfCancel from '../../../integrations/banks/hlf/cancel';
import maybankCancel from '../../../integrations/banks/maybank/cancel';
import uobCancel, { UobCancelResponse } from '../../../integrations/banks/uob/cancel';
import { UobErrorResponse } from '../../../integrations/banks/uob/shared';
import { getApplicationLogStages, getStatusUpdates } from '../../../utils/application';
import { releaseStock } from '../onApplicationDeclined';
import sendGenericCancellationEmail from './sendGenericCancellationEmail';
import sendMobilityCancellationEmail from './sendMobilityCancellationEmail';
import { ApplicationCancelSource, OnApplicationCancelledMessage } from './types';

const generateAuditTrail = (
    message: OnApplicationCancelledMessage,
    application: Application,
    reference: string | null | undefined
): ApplicationCancelledAuditTrail => {
    const auditTrail: Omit<ApplicationCancelledAuditTrail, 'author'> = {
        _id: new ObjectId(),
        _kind: AuditTrailKind.ApplicationCancelled,
        _date: new Date(),
        applicationId: application._id,
        applicationSuiteId: application._versioning.suiteId,
        stages: getApplicationLogStages(
            application,
            AuditTrailKind.ApplicationCancelled,
            message.source === ApplicationCancelSource.User ? [message.stage] : null
        ),
        ...(reference && { reference }),
    };

    switch (message.source) {
        case ApplicationCancelSource.User:
            return {
                ...auditTrail,
                author: { kind: AuthorKind.User, id: message.userId },
            };

        case ApplicationCancelSource.UOB:
        case ApplicationCancelSource.DBS:
            return {
                ...auditTrail,
                author: { kind: AuthorKind.Bank, id: message.bankId },
            };

        case ApplicationCancelSource.Customer: {
            return {
                ...auditTrail,
                author: { kind: AuthorKind.Customer, id: message.applicantId },
            };
        }

        default:
            return null;
    }
};

type CancelApplicationByBankApiResult =
    | {
          kind: 'success';
          reference?: string;
      }
    | {
          kind: 'failure';
          reason: string;
      };

const cancelApplicationByBankApi = async (
    application: Application,
    salesRef?: string,
    remark?: string
): Promise<CancelApplicationByBankApiResult> => {
    const { collections } = await getDatabaseContext();
    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    let result: CancelApplicationByBankApiResult = {
        kind: 'success',
    };
    if ('bankId' in application && application.bankId && !!journey) {
        const { moduleId } = application;
        const bank = await collections.banks.findOne({ _id: application.bankId });
        const { submission } = journey;

        switch (bank?.integration?.provider) {
            case BankIntegrationProvider.HLF:
            case BankIntegrationProvider.HLFV2: {
                const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
                if (
                    (setting.settingId === SettingId.HlfBankIntegration ||
                        setting.settingId === SettingId.HlfBankV2Integration) &&
                    submission?.kind === ApplicationJourneySubmissionKind.HLF
                ) {
                    const assignee = application?.financingStage?.assigneeId
                        ? await collections.users.findOne({ _id: application.financingStage.assigneeId })
                        : null;

                    const { hlfref } = await hlfCancel(setting, {
                        hlfref: submission.reference,
                        bookingref: application.financingStage?.identifier,
                        salesref: salesRef ?? assignee?.alias ?? '',
                    });

                    result = {
                        kind: 'success',
                        reference: hlfref,
                    };
                }

                break;
            }

            case BankIntegrationProvider.UOB: {
                const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
                if (
                    setting.settingId === SettingId.UobBankIntegration &&
                    submission?.kind === ApplicationJourneySubmissionKind.UOB
                ) {
                    const module = await collections.modules.findOne({ _id: moduleId });
                    const company = module ? await collections.companies.findOne({ _id: module.companyId }) : null;

                    const response = await uobCancel(
                        setting,
                        {
                            referenceNumber: submission.reference,
                            salesAgreementRefNumber: application.financingStage?.identifier,
                            ...(remark && { cancellationReason: remark }),
                        },
                        company?.countryCode ?? ''
                    );

                    if (response.code === '0000000') {
                        const casted = response as UobCancelResponse;

                        result = {
                            kind: 'success',
                            reference: casted.referenceNumber,
                        };
                    } else {
                        const casted = response as UobErrorResponse;

                        result = {
                            kind: 'failure',
                            reason: casted.subDescription,
                        };
                    }
                }

                break;
            }

            case BankIntegrationProvider.Maybank: {
                const setting = await collections.settings.findOne({ _id: bank.integration.settingId });
                if (
                    setting.settingId === SettingId.MaybankIntegration &&
                    submission?.kind === ApplicationJourneySubmissionKind.Maybank
                ) {
                    const response = await maybankCancel(setting, {
                        msg: {
                            applicationData: {
                                DealerID: setting.secrets.dealerId,
                                LOSRefNumber: submission.reference,
                                SalesAgreementNo: application.financingStage?.identifier,
                            },
                        },
                    });

                    if (response?.msg?.status?.StatusCode === 'S') {
                        result = {
                            kind: 'success',
                            reference: response.msg.applicationData.LOSRefNumber,
                        };
                    } else {
                        result = {
                            kind: 'failure',
                            reason: response?.msg?.status?.ErrorDesc,
                        };
                    }
                }
            }
        }
    }

    return result;
};

const onApplicationCancelled = async (message: OnApplicationCancelledMessage, job: Job<Document>) => {
    const { collections } = await getDatabaseContext();

    const { applicationId } = message;

    const application = await collections.applications.findOne({ _id: applicationId, '_versioning.isLatest': true });

    if (isNil(application)) {
        throw new Error('unable to locate application');
    }

    const lead = await collections.leads.findOne({ _id: application.leadId });

    let remark: string | undefined;
    let salesRef: string | undefined;

    let reference: string | undefined;
    if (message.source === ApplicationCancelSource.User && message.stage === ApplicationStage.Financing) {
        if (application.kind !== ApplicationKind.Mobility && application.kind !== ApplicationKind.Launchpad) {
            const result = await cancelApplicationByBankApi(application, salesRef, remark);

            if (result.kind === 'success') {
                reference = result.reference;
            } else {
                // create the audit trail for cancellation failures
                await collections.auditTrails.insertOne({
                    _id: new ObjectId(),
                    _kind: AuditTrailKind.ApplicationCancellationToBankFailed,
                    _date: new Date(),
                    applicationId: application._id,
                    applicationSuiteId: application._versioning.suiteId,
                    stages: getApplicationLogStages(application, AuditTrailKind.ApplicationCancellationToBankFailed),
                    bankId: application.bankId,
                    reason: result.reason,
                });

                return;
            }
        }
    }

    if (message.source === ApplicationCancelSource.UOB) {
        const { reference, subDescription } = message;
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    'submission.reference': reference,
                    'submission.subDescription': subDescription,
                },
            }
        );
    }

    if (message.source === ApplicationCancelSource.DBS) {
        const { partnerCode } = message;
        await collections.applicationJourneys.updateOne(
            { applicationSuiteId: application._versioning.suiteId },
            {
                $set: {
                    'submission.partnerCode': partnerCode,
                },
            }
        );
    }

    // update application
    const statusUpdates = getStatusUpdates(
        application,
        lead,
        ApplicationStatus.Cancelled,
        message.source === ApplicationCancelSource.User ? [message.stage] : null
    );
    await collections.applications.findOneAndUpdate(
        { _id: application._id },
        {
            ...(!isEmpty(statusUpdates) && { $set: statusUpdates }),
            $unset: { promoCodeId: 1 },
        }
    );

    // create audit trail
    await collections.auditTrails.insertOne(generateAuditTrail(message, application, reference));
    switch (application.kind) {
        case ApplicationKind.Configurator: {
            await releaseStock(
                InventoryKind.ConfiguratorInventory,
                application.configuratorId,
                collections,
                application._versioning.suiteId
            );

            if (message.source === ApplicationCancelSource.User) {
                await sendGenericCancellationEmail(application, message.stage);
            }

            break;
        }

        case ApplicationKind.Event:
        case ApplicationKind.Standard:
        case ApplicationKind.Finder:
        case ApplicationKind.SalesOffer:
        case ApplicationKind.Launchpad: {
            if (message.source === ApplicationCancelSource.User) {
                await sendGenericCancellationEmail(application, message.stage);
            }

            break;
        }

        case ApplicationKind.Mobility: {
            sendMobilityCancellationEmail(application);
            break;
        }

        default:
            throw new Error('ApplicationKind not support');
    }
};

export default onApplicationCancelled;

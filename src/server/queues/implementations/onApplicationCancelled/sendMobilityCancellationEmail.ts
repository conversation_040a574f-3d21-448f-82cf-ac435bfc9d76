import { getUrlForUpload } from '../../../core/storage';
import type { MobilityApplication } from '../../../database/documents/Applications/kinds';
import { AuditTrailKind } from '../../../database/documents/AuditTrail/core';
import {
    InventoryKind,
    type MobilityStockInventory,
    StockInventoryKind,
    type MobilityInventory,
} from '../../../database/documents/Inventory';
import type { LocalModel, LocalVariant } from '../../../database/documents/Vehicle/types';
import { type DealerTranslationText } from '../../../database/documents/moduleShared';
import { ModuleType } from '../../../database/documents/modules';
import getDatabaseContext from '../../../database/getDatabaseContext';
import { updateAuditTrailForMobilityEmailSent } from '../../../database/helpers/auditTrails';
import { getCustomerEmail, getCustomerFullNameWithTitle, getCustomerName } from '../../../database/helpers/customers';
import { getKYCPresetsForCustomerModule } from '../../../database/helpers/kyc';
import { createCompanySMTPTransports, sendMobilityEmail } from '../../../emails';
import { AudienceMessage } from '../../../emails/type';
import getTranslatedEmailContent from '../../../emails/utils/getTranslatedEmailContent';
import { MobilityAsset, MobilityRecipient } from '../../../schema/resolvers/enums';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';
import getEdmEmailFooterContext from '../../../utils/getEdmEmailFooterContext';
import getMobilityEmailActionLinks from '../shared/getMobilityEmailActionLinks';
import getMobilityEmailContent from '../shared/getMobilityEmailContent';

const sendMobilityCancellationEmail = async (application: MobilityApplication) => {
    const { collections } = await getDatabaseContext();

    const journey = await collections.applicationJourneys.findOne({
        applicationSuiteId: application._versioning.suiteId,
    });

    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails']);
    const { t } = i18n;
    const module = await collections.modules.findOne({ _id: application.moduleId });
    if (module._type !== ModuleType.MobilityModule) {
        throw new Error('ModuleType not support');
    }

    const inventory = (await collections.inventories.findOne({
        _kind: InventoryKind.MobilityInventory,
        moduleId: application.moduleId,
        'stocks._id': application.mobilityBookingDetails.inventoryStockId,
    })) as MobilityInventory;

    const stock = inventory.stocks.find(
        stock =>
            stock._kind === StockInventoryKind.MobilityStock &&
            stock.reservations.find(reserve => reserve.applicationId.equals(application._versioning.suiteId))
    ) as MobilityStockInventory;

    const reservation = stock.reservations.find(reserve =>
        reserve.applicationId.equals(application._versioning.suiteId)
    );

    const [applicant, variant, company, dealer] = await Promise.all([
        collections.customers.findOne({ _id: application.applicantId }),
        collections.vehicles.findOne({ _id: application.vehicleId }) as Promise<LocalVariant>,
        collections.companies.findOne({ _id: module.companyId }),
        collections.dealers.findOne({ _id: application.dealerId }),
    ]);
    const customerModule = await collections.modules.findOne({ _id: applicant.moduleId });

    if (customerModule._type !== ModuleType.LocalCustomerManagement) {
        throw new Error('Customer Module not found');
    }
    const model = (await collections.vehicles.findOne({ _id: variant.modelId })) as LocalModel;

    const [emailContext, edmEmailFooterContext] = await Promise.all([
        getCompanyEmailContext(company),
        getEdmEmailFooterContext({ company }),
    ]);
    const kycPresets = getKYCPresetsForCustomerModule(customerModule, applicant._kind);

    const customerName = getCustomerName(t, applicant, company, kycPresets);

    const customerFullName = getCustomerFullNameWithTitle(t, applicant, company, kycPresets);
    const customerEmail = getCustomerEmail(t, applicant);
    const dealerEmailContext = await getDealerEmailContext(dealer);

    const transporter = await createCompanySMTPTransports(company);

    const introImageUrl = await getUrlForUpload(variant.images?.[0]);

    // release the booked stock to become available
    await collections.inventories.updateOne(
        {
            _kind: InventoryKind.MobilityInventory,
            moduleId: application.moduleId,
            variantSuiteId: variant._versioning.suiteId,
            'stocks._id': application.mobilityBookingDetails.inventoryStockId,
        },
        {
            $pull: {
                'stocks.$[stock].reservations': reservation,
            },
        },
        {
            arrayFilters: [{ 'stock._id': application.mobilityBookingDetails.inventoryStockId }],
        }
    );

    const { amendLink, cancelLink } = await getMobilityEmailActionLinks(module._id, application);

    const getEmailContent = getMobilityEmailContent(application, module);

    const customerSubject: DealerTranslationText = getEmailContent('customers.bookingCancellation', 'subject');
    const operatorSubject: DealerTranslationText = getEmailContent('operators.bookingCancellation', 'subject');

    // send to customer
    await sendMobilityEmail(
        {
            i18n,
            data: {
                emailContext,
                mobilityRecipient: MobilityRecipient.Customers,
                mobilityAsset: MobilityAsset.BookingCancellation,
                dealer,

                customerName,
                customerFullName,
                customerEmail,
                variant,
                modelName: model.name,

                application,
                applicant,

                edmEmailFooterContext,
                introImageUrl,
                introTitle: getEmailContent('customers.bookingCancellation', 'introTitle'),
                contentText: getEmailContent('customers.bookingCancellation', 'contentText'),

                payment: journey.deposit,
                amendLink,
                cancelLink,
                dealerEmailContext,
            },
            to: { name: getCustomerEmail(t, applicant), address: getCustomerEmail(t, applicant) },
            subject: getTranslatedEmailContent({
                i18nLanguage: i18n.language,
                dealerTranslationText: customerSubject,
                preferredDealerId: dealer._id.toHexString(),
            }),
        },
        transporter,
        emailContext.sender
    );

    // send to Operators/ administrator of company
    await sendMobilityEmail(
        {
            i18n,
            data: {
                emailContext,
                mobilityRecipient: MobilityRecipient.Operators,
                mobilityAsset: MobilityAsset.BookingCancellation,
                dealer,

                // this is a temporarily name to dealer, will have new ticket to resolve dealer email
                customerName: dealer.displayName,
                customerFullName: dealer.displayName,

                // this is a temporarily email to dealer, will have new ticket to resolve dealer email
                customerEmail: dealer.contact.email,
                variant,
                modelName: model.name,

                application,
                applicant,

                edmEmailFooterContext,
                introImageUrl,
                introTitle: getEmailContent('operators.bookingCancellation', 'introTitle'),
                contentText: getEmailContent('operators.bookingCancellation', 'contentText'),

                payment: journey.deposit,
                amendLink,
                cancelLink,
                dealerEmailContext,
            },
            to: { name: dealer.displayName, address: dealer.contact.email },
            subject: getTranslatedEmailContent({
                i18nLanguage: i18n.language,
                dealerTranslationText: operatorSubject,
                preferredDealerId: dealer._id.toHexString(),
            }),
        },
        transporter,
        emailContext.sender
    );

    await updateAuditTrailForMobilityEmailSent(
        application,
        AuditTrailKind.BookingCancellationEmailSent,
        AudienceMessage.Customer
    );
    await updateAuditTrailForMobilityEmailSent(
        application,
        AuditTrailKind.BookingCancellationEmailSent,
        AudienceMessage.Dealer
    );
};

export default sendMobilityCancellationEmail;

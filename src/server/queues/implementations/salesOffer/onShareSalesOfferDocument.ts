import { Document } from 'bson';
import { Job } from 'bull';
import { TFunction } from 'i18next';
import { ObjectId } from 'mongodb';
import { getFileStream } from '../../../core/storage';
import {
    LocalModel,
    LocalVariant,
    SalesOffer,
    SalesOfferDocumentKind,
    SalesOfferModule,
} from '../../../database/documents';
import { createCompanySMTPTransports, sendShareSalesOfferDocumentEmail } from '../../../emails';
import getTranslatedEmailContent from '../../../emails/utils/getTranslatedEmailContent';
import getTranslatedString from '../../../emails/utils/getTranslatedString';
import createLoaders from '../../../loaders';
import { getSalesOfferDocuments } from '../../../schema/resolvers/mutations/salesOffers/downloadSalesOfferDocument';
import { streamToBuffer } from '../../../utils';
import createI18nInstance from '../../../utils/createI18nInstance';
import getCompanyEmailContext from '../../../utils/getCompanyEmailContext';
import getDealerEmailContext from '../../../utils/getDealerEmailContext';

const getDocumentName = (documentKind: SalesOfferDocumentKind, fileName: string, t: TFunction): string => {
    switch (documentKind) {
        case SalesOfferDocumentKind.VSA:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.vsa');

        case SalesOfferDocumentKind.MainDetails:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.coe');

        case SalesOfferDocumentKind.Vehicle:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.vehicle');

        case SalesOfferDocumentKind.Deposit:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.deposit');

        case SalesOfferDocumentKind.Finance:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.finance');

        case SalesOfferDocumentKind.Insurance:
            return t('launchpadSalesOfferDetails:sections.summaryDetails.category.insurance');

        case SalesOfferDocumentKind.Others:
            return fileName.split('.')[0];

        default:
            return '';
    }
};

export type OnShareSalesOfferDocumentMessage = {
    salesOfferId: ObjectId;
    fileId: ObjectId;
    kind: SalesOfferDocumentKind;
    firstName: string;
    lastName: string;
    email: string;
};

export const onShareSalesOfferDocument = async (message: OnShareSalesOfferDocumentMessage, job: Job<Document>) => {
    const { salesOfferId, fileId, kind, firstName, lastName, email } = message;
    const loaders = createLoaders();

    const salesOffer = (await loaders.salesOfferById.load(salesOfferId)) as SalesOffer;

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const documents = getSalesOfferDocuments(kind, salesOffer);
    const document = documents.find(i => i._id.equals(fileId));

    if (!document) {
        throw new Error('Document not found');
    }

    const source = await streamToBuffer(await getFileStream(document));
    const attachments = [{ filename: document.filename, content: source }];

    const [salesOfferModule, lead, variant] = await Promise.all([
        loaders.moduleById.load(salesOffer.moduleId) as Promise<SalesOfferModule>,
        loaders.leadBySuiteId.load(salesOffer.leadSuiteId),
        loaders.vehicleById.load(salesOffer.vehicle.vehicleId) as Promise<LocalVariant>,
    ]);
    const model = (await loaders.vehicleById.load(variant.modelId)) as LocalModel;
    const company = await loaders.companyById.load(salesOfferModule.companyId);
    const dealer = await loaders.dealerById.load(lead.dealerId);

    const { i18n } = await createI18nInstance(lead.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'emails', 'variant', 'launchpadSalesOfferDetails']);
    const { t, language } = i18n;

    const emailContent = salesOfferModule.emailContents.shareTemplate;
    const { dealerId } = lead;
    const updatedParams = {
        firstName,
        lastName,
        vehicleModel: getTranslatedString(model.name, language),
        vehicleModelVariant: getTranslatedString(variant.name, language),
        documentName: getDocumentName(document.kind, document.filename, t),
    };

    const introTitle = getTranslatedEmailContent({
        i18nLanguage: language,
        dealerTranslationText: emailContent.introTitle,
        preferredDealerId: dealerId.toHexString(),
        params: updatedParams,
    });

    const contentText = getTranslatedEmailContent({
        i18nLanguage: language,
        dealerTranslationText: emailContent.contentText,
        preferredDealerId: dealerId.toHexString(),
        params: updatedParams,
    });

    const subject = getTranslatedEmailContent({
        i18nLanguage: language,
        dealerTranslationText: emailContent.subject,
        preferredDealerId: dealerId.toHexString(),
        params: updatedParams,
    });

    const [emailContext, dealerEmailContext] = await Promise.all([
        getCompanyEmailContext(company),
        getDealerEmailContext(dealer),
    ]);
    const transporter = await createCompanySMTPTransports(company);

    await sendShareSalesOfferDocumentEmail(
        {
            data: {
                dealerEmailContext,
                contentText,
                emailContext,
                introTitle,
            },
            to: {
                name: `${firstName} ${lastName}`,
                address: email,
            },
            attachments,
            subject,
            i18n,
        },
        transporter,
        emailContext.sender
    );
};

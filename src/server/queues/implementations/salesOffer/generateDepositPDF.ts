import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../../core/storage';
import {
    ConsentsAndDeclarationsPurpose,
    LaunchpadLead,
    LocalCustomerFieldKey,
    SalesOffer,
    SalesOfferModule,
} from '../../../database/documents';
import { getKycFieldsFromLead } from '../../../database/helpers';
import getCompanyFontInBase64 from '../../../journeys/helper/getCompanyFontInBase64';
import { Loaders } from '../../../loaders';
import { renderDepositPDF } from '../../../pdf';
import { salesOfferPDFRenderOptions } from '../../../pdf/shared';
import createI18nInstance from '../../../utils/createI18nInstance';
import { processAgreementsForPdf } from '../../../utils/processAgreementsForPdf';
import { translateOptions } from '../../../utils/translateOptions';

type GenerateDepositPDFParams = {
    languageId: ObjectId;
    salesOffer: SalesOffer;
    lead: LaunchpadLead;
    loaders: Loaders;
};

const generateDepositPDF = async ({ languageId, salesOffer, lead, loaders }: GenerateDepositPDFParams) => {
    // load translations
    const { i18n } = await createI18nInstance(languageId?.toHexString() ?? null);

    await i18n.loadNamespaces(['common', 'saleOfferPdf', 'applicationPdf']);

    const { t } = i18n;
    const options = translateOptions(t);

    const salesOfferModule = (await loaders.moduleById.load(salesOffer.moduleId)) as SalesOfferModule;

    const company = await loaders.companyById.load(salesOfferModule.companyId);

    const applicant = await loaders.customerById.load(lead.customerId);

    const kycFields = (await getKycFieldsFromLead(lead)).filter(
        field =>
            field.key === LocalCustomerFieldKey.FirstName ||
            field.key === LocalCustomerFieldKey.LastName ||
            field.key === LocalCustomerFieldKey.Email ||
            field.key === LocalCustomerFieldKey.Phone
    );

    const journey = salesOffer.latestReservationApplicationSuiteId
        ? await loaders.applicationJourneyBySuiteId.load(salesOffer.latestReservationApplicationSuiteId)
        : null;

    // by right this should never happen,
    if (isNil(journey)) {
        throw new Error('Reservation Application Journey not found');
    }

    const processedAgreements = await processAgreementsForPdf(
        journey.applicantAgreements.agreements.filter(agreement =>
            agreement.purpose.includes(ConsentsAndDeclarationsPurpose.Payment)
        ) ?? [],
        loaders
    );

    return renderDepositPDF(
        {
            companyLogo: company.logo ? await getUrlForUpload(company.logo) : null,
            deposit: journey.deposit,
            applicant,
            kycFields,
            options,
            agreements: processedAgreements,
            fontUrlInBase64: await getCompanyFontInBase64(company),
            company,
            pdsRenderOptions: salesOfferPDFRenderOptions,
        },
        i18n
    );
};

export default generateDepositPDF;

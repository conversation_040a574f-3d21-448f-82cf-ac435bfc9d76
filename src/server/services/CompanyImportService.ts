import * as crypto from 'crypto';
import { Readable } from 'stream';
import AdmZip from 'adm-zip';
import { EJSON, Document } from 'bson';
import mime from 'mime-types';
import { ObjectId, type ClientSession } from 'mongodb';
import { minioClient, getBucketName } from '../core/storage';
import type { AdvancedVersioning, Module } from '../database/documents';
import type { Company } from '../database/documents/Company';
import type { BucketType, UploadedFile } from '../database/documents/UploadedFile';
import type { DatabaseContext } from '../database/getDatabaseContext';
import { mainQueue } from '../queues';
import scheduleApplicationDataPurgeJob from '../queues/implementations/shared/scheduleApplicationDataPurgeJob';
import { getSimpleVersioningByUserForCreation, getAdvancedVersioningByUserForCreation } from '../utils/versioning';

export type ImportOptions = {
    skipFiles?: boolean;
    dryRun?: boolean;
};

export type ImportStatistics = {
    modulesCount: number;
    filesCount: number;
    totalSize: number;
    collectionsCount: number;
};

export type ImportConflict = {
    collection: string;
    documentId: string;
    field?: string;
    message: string;
};

export type ImportResult = {
    success: boolean;
    companyId?: ObjectId;
    message: string;
    conflicts: ImportConflict[];
    statistics?: ImportStatistics;
    errors?: string[];
};

export type FileReference = {
    fileId: string;
    collection: string;
    documentId: string;
    fieldPath: string;
    bucketType: BucketType;
    filename: string;
    objectName: string;
    size: number;
    data?: Buffer; // File data for processing during import
};

export type ImportData = {
    company: Document;
    modules: Document[];
    languagePacks?: Document[];
    dealers?: Document[];
    roles?: Document[];
    userGroups?: Document[];
    settings?: Document[];
    permissions?: Document[];
    routers?: Document[];
    events?: Document[];
    banners?: Document[];
    consentsAndDeclarations?: Document[];
    campaigns?: Document[];
    fileReferences: FileReference[];
    [key: string]: Document | Document[] | FileReference[] | undefined;
};

export type ImportManifest = {
    version: string;
    exportDate: Date;
    exportedFrom: string;
    exportedBy?: string;
    checksum: string;
    company: {
        _id: string;
        displayName: string;
    };
    statistics: ImportStatistics;
};

export class CompanyImportService {
    private filesToProcess: FileReference[] = [];

    private conflicts: ImportConflict[] = [];

    private fileUploadErrors: string[] = [];

    constructor(private databaseContext: DatabaseContext) {
        // Initialize the service
    }

    /**
     * Upload file preserving exact original filename, path, and ObjectId from export
     * This bypasses the normal uploadFile function which generates unique filenames and ObjectIds
     */
    private async uploadFileWithOriginalName(
        bucketType: BucketType,
        objectName: string,
        originalFilename: string,
        originalFileId: ObjectId,
        data: Buffer
    ): Promise<UploadedFile> {
        const bucket = getBucketName(bucketType);
        const meta = {
            'Content-Type': mime.lookup(originalFilename) || 'application/octet-stream',
            ContentType: mime.lookup(originalFilename) || 'application/octet-stream',
            _id: originalFileId.toHexString(),
        };

        // Upload using the exact original objectName to preserve the exported structure
        const { etag } = await minioClient.putObject(bucket, objectName, data, data.length, meta);

        const stats = await minioClient.statObject(bucket, objectName);

        return {
            _id: originalFileId,
            filename: originalFilename,
            displayName: originalFilename,
            uploadedAt: new Date(),
            etag,
            size: stats.size,
            objectName,
            bucketType,
        };
    }

    /**
     * Import company configuration from stream
     */
    async importCompanyFromStream(
        stream: Readable,
        filename: string,
        userId: ObjectId,
        options: ImportOptions = {
            skipFiles: false,
            dryRun: false,
        }
    ): Promise<ImportResult> {
        try {
            // Reset state for new import
            this.conflicts = [];
            this.fileUploadErrors = [];

            // Parse the uploaded file
            const importData = await this.parseImportFile(stream, filename);

            // Validate the import data
            await this.validateImportData(importData);

            // Import the company data (preserve original IDs)
            const companyId = await this.importCompanyData(importData, userId, options);

            // Schedule post-import jobs
            if (!options.dryRun) {
                await this.schedulePostImportJobs(companyId, importData.company as Company);
            }

            // Calculate statistics
            const statistics = this.calculateStatistics(importData);

            return {
                success: true,
                companyId,
                message: `Company "${importData.company.displayName}" imported successfully`,
                conflicts: this.conflicts,
                statistics,
                errors: this.fileUploadErrors,
            };
        } catch (error) {
            console.error(`Import failed for file "${filename}" by user "${userId}":`, {
                errorMessage: error?.message || 'Unknown error',
                errorStack: error?.stack,
                fileName: filename,
                userId: userId.toString(),
                conflicts: this.conflicts,
                fileUploadErrors: this.fileUploadErrors,
                timestamp: new Date().toISOString(),
            });

            return {
                success: false,
                message: error?.message || 'Import failed due to an unexpected error',
                conflicts: this.conflicts,
                errors: [
                    error?.message
                        ? error.message
                        : 'Unknown error occurred during import. Please verify the file format and contents.',
                ],
            };
        }
    }

    /**
     * Parse import file (ZIP or JSON)
     */
    private async parseImportFile(stream: Readable, filename: string): Promise<ImportData> {
        const fileExtension = filename.toLowerCase().split('.').pop();

        if (fileExtension === 'zip') {
            return this.parseZipFile(stream);
        }

        if (fileExtension === 'json') {
            return this.parseJsonFile(stream);
        }

        throw new Error('Unsupported file format. Only ZIP and JSON files are supported.');
    }

    /**
     * Parse ZIP file containing exported company data
     */
    private async parseZipFile(stream: Readable): Promise<ImportData> {
        return new Promise((resolve, reject) => {
            const chunks: Buffer[] = [];

            stream.on('data', chunk => {
                chunks.push(chunk);
            });

            stream.on('end', () => {
                try {
                    const buffer = Buffer.concat(chunks);
                    const zip = new AdmZip(buffer);
                    const zipEntries = zip.getEntries();

                    let manifest: ImportManifest | null = null;
                    let importData: ImportData | null = null;
                    const files: { [key: string]: Buffer } = {};

                    // Process each entry in the ZIP file
                    zipEntries.forEach(entry => {
                        if (entry.isDirectory) {
                            return; // Skip directories
                        }

                        const fileName = entry.entryName;
                        const fileData = entry.getData();

                        if (fileName === 'manifest.json') {
                            try {
                                manifest = JSON.parse(fileData.toString('utf8'));
                            } catch (parseErr) {
                                console.error('Failed to parse manifest.json:', {
                                    errorMessage: parseErr.message,
                                    errorStack: parseErr.stack,
                                    fileName,
                                    timestamp: new Date().toISOString(),
                                });
                                reject(new Error(`Failed to parse manifest: ${parseErr.message}`));
                            }
                        } else if (fileName === 'data/export.json') {
                            try {
                                const dataString = fileData.toString('utf8');
                                importData = EJSON.deserialize(JSON.parse(dataString)) as ImportData;
                            } catch (parseErr) {
                                console.error('Failed to parse export.json:', {
                                    errorMessage: parseErr.message,
                                    errorStack: parseErr.stack,
                                    fileName,
                                    timestamp: new Date().toISOString(),
                                });
                                reject(new Error(`Failed to parse import data: ${parseErr.message}`));
                            }
                        } else if (fileName.startsWith('files/')) {
                            files[fileName] = fileData;
                        }
                    });

                    if (!importData) {
                        reject(new Error('No import data found in ZIP file'));

                        return;
                    }

                    if (manifest) {
                        // Validate checksum if manifest is present
                        const calculatedChecksum = this.calculateChecksum(importData);
                        if (calculatedChecksum !== manifest.checksum) {
                            reject(new Error('Import data checksum validation failed'));

                            return;
                        }
                    }

                    // Store files for later processing
                    this.storeFilesForProcessing(files, importData.fileReferences);

                    resolve(importData);
                } catch (error) {
                    reject(new Error(`Failed to process ZIP file: ${error.message}`));
                }
            });

            stream.on('error', streamErr => {
                reject(streamErr);
            });
        });
    }

    /**
     * Parse JSON file containing exported company data
     */
    private async parseJsonFile(stream: Readable): Promise<ImportData> {
        return new Promise((resolve, reject) => {
            const chunks: Buffer[] = [];

            stream.on('data', chunk => {
                chunks.push(chunk);
            });

            stream.on('end', () => {
                try {
                    const jsonString = Buffer.concat(chunks).toString('utf8');
                    const importData = EJSON.deserialize(JSON.parse(jsonString)) as ImportData;
                    resolve(importData);
                } catch (error) {
                    reject(new Error(`Failed to parse JSON file: ${error.message}`));
                }
            });

            stream.on('error', error => {
                reject(error);
            });
        });
    }

    /**
     * Validate import data structure
     */
    private async validateImportData(importData: ImportData): Promise<void> {
        const validationErrors: string[] = [];

        if (!importData.company) {
            validationErrors.push('company information is missing');
        }

        if (!importData.modules || !Array.isArray(importData.modules)) {
            validationErrors.push('modules information is missing or invalid');
        }

        if (!importData.company?.displayName) {
            validationErrors.push('company display name is required');
        }

        // Validate that company has a valid ObjectId
        if (!importData.company?._id || !(importData.company._id instanceof ObjectId)) {
            validationErrors.push('company must have a valid _id');
        }

        // Validate that all modules have valid ObjectIds
        importData.modules?.forEach((module, index) => {
            if (!module._id || !(module._id instanceof ObjectId)) {
                validationErrors.push(`module at index ${index} must have a valid _id`);
            }
            if (!module.companyId || !(module.companyId instanceof ObjectId)) {
                validationErrors.push(`module at index ${index} must have a valid companyId`);
            }
        });

        if (validationErrors.length > 0) {
            console.error('Import data validation failed:', {
                validationErrors,
                companyId: importData?.company?._id?.toString(),
                companyDisplayName: importData?.company?.displayName,
                modulesCount: importData?.modules?.length || 0,
                timestamp: new Date().toISOString(),
            });

            throw new Error(`Invalid import data: ${validationErrors.join(', ')}`);
        }
    }

    /**
     * Check if company already exists
     */
    private async checkExistingCompany(importData: ImportData): Promise<Company | null> {
        const { displayName } = importData.company;

        return this.databaseContext.collections.companies.findOne({
            displayName,
            isDeleted: false,
        });
    }

    /**
     * Import company data with transaction
     */
    private async importCompanyData(
        importData: ImportData,
        userId: ObjectId,
        options: ImportOptions
    ): Promise<ObjectId> {
        const { client } = this.databaseContext.regular;
        const session = client.startSession();

        try {
            let companyId: ObjectId;

            await session.withTransaction(async () => {
                companyId = importData.company._id;

                const updatedCompany = this.updateDocumentVersioning(importData.company, userId);
                await this.databaseContext.collections.companies.insertOne(updatedCompany as Company, { session });

                if (importData.modules) {
                    const updatedModules = importData.modules.map(module =>
                        this.updateDocumentVersioning(module, userId)
                    );
                    await this.databaseContext.collections.modules.insertMany(updatedModules as Module[], { session });
                }

                await this.importRelatedCollections(importData, session, userId, options);
            });

            return companyId;
        } catch (error) {
            console.error('Transaction failed during company import:', {
                errorMessage: error?.message || 'Unknown transaction error',
                errorStack: error?.stack,
                companyId: importData.company._id?.toString(),
                companyDisplayName: importData.company.displayName,
                userId: userId.toString(),
                modulesCount: importData.modules?.length || 0,
                timestamp: new Date().toISOString(),
            });
            throw new Error(`Import failed: ${error.message}`);
        } finally {
            await session.endSession();
        }
    }

    private isAdvancedVersioning(versioning: Document['_versioning']): versioning is AdvancedVersioning {
        return versioning && 'suiteId' in versioning && 'isLatest' in versioning;
    }

    /**
     * Update document versioning for import
     */
    private updateDocumentVersioning(document: Document, userId: ObjectId): Document {
        const now = new Date();

        if (document._versioning) {
            if (this.isAdvancedVersioning(document._versioning)) {
                // AdvancedVersioning - preserve suiteId and isLatest
                const newVersioning = getAdvancedVersioningByUserForCreation(userId, document._versioning.suiteId, now);

                return {
                    ...document,
                    _versioning: { ...newVersioning, isLatest: document._versioning.isLatest },
                };
            }

            // SimpleVersioning
            const newVersioning = getSimpleVersioningByUserForCreation(userId, now);

            return {
                ...document,
                _versioning: newVersioning,
            };
        }

        return document;
    }

    /**
     * Store files for later processing
     */
    private storeFilesForProcessing(files: { [key: string]: Buffer }, fileReferences: FileReference[]): void {
        // Store file data for later upload during import
        // Files in ZIP are organized as: files/{bucketType}/{objectName}
        // The objectName contains the original directory structure that will be preserved during upload
        this.filesToProcess = fileReferences.map(ref => {
            const filePath = `files/${ref.bucketType}/${ref.objectName}`;

            return {
                ...ref,
                // Add buffer data for later processing
                data: files[filePath],
            } as FileReference;
        });

        const missingFiles = this.filesToProcess.filter(ref => !ref.data);
        if (missingFiles.length > 0) {
            const missingFilesList = missingFiles.map(f => f.filename).join(', ');
            this.fileUploadErrors.push(`Missing ${missingFiles.length} files in ZIP: ${missingFilesList}`);
        }
    }

    /**
     * Calculate checksum for data validation
     */
    private calculateChecksum(data: ImportData): string {
        const hash = crypto.createHash('sha256');
        hash.update(EJSON.stringify(data));

        return hash.digest('hex');
    }

    /**
     * Calculate import statistics
     */
    private calculateStatistics(importData: ImportData): ImportStatistics {
        return {
            modulesCount: importData.modules?.length || 0,
            filesCount: importData.fileReferences?.length || 0,
            collectionsCount: this.countCollections(importData),
            totalSize: this.calculateTotalSize(importData.fileReferences || []),
        };
    }

    /**
     * Count number of collections in import data
     */
    private countCollections(importData: ImportData): number {
        let count = 1; // Company collection

        const collections = [
            'modules',
            'dealers',
            'roles',
            'userGroups',
            'languagePacks',
            'permissions',
            'routers',
            'events',
            'banners',
            'consentsAndDeclarations',
            'settings',
            'campaigns',
        ];

        collections.forEach(collection => {
            if (importData[collection] && Array.isArray(importData[collection]) && importData[collection].length > 0) {
                count++;
            }
        });

        return count;
    }

    /**
     * Calculate total size of files
     */
    private calculateTotalSize(fileReferences: FileReference[]): number {
        return fileReferences.reduce((total, ref) => total + (ref.size || 0), 0);
    }

    /**
     * Import related collections
     */
    private async importRelatedCollections(
        importData: ImportData,
        session: ClientSession,
        userId: ObjectId,
        options: ImportOptions
    ): Promise<void> {
        // Import all available collections from the export data
        const collections = [
            'dealers',
            'roles',
            'userGroups',
            'languagePacks',
            'permissions',
            'routers',
            'events',
            'consentsAndDeclarations',
            'settings',
            'banners',
            'campaigns',
        ];

        // Import documents for each collection
        const insertPromises = collections.map(async collectionName => {
            const documents = importData[collectionName];
            if (documents && Array.isArray(documents) && documents.length > 0) {
                try {
                    // Update versioning for all documents and import
                    const updatedDocuments = documents.map(document => this.updateDocumentVersioning(document, userId));

                    if (this.databaseContext.collections[collectionName]) {
                        await this.databaseContext.collections[collectionName].insertMany(updatedDocuments, {
                            session,
                        });
                    }
                } catch (error) {
                    // Convert MongoDB duplicate key errors to more descriptive conflict information
                    if (error.code === 11000) {
                        const conflictInfo = this.parseDuplicateKeyError(error, collectionName);
                        this.conflicts.push(conflictInfo);

                        console.error(`Duplicate key conflict during import in collection "${collectionName}":`, {
                            errorMessage: error.message,
                            errorCode: error.code,
                            conflictInfo,
                            collection: collectionName,
                            documentsCount: documents.length,
                            timestamp: new Date().toISOString(),
                        });

                        throw new Error(
                            `Duplicate key conflict in ${collectionName}: ${conflictInfo.message}. 
                            To resolve this, check if the data already exists in the database.`
                        );
                    }

                    console.error(`Error importing collection "${collectionName}":`, {
                        errorMessage: error?.message || 'Unknown collection import error',
                        errorStack: error?.stack,
                        errorCode: error?.code,
                        collection: collectionName,
                        documentsCount: documents.length,
                        timestamp: new Date().toISOString(),
                    });

                    throw error;
                }
            }
        });

        await Promise.all(insertPromises);

        if (!options.skipFiles && this.filesToProcess.length > 0) {
            await this.processFilesUpload(options);
        }
    }

    /**
     * Process file uploads to storage
     * Since we preserve original ObjectIds, no database updates are needed
     * The document references already point to the correct file ObjectIds
     */
    private async processFilesUpload(options: ImportOptions): Promise<void> {
        if (options.dryRun) {
            // In dry run mode, just validate files but don't upload
            return;
        }

        // Process files in batches to avoid overwhelming storage
        const batchSize = 5;

        // Process files in batches using Promise.all
        for (let i = 0; i < this.filesToProcess.length; i += batchSize) {
            const batch = this.filesToProcess.slice(i, i + batchSize);

            // eslint-disable-next-line no-await-in-loop
            await Promise.all(batch.map(async fileRef => this.uploadSingleFile(fileRef)));
        }
    }

    /**
     * Upload a single file to storage preserving original ObjectId
     */
    private async uploadSingleFile(fileRef: FileReference): Promise<void> {
        try {
            if (!fileRef.data) {
                this.fileUploadErrors.push(`No file data available for ${fileRef.filename}`);

                return;
            }

            // Upload file to storage preserving the exact original objectName and ObjectId from export
            await this.uploadFileWithOriginalName(
                fileRef.bucketType,
                fileRef.objectName, // Use the exact objectName from export
                fileRef.filename,
                new ObjectId(fileRef.fileId),
                fileRef.data
            );
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';
            console.error(`File upload failed during import:`, {
                errorMessage,
                errorStack: error instanceof Error ? error.stack : undefined,
                fileName: fileRef.filename,
                fileId: fileRef.fileId,
                bucketType: fileRef.bucketType,
                objectName: fileRef.objectName,
                fileSize: fileRef.size,
                timestamp: new Date().toISOString(),
            });
            this.fileUploadErrors.push(`Failed to upload file ${fileRef.filename}: ${errorMessage}`);
        }
    }

    /**
     * Schedule post-import jobs for the imported company
     */
    private async schedulePostImportJobs(companyId: ObjectId, company: Company): Promise<void> {
        try {
            await mainQueue.add({ type: 'upsertPermissions', target: 'company', companyId });

            if (company.isDataPurgeEnabled) {
                await scheduleApplicationDataPurgeJob(companyId, company.timeZone, { check: true });
            }
        } catch (error) {
            console.error('Failed to schedule post-import jobs:', {
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                errorStack: error instanceof Error ? error.stack : undefined,
                companyId: companyId.toString(),
                companyDisplayName: company.displayName,
                isDataPurgeEnabled: company.isDataPurgeEnabled,
                timeZone: company.timeZone,
                timestamp: new Date().toISOString(),
            });
        }
    }

    /**
     * Parse MongoDB duplicate key error into conflict information
     */
    private parseDuplicateKeyError(error: any, collectionName: string): ImportConflict {
        let documentId = 'unknown';
        let field = '_id';

        if (error.keyValue) {
            if (error.keyValue._id) {
                documentId = error.keyValue._id.toString();
            }
            field = Object.keys(error.keyValue)[0] || '_id';
        }

        return {
            collection: collectionName,
            documentId,
            field,
            // eslint-disable-next-line max-len
            message: `Document with ${field} "${error.keyValue && error.keyValue[field] ? error.keyValue[field] : documentId}" already exists in ${collectionName}`,
        };
    }
}

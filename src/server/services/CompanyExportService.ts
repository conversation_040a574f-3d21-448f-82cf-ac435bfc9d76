import * as crypto from 'crypto';
import { Readable } from 'stream';
import archiver from 'archiver';
import { EJSON, Document } from 'bson';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { getFileStream } from '../core/storage';
import type { Collections } from '../database/collections';
import type { Banner } from '../database/documents/Banner';
import type { Campaign } from '../database/documents/Campaign';
import type { Company } from '../database/documents/Company';
import type { Condition } from '../database/documents/Conditions';
import type { ConsentsAndDeclarations } from '../database/documents/ConsentsAndDeclarations';
import type { Dealer } from '../database/documents/Dealer';
import type { Event } from '../database/documents/Event';
import type { LanguagePack } from '../database/documents/LanguagePack';
import type { Role, Permission } from '../database/documents/Permissions';
import type { Router } from '../database/documents/Router';
import type { Setting } from '../database/documents/Setting';
import type { UploadedFile, UploadedFileWithPreview, BucketType } from '../database/documents/UploadedFile';
import type { UserGroup } from '../database/documents/UserGroup';
import type { Module, LaunchPadModule, EventApplicationModule } from '../database/documents/modules';
import { ModuleType } from '../database/documents/modules';
// Types and interfaces
export type ExportOptions = {
    includeFiles?: boolean;
    compress?: boolean;
    format?: 'EJSON' | 'JSON';
};

export type ExportStatistics = {
    modulesCount: number;
    filesCount: number;
    totalSize: number;
    collectionsCount: number;
};

export type FileReference = {
    fileId: string;
    collection: string;
    documentId: string;
    fieldPath: string;
    bucketType: BucketType;
    filename: string;
    objectName: string;
    size: number;
};

export type ExportData = {
    company: Document;
    modules: Document[];
    languagePacks?: Document[];
    dealers?: Document[];
    roles?: Document[];
    userGroups?: Document[];
    settings?: Document[];
    permissions?: Document[];
    routers?: Document[];
    events?: Document[];
    banners?: Document[];
    consentsAndDeclarations?: Document[];
    agreementsModules?: Document[];
    campaigns?: Document[];
    fileReferences: FileReference[];
    [key: string]: Document | Document[] | FileReference[] | undefined;
};

export type ExportManifest = {
    version: string;
    exportDate: Date;
    exportedFrom: string;
    exportedBy?: string;
    checksum: string;
    company: {
        _id: string;
        displayName: string;
    };
    statistics: ExportStatistics;
};

type ExportedModuleTypes =
    | ModuleType.ConsentsAndDeclarations
    | ModuleType.PorscheMasterDataModule
    | ModuleType.SimpleVehicleManagement
    | ModuleType.LocalCustomerManagement
    | ModuleType.AppointmentModule
    | ModuleType.CapModule
    | ModuleType.LaunchPadModule
    | ModuleType.EventApplicationModule;

interface HasModuleReferences {
    [key: string]: unknown;
    moduleId?: ObjectId;
    appointmentModuleId?: ObjectId;
    visitAppointmentModuleId?: ObjectId;
    applicationModuleId?: ObjectId;
    eventApplicationModuleId?: ObjectId;
    launchPadApplicationModuleId?: ObjectId;
    configuratorApplicationModuleId?: ObjectId;
    mobilityApplicationModuleId?: ObjectId;
    websiteModuleId?: ObjectId;
    languagePackId?: ObjectId;
    bankId?: ObjectId;
    insurerId?: ObjectId;
    applicationModuleIds?: ObjectId[];
    finderApplicationModuleIds?: ObjectId[];
}

// Constants
// Constants moved to class constructor for better encapsulation and testability

// Type for related data collections
type RelatedDataCollections = {
    dealers?: Dealer[];
    roles?: Role[];
    userGroups?: UserGroup[];
    languagePacks?: LanguagePack[];
    permissions?: Permission[];
    routers?: Router[];
    events?: Event[];
    banners?: Banner[];
    consentsAndDeclarations?: ConsentsAndDeclarations[];
    settings?: Setting[];
    porscheMasterDataModules?: Setting[];
    capModules?: Setting[];
    smtpEmailSettings?: Setting[];
    twilioSmsSettings?: Setting[];
    campaigns?: Campaign[];
    [key: string]: unknown[] | undefined;
};

// Type for documents that can contain file references
type DocumentWithFiles = {
    _id: ObjectId;
    [key: string]: unknown;
};

// Types for modules with specific properties
type ModuleWithVehicles = Module & {
    dealerVehicles: Record<string, unknown>[];
};

type ModuleWithFinanceProducts = Module & {
    dealerFinanceProducts: Record<string, unknown>[];
};

type ModuleWithInsuranceProducts = Module & {
    dealerInsuranceProducts: Record<string, unknown>[];
};

export class CompanyExportService {
    private fileReferences: FileReference[] = [];

    private readonly allowedModuleTypes: readonly ExportedModuleTypes[];

    private readonly settingsCollections: readonly string[];

    private readonly singleModuleReferenceFields: readonly (keyof HasModuleReferences)[];

    private readonly fileBatchSize: number;

    constructor(
        private collections: Collections,
        options: { fileBatchSize?: number } = {}
    ) {
        // Initialize constants as instance properties for better encapsulation and testability
        this.allowedModuleTypes = [
            ModuleType.ConsentsAndDeclarations,
            ModuleType.PorscheMasterDataModule,
            ModuleType.SimpleVehicleManagement,
            ModuleType.LocalCustomerManagement,
            ModuleType.AppointmentModule,
            ModuleType.CapModule,
            ModuleType.LaunchPadModule,
            ModuleType.EventApplicationModule,
        ] as const;

        this.settingsCollections = [
            'porscheMasterDataModules',
            'capModules',
            'smtpEmailSettings',
            'twilioSmsSettings',
            'settings',
        ] as const;

        this.singleModuleReferenceFields = [
            'moduleId',
            'appointmentModuleId',
            'visitAppointmentModuleId',
            'applicationModuleId',
            'eventApplicationModuleId',
            'launchPadApplicationModuleId',
            'configuratorApplicationModuleId',
            'mobilityApplicationModuleId',
            'websiteModuleId',
        ] as const;

        /**
         * Number of files to process concurrently during export operations.
         *
         * This value controls the batch size for file processing to prevent:
         * - Memory exhaustion when handling large files (images, documents, PDFs)
         * - Storage system rate limiting and connection pool exhaustion
         * - Network timeout issues from too many concurrent streams
         *
         * A lower value (1-5) reduces memory usage but increases processing time.
         * A higher value (20+) may cause memory issues or storage system overload.
         * The default of 10 provides optimal balance between performance and resource usage.
         *
         * Can be configured via EXPORT_FILE_BATCH_SIZE environment variable or constructor options.
         */
        this.fileBatchSize = options.fileBatchSize ?? parseInt(process.env.EXPORT_FILE_BATCH_SIZE || '10', 10);
    }

    /**
     * Export company configuration to stream
     */
    async exportCompanyToStream(
        companyId: ObjectId,
        options: ExportOptions = { includeFiles: true, compress: true, format: 'EJSON' }
    ): Promise<{ stream: Readable; filename: string; contentType: string; statistics: ExportStatistics }> {
        const companyObjId = typeof companyId === 'string' ? new ObjectId(companyId) : companyId;

        const company = await this.collections.companies.findOne({ _id: companyObjId, isDeleted: false });
        if (!company) {
            throw new Error(`Company with ID ${companyObjId} not found`);
        }

        const exportData = await this.collectExportData(company);
        const manifest = await this.createManifest(company, exportData);

        const exportResult = await this.createCompressedExport(company, exportData, manifest, options);

        return {
            ...exportResult,
            statistics: manifest.statistics,
        };
    }

    // Validation methods
    private hasValidModuleReferences(
        obj: HasModuleReferences,
        exportedModuleIds: Set<string>,
        exportedLanguagePackIds: Set<string>
    ): boolean {
        // Check single module references
        for (const field of this.singleModuleReferenceFields) {
            const value = obj[field];
            if (value && !exportedModuleIds.has(value.toString())) {
                return false;
            }
        }

        // Check array module references
        if (obj.applicationModuleIds?.length) {
            const hasInvalidModuleRef = obj.applicationModuleIds.some(
                moduleId => !exportedModuleIds.has(moduleId.toString())
            );
            if (hasInvalidModuleRef) {
                return false;
            }
        }

        // Exclude objects with finder references regardless of export content
        if (obj.finderApplicationModuleIds) {
            return false;
        }

        // Check language pack references
        if (obj.languagePackId && !exportedLanguagePackIds.has(obj.languagePackId.toString())) {
            return false;
        }

        // Exclude objects with bank or insurer references
        if (obj.bankId || obj.insurerId) {
            return false;
        }

        return true;
    }

    private filterConditionsByExportedEntities(
        conditions: Condition[],
        exportedModuleIds: Set<string>,
        exportedLanguagePackIds: Set<string> = new Set()
    ): Condition[] {
        const filterCondition = (condition: Condition): boolean => {
            const conditionWithRefs = condition as HasModuleReferences;

            return this.hasValidModuleReferences(conditionWithRefs, exportedModuleIds, exportedLanguagePackIds);
        };

        return conditions
            .filter(filterCondition)
            .map(condition => {
                const conditionWithChildren = condition as HasModuleReferences & { children?: Condition[] };

                // For logic conditions, recursively filter children
                if (conditionWithChildren.children?.length) {
                    const filteredChildren = this.filterConditionsByExportedEntities(
                        conditionWithChildren.children,
                        exportedModuleIds,
                        exportedLanguagePackIds
                    );

                    // If no children remain, the logic condition is invalid
                    if (filteredChildren.length === 0) {
                        return null;
                    }

                    return {
                        ...condition,
                        children: filteredChildren,
                    } as Condition;
                }

                return condition;
            })
            .filter((condition): condition is Condition => condition !== null);
    }

    // Export creation methods
    private async createCompressedExport(
        company: Company,
        exportData: ExportData,
        manifest: ExportManifest,
        options: ExportOptions
    ): Promise<{ stream: Readable; filename: string; contentType: string }> {
        const archive = archiver('zip', { zlib: { level: 9 } });

        // Add manifest
        archive.append(JSON.stringify(manifest, null, 2), { name: 'manifest.json' });

        // Add export data
        const dataContent = this.serializeExportData(exportData, options.format);
        archive.append(dataContent, { name: 'data/export.json' });

        // Add files if requested
        if (options.includeFiles && this.fileReferences.length > 0) {
            await this.addFilesToArchive(archive);
        }

        archive.finalize();

        const filename = this.generateCompressedFilename(company);

        return {
            stream: archive,
            filename,
            contentType: 'application/zip',
        };
    }

    // Utility methods for export creation
    private serializeExportData(exportData: ExportData, format?: 'EJSON' | 'JSON'): string {
        return format === 'EJSON' ? EJSON.stringify(exportData, null, 2) : JSON.stringify(exportData, null, 2);
    }

    private generateCompressedFilename(company: Company): string {
        const companyName = company.displayName.replace(/\s+/g, '_');
        const timestamp = dayjs().format('YYYY-MM-DD_HHmmss');
        const environment = process.env.NODE_ENV || 'development';
        // Use VERSION env var from Docker build, fallback to env var from CI/CD pipeline
        const version = process.env.VERSION || process.env.CIRCLE_TAG || '1.0.0';

        return `${companyName}_config_export_${timestamp}_${environment}_${version}.zip`;
    }

    private async addFilesToArchive(archive: archiver.Archiver): Promise<void> {
        // Process files in batches to avoid overwhelming storage
        const batchPromises = [];

        for (let i = 0; i < this.fileReferences.length; i += this.fileBatchSize) {
            const batch = this.fileReferences.slice(i, i + this.fileBatchSize);

            const batchPromise = Promise.all(
                batch.map(async fileRef => {
                    try {
                        const fileStream = await getFileStream({
                            _id: new ObjectId(),
                            filename: fileRef.filename,
                            displayName: fileRef.filename,
                            objectName: fileRef.objectName,
                            etag: '',
                            size: 0,
                            uploadedAt: new Date(),
                            bucketType: fileRef.bucketType,
                        });
                        const archivePath = `files/${fileRef.bucketType}/${fileRef.objectName}`;
                        archive.append(fileStream, { name: archivePath });
                    } catch (error) {
                        console.error(`Failed to add file ${fileRef.filename} to archive:`, error);
                        // Continue with other files
                    }
                })
            );

            batchPromises.push(batchPromise);
        }

        await Promise.all(batchPromises);
    }

    // Type guards and utility methods
    private clearSuiteIds<T extends Record<string, unknown>>(arr: T[], field: keyof T): T[] {
        return Array.isArray(arr) ? arr.map(item => ({ ...item, [field]: [] })) : arr;
    }

    private hasFinanceProducts(
        module: Module
    ): module is Module & { _type: ExportedModuleTypes; dealerFinanceProducts: Record<string, unknown>[] } {
        return 'dealerFinanceProducts' in module;
    }

    private hasInsuranceProducts(
        module: Module
    ): module is Module & { _type: ExportedModuleTypes; dealerInsuranceProducts: Record<string, unknown>[] } {
        return 'dealerInsuranceProducts' in module;
    }

    private hasVehicles(
        module: Module
    ): module is Module & { _type: ExportedModuleTypes; dealerVehicles: Record<string, unknown>[] } {
        return 'dealerVehicles' in module;
    }

    private isExportedModuleType(module: Module): module is Module & { _type: ExportedModuleTypes } {
        return this.allowedModuleTypes.includes(module._type as ExportedModuleTypes);
    }

    private hasKycPresets(
        module: Module
    ): module is Module & { _type: ModuleType.LocalCustomerManagement; kycPresets: Record<string, unknown>[] } {
        return module._type === ModuleType.LocalCustomerManagement && 'kycPresets' in module;
    }

    private async processKycPresetsForExport(modules: Module[]): Promise<Module[]> {
        const exportedModuleIds = new Set(modules.map(m => m._id.toString()));

        return modules.map(module => {
            if (this.hasKycPresets(module) && module.kycPresets?.length) {
                const filteredKycPresets = module.kycPresets.map(kycPreset => {
                    const conditions = (kycPreset as { conditions?: Condition[] }).conditions || [];

                    return {
                        ...kycPreset,
                        conditions: this.filterConditionsByExportedEntities(conditions, exportedModuleIds),
                    };
                });

                return {
                    ...module,
                    kycPresets: filteredKycPresets,
                } as Module;
            }

            return module;
        });
    }

    // Main data collection method
    private async collectExportData(company: Company): Promise<ExportData> {
        // Fetch and process modules
        const modules = await this.fetchAndProcessModules(company._id);
        const allModules = await this.addReferencedModules(modules, company._id);

        // Collect related data
        const referencedIds = this.collectReferencedIds(company, allModules);
        const relatedData = await this.fetchRelatedData(referencedIds, company._id);

        // Process permissions and filters
        const relatedDataWithPermissions = await this.processPermissions(relatedData, allModules);
        const processedModules = await this.processKycPresetsForExport(allModules);

        // Add filtered collections
        const finalRelatedData = await this.addFilteredCollections(
            relatedDataWithPermissions,
            processedModules,
            company._id
        );

        // Collect file references
        this.collectAllFileReferences(company, processedModules, finalRelatedData);

        // Create export data structure
        return this.createExportDataStructure(company, processedModules, finalRelatedData);
    }

    private async fetchAndProcessModules(companyId: ObjectId): Promise<Module[]> {
        const modules = await this.collections.modules
            .find({
                companyId,
                _type: { $in: this.allowedModuleTypes },
            })
            .toArray();

        return this.processModulesForExport(modules);
    }

    private processModulesForExport(modules: Module[]): Module[] {
        return modules.map(module => {
            if (!this.isExportedModuleType(module)) {
                return module;
            }

            const updatedModule = { ...module };
            let updated = false;

            // Clear suite IDs from various product arrays
            if (this.hasVehicles(module) && module.dealerVehicles?.length) {
                (updatedModule as ModuleWithVehicles).dealerVehicles = this.clearSuiteIds(
                    module.dealerVehicles,
                    'vehicleSuiteIds'
                );
                updated = true;
            }

            if (this.hasFinanceProducts(module) && module.dealerFinanceProducts?.length) {
                (updatedModule as ModuleWithFinanceProducts).dealerFinanceProducts = this.clearSuiteIds(
                    module.dealerFinanceProducts,
                    'financeProductSuiteIds'
                );
                updated = true;
            }

            if (this.hasInsuranceProducts(module) && module.dealerInsuranceProducts?.length) {
                (updatedModule as ModuleWithInsuranceProducts).dealerInsuranceProducts = this.clearSuiteIds(
                    module.dealerInsuranceProducts,
                    'insuranceProductSuiteIds'
                );
                updated = true;
            }

            // Handle LaunchPadModule specific exclusions
            if (module._type === ModuleType.LaunchPadModule) {
                const launchPadModule = updatedModule as LaunchPadModule;
                launchPadModule.financeAndInsuranceCalculator = null;
                launchPadModule.finderAssignedStock = null;
                launchPadModule.salesOfferModuleId = null;
                launchPadModule.salesManager = null;
                updated = true;
            }

            return updated ? updatedModule : module;
        });
    }

    private async addReferencedModules(modules: Module[], companyId: ObjectId): Promise<Module[]> {
        const referencedModuleIds = this.collectReferencedModuleIds(modules);

        if (referencedModuleIds.size === 0) {
            return modules;
        }

        const referencedModules = await this.collections.modules
            .find({
                _id: { $in: Array.from(referencedModuleIds).map(id => new ObjectId(id)) },
                companyId,
            })
            .toArray();

        // Add referenced modules avoiding duplicates
        const existingModuleIds = new Set(modules.map(m => m._id.toString()));
        const newModules = referencedModules.filter(refModule => !existingModuleIds.has(refModule._id.toString()));

        return [...modules, ...newModules];
    }

    private collectReferencedModuleIds(modules: Module[]): Set<string> {
        const referencedModuleIds = new Set<string>();

        for (const module of modules) {
            if (module._type === ModuleType.LaunchPadModule) {
                const launchPadModule = module as LaunchPadModule;
                this.addModuleReference(referencedModuleIds, launchPadModule.customerModuleId);
                this.addModuleReference(referencedModuleIds, launchPadModule.agreementsModuleId);
                this.addModuleReference(referencedModuleIds, launchPadModule.vehicleModuleId);
                this.addModuleReference(referencedModuleIds, launchPadModule.appointmentModuleId);
                this.addModuleReference(referencedModuleIds, launchPadModule.visitAppointmentModuleId);
                this.addModuleReference(referencedModuleIds, launchPadModule.capModuleId);
            } else if (module._type === ModuleType.EventApplicationModule) {
                const eventModule = module as EventApplicationModule;
                this.addModuleReference(referencedModuleIds, eventModule.customerModuleId);
                this.addModuleReference(referencedModuleIds, eventModule.agreementsModuleId);
                this.addModuleReference(referencedModuleIds, eventModule.vehicleModuleId);
                this.addModuleReference(referencedModuleIds, eventModule.appointmentModuleId);
                this.addModuleReference(referencedModuleIds, eventModule.visitAppointmentModuleId);
            }
        }

        return referencedModuleIds;
    }

    private addModuleReference(referencedModuleIds: Set<string>, moduleId?: ObjectId): void {
        if (moduleId) {
            referencedModuleIds.add(moduleId.toString());
        }
    }

    private async processPermissions(
        relatedData: RelatedDataCollections,
        modules: Module[]
    ): Promise<RelatedDataCollections> {
        const validPermissions = await this.getValidPermissions(relatedData, modules);
        const updatedRelatedData = { ...relatedData };

        if (validPermissions.length > 0) {
            updatedRelatedData.permissions = validPermissions;

            // Filter role permissions to only include valid ones
            if (updatedRelatedData.roles) {
                updatedRelatedData.roles = updatedRelatedData.roles.map(role => ({
                    ...role,
                    permissionIds: role.permissionIds.filter(permId =>
                        validPermissions.some(validPerm => validPerm._id.equals(permId))
                    ),
                }));
            }
        }

        return updatedRelatedData;
    }

    private async addFilteredCollections(
        relatedData: RelatedDataCollections,
        modules: Module[],
        companyId: ObjectId
    ): Promise<RelatedDataCollections> {
        const updatedRelatedData = { ...relatedData };

        const filteredRouters = await this.fetchAndFilterRouters(modules, companyId);
        if (filteredRouters.length > 0) {
            updatedRelatedData.routers = filteredRouters;
        }

        const sanitizedEvents = await this.fetchAndFilterEvents(modules);
        if (sanitizedEvents.length > 0) {
            updatedRelatedData.events = sanitizedEvents;
        }

        const filteredConsents = await this.fetchAndFilterConsentsAndDeclarations(modules, relatedData);
        if (filteredConsents.length > 0) {
            updatedRelatedData.consentsAndDeclarations = filteredConsents;
        }

        const filteredCampaigns = await this.fetchCapCampaigns(modules, companyId);
        if (filteredCampaigns.length > 0) {
            updatedRelatedData.campaigns = filteredCampaigns;
        }

        // Fetch banners related to events
        if (updatedRelatedData.events && updatedRelatedData.events.length > 0) {
            const relatedBanners = await this.fetchBannersForEvents(updatedRelatedData.events);
            if (relatedBanners.length > 0) {
                updatedRelatedData.banners = relatedBanners;
            }
        }

        return updatedRelatedData;
    }

    private collectAllFileReferences(company: Company, modules: Module[], relatedData: RelatedDataCollections): void {
        this.fileReferences = [];

        this.collectFileReferences(company, 'companies', company._id.toString());

        modules.forEach(module => {
            this.collectFileReferences(module, 'modules', module._id.toString());
        });

        for (const [collection, documents] of Object.entries(relatedData)) {
            if (Array.isArray(documents)) {
                documents.forEach((doc: DocumentWithFiles) => {
                    this.collectFileReferences(doc, collection, doc._id.toString());
                });
            }
        }
    }

    private createExportDataStructure(
        company: Company,
        modules: Module[],
        relatedData: RelatedDataCollections
    ): ExportData {
        const exportData: ExportData = {
            company: EJSON.serialize(company),
            modules: modules.map(m => EJSON.serialize(m)),
            fileReferences: this.fileReferences,
        };

        // Consolidate settings and add other collections
        const allSettings: Document[] = [];

        for (const [collectionName, documents] of Object.entries(relatedData)) {
            if (Array.isArray(documents) && documents.length > 0) {
                if (this.settingsCollections.includes(collectionName)) {
                    allSettings.push(...documents.map(doc => EJSON.serialize(doc)));
                } else {
                    (exportData as Record<string, Document[]>)[collectionName] = documents.map(doc =>
                        EJSON.serialize(doc)
                    );
                }
            }
        }

        if (allSettings.length > 0) {
            exportData.settings = allSettings;
        }

        return exportData;
    }

    /**
     * Filter role permissions based on exported modules and related collections
     */
    private async getValidPermissions(relatedData: RelatedDataCollections, modules: Module[]): Promise<Permission[]> {
        // Get roles from related data
        const { roles } = relatedData;
        if (!roles || roles.length === 0) {
            return [];
        }

        // Create sets of exported IDs for quick lookup
        const exportedModuleIds = new Set(modules.map(m => m._id.toString()));
        const exportedLanguagePackIds = new Set(relatedData.languagePacks?.map(lp => lp._id.toString()) || []);

        // Collect all permission IDs from all roles
        const allPermissionIds = new Set<string>();
        roles.forEach(role => {
            role.permissionIds.forEach(permId => allPermissionIds.add(permId.toString()));
        });

        // Fetch all permissions referenced by roles
        const permissions = await this.collections.permissions
            .find({
                _id: { $in: Array.from(allPermissionIds).map(id => new ObjectId(id)) },
            })
            .toArray();

        // Create a map of permission ID to permission for quick lookup
        const permissionMap = new Map<string, Permission>();
        permissions.forEach(permission => {
            permissionMap.set(permission._id.toString(), permission);
        });

        // Collect valid permissions that will be included in export
        const validPermissions: Permission[] = [];

        // Filter permissions for each role
        for (const role of roles) {
            const validPermissionIds: ObjectId[] = [];

            for (const permissionId of role.permissionIds) {
                const permission = permissionMap.get(permissionId.toString());
                if (!permission) {
                    continue; // Skip if permission not found
                }

                let isValidPermission = true;

                // Check each policy in the permission
                for (const policy of permission.policies) {
                    const conditions = policy.conditions as HasModuleReferences;

                    // Use utility method to check if conditions have valid module references
                    if (!this.hasValidModuleReferences(conditions, exportedModuleIds, exportedLanguagePackIds)) {
                        isValidPermission = false;
                        break;
                    }
                }

                // If permission is valid, keep it
                if (isValidPermission) {
                    validPermissionIds.push(permissionId);

                    // Add to valid permissions list if not already added
                    if (!validPermissions.some(p => p._id.equals(permission._id))) {
                        validPermissions.push(permission);
                    }
                }
            }

            // Update role's permission IDs to only include valid ones
            role.permissionIds = validPermissionIds;
        }

        return validPermissions;
    }

    /**
     * Fetch and filter routers based on exported modules
     */
    private async fetchAndFilterRouters(modules: Module[], companyId: ObjectId): Promise<Router[]> {
        const exportedModuleIds = new Set(modules.map(m => m._id.toString()));
        const routers = await this.collections.routers.find({ companyId }).toArray();
        const filteredRouters: Router[] = [];

        for (const router of routers) {
            const validEndpoints = [];
            const excludedEndpointIds = new Set<string>();

            // Filter endpoints based on module references
            for (const endpoint of router.endpoints) {
                const endpointWithRefs = endpoint as HasModuleReferences;
                let shouldIncludeEndpoint = true;
                const updatedEndpoint = { ...endpoint };

                // Filter applicationModuleIds array to only include exported modules
                if (endpointWithRefs.applicationModuleIds && Array.isArray(endpointWithRefs.applicationModuleIds)) {
                    const validApplicationModuleIds = endpointWithRefs.applicationModuleIds.filter(moduleId =>
                        exportedModuleIds.has(moduleId.toString())
                    );

                    // If no valid application module IDs remain, exclude this endpoint
                    if (validApplicationModuleIds.length === 0) {
                        shouldIncludeEndpoint = false;
                    } else {
                        (
                            updatedEndpoint as typeof updatedEndpoint & { applicationModuleIds: ObjectId[] }
                        ).applicationModuleIds = validApplicationModuleIds;
                    }
                }

                // Use utility method to check other module references
                if (
                    shouldIncludeEndpoint &&
                    !this.hasValidModuleReferences(endpointWithRefs, exportedModuleIds, new Set())
                ) {
                    shouldIncludeEndpoint = false;
                }

                // Track excluded endpoints and include valid ones
                if (shouldIncludeEndpoint) {
                    validEndpoints.push(updatedEndpoint);
                } else {
                    excludedEndpointIds.add(endpoint._id.toString());
                }
            }

            // Filter menu items to exclude those referencing excluded endpoints
            const validMenuItems =
                router.menuItems?.filter(menuItem => {
                    const menuItemWithEndpoint = menuItem as { endpointId?: ObjectId };
                    // Check if menu item references an excluded endpoint
                    if (
                        menuItemWithEndpoint.endpointId &&
                        excludedEndpointIds.has(menuItemWithEndpoint.endpointId.toString())
                    ) {
                        return false;
                    }

                    return true;
                }) || [];

            filteredRouters.push({
                ...router,
                endpoints: validEndpoints,
                menuItems: validMenuItems,
            });
        }

        return filteredRouters;
    }

    /**
     * Fetch and filter events based on event application modules
     */
    private async fetchAndFilterEvents(modules: Module[]): Promise<Event[]> {
        const eventApplicationModuleIds = new Set(
            modules.filter(m => m._type === ModuleType.EventApplicationModule).map(m => m._id.toString())
        );

        if (eventApplicationModuleIds.size === 0) {
            return [];
        }

        const events = await this.collections.events
            .find({
                moduleId: { $in: Array.from(eventApplicationModuleIds).map(id => new ObjectId(id)) },
            })
            .toArray();

        const exportedModuleIds = new Set(modules.map(m => m._id.toString()));

        // Sanitize events data and filter KYC preset conditions
        const sanitizedEvents: Event[] = events.map(event => ({
            ...event,
            dealerVehicles: event.dealerVehicles.map(dv => ({
                ...dv,
                vehicleSuiteIds: [],
            })),
            userIds: [],
            publicSalesPerson: null,
            // TODO: readd later if new ticket under VF-1272 includes porscheIdModule as part of export
            porscheIdModuleId: null,
            // TODO: readd later if new ticket under VF-1272 includes porschePaymentModule as part of export
            paymentSetting: null,
            // Filter KYC preset conditions that depend on modules not in the export
            kycPresets: event.kycPresets.map(kycPreset => ({
                ...kycPreset,
                conditions: this.filterConditionsByExportedEntities(kycPreset.conditions, exportedModuleIds),
            })),
        }));

        return sanitizedEvents;
    }

    /**
     * Fetch CAP campaigns for the given modules and company ID.
     */
    private async fetchCapCampaigns(modules: Module[], companyId: ObjectId): Promise<Campaign[]> {
        const hasCapModule = modules.some(m => m._type === ModuleType.CapModule);

        if (!hasCapModule) {
            return [];
        }

        const campaigns = await this.collections.campaigns
            .find({
                companyId,
                isDeleted: false,
            })
            .toArray();

        return campaigns;
    }

    /**
     * Fetch and filter consents and declarations based on exported modules and language packs
     */
    private async fetchAndFilterConsentsAndDeclarations(
        modules: Module[],
        relatedData: RelatedDataCollections
    ): Promise<ConsentsAndDeclarations[]> {
        const exportedModuleIds = new Set(modules.map(m => m._id.toString()));
        const consentsAndDeclarationsModuleIds = new Set(
            modules.filter(m => m._type === ModuleType.ConsentsAndDeclarations).map(m => m._id.toString())
        );

        if (consentsAndDeclarationsModuleIds.size === 0) {
            return [];
        }

        const consentsAndDeclarations = await this.collections.consentsAndDeclarations
            .find({
                moduleId: { $in: Array.from(consentsAndDeclarationsModuleIds).map(id => new ObjectId(id)) },
            })
            .toArray();

        // Filter conditions for each consent
        const filteredConsents: ConsentsAndDeclarations[] = [];
        const exportedLanguagePackIds = new Set(relatedData.languagePacks?.map(lp => lp._id.toString()) || []);

        for (const consent of consentsAndDeclarations) {
            let shouldIncludeConsent = true;

            // Check if all conditions are valid using utility method
            for (const condition of consent.conditions) {
                const conditionWithRefs = condition as HasModuleReferences;

                // Use utility method to check if condition has valid module references
                if (!this.hasValidModuleReferences(conditionWithRefs, exportedModuleIds, exportedLanguagePackIds)) {
                    shouldIncludeConsent = false;
                    break;
                }
            }

            // Only include consent if all conditions are valid
            if (shouldIncludeConsent) {
                filteredConsents.push(consent);
            }
        }

        return filteredConsents;
    }

    /**
     * Fetch banners related to exported events
     */
    private async fetchBannersForEvents(events: Event[]): Promise<Banner[]> {
        // Collect banner IDs from events
        const bannerIds = new Set<string>();
        for (const event of events) {
            if (event.bannerId) {
                bannerIds.add(event.bannerId.toString());
            }
        }

        if (bannerIds.size === 0) {
            return [];
        }

        const banners = await this.collections.banners
            .find({
                _id: { $in: Array.from(bannerIds).map(id => new ObjectId(id)) },
            })
            .toArray();

        return banners;
    }

    // Manifest and utility methods
    private async createManifest(company: Company, exportData: ExportData): Promise<ExportManifest> {
        return {
            version: '1.0.0',
            exportDate: new Date(),
            exportedFrom: process.env.NODE_ENV || 'development',
            exportedBy: 'api-export',
            checksum: await this.calculateChecksum(exportData),
            company: {
                _id: company._id.toString(),
                displayName: company.displayName,
            },
            statistics: {
                modulesCount: exportData.modules.length,
                filesCount: this.fileReferences.length,
                totalSize: await this.calculateTotalSize(this.fileReferences),
                collectionsCount: this.countCollections(exportData),
            },
        };
    }

    private async calculateChecksum(data: ExportData): Promise<string> {
        const hash = crypto.createHash('sha256');
        hash.update(EJSON.stringify(data));

        return hash.digest('hex');
    }

    private async calculateTotalSize(fileRefs: FileReference[]): Promise<number> {
        return fileRefs.reduce((total, ref) => total + ref.size, 0);
    }

    private countCollections(exportData: ExportData): number {
        return Object.keys(exportData).filter(
            key =>
                !['company', 'modules', 'fileReferences'].includes(key) &&
                Array.isArray(exportData[key as keyof ExportData])
        ).length;
    }

    // File reference collection methods
    private collectFileReferences(
        obj: DocumentWithFiles,
        collection: string,
        documentId: string,
        fieldPath: string = ''
    ): void {
        if (!obj) {
            return;
        }

        this.traverseForFiles(obj, collection, documentId, fieldPath);
    }

    private traverseForFiles(
        data: Record<string, unknown>,
        collection: string,
        documentId: string,
        path: string
    ): void {
        if (!data) {
            return;
        }

        // Check if it's an uploaded file
        if (this.isUploadedFile(data)) {
            this.processUploadedFile(data, collection, documentId, path);

            return;
        }

        if (Array.isArray(data)) {
            data.forEach((item, index) => {
                if (typeof item === 'object' && item !== null) {
                    this.traverseForFiles(item as Record<string, unknown>, collection, documentId, `${path}[${index}]`);
                }
            });
        } else if (typeof data === 'object' && !(data instanceof Date) && !(data instanceof ObjectId)) {
            Object.entries(data).forEach(([key, value]) => {
                const newPath = path ? `${path}.${key}` : key;
                if (typeof value === 'object' && value !== null) {
                    this.traverseForFiles(value as Record<string, unknown>, collection, documentId, newPath);
                }
            });
        }
    }

    private isUploadedFile(obj: unknown): obj is UploadedFile | UploadedFileWithPreview {
        return (
            typeof obj === 'object' &&
            obj !== null &&
            '_id' in obj &&
            'filename' in obj &&
            'objectName' in obj &&
            'bucketType' in obj
        );
    }

    private processUploadedFile(
        file: UploadedFile | UploadedFileWithPreview,
        collection: string,
        documentId: string,
        currentPath: string
    ): void {
        this.fileReferences.push({
            fileId: file._id.toString(),
            collection,
            documentId,
            fieldPath: currentPath,
            bucketType: file.bucketType,
            filename: file.filename,
            objectName: file.objectName,
            size: file.size,
        });

        // Handle preview if exists
        if ('preview' in file && file.preview) {
            this.fileReferences.push({
                fileId: file.preview._id.toString(),
                collection,
                documentId,
                fieldPath: `${currentPath}.preview`,
                bucketType: file.preview.bucketType,
                filename: file.preview.filename,
                objectName: file.preview.objectName,
                size: file.preview.size,
            });
        }
    }

    // Reference collection methods
    private collectReferencedIds(company: Company, modules: Module[]): Record<string, Set<string>> {
        const ids: Record<string, Set<string>> = {
            porscheMasterDataModules: new Set<string>(),
            capModules: new Set<string>(),
            languagePacks: new Set<string>(),
            smtpEmailSettings: new Set<string>(),
            twilioSmsSettings: new Set<string>(),
        };

        this.collectCompanyReferences(company, ids);
        this.collectModuleReferences(modules, ids);

        return ids;
    }

    private collectCompanyReferences(company: Company, ids: Record<string, Set<string>>): void {
        // Language pack references
        if (company.languages?.length) {
            company.languages.forEach(id => ids.languagePacks.add(id.toString()));
        }

        // Email settings references
        if (company.emailSettings?.provider === 'smtp' && company.emailSettings.settingId) {
            ids.smtpEmailSettings.add(company.emailSettings.settingId.toString());
        }

        // SMS settings references
        if (company.smsSettings?.provider === 'twilio' && company.smsSettings.settingId) {
            ids.twilioSmsSettings.add(company.smsSettings.settingId.toString());
        }
    }

    private collectModuleReferences(modules: Module[], ids: Record<string, Set<string>>): void {
        modules.forEach(module => {
            // PorscheMasterDataModule handling
            if (module._type === ModuleType.PorscheMasterDataModule) {
                ids.porscheMasterDataModules.add(module._id.toString());
            }

            // SimpleVehicleManagement module dependency on PorscheMasterDataModule
            if (
                module._type === ModuleType.SimpleVehicleManagement &&
                'porscheMasterDataModuleId' in module &&
                module.porscheMasterDataModuleId
            ) {
                ids.porscheMasterDataModules.add(module.porscheMasterDataModuleId.toString());
            }

            // CapModule handling
            if (module._type === ModuleType.CapModule) {
                ids.capModules.add(module._id.toString());
            }
        });
    }

    private async fetchRelatedData(
        referencedIds: Record<string, Set<string>>,
        companyId: ObjectId
    ): Promise<RelatedDataCollections> {
        // Fetch core company-related data
        const companyRelatedData = await this.fetchCompanyRelatedData(companyId);

        // Fetch referenced data by IDs
        const referencedData = await this.fetchReferencedData(referencedIds);

        // Merge both data sets
        return { ...companyRelatedData, ...referencedData };
    }

    private async fetchCompanyRelatedData(companyId: ObjectId): Promise<RelatedDataCollections> {
        const relatedData: RelatedDataCollections = {};

        // Fetch dealers (including deleted ones as dealerVehicles under LCF Module are retained)
        const dealers = await this.collections.dealers.find({ companyId }).toArray();
        if (dealers.length > 0) {
            relatedData.dealers = dealers;
        }

        // Fetch roles (exclude user references)
        const roles = await this.collections.roles.find({ companyId, isDeleted: false }).toArray();
        if (roles.length > 0) {
            relatedData.roles = roles.map(role => ({
                ...role,
                userIds: [], // Users should not be included in export
            }));
        }

        // Fetch user groups (exclude user references)
        const userGroups = await this.collections.userGroups.find({ companyId }).toArray();
        if (userGroups.length > 0) {
            relatedData.userGroups = userGroups.map(userGroup => ({
                ...userGroup,
                userIds: [], // Users should not be included in export
            }));
        }

        return relatedData;
    }

    private async fetchReferencedData(referencedIds: Record<string, Set<string>>): Promise<RelatedDataCollections> {
        const relatedData: RelatedDataCollections = {};

        // Fetch language packs
        if (referencedIds.languagePacks.size > 0) {
            const objectIds = Array.from(referencedIds.languagePacks).map(id => new ObjectId(id));
            const documents = await this.collections.languagePacks.find({ _id: { $in: objectIds } }).toArray();
            if (documents.length > 0) {
                relatedData.languagePacks = documents;
            }
        }

        // Fetch settings for each referenced collection
        const settingsPromises = Object.entries(referencedIds).map(async ([collection, ids]) => {
            if (ids.size > 0) {
                try {
                    const objectIds = Array.from(ids).map(id => new ObjectId(id));
                    const key = this.getSettingsKey(collection);
                    const documents = await this.collections.settings.find({ [key]: { $in: objectIds } }).toArray();

                    if (documents.length > 0) {
                        return { collection, documents };
                    }
                } catch (error) {
                    console.warn(`Failed to fetch ${collection}:`, error);
                }
            }

            return null;
        });

        const settingsResults = await Promise.all(settingsPromises);

        // Add results to relatedData
        settingsResults.forEach(result => {
            if (result) {
                relatedData[result.collection] = result.documents;
            }
        });

        return relatedData;
    }

    private getSettingsKey(collection: string): string {
        switch (collection) {
            case 'porscheMasterDataModules':
                return 'moduleId';

            case 'capModules':
                return 'capModuleId';

            default:
                return '_id';
        }
    }
}

/* eslint-disable consistent-return */
import { RequestHand<PERSON> } from 'express';
import { ObjectId } from 'mongodb';
import { RequestLocals } from '../core/express';
import type { Collections } from '../database';
import getDatabaseContext from '../database/getDatabaseContext';
import { createWorkbookWithoutCompanyAndCountryCode } from '../utils/excel/utils';

export const dealerTemplateHeaders = {
    'Display Name': 'displayName',
    'Legal Name': 'legalName',
    'Limit Features': 'limitFeatures',
    Active: 'isActive',
    Telephone: 'telephone',
    Email: 'email',
    Address: 'address',
    Latitude: 'latitude',
    Longitude: 'longitude',
    'Additional Information': 'additionalInformation',
    'Dealer Code': 'dealerCode',
};

const getCompanyLanguages = async (
    companyId: ObjectId,
    collections: Collections
): Promise<{ _id: string; referenceName: string }[]> => {
    const company = await collections.companies.findOne({ _id: companyId });

    if (!company?.languages?.length) {
        return [];
    }

    const languagePacks = await collections.languagePacks.find({ _id: { $in: company.languages } }).toArray();

    return languagePacks.map(pack => ({ _id: pack._id.toString(), referenceName: pack.referenceName }));
};

export const getImportDealerExcelHeaders = async (companyId: ObjectId): Promise<Record<string, string>> => {
    const { collections } = await getDatabaseContext();

    const companyLanguages = await getCompanyLanguages(companyId, collections);
    const headerMap: Record<string, string> = {};
    const translatableFields = ['legalName', 'address', 'additionalInformation'];

    Object.entries(dealerTemplateHeaders).forEach(([displayName, fieldName]) => {
        headerMap[displayName] = fieldName;

        if (translatableFields.includes(fieldName)) {
            companyLanguages.forEach(language => {
                const key = `${fieldName}.${language._id}`;
                headerMap[`${displayName} (${language.referenceName})`] = key;
            });
        }
    });

    return headerMap;
};

type QueryParams = {
    companyId: string;
};

const exportDealerTemplate: RequestHandler<
    Record<string, never>,
    unknown,
    unknown,
    QueryParams,
    RequestLocals
> = async (req, res, next) => {
    try {
        const { collections } = await getDatabaseContext();
        const { companyId } = req.query;

        if (!companyId || typeof companyId !== 'string') {
            return res.status(400).json({ error: 'Company ID is required' });
        }

        if (!ObjectId.isValid(companyId)) {
            return res.status(400).json({ error: 'Invalid Company ID format' });
        }

        const companyObjectId = new ObjectId(companyId);
        const company = await collections.companies.findOne({ _id: companyObjectId });

        if (!company) {
            return res.status(404).json({ error: 'Company not found' });
        }

        const headerMap = await getImportDealerExcelHeaders(companyObjectId);
        const processedHeaders = Object.keys(headerMap);

        const workbook = createWorkbookWithoutCompanyAndCountryCode('Dealers', processedHeaders);

        res.set({
            'Content-Disposition': 'attachment; filename="Dealers_Template.xlsx"',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });

        await workbook.xlsx.write(res);

        return res.end();
    } catch (error) {
        next(error);
    }
};

export default exportDealerTemplate;

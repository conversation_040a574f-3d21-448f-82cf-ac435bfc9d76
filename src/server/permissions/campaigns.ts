import type { Filter, ObjectId } from 'mongodb';
import type { Campaign } from '../database/documents';
import { isPolicyIncluded, isPolicyOnCompany } from './shared';
import { ResourceType, CampaignPolicyAction } from './types';
import type { CampaignPolicy, Policy, ExtendedPolicy } from './types';

// eslint-disable-next-line import/prefer-default-export
export class CampaignPermissionController {
    private policies: ExtendedPolicy<CampaignPolicy>[];

    constructor(policies: ExtendedPolicy<Policy>[]) {
        this.policies = policies.filter(CampaignPermissionController.isCampaignPolicy);
    }

    getPoliciesForAction(action: CampaignPolicyAction) {
        return this.policies.filter(policy => policy.actions === '*' || policy.actions.includes(action));
    }

    static getFilterQueryForPolicies(policies: CampaignPolicy[]): Filter<Campaign> {
        if (!policies.length) {
            return { _wronged: { $exists: true, $eq: true } };
        }

        const conditions = policies.map(({ conditions }) => {
            const filter: Filter<Campaign> = { isDeleted: false };

            if (conditions.companyId) {
                filter.companyId = conditions.companyId;
            }

            return filter;
        });

        return { $or: conditions };
    }

    getFilterQueryForAction(action: CampaignPolicyAction) {
        const policies = this.policies.filter(policy => policy.actions === '*' || policy.actions.includes(action));

        return CampaignPermissionController.getFilterQueryForPolicies(policies);
    }

    static isCampaignCompliantWithPolicies(campaign: Campaign, policies: CampaignPolicy[]) {
        if (!policies.length) {
            return false;
        }

        return policies.some(({ conditions }) => {
            if (conditions.companyId) {
                if (!campaign || !conditions.companyId.equals(campaign.companyId)) {
                    return false;
                }
            }

            return true;
        });
    }

    mayOperateOn(campaign: Campaign, action: CampaignPolicyAction) {
        const policies = this.policies.filter(policy => policy.actions === '*' || policy.actions.includes(action));

        return CampaignPermissionController.isCampaignCompliantWithPolicies(campaign, policies);
    }

    hasPolicyForAction(action: CampaignPolicyAction) {
        return this.getPoliciesForAction(action).length > 0;
    }

    hasCompanyPolicyForAction(action: CampaignPolicyAction, companyId: ObjectId) {
        return this.getPoliciesForAction(action).some(isPolicyOnCompany(companyId));
    }

    hasPolicy(target: CampaignPolicy) {
        return isPolicyIncluded(target, this.policies);
    }

    static isCampaignPolicy(policy: ExtendedPolicy<Policy>): policy is ExtendedPolicy<CampaignPolicy> {
        return policy.resourceType === ResourceType.Campaign;
    }
}

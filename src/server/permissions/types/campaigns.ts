import { ObjectId } from 'mongodb';
import type { PolicyCore } from './policyCore';
import ResourceType from './resourceType';

export type CampaignPolicy = PolicyCore<ResourceType.Campaign, CampaignPolicyAction, CampaignPolicyConditions>;

export type CampaignPolicyConditions = {
    companyId?: ObjectId;
};

export enum CampaignPolicyAction {
    /* actions to view campaign */
    View = 'campaign:view',

    /* actions to create campaign */
    Create = 'campaign:create',

    /* actions to update campaign */
    Update = 'campaign:update',

    /* actions to delete campaign */
    Delete = 'campaign:delete',
}

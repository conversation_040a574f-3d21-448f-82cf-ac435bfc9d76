import { Col, Row } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { ApplicationJourneyDeposit, Company } from '../../../database';
import useFormats from '../../../emails/utils/useFormats';
import Field from '../Field';
import { SectionHeader, colSpan } from '../ui';

type DepositProps = {
    deposit: ApplicationJourneyDeposit;
    company: Company;
    // Define any props needed for the Deposit component
};
const Deposit = ({ deposit, company }: DepositProps) => {
    const { t } = useTranslation(['saleOfferPdf', 'common']);

    const { amount, completedAt, transactionId, status, paymentMethod } = deposit;

    const { formatAmountWithCurrency } = useFormats(
        company?.currency || '',
        company?.roundings?.amount?.decimals || 0,
        company?.roundings?.percentage?.decimals || 0
    );

    return (
        <div>
            <SectionHeader>{t('saleOfferPdf:deposit.sectionTitle.paymentDetails')}</SectionHeader>
            <Row gutter={[16, 16]}>
                <Col {...colSpan}>
                    <Field
                        label={t('saleOfferPdf:deposit.fields.depositAmount')}
                        value={formatAmountWithCurrency(amount)}
                    />
                </Col>
                <Col {...colSpan}>
                    <Field
                        label={t('saleOfferPdf:deposit.fields.paymentDate')}
                        value={dayjs(completedAt).format(t('common:formats.dateTimeFormat'))}
                    />
                </Col>
                <Col {...colSpan}>
                    <Field label={t('saleOfferPdf:deposit.fields.transactionId')} value={transactionId} />
                </Col>
                <Col {...colSpan}>
                    <Field label={t('saleOfferPdf:deposit.fields.status')} value={status} />
                </Col>
                <Col {...colSpan}>
                    <Field label={t('saleOfferPdf:deposit.fields.paymentMethod')} value={paymentMethod} />
                </Col>
            </Row>
        </div>
    );
};
export default Deposit;

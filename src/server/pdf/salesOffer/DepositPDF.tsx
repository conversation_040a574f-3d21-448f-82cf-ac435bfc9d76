import { Row } from 'antd';
import { useTranslation } from 'react-i18next';
import { ApplicationJourneyDeposit, Company, Customer, KYCField } from '../../database';
import { type Options } from '../../utils/translateOptions';
import PDFLayout from '../components/PDFLayout';
import CustomerField from '../components/application/ApplicantDetails/CustomerField';
import ConsentAndDeclarationDetails, {
    ConsentAndDeclarationsProps,
} from '../components/application/ConsentAndDeclaration';
import { PDFRenderOptions } from '../shared';
import Field from './Field';
import Deposit from './components/Deposit';
import { ContentBody, ContentHeader, HeaderLogo, PDFContainer, VSASectionHeader, colSpan } from './ui';
import useCustomerData from './useCustomerData';

type DepositPDFProps = {
    // only display firstName, lastName, email, mobile
    kycFields: KYCField[];
    agreements: ConsentAndDeclarationsProps['agreements'];
    deposit: ApplicationJourneyDeposit;
    fontUrlInBase64?: string;
    applicant: Customer;
    companyLogo: string;
    options: Options;
    company: Company;
    pdsRenderOptions?: PDFRenderOptions;
};
const DepositPDF = ({
    kycFields,
    agreements,
    deposit,
    fontUrlInBase64,
    applicant,
    companyLogo,
    options,
    company,
    pdsRenderOptions,
}: DepositPDFProps) => {
    const { t } = useTranslation(['saleOfferPdf', 'applicationPdf']);
    const { customerData, orderedKycFields } = useCustomerData(applicant, kycFields);

    return (
        <PDFLayout fontUrlInBase64={fontUrlInBase64} pdsRenderOptions={pdsRenderOptions}>
            <PDFContainer>
                <HeaderLogo>
                    <img alt="logo" src={companyLogo} />
                </HeaderLogo>
                <ContentHeader>{t('saleOfferPdf:deposit.title')}</ContentHeader>
                <ContentBody>
                    {orderedKycFields.length > 0 && (
                        <div>
                            <VSASectionHeader>
                                {t('saleOfferPdf:coeBidding.sectionTitle.purchaseParticulars')}
                            </VSASectionHeader>
                            <Row gutter={[16, 16]}>
                                {(orderedKycFields || []).map(field => (
                                    <CustomerField
                                        key={field.key}
                                        Component={Field}
                                        colSpan={colSpan}
                                        data={customerData}
                                        fieldKey={field.key}
                                        options={options}
                                        timeZone={company.timeZone}
                                    />
                                ))}
                            </Row>
                        </div>
                    )}

                    <Deposit company={company} deposit={deposit} />

                    <ConsentAndDeclarationDetails agreements={agreements} hideFinancingConsent hideInsuranceConsent />
                </ContentBody>
            </PDFContainer>
        </PDFLayout>
    );
};

export default DepositPDF;

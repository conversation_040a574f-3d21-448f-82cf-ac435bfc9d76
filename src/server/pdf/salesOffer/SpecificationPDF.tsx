import { useTranslation } from 'react-i18next';
import PDFLayout from '../components/PDFLayout';
import ConsentAndDeclarationDetails, {
    ConsentAndDeclarationsProps,
} from '../components/application/ConsentAndDeclaration';
import Signing from '../components/application/Signing';
import { PDFRenderOptions } from '../shared';
import { ContentBody, HeaderLogo, PDFContainer } from './ui';

type SpecificationPDFProps = {
    companyLogo: string;
    fontUrlInBase64?: string;
    agreements: ConsentAndDeclarationsProps['agreements'];
    signingProvider?: 'namirial' | 'docusign';
    pdsRenderOptions?: PDFRenderOptions;
};
const SpecificationPDF = ({
    agreements,
    companyLogo,
    fontUrlInBase64,
    signingProvider,
    pdsRenderOptions,
}: SpecificationPDFProps) => {
    const { t } = useTranslation(['saleOfferPdf', 'applicationPdf']);

    return (
        <PDFLayout fontUrlInBase64={fontUrlInBase64} pdsRenderOptions={pdsRenderOptions}>
            <PDFContainer>
                <HeaderLogo>
                    <img alt="logo" src={companyLogo} />
                </HeaderLogo>
                <ContentBody>
                    <ConsentAndDeclarationDetails agreements={agreements} hideFinancingConsent hideInsuranceConsent />
                    {!!signingProvider && (
                        <div style={{ margin: '0 -5px', marginTop: '-35px' }}>
                            <Signing
                                hasGuarantor={false}
                                isA4={false}
                                label={t('saleOfferPdf:specification.signingLabel')}
                                provider={signingProvider}
                            />
                        </div>
                    )}
                </ContentBody>
            </PDFContainer>
        </PDFLayout>
    );
};

export default SpecificationPDF;

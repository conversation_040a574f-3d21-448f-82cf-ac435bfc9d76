import { isNil } from 'lodash/fp';

export type PDFRenderOptions = {
    pdf?: {
        // format will override width and height if set
        format?: 'letter' | 'legal' | 'tabloid' | 'ledger' | 'a0' | 'a1' | 'a2' | 'a3' | 'a4' | 'a5' | 'a6';
        width?: number;
        height?: number;
    };
};

export const isA4PageSize = (pdsRenderOptions?: PDFRenderOptions) =>
    isNil(pdsRenderOptions) || isNil(pdsRenderOptions?.pdf) || pdsRenderOptions?.pdf?.format === 'a4';

// eslint-disable-next-line max-len
export const signingField1 = `[[!sigField1:signer1:signature(sigType="Draw2Sign",batch=1):label("Please sign here"):size(width=224,height=127)]]`;
// eslint-disable-next-line max-len
export const signingField2 = `[[!sigField2:signer2:signature(sigType="Draw2Sign",batch=1):label("Please sign here"):size(width=224,height=127)]]`;

// A4 pdf width and height are used for singing box
export const defaultSigningBoxWidth = 300;
export const defaultSigningBoxHeight = 190;

// pdf width and height of configuration should be matched with
// salesOfferSigningBoxWidth and salesOfferSigningBoxHeight
export const salesOfferPDFRenderOptions: PDFRenderOptions = {
    pdf: { format: null, width: 1372, height: 1944 },
};
export const salesOfferSigningBoxWidth = 515;
export const salesOfferSigningBoxHeight = 290;

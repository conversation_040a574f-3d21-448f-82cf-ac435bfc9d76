import isEqual from 'fast-deep-equal';
import { isNil } from 'lodash/fp';
import type { KnownChange } from '../database/documents';

const getKnownChanges = (inputs: KnownChange[]): KnownChange[] =>
    inputs.filter(field => {
        if (isNil(field.before) && isNil(field.after)) {
            return false;
        }

        return !isEqual(field.before, field.after);
    });

export default getKnownChanges;

import { ObjectId } from 'bson';
import type { AnyBulkWriteOperation, Document } from 'mongodb';
import type { Dealer } from '../database/documents/Dealer';
import type { Vehicle } from '../database/documents/Vehicle';
import { VehicleKind } from '../database/documents/Vehicle/types';
import type { DealerVehicles } from '../database/documents/moduleShared';
import {
    ModuleType,
    type ConfiguratorModule,
    type EventApplicationModule,
    type FinderApplicationPrivateModule,
    type FinderApplicationPublicModule,
    type LaunchPadModule,
    type Module,
    type StandardApplicationModule,
} from '../database/documents/modules';
import getDatabaseContext from '../database/getDatabaseContext';
import { ModulePolicyAction, PermissionController, VehiclePolicyAction } from '../permissions';
import { uniqueObjectIds } from './fp';
import upsertDealerAssignments from './upsertDealerAssignments';

type ModuleWithDealerVehicles =
    | StandardApplicationModule
    | EventApplicationModule
    | LaunchPadModule
    | ConfiguratorModule
    | FinderApplicationPublicModule
    | FinderApplicationPrivateModule;

// do not include ConfiguratorModule, FinderApplicationPublicModule, FinderApplicationPrivateModule here
// as they do not have vehicle assignment section on Dealer details -> Application Module tab
const APPLICATION_MODULES_WITH_VEHICLE_ASSIGNMENT = [
    ModuleType.StandardApplicationModule,
    ModuleType.EventApplicationModule,
    ModuleType.LaunchPadModule,
];

// Get all dealer IDs (include inactive)
const getDealerIds = async (companyId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const pipelines: Document[] = [
        {
            $match: {
                companyId,
                isDeleted: false,
            },
        },
    ];

    const dealers = await collections.dealers.aggregate<Dealer>(pipelines).toArray();

    return dealers?.map(dealer => dealer._id) || [];
};

// Get local variant IDs for the vehicle module
const getLocalVariantSuiteIds = async (vehicleModuleId: ObjectId, permissionController: PermissionController) => {
    const { collections } = await getDatabaseContext();

    const pipelines: Document[] = [
        {
            $match: {
                _kind: VehicleKind.LocalVariant,
                isDeleted: false,
                '_versioning.isLatest': true,
                moduleId: vehicleModuleId,
            },
        },
    ];

    pipelines.push({ $match: permissionController.vehicles.getFilterQueryForAction(VehiclePolicyAction.View) });

    const vehicles = await collections.vehicles.aggregate<Vehicle>(pipelines).toArray();

    return vehicles?.map(vehicle => vehicle._versioning.suiteId) || [];
};

// Get all application modules
const getApplicationModules = async (
    companyId: ObjectId,
    permissionController: PermissionController
): Promise<ModuleWithDealerVehicles[]> => {
    const { collections } = await getDatabaseContext();

    const pipelines: Document[] = [
        {
            $match: {
                _type: { $in: APPLICATION_MODULES_WITH_VEHICLE_ASSIGNMENT },
                companyId,
            },
        },
    ];

    pipelines.push({ $match: permissionController.modules.getFilterQueryForAction(ModulePolicyAction.View) });

    const modules = await collections.modules.aggregate<Module>(pipelines).toArray();

    return (modules || []) as ModuleWithDealerVehicles[];
};

// Get local variant suite IDs grouped by vehicle module ID
const getGroupLocalVariantSuiteIdsByVehicleModuleId = async (
    applicationModules: ModuleWithDealerVehicles[],
    permissionController: PermissionController
): Promise<Map<string, ObjectId[]>> => {
    // Extract unique vehicleModuleIds
    const uniqueVehicleModuleIds = uniqueObjectIds(
        applicationModules.map(module => module.vehicleModuleId).filter(Boolean)
    );

    // Map each vehicleModuleId to a Promise that fetches its suiteIds
    const suiteIdPromises = uniqueVehicleModuleIds.map(async vehicleModuleId => {
        // get local variants suiteIds
        const localVariantSuiteIds = await getLocalVariantSuiteIds(vehicleModuleId, permissionController);

        return [vehicleModuleId.toString(), localVariantSuiteIds] as [string, ObjectId[]];
    });

    const results = await Promise.all(suiteIdPromises);

    return new Map(results);
};

// When creating a new application module, this function gets the auto assignment vehicles
// for the new module and returns an array of dealerVehicles.
export const getAutoAssignmentVehiclesForNewApplicationModule = async (
    vehicleModuleId: ObjectId,
    companyId: ObjectId,
    permissionController: PermissionController
): Promise<DealerVehicles[]> => {
    // get local variants suiteIds
    const localVariantSuiteIds = await getLocalVariantSuiteIds(vehicleModuleId, permissionController);
    if (!localVariantSuiteIds.length) {
        return [];
    }

    // get all dealer IDs
    const dealerIds = await getDealerIds(companyId);
    if (!dealerIds.length) {
        return [];
    }

    // create dealerVehicles for each dealer
    const dealerVehicles: DealerVehicles[] = dealerIds.map(dealerId => ({
        dealerId,
        vehicleSuiteIds: [...localVariantSuiteIds],
    }));

    return dealerVehicles;
};

// When creating new dealers, this function updates the auto assignment vehicles for all modules that need updates.
export const updateAutoAssignmentVehiclesForNewDealers = async (
    dealerIds: ObjectId[],
    companyId: ObjectId,
    permissionController: PermissionController
) => {
    // Get all application modules
    const applicationModules = await getApplicationModules(companyId, permissionController);
    if (!applicationModules?.length) {
        return;
    }

    const groupLocalVariantSuiteIdsByVehicleModuleId = await getGroupLocalVariantSuiteIdsByVehicleModuleId(
        applicationModules,
        permissionController
    );

    const bulkOperations: AnyBulkWriteOperation<Module>[] = applicationModules
        .map(module => {
            if (!module.vehicleModuleId) {
                return null;
            }

            const vehicleModuleId = module.vehicleModuleId.toString();

            const dealerVehicles: DealerVehicles[] = dealerIds.map(dealerId => {
                const vehicleSuiteIds = groupLocalVariantSuiteIdsByVehicleModuleId.get(vehicleModuleId) || [];

                return {
                    dealerId,
                    vehicleSuiteIds: [...vehicleSuiteIds],
                };
            });

            const updatedDealerVehicles = upsertDealerAssignments(module.dealerVehicles || [], dealerVehicles);

            return {
                updateOne: {
                    filter: { _id: module._id },
                    update: {
                        $set: {
                            dealerVehicles: updatedDealerVehicles,
                        },
                    },
                },
            };
        })
        .filter(Boolean) as AnyBulkWriteOperation<Module>[];

    if (bulkOperations.length > 0) {
        // get collections from database context
        const { collections } = await getDatabaseContext();

        await collections.modules.bulkWrite(bulkOperations);
    }
};

import { ObjectId } from 'mongodb';

const upsertDealerAssignments = <T extends { dealerId: ObjectId }>(existingArray: T[], assignments: T | T[]): T[] => {
    const updatedArray = [...existingArray];

    const assignmentsArray = Array.isArray(assignments) ? assignments : [assignments];

    for (const assignment of assignmentsArray) {
        const index = updatedArray.findIndex(({ dealerId }) => dealerId?.equals(assignment.dealerId));
        if (index > -1) {
            updatedArray[index] = assignment;
        } else {
            updatedArray.push(assignment);
        }
    }

    return updatedArray;
};

export default upsertDealerAssignments;

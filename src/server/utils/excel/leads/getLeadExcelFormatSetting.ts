import type { LeadStageOption } from '../../../database/documents/shared';
import type { FormatCapPurpose } from '../applications/cap/types';
import { ExcelExportFormat } from '../enums';
import type { FormatSetting } from '.';

type GetLeadExcelFormatSettingsParams = {
    format: ExcelExportFormat;
    capPurpose?: FormatCapPurpose;
    company: {
        displayName: string;
        countryCode: string;
        currency: string;
        timeZone?: string;
    };
    validatedLeadStage: LeadStageOption;
    languageId: string | null | undefined;
};

const getLeadExcelFormatSettings = ({
    format,
    capPurpose,
    company,
    validatedLeadStage,
    languageId,
}: GetLeadExcelFormatSettingsParams): FormatSetting => {
    if (format === ExcelExportFormat.cap) {
        return {
            format,
            capPurpose: capPurpose as FormatCapPurpose,
            tenant: `${company.displayName}_${company.countryCode}`.toUpperCase(),
            stage: validatedLeadStage,
            routerFirstLanguage: languageId || null,
            timeZone: company?.timeZone,
        };
    }

    return {
        format,
        stage: validatedLeadStage,
        currencyCode: company.currency,
        timeZone: company.timeZone,
        routerFirstLanguage: languageId || null,
    };
};

export default getLeadExcelFormatSettings;

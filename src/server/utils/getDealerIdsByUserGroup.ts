import { ObjectId } from 'mongodb';
import type { User } from '../database/documents/User';
import type { Loaders } from '../loaders';

/**
 * Gets dealer IDs accessible to a user based on their user groups, with optional company filtering
 *
 * @param user - The user for whom to retrieve dealer IDs
 * @param filter - Optional filtering rules, can specify a company ID to filter user groups
 * @param loaders - DataLoader instances for efficient data fetching
 * @returns Promise resolving to an array of dealer IDs the user has access to
 */
const getDealerIdsByUserGroup = async (user: User, filter: { companyId?: ObjectId }, loaders: Loaders) =>
    loaders.userGroupsByUserId
        .load(user._id)
        .then(groups =>
            filter?.companyId
                ? groups.filter(userGroup => userGroup.companyId.toHexString() === filter.companyId.toHexString())
                : groups
        )
        .then(groups => groups.flatMap(group => group.dealerIds));

export default getDealerIdsByUserGroup;

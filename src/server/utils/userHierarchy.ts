import { isNil } from 'lodash/fp';
import type { ObjectId, Document } from 'mongodb';
import type { UserGroup } from '../database/documents';
import getDatabaseContext from '../database/getDatabaseContext';
import * as oid from './oid';

export type UserHierarchy = {
    self: ObjectId;
    ancestors: ObjectId[];
    descendants: ObjectId[];
    siblings: ObjectId[];
};

type AggregatedUserGroup = UserGroup & {
    ancestors?: UserGroup[] | null;
};

const loadUserHierarchy = async (currentUserId: ObjectId, companyId?: ObjectId): Promise<UserHierarchy> => {
    const { collections } = await getDatabaseContext();

    // Find user's own groups
    const userGroups = await collections.userGroups
        .find({
            userIds: { $in: [currentUserId] },
            ...(companyId ? { companyId } : {}),
        })
        .toArray();

    const userGroupIds = userGroups.map(g => g._id);

    // Find all ancestors using $graphLookup
    const ancestorPipeline: Document[] = [
        {
            $match: {
                _id: { $in: userGroupIds },
                ...(companyId ? { companyId } : {}),
            },
        },
        {
            $graphLookup: {
                from: 'userGroups',
                startWith: '$superiorUserGroupIds',
                connectFromField: 'superiorUserGroupIds',
                connectToField: '_id',
                as: 'ancestors',
                maxDepth: 100, // to prevent dead loops
            },
        },
    ];

    const ancestorResults = await collections.userGroups.aggregate<AggregatedUserGroup>(ancestorPipeline).toArray();
    const ancestorUsers = oid.unique(
        ancestorResults
            .flatMap(result => result.ancestors || [])
            .flatMap(group => group.userIds)
            .filter(id => !id.equals(currentUserId))
    );

    // Find all descendants using $graphLookup
    const descendantPipeline: Document[] = [
        {
            $graphLookup: {
                from: 'userGroups',
                startWith: '$superiorUserGroupIds',
                connectFromField: 'superiorUserGroupIds',
                connectToField: '_id',
                as: 'ancestors',
                maxDepth: 100, // to prevent dead loops
            },
        },
        {
            $match: {
                'ancestors._id': { $in: userGroupIds },
                ...(companyId ? { companyId } : {}),
            },
        },
    ];

    const descendantResults = await collections.userGroups.aggregate<AggregatedUserGroup>(descendantPipeline).toArray();
    const descendantUsers = oid.unique(
        descendantResults.flatMap(group => group.userIds).filter(id => !id.equals(currentUserId))
    );

    // Find siblings (users in parallel groups)
    const siblingGroups = await collections.userGroups
        .find({
            userIds: { $in: [currentUserId] },
            isParallel: true,
            ...(companyId ? { companyId } : {}),
        })
        .toArray();

    const siblingUsers = oid.unique(
        siblingGroups.flatMap(group => group.userIds).filter(id => !id.equals(currentUserId))
    );

    return {
        self: currentUserId,
        ancestors: ancestorUsers,
        descendants: descendantUsers,
        siblings: siblingUsers,
    };
};

export const batchUserHierarchy = async (
    currentUserIds: readonly ObjectId[] | null | undefined,
    companyId?: ObjectId
): Promise<Record<string, UserHierarchy>> => {
    if (isNil(currentUserIds) || currentUserIds.length === 0) {
        return {};
    }

    const hierarchies = await Promise.all(
        currentUserIds.map(async userId => {
            const hierarchy = await loadUserHierarchy(userId, companyId);

            return [userId.toHexString(), hierarchy] as const;
        })
    );

    return Object.fromEntries(hierarchies);
};

const isUserAncestorOf = (hierarchy: UserHierarchy, targetUserId: ObjectId): boolean =>
    hierarchy.descendants.some(descendantId => descendantId.equals(targetUserId));

const isUserDescendantOf = (hierarchy: UserHierarchy, targetUserId: ObjectId): boolean =>
    hierarchy.ancestors.some(ancestorId => ancestorId.equals(targetUserId));

const isUserSiblingOf = (hierarchy: UserHierarchy, targetUserId: ObjectId): boolean =>
    hierarchy.siblings.some(siblingId => siblingId.equals(targetUserId));

export const getUserRelationship = (
    hierarchy: UserHierarchy,
    targetUserId: ObjectId
): 'self' | 'ancestor' | 'descendant' | 'sibling' | 'none' => {
    if (hierarchy.self.equals(targetUserId)) {
        return 'self';
    }

    if (isUserAncestorOf(hierarchy, targetUserId)) {
        return 'ancestor';
    }

    if (isUserDescendantOf(hierarchy, targetUserId)) {
        return 'descendant';
    }

    if (isUserSiblingOf(hierarchy, targetUserId)) {
        return 'sibling';
    }

    return 'none';
};

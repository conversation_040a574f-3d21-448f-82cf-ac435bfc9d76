import { ObjectId } from 'mongodb';
import { ConsentsAndDeclarations } from '../database/documents';
import type { ApplicationAgreements } from '../database/helpers/getInitialAgreementsForApplication';
import { Loaders } from '../loaders';
import ensureManyFromLoaders from './ensureManyFromLoaders';

/**
 * Extract unique parent IDs from agreements that have parentId in their consents
 */
const extractParentIds = (agreements: ApplicationAgreements, consents: ConsentsAndDeclarations[]): string[] => {
    if (agreements.length === 0 || consents.length === 0) {
        return [];
    }

    const consentMap = new Map(consents.map(consent => [consent._id.toHexString(), consent]));

    return Array.from(
        new Set(
            agreements
                .map(agreement => consentMap.get(agreement.consentId.toHexString()))
                .filter(consent => (consent as any)?.parentId)
                .map(consent => (consent as any).parentId.toHexString())
        )
    );
};

/**
 * Optimized grouping of agreements by parentId for PDF rendering.
 *
 * Groups agreements with the same parentId and sorts them by their parent's orderNumber.
 * Individual agreements (without parentId) are sorted by their own orderNumber.
 *
 * @param agreements - Array of agreements to process
 * @param consents - Array of consent documents
 * @param parentConsents - Array of parent consent documents for ordering
 * @returns Sorted array of agreements with proper grouping
 */
const groupAgreementsByParent = (
    agreements: ApplicationAgreements,
    consents: ConsentsAndDeclarations[],
    parentConsents: ConsentsAndDeclarations[] = []
): ApplicationAgreements => {
    if (agreements.length === 0) {
        return [];
    }

    // Create unified consent map (single pass for both regular and parent consents)
    const consentMap = new Map<string, ConsentsAndDeclarations>(
        [...consents, ...parentConsents].map(consent => [consent._id.toHexString(), consent])
    );

    // Group agreements and collect order info in single pass
    const groups = new Map<string, { agreements: ApplicationAgreements[number][]; orderNumber: number }>();
    const individuals: Array<{ agreement: ApplicationAgreements[number]; orderNumber: number }> = [];

    for (const agreement of agreements) {
        const consent = consentMap.get(agreement.consentId.toHexString());
        const parentId = (consent as any)?.parentId;

        if (parentId) {
            const parentKey = parentId.toHexString();
            const parentConsent = consentMap.get(parentKey);
            const orderNumber = parentConsent?.orderNumber || 0;

            if (!groups.has(parentKey)) {
                groups.set(parentKey, { agreements: [], orderNumber });
            }
            groups.get(parentKey)!.agreements.push(agreement);
        } else {
            individuals.push({
                agreement,
                orderNumber: consent?.orderNumber || 0,
            });
        }
    }

    // Combine and sort all items by orderNumber
    const allItems = [
        ...individuals.map(({ agreement, orderNumber }) => ({ items: [agreement], orderNumber })),
        ...Array.from(groups.values()).map(({ agreements, orderNumber }) => ({ items: agreements, orderNumber })),
    ];

    allItems.sort((a, b) => a.orderNumber - b.orderNumber);

    return allItems.flatMap(({ items }) => items);
};

/**
 * Processes agreements for PDF generation by:
 * 1. Filtering for agreed agreements only
 * 2. Loading consent documents
 * 3. Loading parent consents for grouping
 * 4. Grouping agreements by parent for proper ordering
 * 5. Mapping agreements with their consent documents
 *
 * This utility streamlines agreement processing across all PDF generation files.
 */
export const processAgreementsForPdf = async (agreements: ApplicationAgreements, loaders: Loaders) => {
    // Filter agreed agreements
    const agreedAgreements = agreements.filter(agreement => agreement.isAgreed === true);

    if (agreedAgreements.length === 0) {
        return [];
    }

    // Load agreed consents
    const agreedConsentIds = agreedAgreements.map(agreement => agreement.consentId);
    const consents = await loaders.consentById
        .loadMany(agreedConsentIds)
        .then(ensureManyFromLoaders<ConsentsAndDeclarations>);

    // Extract parent IDs and load parent consents for grouping
    const parentIds = extractParentIds(agreedAgreements, consents);
    const parentConsents =
        parentIds.length > 0
            ? await loaders.consentById
                  .loadMany(parentIds.map(id => new ObjectId(id)))
                  .then(ensureManyFromLoaders<ConsentsAndDeclarations>)
            : [];

    // Group agreements by parent for proper ordering
    const groupedAgreements = groupAgreementsByParent(agreedAgreements, consents, parentConsents);

    // Map agreements with their consent documents
    return groupedAgreements.map(agreement => ({
        ...agreement,
        consent: consents.find(consent => consent._id.equals(agreement.consentId)),
    }));
};

/**
 * Processes both applicant and guarantor agreements for PDF generation.
 * Returns an object with processed agreements for both roles.
 */
export const processApplicantAndGuarantorAgreements = async (
    applicantAgreements: ApplicationAgreements,
    guarantorAgreements: ApplicationAgreements | undefined,
    loaders: Loaders
) => {
    const [processedApplicantAgreements, processedGuarantorAgreements] = await Promise.all([
        processAgreementsForPdf(applicantAgreements, loaders),
        guarantorAgreements ? processAgreementsForPdf(guarantorAgreements, loaders) : Promise.resolve([]),
    ]);

    return {
        applicantAgreements: processedApplicantAgreements,
        guarantorAgreements: processedGuarantorAgreements,
    };
};

import { unlink, writeFile } from 'fs/promises';
import { tmpdir } from 'os';
import { join } from 'path';
import { nanoid } from 'nanoid';
import { execute } from './encryptPdf';

const combinePdfs = async (pdfs: Buffer[]): Promise<Buffer | null> => {
    if (!pdfs.length) {
        return null;
    }

    let tmpPaths: string[] = [];
    try {
        // Write each PDF buffer to a temp file
        const paths = await Promise.all(
            pdfs.map(async pdf => {
                const path = join(tmpdir(), nanoid());
                await writeFile(path, pdf);

                return path;
            })
        );
        tmpPaths = paths;

        // Prepare qpdf arguments for combining
        // qpdf --empty --pages file1.pdf file2.pdf ... -- -
        const args = ['--empty', '--pages', ...paths, '--', '-'];

        // Combine PDFs
        const combinedPdf = await execute(args);

        return combinedPdf;
    } catch (error) {
        console.error(error);

        return null;
    } finally {
        // Clean up temp files
        await Promise.all(tmpPaths.map(path => unlink(path)));
    }
};

export default combinePdfs;

import { ObjectId } from 'mongodb';
import { AuthorKind } from '../../database/documents/Versioning';
import { ModuleType } from '../../database/documents/modules';
import getDatabaseContext from '../../database/getDatabaseContext';
import { type LeadModuleExtended } from './types';

export const getLeadModuleFilter = (modules: LeadModuleExtended[], companyIds: ObjectId[]) => {
    if (!modules?.length) {
        return {};
    }

    const hasLaunchPadModule = modules.some(m => m._type === ModuleType.LaunchPadModule);
    const hasRetainModule = modules.some(m => m._type === ModuleType.PorscheRetainModule);

    const nonLaunchpadModuleIds = modules.filter(m => m._type !== ModuleType.LaunchPadModule).map(m => m._id);

    // Case 1: Only LaunchPad module (exclude Launchpad Leads created by PorscheR<PERSON>in)
    if (hasLaunchPadModule && !hasRetainModule) {
        const launchPadModuleIds = modules.filter(m => m._type === ModuleType.LaunchPadModule).map(m => m._id);

        return {
            $or: [
                { moduleId: { $in: nonLaunchpadModuleIds } }, // non-LaunchPad
                {
                    moduleId: { $in: launchPadModuleIds },
                    '_versioning.createdBy.kind': { $ne: AuthorKind.PorscheRetain },
                },
            ],
        };
    }

    // Case 2: Only Retain module (include only Launchpad Leads created by PorscheRetain)
    if (hasRetainModule && !hasLaunchPadModule) {
        const launchpadModuleIds = modules
            .filter(m => m._type === ModuleType.PorscheRetainModule)
            .map(m => m.launchPadModuleId);

        return {
            $or: [
                { moduleId: { $in: nonLaunchpadModuleIds } }, // non-LaunchPad
                {
                    moduleId: { $in: launchpadModuleIds },
                    '_versioning.createdBy.kind': AuthorKind.PorscheRetain,
                },
            ],
        };
    }

    // Default: all moduleIds
    const moduleIds = modules.map(m => m._id);

    return {
        moduleId: { $in: moduleIds },
    };
};

export const addLaunchpadModuleToLeadModuleFilter = async (modules: LeadModuleExtended[]) => {
    if (modules?.length) {
        const hasLaunchPadModule = modules.some(m => m._type === ModuleType.LaunchPadModule);
        const hasRetainModule = modules.some(m => m._type === ModuleType.PorscheRetainModule);

        if (hasRetainModule && !hasLaunchPadModule) {
            const { collections } = await getDatabaseContext();

            const launchpadModuleIds = modules
                .filter(m => m._type === ModuleType.PorscheRetainModule)
                .map(m => m.launchPadModuleId);

            const launchpadModules = await collections.modules
                .find({
                    _type: ModuleType.LaunchPadModule,
                    _id: { $in: launchpadModuleIds },
                })
                .toArray();

            return [...modules, ...launchpadModules] as LeadModuleExtended[];
        }
    }

    return modules;
};

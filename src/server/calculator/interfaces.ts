import { CalculatorFinanceProduct, Query } from './types';

/**
 * Interface for root instances that manage collections of nodes
 */
export interface IRoot {
    /**
     * Collection of nodes managed by this root
     */
    readonly nodes: INode[];

    /**
     * Find a node by its identifier
     * @param identifier - The string identifier of the node
     * @param error - Whether to throw an error if not found (default: true)
     * @returns The node instance or undefined
     */
    seekByIdentifier(identifier: string, error?: boolean): INode | undefined;

    seekByInterface<T extends INode>(predicate: (node: INode) => node is T, error?: boolean): T | undefined;

    /**
     * Reset all nodes managed by this root.
     * This will also re-prioritize nodes based on dependencies.
     */
    reset(): void;

    /**
     * Get all node identifiers in this root
     */
    getNodeIdentifiers(): string[];

    /**
     * Check if a node with the given identifier exists
     */
    hasNode(identifier: string): boolean;
}

/**
 * Interface for Calculator to break circular dependencies
 */
export interface ICalculator {
    /**
     * The financial product configuration
     */
    readonly product: CalculatorFinanceProduct;

    /**
     * Optional calculation rounding configuration
     */
    readonly calculationRounding?: number | null;

    /**
     * The current query being calculated
     */
    readonly query: Query;

    /**
     * The root node manager
     */
    readonly root: IRoot;

    /**
     * Perform the calculation
     */
    calculate(query: Query, preflight?: boolean): void;

    /**
     * Get calculation results
     */
    getResult(): Record<string, any>;

    /**
     * Get a specific node value by identifier
     */
    getNode(identifier: string): any;

    /**
     * Check if a node exists
     */
    hasNode(identifier: string): boolean;

    /**
     * Debug method to print dependency tree
     */
    debugDependencyTree(): string;
}

/**
 * Interface for all node instances
 */
export interface INode {
    /**
     * The calculator instance this node belongs to
     */
    readonly calculator: ICalculator;

    /**
     * List of node identifiers that this node depends on.
     * These are string identifiers, not class references.
     */
    readonly dependencies: string[];

    /**
     * The computed value of this node.
     * The type varies by node implementation.
     */
    readonly value: any;

    /**
     * Compute the value for this node based on its dependencies
     * and the calculator's query.
     */
    resolve(): void;

    /**
     * Reset this node's value to its initial state
     */
    reset(): void;
}

/**
 * Interface for node constructors (classes)
 */
export interface INodeConstructor {
    /**
     * Create a new instance of the node
     */
    new (calculator: ICalculator): INode;

    /**
     * Static identifier for this node type.
     * Used for registration and dependency resolution.
     */
    readonly identifier: string;
}

/**
 * Type guard to check if a value is a node instance
 */
export function isNode(value: any): value is INode {
    return (
        value &&
        typeof value === 'object' &&
        'calculator' in value &&
        'dependencies' in value &&
        'value' in value &&
        typeof value.resolve === 'function' &&
        typeof value.reset === 'function'
    );
}

/**
 * Type guard to check if a value is a node constructor
 */
export function isNodeConstructor(value: any): value is INodeConstructor {
    return value && typeof value === 'function' && 'identifier' in value && typeof value.identifier === 'string';
}

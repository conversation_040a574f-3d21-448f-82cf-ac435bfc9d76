import { isNil } from 'lodash/fp';
import { CalculationMode, PaymentMode, DeriveMethod } from '../../database/documents';
import { equalTotalPayment, simple } from '../roots/formulas';
import MonthlyInstalment from './monthlyInstalment';
import { BalloonPaymentNode, InterestRateNode, LoanNode, TermNode } from './types';

export default class HirePurchaseWithBalloonMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'hirePurchaseWithBalloonMonthlyInstalment';

    get dependencies(): string[] {
        return ['term', 'loan', 'interestRate', 'balloonPayment'];
    }

    resolve() {
        const { calculationMode, paymentMode, deriveMethod } = this;

        if (deriveMethod === DeriveMethod.LookupTable) {
            throw new Error(
                `[${HirePurchaseWithBalloonMonthlyInstalment.identifier}] configured with invalid derive ` +
                    `method ${deriveMethod}`
            );
        }

        const term = this.getDependency<TermNode>('term');
        const loan = this.getDependency<LoanNode>('loan');
        const interestRate = this.getDependency<InterestRateNode>('interestRate');
        const balloonPayment = this.getDependency<BalloonPaymentNode>('balloonPayment');

        if (isNil(term?.value) || isNil(loan?.value) || isNil(interestRate?.value) || isNil(balloonPayment?.value)) {
            throw new Error(`[${HirePurchaseWithBalloonMonthlyInstalment.identifier}] configured with a missing input`);
        }

        switch (calculationMode) {
            case CalculationMode.Flat:
                this._value = this.applyCalculationRounding(
                    simple(term.value, loan.value.amount, interestRate.value, balloonPayment.value.amount)
                );
                break;

            case CalculationMode.Effective:
                this._value = this.applyCalculationRounding(
                    equalTotalPayment(
                        term.value,
                        loan.value.amount,
                        interestRate.value,
                        paymentMode === PaymentMode.Advance,
                        balloonPayment.value.amount
                    )
                );
                break;

            default:
                throw new Error(
                    `[${HirePurchaseWithBalloonMonthlyInstalment.identifier}] configured with invalid calculation ` +
                        `mode: ${calculationMode}`
                );
        }
    }
}

/* eslint-disable max-len */
import { isNil } from 'lodash/fp';
import { CalculationMode, DeriveMethod, FinanceProductType } from '../../database/documents';
import { simple } from '../roots/formulas';
import MonthlyInstalment from './monthlyInstalment';
import { InterestRateNode, LoanNode, TermNode } from './types';
import { isNaturalInteger } from './utils';

export default class DeferredPrincipalMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'deferredPrincipalMonthlyInstalment';

    get dependencies(): string[] {
        return ['term', 'loan', 'interestRate'];
    }

    resolve(): void {
        const { deriveMethod } = this;
        if (deriveMethod === DeriveMethod.LookupTable) {
            throw new Error(
                `[${DeferredPrincipalMonthlyInstalment.identifier}] configured with invalid derive method ${deriveMethod}`
            );
        }

        this._value = this.calculate();
    }

    private calculate() {
        const {
            calculationMode,
            calculator: { product },
        } = this;

        if (product.type !== FinanceProductType.DeferredPrincipal) {
            throw new Error(
                `[${DeferredPrincipalMonthlyInstalment.identifier}] finance product should be Deferred Principal`
            );
        }

        const {
            term: { interestOnly },
        } = product;

        const term = this.getDependency<TermNode>('term');
        if (isNil(interestOnly) || !isNaturalInteger(interestOnly) || interestOnly > term.value) {
            throw new Error(
                `[${DeferredPrincipalMonthlyInstalment.identifier}] configured with invalid interest only terms: ${interestOnly}`
            );
        }

        const loan = this.getDependency<LoanNode>('loan');
        const interestRate = this.getDependency<InterestRateNode>('interestRate');
        switch (calculationMode) {
            case CalculationMode.Flat:
                return this.applyCalculationRounding([
                    simple(interestOnly, loan.value.amount, interestRate.value, loan.value.amount),
                    simple(term.value - interestOnly, loan.value.amount, interestRate.value),
                ]);
            default:
                throw new Error(
                    `[${DeferredPrincipalMonthlyInstalment.identifier}] configured with invalid calculation mode: ${calculationMode}`
                );
        }
    }
}

import { isNil } from 'lodash/fp';
import { FinanceProductType, SettlementInstalmentOn } from '../../schema/resolvers/enums';
import { calculateBalloonPayment, calculateReducedMonthlyInstalment } from '../roots/formulas';
import MonthlyInstalment from './monthlyInstalment';
import { InterestRateNode, LoanNode, TermNode } from './types';

export default class HirePurchaseWithBalloonGFVMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'hirePurchaseWithBalloonGFVMonthlyInstalment';

    get dependencies(): string[] {
        return ['term', 'loan', 'interestRate'];
    }

    get reducedMonthlyPaymentRate(): number {
        const {
            calculator: { product },
        } = this;

        if (product.type === FinanceProductType.HirePurchaseWithBalloonGFV) {
            return product.balloon.reducedMonthlyPayment;
        }

        return undefined;
    }

    get settlementInstalmentOn(): SettlementInstalmentOn {
        const {
            calculator: { product },
        } = this;

        if (product.type === FinanceProductType.HirePurchaseWithBalloonGFV) {
            return product.balloon.settlementInstalmentOn;
        }

        return undefined;
    }

    resolve() {
        const term = this.getDependency<TermNode>('term');
        const loan = this.getDependency<LoanNode>('loan');
        const interestRate = this.getDependency<InterestRateNode>('interestRate');
        const { reducedMonthlyPaymentRate, settlementInstalmentOn } = this;

        if (
            isNil(term?.value) ||
            isNil(loan?.value) ||
            isNil(interestRate?.value) ||
            isNil(reducedMonthlyPaymentRate)
        ) {
            throw new Error(
                `[${HirePurchaseWithBalloonGFVMonthlyInstalment.identifier}] configured with a missing input`
            );
        }

        const reducedMonthlyInstalment: number = calculateReducedMonthlyInstalment(
            term.value,
            loan.value.amount,
            interestRate.value,
            reducedMonthlyPaymentRate
        );

        const balloonPayment: number = calculateBalloonPayment(
            term.value,
            loan.value.amount,
            interestRate.value,
            reducedMonthlyInstalment
        );

        switch (settlementInstalmentOn) {
            case SettlementInstalmentOn.TwoMonth:
            case SettlementInstalmentOn.OneMonth: {
                this._value = this.applyCalculationRounding([
                    reducedMonthlyInstalment,
                    balloonPayment,
                    reducedMonthlyInstalment,
                ]);
                break;
            }

            case SettlementInstalmentOn.Last: {
                this._value = this.applyCalculationRounding([reducedMonthlyInstalment, balloonPayment]);
                break;
            }

            default:
                throw new Error(
                    `[${HirePurchaseWithBalloonGFVMonthlyInstalment.identifier}] invalid SettlementInstalmentOn Field`
                );
        }
    }
}

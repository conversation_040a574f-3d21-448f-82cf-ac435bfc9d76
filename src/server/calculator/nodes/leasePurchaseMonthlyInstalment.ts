import { CalculationMode, DeriveMethod, PaymentMode } from '../../database/documents';
import { equalTotalPayment, simple } from '../roots/formulas';
import MonthlyInstalment from './monthlyInstalment';
import { InterestRateNode, LoanNode, ResidualValueNode, TermNode } from './types';

export default class LeasePurchaseMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'leasePurchaseMonthlyInstalment';

    get dependencies(): string[] {
        return ['term', 'loan', 'interestRate', 'residualValue'];
    }

    private calculate() {
        const { calculationMode, paymentMode } = this;

        const term = this.getDependency<TermNode>('term');
        const loan = this.getDependency<LoanNode>('loan');
        const interestRate = this.getDependency<InterestRateNode>('interestRate');
        const residualValue = this.getDependency<ResidualValueNode>('residualValue');

        switch (calculationMode) {
            case CalculationMode.Flat:
                return this.applyCalculationRounding(
                    simple(term.value, loan.value.amount, interestRate.value, residualValue.value.amount)
                );

            case CalculationMode.Effective:
                return this.applyCalculationRounding(
                    equalTotalPayment(
                        term.value,
                        loan.value.amount,
                        interestRate.value,
                        paymentMode === PaymentMode.Advance,
                        residualValue.value.amount
                    )
                );

            default:
                throw new Error(
                    // eslint-disable-next-line max-len
                    `[${LeasePurchaseMonthlyInstalment.identifier}] configured with invalid calculation mode: ${calculationMode}`
                );
        }
    }

    resolve(): void {
        const { deriveMethod } = this;

        if (deriveMethod === DeriveMethod.LookupTable) {
            throw new Error(
                `[${LeasePurchaseMonthlyInstalment.identifier}] configured with invalid derive method ${deriveMethod}`
            );
        }

        this._value = this.calculate();
    }
}

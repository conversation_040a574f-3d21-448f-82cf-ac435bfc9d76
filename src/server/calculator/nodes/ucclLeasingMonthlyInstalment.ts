import { isNil } from 'lodash/fp';
import MonthlyInstalment from './monthlyInstalment';
import { InsuranceFeeNode, LicenseAndFuelTaxNode, MonthlyPaymentFixedInterestRateNode, TaxLossNode } from './types';

export default class UcclLeasingMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'ucclLeasingMonthlyInstalment';

    // eslint-disable-next-line class-methods-use-this
    get dependencies(): string[] {
        return ['monthlyPaymentFixedInterestRate', 'licenseAndFuelTax', 'insuranceFee', 'taxLoss'];
    }

    resolve(): void {
        this._value = this.applyCalculationRounding(this.calculate());
    }

    private calculate() {
        const fixedInterestRate = this.getDependency<MonthlyPaymentFixedInterestRateNode>(
            'monthlyPaymentFixedInterestRate'
        );

        if (isNil(fixedInterestRate.value)) {
            throw new Error(`[${UcclLeasingMonthlyInstalment.identifier}] configured with a missing input`);
        }

        const licenseAndFuelTax = this.getDependency<LicenseAndFuelTaxNode>('licenseAndFuelTax');
        const insuranceFee = this.getDependency<InsuranceFeeNode>('insuranceFee');
        const taxLoss = this.getDependency<TaxLossNode>('taxLoss');

        return (
            (fixedInterestRate?.value ?? 0) +
            (licenseAndFuelTax?.value ?? 0) +
            (insuranceFee.value?.amount ?? 0) +
            (taxLoss?.value ?? 0)
        );
    }
}

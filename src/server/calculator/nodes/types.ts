import { AmountUnit } from '../../database';
import { CompoundValue } from '../types';
import Node from './node';

export type AssuredResaleValueNode = Node & { value: CompoundValue };
export type BalloonPaymentNode = Node & { value: CompoundValue };
export type CommissionNode = Node & { value: number };
export type TermNode = Node & { value: number };
export type InterestRateNode = Node & { value: number };
export type LicensePlateFeeNode = Node & { value: number | null };
export type LoanNode = Node & { value: CompoundValue; unit: AmountUnit };
export type DownPaymentNode = Node & { value: CompoundValue; mode: 'range' | 'table'; unit: AmountUnit };
export type DisplacementNode = Node & { value: number };
export type MonthlyInstalmentNode = Node & { value: number | number[] | null };
export type MonthlyPaymentFixedInterestRateNode = Node & { value: number };
export type MileageNode = Node & { value: number | null };
export type ResidualValueNode = Node & { value: CompoundValue | null };
export type LicenseAndFuelTaxNode = Node & { value: number };
export type InsuranceFeeNode = Node & { value: CompoundValue };
export type TaxLossNode = Node & { value: number };

import { isNil } from 'lodash/fp';
import { CalculationMode, PaymentMode } from '../../database';
import { equalTotalPayment, simple } from '../roots/formulas';
import MonthlyInstalment from './monthlyInstalment';
import { InterestRateNode, LoanNode, TermNode } from './types';

export default class HirePurchaseMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'hirePurchaseMonthlyInstalment';

    get dependencies(): string[] {
        return ['term', 'loan', 'interestRate'];
    }

    resolve() {
        const { calculationMode, paymentMode } = this;

        const term = this.getDependency<TermNode>('term');
        const loan = this.getDependency<LoanNode>('loan');
        const interestRate = this.getDependency<InterestRateNode>('interestRate');

        if (isNil(term?.value) || isNil(loan?.value) || isNil(interestRate?.value)) {
            throw new Error(`[${HirePurchaseMonthlyInstalment.identifier}] configured with a missing input`);
        }

        switch (calculationMode) {
            case CalculationMode.Flat:
                this._value = this.applyCalculationRounding(simple(term.value, loan.value.amount, interestRate.value));
                break;

            case CalculationMode.Effective:
                this._value = this.applyCalculationRounding(
                    equalTotalPayment(
                        term.value,
                        loan.value.amount,
                        interestRate.value,
                        paymentMode === PaymentMode.Advance
                    )
                );
                break;

            default:
                throw new Error(
                    `[${HirePurchaseMonthlyInstalment.identifier}] configured with invalid calculation ` +
                        `mode: ${calculationMode}`
                );
        }
    }
}

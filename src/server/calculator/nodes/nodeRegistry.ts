import { INodeConstructor } from '../interfaces';

/**
 * Central registry for all node types.
 * This eliminates the need for importing node classes directly,
 * breaking circular dependencies.
 */
export class NodeRegistry {
    private static nodes = new Map<string, INodeConstructor>();

    private static initialized = false;

    /**
     * Register a node class with its identifier
     */
    static register(identifier: string, NodeClass: INodeConstructor, aliases?: string[]): void {
        if (this.nodes.has(identifier)) {
            console.warn(`Node with identifier "${identifier}" is already registered. Overwriting.`);
        }
        this.nodes.set(identifier, NodeClass);
    }

    /**
     * Get a node class by its identifier
     */
    static getNodeClass(identifier: string): INodeConstructor {
        const NodeClass = this.nodes.get(identifier);
        if (!NodeClass) {
            throw new Error(`Node class for identifier "${identifier}" not found`);
        }

        return NodeClass;
    }

    /**
     * Check if a node is registered
     */
    static has(identifier: string): boolean {
        return this.nodes.has(identifier);
    }

    /**
     * Get all registered identifiers
     */
    static getAllIdentifiers(): string[] {
        return Array.from(this.nodes.keys());
    }

    /**
     * Clear the registry (useful for testing)
     */
    static clear(): void {
        this.nodes.clear();
        this.initialized = false;
    }

    /**
     * Mark registry as initialized
     */
    static markInitialized(): void {
        this.initialized = true;
    }

    /**
     * Check if registry is initialized
     */
    static isInitialized(): boolean {
        return this.initialized;
    }

    /**
     * Get registry size
     */
    static size(): number {
        return this.nodes.size;
    }
}

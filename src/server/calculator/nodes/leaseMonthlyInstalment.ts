import { filter, flow, head, isNil, orderBy } from 'lodash/fp';
import {
    FinanceProductType,
    LeaseTableCell,
    LeaseValueTableCellForVehicleParameterFilter,
} from '../../database/documents';
import { InvalidFinancialProductError, InvalidQueryError, InvalidQueryReason } from '../errors';
import { CalculatorFinanceProduct } from '../types';
import MonthlyInstalment from './monthlyInstalment';
import { TermNode } from './types';

const getLeaseSettings = (financeProduct: CalculatorFinanceProduct) => {
    if (financeProduct.type === FinanceProductType.Lease) {
        return financeProduct.lease;
    }

    throw new InvalidFinancialProductError(`[${LeaseMonthlyInstalment.identifier}] settings missing`);
};

export default class LeaseMonthlyInstalment extends MonthlyInstalment {
    public static readonly identifier: string = 'leaseMonthlyInstalment';

    get dependencies(): string[] {
        return ['term'];
    }

    private get table() {
        const {
            calculator: { product },
        } = this;

        const leaseSettings = getLeaseSettings(product);

        return leaseSettings.table;
    }

    private get tableParameter(): LeaseValueTableCellForVehicleParameterFilter[] {
        const {
            calculator: { product },
        } = this;

        const balloonValueSetting = getLeaseSettings(product);

        if (balloonValueSetting.type === 'table') {
            return balloonValueSetting.vehicleFilterParameterTable;
        }

        return [];
    }

    resolve(): void {
        const {
            table,
            calculator: {
                query: { variantSuiteId, vehicleKind },
            },
            tableParameter,
        } = this;

        if (!variantSuiteId) {
            throw new Error(`[${LeaseMonthlyInstalment.identifier}] input variant id is missing`);
        }

        if (isNil(table)) {
            throw new InvalidFinancialProductError(
                `[${LeaseMonthlyInstalment.identifier}] configured to use table but the table is not found`
            );
        }

        const { value: termValue } = this.getDependency<TermNode>('term');

        if (termValue === null || termValue === undefined) {
            throw new Error(`[${LeaseMonthlyInstalment.identifier}] configured to use table but the term is not found`);
        }

        const target: LeaseTableCell = flow([
            filter((entry: LeaseTableCell) => entry.term === termValue && variantSuiteId.equals(entry.variantSuiteId)),
            orderBy(['term'], ['asc']),
            head,
        ])(table);

        if (target) {
            this._value = target.value;

            return;
        }

        const targetFromVehicleParameter = tableParameter.find(
            entry => entry.type === vehicleKind && entry.term === termValue
        );

        if (targetFromVehicleParameter) {
            this._value = targetFromVehicleParameter.value;

            return;
        }

        throw new InvalidQueryError(
            'configured to use table but the entry cannot be resolved',
            LeaseMonthlyInstalment.identifier,
            InvalidQueryReason.General
        );
    }
}

import { ICalculator, INode } from '../interfaces';
import { NodeRegistry } from '../nodes/nodeRegistry';
import Root from './root';

/**
 * Generic root implementation that creates nodes based on identifiers.
 * This replaces all the individual root classes.
 */
export class GenericRoot extends Root {
    constructor(
        calculator: ICalculator,
        nodeIdentifiers: string[],
        options?: {
            validateDependencies?: boolean;
            allowMissingNodes?: boolean;
        }
    ) {
        super(calculator);

        const { validateDependencies = false, allowMissingNodes = false } = options || {};

        // Create nodes from identifiers using the registry
        this.nodes = [];

        for (const identifier of nodeIdentifiers) {
            try {
                const NodeClass = NodeRegistry.getNodeClass(identifier);
                const node = new NodeClass(calculator) as INode;
                this.nodes.push(node);
            } catch (error) {
                if (!allowMissingNodes) {
                    throw new Error(`[GenericRoot] Failed to create node "${identifier}": ${error.message}`);
                }
                console.warn(`[GenericRoot] Skipping missing node: ${identifier}`);
            }
        }

        // Validate dependencies if requested
        if (validateDependencies) {
            this.validateDependencies();
        }
    }

    /**
     * Validate that all node dependencies are satisfied
     */
    private validateDependencies(): void {
        const availableIdentifiers = new Set(this.getNodeIdentifiers());

        for (const node of this.nodes) {
            const NodeClass = node.constructor as any;
            const nodeId = NodeClass.identifier;

            for (const depId of node.dependencies) {
                if (!availableIdentifiers.has(depId)) {
                    throw new Error(
                        `[GenericRoot] Node "${nodeId}" depends on "${depId}", ` +
                            `but "${depId}" is not in the root. ` +
                            `Available nodes: ${Array.from(availableIdentifiers).join(', ')}`
                    );
                }
            }
        }
    }

    /**
     * Add a node to this root dynamically
     */
    public addNode(identifier: string): void {
        if (this.hasNode(identifier)) {
            console.warn(`[GenericRoot] Node "${identifier}" already exists`);

            return;
        }

        const NodeClass = NodeRegistry.getNodeClass(identifier);
        const node = new NodeClass(this.calculator) as INode;
        this.nodes.push(node);

        // Re-prioritize to include the new node
        this.reset();
    }

    /**
     * Remove a node from this root
     */
    public removeNode(identifier: string): boolean {
        const initialLength = this.nodes.length;

        this.nodes = this.nodes.filter(node => {
            const NodeClass = node.constructor as any;

            return NodeClass.identifier !== identifier;
        });

        if (this.nodes.length < initialLength) {
            // Re-prioritize after removal
            this.reset();

            return true;
        }

        return false;
    }
}

import { FinanceProductType } from '../../database/documents';

/**
 * Configuration for a root type
 */
export interface RootConfiguration {
    /**
     * The finance product type this configuration is for
     */
    type: FinanceProductType;

    /**
     * List of node identifiers that make up this root
     */
    nodeIdentifiers: string[];

    /**
     * Optional description for documentation
     */
    description?: string;

    /**
     * Optional validation rules
     */
    validation?: {
        requiredNodes?: string[];
        optionalNodes?: string[];
    };
}

/**
 * All root configurations.
 * This replaces individual root class files.
 */
export const rootConfigurations: RootConfiguration[] = [
    {
        type: FinanceProductType.HirePurchase,
        nodeIdentifiers: ['term', 'downPayment', 'loan', 'interestRate', 'hirePurchaseMonthlyInstalment'],
        description: 'Standard hire purchase calculation',
    },
    {
        type: FinanceProductType.HirePurchaseWithBalloon,
        nodeIdentifiers: [
            'term',
            'downPayment',
            'loan',
            'interestRate',
            'balloonPayment',
            'hirePurchaseWithBalloonMonthlyInstalment',
        ],
        description: 'Hire purchase with balloon payment',
    },
    {
        type: FinanceProductType.HirePurchaseWithBalloonGFV,
        nodeIdentifiers: [
            'term',
            'downPayment',
            'loan',
            'interestRate',
            'assuredResaleValue',
            'estimatedSurplusBalloon',
            'hirePurchaseWithBalloonGFVMonthlyInstalment',
        ],
        description: 'Hire purchase with guaranteed future value',
    },
    {
        type: FinanceProductType.Lease,
        nodeIdentifiers: ['term', 'deposit', 'leaseMonthlyInstalment'],
        description: 'Lease calculation',
    },
    {
        type: FinanceProductType.UCCLLeasing,
        nodeIdentifiers: [
            'term',
            'downPayment',
            'loan',
            'interestRate',
            'licensePlateFee',
            'commission',
            'monthlyPaymentFixedInterestRate',
            'displacement',
            'licenseAndFuelTax',
            'insuranceFee',
            'iaxLoss',
            'ucclLeasingMonthlyInstalment',
        ],
        description: 'UCCL Leasing calculation',
    },
    {
        type: FinanceProductType.DeferredPrincipal,
        nodeIdentifiers: ['term', 'downPayment', 'loan', 'interestRate', 'deferredPrincipalMonthlyInstalment'],
        description: 'Deferred principal calculation',
    },
    {
        type: FinanceProductType.LeasePurchase,
        nodeIdentifiers: [
            'term',
            'downPayment',
            'loan',
            'interestRate',
            'mileage',
            'residualValue',
            'leasePurchaseMonthlyInstalment',
        ],
        description: 'Lease purchase calculation',
    },
];

/**
 * Get configuration for a specific finance product type
 */
export function getRootConfiguration(type: FinanceProductType): RootConfiguration | undefined {
    return rootConfigurations.find(config => config.type === type);
}

/**
 * Validate that all required nodes are present in a configuration
 */
export function validateRootConfiguration(config: RootConfiguration): string[] {
    const errors: string[] = [];

    if (!config.nodeIdentifiers || config.nodeIdentifiers.length === 0) {
        errors.push(`Configuration for ${config.type} has no nodes`);
    }

    // Check for duplicates
    const seen = new Set<string>();
    for (const id of config.nodeIdentifiers) {
        if (seen.has(id)) {
            errors.push(`Duplicate node "${id}" in configuration for ${config.type}`);
        }
        seen.add(id);
    }

    return errors;
}

/**
 * Get all unique node identifiers across all configurations
 */
export function getAllRequiredNodeIdentifiers(): string[] {
    const identifiers = new Set<string>();

    for (const config of rootConfigurations) {
        for (const id of config.nodeIdentifiers) {
            identifiers.add(id);
        }
    }

    return Array.from(identifiers).sort();
}

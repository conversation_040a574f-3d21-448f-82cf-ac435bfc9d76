import { Document } from 'bson';
import { AuditTrail } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { paginateAggregation } from '../../../../utils/pagination';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['listSalesOfferAuditTrails'] = async (root, { salesOfferId, pagination }) => {
    const { collections } = await getDatabaseContext();

    const pipeline: Document[] = [
        {
            $match: {
                salesOfferId,
            },
        },
        { $sort: { _date: -1 } },
    ];

    return paginateAggregation<AuditTrail>(collections.auditTrails, pipeline, pagination);
};

export default requiresLoggedUser(query);

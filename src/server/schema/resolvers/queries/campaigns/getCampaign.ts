import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getCampaign'] = async (root, { id }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    return collections.campaigns.findOne({
        $and: [
            { _id: id, isDeleted: false },
            permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.View),
        ],
    });
};

export default requiresLoggedUser(query);

import { isBoolean } from 'lodash/fp';
import type { Filter, Sort, Document } from 'mongodb';
import type { Campaign } from '../../../../database/documents/Campaign';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import type { PermissionController } from '../../../../permissions/core';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { getSortingValue, paginateAggregation } from '../../../../utils/pagination';
import { requiresLoggedUser } from '../../../middlewares';
import {
    CampaignSortingField,
    type GraphQLCampaignFilteringRule,
    type GraphQLCampaignSortingRule,
    type GraphQLQueryResolvers,
    type Maybe,
} from '../../definitions';

const getFilter = (
    permissionController: PermissionController,
    rule?: Maybe<GraphQLCampaignFilteringRule>
): Filter<Campaign> => {
    const filter: Filter<Campaign> = {
        $and: [
            {
                isDeleted: false,
            },
            permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.View),
        ],
    };

    if (!rule) {
        return filter;
    }

    if (rule.campaignId) {
        filter.campaignId = { $regex: rule.campaignId, $options: 'i' };
    }

    if (rule.description) {
        filter['description.defaultValue'] = { $regex: rule.description, $options: 'i' };
    }

    if (isBoolean(rule.isActive)) {
        filter.isActive = rule.isActive;
    }

    if (rule.companyId) {
        filter.companyId = rule.companyId;
    }

    return filter;
};

const getSort = (rule?: Maybe<GraphQLCampaignSortingRule>): Sort => {
    const sort: Sort = { _id: -1 };

    if (!rule) {
        return sort;
    }

    switch (rule.field) {
        case CampaignSortingField.CampaignId:
            return { campaignId: getSortingValue(rule.order), ...sort };

        case CampaignSortingField.Description:
            return { description: getSortingValue(rule.order), ...sort };

        case CampaignSortingField.IsActive:
            return { isActive: getSortingValue(rule.order), ...sort };

        case CampaignSortingField.CreatedAt:
            return { '_versioning.createdAt': getSortingValue(rule.order), ...sort };

        case CampaignSortingField.UpdatedAt:
            return { '_versioning.updatedAt': getSortingValue(rule.order), ...sort };

        default:
            throw new Error('Sorting Field not supported');
    }
};

export const pipelineHandler = (
    permissionController: PermissionController,
    sort?: Maybe<GraphQLCampaignSortingRule>,
    filter?: Maybe<GraphQLCampaignFilteringRule>
) => {
    const pipeline: Document[] = [];

    pipeline.push({ $match: getFilter(permissionController, filter) }, { $sort: getSort(sort) });

    return pipeline;
};

const query: GraphQLQueryResolvers['listCampaigns'] = async (
    root,
    { pagination, sort, filter },
    { getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const pipeline = pipelineHandler(permissionController, sort, filter);

    return paginateAggregation(collections.campaigns, pipeline, pagination);
};

export default requiresLoggedUser(query);

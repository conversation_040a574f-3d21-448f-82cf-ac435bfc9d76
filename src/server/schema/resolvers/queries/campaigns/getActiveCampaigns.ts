import getDatabaseContext from '../../../../database/getDatabaseContext';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getActiveCampaigns'] = async (root, { companyId }, { getPermissionController }) => {
    const { collections } = await getDatabaseContext();

    // No permission filtering needed - this query is used to fetch campaigns as options
    // Users should be able to see all active campaigns for selection purposes

    const campaigns = await collections.campaigns
        .find({
            $and: [
                {
                    companyId,
                    isActive: true,
                    isDeleted: false,
                },
            ],
        })
        .toArray();

    return campaigns;
};

export default requiresLoggedUser(query);

import { Filter } from 'mongodb';
import { Vehicle } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { FinderVehiclePolicyAction, VehiclePolicyAction } from '../../../../permissions';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLQueryResolvers, VehicleKind } from '../../definitions';

const query: GraphQLQueryResolvers['getVehicleBySuiteId'] = async (
    root,
    { suiteId, productionOnly },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    let filters: Filter<Vehicle> = { '_versioning.suiteId': suiteId, isDeleted: false, '_versioning.isLatest': true };

    if (productionOnly) {
        filters.isActive = true;
    } else {
        const user = await getUser(true);

        if (!user) {
            throw new InvalidPermission();
        }

        filters = {
            $or: [
                {
                    $and: [
                        filters,
                        { _kind: VehicleKind.FinderVehicle },
                        permissionController.finderVehicles.getFilterQueryForAction(FinderVehiclePolicyAction.View),
                    ],
                },
                {
                    $and: [
                        filters,
                        { _kind: { $ne: VehicleKind.FinderVehicle } },
                        permissionController.vehicles.getFilterQueryForAction(VehiclePolicyAction.View),
                    ],
                },
            ],
        };
    }

    return collections.vehicles.findOne(filters);
};

export default requiresLoggedUser(query);

import getDatabaseContext from '../../../../database/getDatabaseContext';
import { requiresLoggedUser } from '../../../middlewares';
import { ConditionType, GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['getConditionDetail'] = async (root, { id, kind }) => {
    const { collections } = await getDatabaseContext();

    switch (kind) {
        case ConditionType.IsBank: {
            const bank = await collections.banks.findOne({ _id: id, isDeleted: false });

            return { displayName: bank?.displayName };
        }
        case ConditionType.IsInsurer: {
            const insurer = await collections.insurers.findOne({ _id: id, isDeleted: false });

            return { displayName: insurer?.displayName };
        }
        case ConditionType.IsApplicationModule:
        case ConditionType.IsGiftVoucher: {
            const applicationModule = await collections.modules.findOne({ _id: id });

            return { displayName: applicationModule?.displayName };
        }
        case ConditionType.IsDealer: {
            const dealer = await collections.dealers.findOne({ _id: id, isDeleted: false });

            return { displayName: dealer?.displayName };
        }
        default:
            throw new Error(`Unsupported condition type: ${kind}`);
    }
};

export default requiresLoggedUser(query);

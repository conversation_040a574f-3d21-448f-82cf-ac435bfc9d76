import { LocalCustomerFieldKey } from '../../enums';

// Constructs a "fullName" string for customer search and display purposes.
// - The order of fields in orderedKeys ensures all possible name variations are included,
//   supporting both Western and Japanese conventions.
// - For Japanese customers, "LastNameFront"+"FirstName" and "LastNameJapan"+"FirstNameJapan"
//   are concatenated with no space, matching how names are typically written and searched in Japan.
// - For other fields, a space is used as a separator.
// - This approach allows users to search by any combination of romanized or Japanese names,
//   improving search accuracy and UX for the JP market.
const fullNameReduce = {
    $reduce: {
        input: {
            $let: {
                vars: {
                    // define order of keys to be used
                    orderedKeys: [
                        LocalCustomerFieldKey.FullName,
                        LocalCustomerFieldKey.LastNameFront,
                        LocalCustomerFieldKey.FirstName,
                        LocalCustomerFieldKey.LastName,
                        LocalCustomerFieldKey.LastNameJapan,
                        LocalCustomerFieldKey.FirstNameJapan,
                    ],
                },
                in: {
                    $filter: {
                        input: {
                            // get customer fields based on the ordered keys
                            $map: {
                                input: '$$orderedKeys',
                                as: 'key',
                                in: {
                                    $first: {
                                        $filter: {
                                            input: '$customer.fields',
                                            as: 'field',
                                            // field should not be empty and should match the key
                                            cond: {
                                                $and: [
                                                    { $eq: ['$$field.key', '$$key'] },
                                                    { $ne: ['$$field.deterministicString', ''] },
                                                ],
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        as: 'filteredField',
                        // filter out empty fields
                        cond: { $ne: ['$$filteredField', null] },
                    },
                },
            },
        },
        initialValue: '',
        in: {
            $cond: {
                if: { $eq: ['$$value', ''] },
                then: '$$this.deterministicString',
                else: {
                    $cond: {
                        if: {
                            $or: [
                                { $eq: ['$$this.key', LocalCustomerFieldKey.FirstName] },
                                { $eq: ['$$this.key', LocalCustomerFieldKey.FirstNameJapan] },
                            ],
                        },
                        then: {
                            $concat: ['$$value', '$$this.deterministicString'],
                        },
                        else: {
                            $concat: ['$$value', ' ', '$$this.deterministicString'],
                        },
                    },
                },
            },
        },
    },
};

export default fullNameReduce;

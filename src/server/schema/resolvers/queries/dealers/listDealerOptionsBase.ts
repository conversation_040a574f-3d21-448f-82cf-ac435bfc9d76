import type { Document } from 'mongodb';
import { ReservationStockStatus, type Dealer, type User } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { type Loaders } from '../../../../loaders';
import { type PermissionController } from '../../../../permissions';
import getDealerIdsByUserGroup from '../../../../utils/getDealerIdsByUserGroup';
import { InvalidPermission } from '../../../errors';
import { type GraphQLQueryListDealerOptionsArgs } from '../../definitions';
import { getFilter, getSort } from './shared';

type DealerOptionsContext = {
    getPermissionController: () => Promise<PermissionController>;
    getUser: (required?: boolean) => Promise<User | null>;
    loaders: Loaders;
};

/**
 * Shared logic for listing dealer options with or without contact information.
 * This function contains the common query logic used by both listDealerOptions
 * and listDealerOptionsWithContact resolvers.
 */
const listDealerOptionsBase = async (
    filter: GraphQLQueryListDealerOptionsArgs['filter'],
    sort: GraphQLQueryListDealerOptionsArgs['sort'],
    { getPermissionController, getUser, loaders }: DealerOptionsContext
): Promise<Dealer[]> => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const sorting = getSort(sort);

    const pipelines: Document[] = [{ $match: getFilter(filter) }];

    if (!filter?.productionOnly) {
        const user = await getUser(true);

        if (!user) {
            throw new InvalidPermission();
        }

        if (!permissionController.hasRootPermission()) {
            const dealerIdsByUserGroup = await getDealerIdsByUserGroup(user, filter, loaders);

            if (!dealerIdsByUserGroup?.length) {
                return [];
            }

            pipelines.push({
                $match: {
                    _id: { $in: dealerIdsByUserGroup },
                },
            });
        }
    } else {
        // limit to active only
        pipelines.push({ $match: { isActive: true } });
    }

    if (filter?.checkConfiguratorStock) {
        pipelines.push(
            {
                $lookup: {
                    from: 'inventories',
                    localField: '_id',
                    foreignField: 'dealerId',
                    pipeline: [
                        {
                            $match: {
                                'stocks.reservationStatus': ReservationStockStatus.Available,
                                isActive: true,
                                isDeleted: false,
                            },
                        },
                    ],
                    as: 'inventories',
                },
            },
            {
                $match: {
                    inventories: {
                        $gt: {
                            $size: 0,
                        },
                    },
                },
            }
        );
    }

    pipelines.push(...sorting.pipelines, { $sort: sorting.query });

    return collections.dealers.aggregate<Dealer>(pipelines).toArray();
};

export default listDealerOptionsBase;

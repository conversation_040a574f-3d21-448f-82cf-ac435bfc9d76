import dayjs from 'dayjs';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { DealerPolicyAction } from '../../../../../permissions';
import { InvalidPermission } from '../../../../errors';
import { requiresLoggedUser } from '../../../../middlewares';
import { GraphQLQueryResolvers } from '../../../definitions';

const query: GraphQLQueryResolvers['getMonthOfImportOptions'] = async (
    root,
    { dealerId },
    { loaders, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const dealer = await loaders.dealerById.load(dealerId);

    if (!dealer) {
        throw new Error('Dealer not found');
    }

    const hasManagerPermission = await permissionController.dealers.mayOperateOn(
        dealer,
        DealerPolicyAction.ViewSalesControlBoardManager
    );

    const hasConsultantPermission = await permissionController.dealers.mayOperateOn(
        dealer,
        DealerPolicyAction.ViewSalesControlBoardSalesConsultant
    );

    if (!hasManagerPermission && !hasConsultantPermission) {
        throw new InvalidPermission();
    }

    const importDates = await collections.salesControlBoards.distinct('importDate', {
        dealerId,
    });

    const monthOfImportOptions = importDates
        .map(i => ({
            value: i,
            label: `${dayjs(i).format('MMMM YYYY')}`,
        }))
        .sort((a, b) => dayjs(b.value).valueOf() - dayjs(a.value).valueOf());

    return monthOfImportOptions;
};

export default requiresLoggedUser(query);

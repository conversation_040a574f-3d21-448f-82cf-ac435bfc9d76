import dayjs from 'dayjs';
import { groupBy } from 'lodash/fp';
import { AggregationCursor, ObjectId, WithId } from 'mongodb';
import { PeriodPayload } from '../../../../../../app/api';
import { Collections } from '../../../../../database';
import {
    ApplicationStatus,
    LocalModel,
    SalesControlBoard,
    SalesControlBoardDataType,
    SalesControlBoardLead,
    SalesControlBoardModule,
    SalesControlBoardOrderIntake,
    SalesControlBoardRetail,
    User,
    VehicleKind,
} from '../../../../../database/documents';
import { Loaders } from '../../../../../loaders';
import {
    GraphQLSalesControlBoard,
    GraphQLSalesControlBoardItemPerformance,
    GraphQLSalesControlBoardModelTotalPerformance,
    GraphQLSalesControlBoardTotalPerformance,
} from '../../../definitions';

export const FIXED_MODELS = ['911', '718', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Panamera', 'Taycan'];

const currentMonthToThisYear = importDate => dayjs(importDate, 'YYYY-MM').month() + 1;

const parseCount = (value: number) => parseFloat(value.toFixed(1));

const getPercentage = (applied: number, total: number) =>
    total > 0 ? parseFloat(((applied / total) * 100).toFixed(0)) : 0;

const getPercentageRound2 = (applied: number, total: number) =>
    total > 0 ? parseFloat(((applied / total) * 100).toFixed(2)) : 0;

type ModelAverageTargetMonthly = {
    leadsMonthlyModelAverageTarget: number;
    orderIntakesMonthlyModelAverageTarget: number;
    testDriveMonthlyModelAverageTarget: number;
    retailsMonthlyModelAverageTarget: number;
    salesOfferMonthlyModelAverageTarget: number;
};

export const retrieveSalesControlBoardSetting = (
    setting: SalesControlBoardSetting,
    isYTD: boolean,
    importDate: string
): ModelAverageTargetMonthly => {
    if (isYTD) {
        const month = currentMonthToThisYear(importDate);

        return {
            leadsMonthlyModelAverageTarget: parseCount(setting.orderIntakesMonthlyTarget * 4 * month),
            orderIntakesMonthlyModelAverageTarget: parseCount(setting.orderIntakesMonthlyTarget * month),
            testDriveMonthlyModelAverageTarget: parseCount(setting.testDriveMonthlyTarget * month),
            retailsMonthlyModelAverageTarget: parseCount(setting.retailsMonthlyTarget * month),
            salesOfferMonthlyModelAverageTarget: parseCount(setting.orderIntakesMonthlyTarget * 1.1 * month),
        };
    }

    return {
        leadsMonthlyModelAverageTarget: parseCount(setting.orderIntakesMonthlyTarget * 4),
        orderIntakesMonthlyModelAverageTarget: parseCount(setting.orderIntakesMonthlyTarget),
        testDriveMonthlyModelAverageTarget: parseCount(setting.testDriveMonthlyTarget),
        retailsMonthlyModelAverageTarget: parseCount(setting.retailsMonthlyTarget),
        salesOfferMonthlyModelAverageTarget: parseCount(setting.orderIntakesMonthlyTarget * 1.1),
    };
};

const baseQuerySalesControlBoard = (
    dealerId: ObjectId,
    sources: SalesControlBoardDataType[],
    importDate: string,
    modelIds: ObjectId[],
    salesConsultantIds: ObjectId[]
) => ({
    $match: {
        dealerId,
        source: { $in: sources },
        importDate,
        modelId: { $in: modelIds },
        salesConsultantId: { $in: salesConsultantIds },
    },
});

export type SalesControlBoardSetting = {
    testDriveMonthlyTarget: number;
    orderIntakesMonthlyTarget: number;
    retailsMonthlyTarget: number;
    financeCommissionMonthlyTarget: number;
    insuranceCommissionMonthlyTarget: number;
    allSalesConsultantIds: ObjectId[];
};

export const getSalesControlBoardSettingFromDealer = (
    dealerId: ObjectId,
    salesControlBoardModule: SalesControlBoardModule
): SalesControlBoardSetting => {
    const allSalesConsultantIds =
        salesControlBoardModule.salesConsultantsAssignments.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        ).value ?? [];

    const orderIntakesMonthlyTarget =
        salesControlBoardModule.orderIntakesMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const testDriveMonthlyTarget =
        salesControlBoardModule.testDriveMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const retailsMonthlyTarget =
        salesControlBoardModule.retailsMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const financeCommissionMonthlyTarget =
        salesControlBoardModule.financeCommissionMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    const insuranceCommissionMonthlyTarget =
        salesControlBoardModule.insuranceCommissionMonthlyTarget.overrides.find(({ dealerId: overrideDealerId }) =>
            overrideDealerId?.equals(dealerId)
        )?.value ?? 0;

    return {
        allSalesConsultantIds,
        retailsMonthlyTarget,
        financeCommissionMonthlyTarget,
        insuranceCommissionMonthlyTarget,
        orderIntakesMonthlyTarget,
        testDriveMonthlyTarget,
    };
};

type ModelAverageTarget = {
    orderIntakesMonthlyModelAverageTarget: number;
    testDriveMonthlyModelAverageTarget: number;
    retailsMonthlyModelAverageTarget: number;
};

export const getModelAverageTarget = (
    allModelLength: number,
    salesControlBoardSetting: SalesControlBoardSetting
): ModelAverageTarget => {
    const { orderIntakesMonthlyTarget, testDriveMonthlyTarget, retailsMonthlyTarget } = salesControlBoardSetting;

    const orderIntakesMonthlyModelAverageTarget = allModelLength === 0 ? 0 : orderIntakesMonthlyTarget / allModelLength;
    const testDriveMonthlyModelAverageTarget = allModelLength === 0 ? 0 : testDriveMonthlyTarget / allModelLength;
    const retailsMonthlyModelAverageTarget = allModelLength === 0 ? 0 : retailsMonthlyTarget / allModelLength;

    return {
        orderIntakesMonthlyModelAverageTarget,
        testDriveMonthlyModelAverageTarget,
        retailsMonthlyModelAverageTarget,
    };
};

export const getFICommissions = async ({
    dealerId,
    salesControlBoardSetting,
    hasManagerPermission,
    monthOfImport,
    selectedSalesConsultants,
    selectedVehicleModelIds,
    collections,
}: {
    dealerId: ObjectId;
    salesControlBoardSetting: SalesControlBoardSetting;
    hasManagerPermission: boolean;
    monthOfImport: string;
    selectedSalesConsultants: User[];
    selectedVehicleModelIds: ObjectId[];
    collections: Collections;
}) => {
    if (!hasManagerPermission) {
        return [];
    }

    const { financeCommissionMonthlyTarget, insuranceCommissionMonthlyTarget } = salesControlBoardSetting;

    const sumCommissions = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    source: SalesControlBoardDataType.Retails,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultants.map(i => i._id) },
                },
            },
            {
                $group: {
                    _id: '$salesConsultantId',
                    ytdActual: {
                        $sum: '$ytd.actual',
                    },
                    ytdFinanceApplied: {
                        $sum: '$ytd.appliedFinance',
                    },
                    ytdInsuranceApplied: {
                        $sum: '$ytd.appliedInsurance',
                    },
                    monthActual: {
                        $sum: '$month.actual',
                    },
                    monthFinanceApplied: {
                        $sum: '$month.appliedFinance',
                    },
                    monthInsuranceApplied: {
                        $sum: '$month.appliedInsurance',
                    },
                    threeMonthsActual: {
                        $sum: '$threeMonths.actual',
                    },
                    threeMonthsFinanceApplied: {
                        $sum: '$threeMonths.appliedFinance',
                    },
                    threeMonthsInsuranceApplied: {
                        $sum: '$threeMonths.appliedInsurance',
                    },
                },
            },
        ]) as AggregationCursor<{
            _id: ObjectId;
            ytdActual: number;
            monthActual: number;
            threeMonthsActual: number;
            monthFinanceApplied: number;
            ytdFinanceApplied: number;
            threeMonthsFinanceApplied: number;
            monthInsuranceApplied: number;
            ytdInsuranceApplied: number;
            threeMonthsInsuranceApplied: number;
        }>
    ).toArray();

    const fiCommissions = selectedSalesConsultants
        .map((salesConsultant, index) => {
            const find = sumCommissions.find(i => i._id.equals(salesConsultant._id));

            return {
                salesConsultantName: salesConsultant.displayName,
                inHouseFinanceTarget: financeCommissionMonthlyTarget,
                inHouseFinanceMtd: find ? getPercentage(find.monthFinanceApplied, find.monthActual) : 0,
                inHouseFinanceYtd: find ? getPercentage(find.ytdFinanceApplied, find.ytdActual) : 0,
                inHouseFinance3MAvg: find ? getPercentage(find.threeMonthsFinanceApplied, find.threeMonthsActual) : 0,
                inHouseInsuranceTarget: insuranceCommissionMonthlyTarget,
                inHouseInsuranceMtd: find ? getPercentage(find.monthInsuranceApplied, find.monthActual) : 0,
                inHouseInsuranceYtd: find ? getPercentage(find.ytdInsuranceApplied, find.ytdActual) : 0,
                inHouseInsurance3MAvg: find
                    ? getPercentage(find.threeMonthsInsuranceApplied, find.threeMonthsActual)
                    : 0,
            };
        })
        .filter(Boolean);

    return fiCommissions;
};

const getWeeksInCurrentMonth = (monthOfImport: string, timeZone: string) => {
    if (!monthOfImport) {
        return [];
    }
    const firstDay = dayjs.tz(monthOfImport, timeZone).startOf('month');
    const lastDay = dayjs.tz(monthOfImport, timeZone).endOf('month');

    let current = firstDay.startOf('week').add(1, 'day');

    if (current.isBefore(firstDay, 'day')) {
        current = current.add(7, 'day');
    }

    const weeks = [];

    if (current.isAfter(firstDay)) {
        weeks.push({
            start: firstDay.toDate(),
            end: current.subtract(1, 'day').toDate(),
        });
    }

    while (current.isBefore(lastDay) || current.isSame(lastDay, 'day')) {
        const weekEnd = current.add(6, 'day');

        const startOfWeek = current.isAfter(firstDay) ? current : firstDay;
        const endOfWeek = weekEnd.isBefore(lastDay) ? weekEnd : lastDay;

        weeks.push({
            start: startOfWeek.toDate(),
            end: endOfWeek.toDate(),
        });

        current = current.add(7, 'day');
    }

    return weeks;
};

export const getWeekFunnels = async ({
    dealerId,
    monthOfImport,
    timeZone,
    collections,
    selectedSalesConsultantIds,
    selectedVehicleModelIds,
    selectedVehicleModelSuiteIds,
}: {
    dealerId: ObjectId;
    monthOfImport: string;
    timeZone: string;
    collections: Collections;
    selectedSalesConsultantIds: ObjectId[];
    selectedVehicleModelIds: ObjectId[];
    selectedVehicleModelSuiteIds: ObjectId[];
}) => {
    if (selectedSalesConsultantIds.length === 0) {
        return [];
    }

    const weeks = getWeeksInCurrentMonth(monthOfImport, timeZone);

    const salesControlBoards = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultantIds },
                },
            },
            { $unwind: '$weeks' },
            {
                $group: {
                    _id: {
                        source: '$source',
                        week: '$weeks.numberOfWeek',
                    },
                    totalCount: { $sum: '$weeks.count' },
                },
            },
            {
                $project: {
                    _id: 0,
                    source: '$_id.source',
                    week: '$_id.week',
                    totalCount: 1,
                },
            },
            { $sort: { source: 1, week: 1 } },
        ]) as AggregationCursor<{
            source: SalesControlBoardDataType;
            week: number;
            totalCount: number;
        }>
    ).toArray();

    const weekFunnels = await Promise.all(
        weeks.map(async (week, index) => {
            const leadsCreated =
                salesControlBoards.find(i => i.source === SalesControlBoardDataType.Leads && i.week === index + 1)
                    ?.totalCount ?? 0;

            const orderIntakes =
                salesControlBoards.find(
                    i => i.source === SalesControlBoardDataType.OrderIntakes && i.week === index + 1
                )?.totalCount ?? 0;

            const retails =
                salesControlBoards.find(i => i.source === SalesControlBoardDataType.Retails && i.week === index + 1)
                    ?.totalCount ?? 0;

            const testDrivePipeline = getTestDrivePipeline(
                dealerId,
                week,
                selectedSalesConsultantIds,
                selectedVehicleModelSuiteIds
            );
            const testDrives = await collections.applications.aggregate(testDrivePipeline).toArray();

            const salesOfferPipeline = getSalesOfferPipeline(
                dealerId,
                week,
                selectedSalesConsultantIds,
                selectedVehicleModelSuiteIds
            );
            const salesOffers = await collections.salesOffers.aggregate(salesOfferPipeline).toArray();

            return {
                ...week,
                leadsCreated,
                testDrives: testDrives.length,
                salesOffers: salesOffers.length,
                orderIntakes,
                retails,
                leadToTestDriveRate: getPercentageRound2(testDrives.length, leadsCreated),
                testDriveToSalesOfferRate: getPercentageRound2(salesOffers.length, testDrives.length),
                salesOfferToOrderIntakeRate: getPercentageRound2(orderIntakes, salesOffers.length),
                orderIntakeToRetailRate: getPercentageRound2(retails, orderIntakes),
            };
        })
    );

    return weekFunnels;
};

export const getProgressGoal = async ({
    dealerId,
    salesControlBoardSetting,
    modelAverageTarget,
    hasManagerPermission,
    hasConsultantPermission,
    monthOfImport,
    collections,
    user,
    selectedSalesConsultantIds,
    selectedVehicleModelIds,
    isYtd,
}: {
    dealerId: ObjectId;
    salesControlBoardSetting: SalesControlBoardSetting;
    modelAverageTarget: ModelAverageTarget;
    hasManagerPermission: boolean;
    hasConsultantPermission: boolean;
    monthOfImport: string;
    collections: Collections;
    user: User;
    selectedSalesConsultantIds: ObjectId[];
    selectedVehicleModelIds: ObjectId[];
    isYtd: boolean;
}) => {
    if (hasManagerPermission || !hasConsultantPermission || selectedSalesConsultantIds.length === 0) {
        return undefined;
    }

    const { financeCommissionMonthlyTarget, insuranceCommissionMonthlyTarget } = salesControlBoardSetting;

    const { retailsMonthlyModelAverageTarget } = modelAverageTarget;

    const retailsMonthlyTarget = parseCount(retailsMonthlyModelAverageTarget * selectedVehicleModelIds.length);

    const sumCommissions = await (
        collections.salesControlBoards.aggregate([
            {
                $match: {
                    dealerId,
                    source: SalesControlBoardDataType.Retails,
                    importDate: monthOfImport,
                    modelId: { $in: selectedVehicleModelIds },
                    salesConsultantId: { $in: selectedSalesConsultantIds },
                },
            },
            {
                $group: {
                    _id: '$salesConsultantId',
                    ytdActual: {
                        $sum: '$ytd.actual',
                    },
                    ytdFinanceApplied: {
                        $sum: '$ytd.appliedFinance',
                    },
                    ytdInsuranceApplied: {
                        $sum: '$ytd.appliedInsurance',
                    },
                    monthActual: {
                        $sum: '$month.actual',
                    },
                    monthFinanceApplied: {
                        $sum: '$month.appliedFinance',
                    },
                    monthInsuranceApplied: {
                        $sum: '$month.appliedInsurance',
                    },
                },
            },
        ]) as AggregationCursor<{
            _id: ObjectId;
            monthActual: number;
            ytdActual: number;
            monthFinanceApplied: number;
            ytdFinanceApplied: number;
            monthInsuranceApplied: number;
            ytdInsuranceApplied: number;
        }>
    ).toArray();

    const currentCommission = sumCommissions.find(i => i._id.equals(user._id));

    const months = currentMonthToThisYear(monthOfImport);

    if (!currentCommission) {
        return {
            retailTargetMonth: retailsMonthlyTarget ?? 0,
            retailActualMonth: 0,
            retailMonthRate: 0,
            retailMonthDev: 0 - (retailsMonthlyTarget ?? 0),
            retailTargetYtd: (retailsMonthlyTarget ?? 0) * months,
            retailActualYtd: 0,
            retailYtdRate: 0,
            retailYtdDev: 0 - (retailsMonthlyTarget ?? 0) * months,
            financeTarget: financeCommissionMonthlyTarget,
            financeActualRate: 0,
            insuranceTarget: insuranceCommissionMonthlyTarget,
            insuranceActualRate: 0,
        };
    }

    const financeTargetCountMonth = currentCommission.monthActual * (financeCommissionMonthlyTarget / 100);
    const financeTargetCountYtd = currentCommission.ytdActual * (financeCommissionMonthlyTarget / 100);
    const financeActualRate = (() => {
        if (isYtd) {
            return getPercentage(currentCommission.ytdFinanceApplied, financeTargetCountYtd);
        }

        return getPercentage(currentCommission.monthFinanceApplied, financeTargetCountMonth);
    })();

    const insuranceTargetCountMonth = currentCommission.monthActual * (insuranceCommissionMonthlyTarget / 100);
    const insuranceTargetCountYtd = currentCommission.ytdActual * (insuranceCommissionMonthlyTarget / 100);
    const insuranceActualRate = (() => {
        if (isYtd) {
            return getPercentage(currentCommission.ytdInsuranceApplied, insuranceTargetCountYtd);
        }

        return getPercentage(currentCommission.monthInsuranceApplied, insuranceTargetCountMonth);
    })();

    return {
        retailTargetMonth: retailsMonthlyTarget ?? 0,
        retailActualMonth: currentCommission.monthActual ?? 0,
        retailMonthRate: getPercentage(currentCommission.monthActual ?? 0, retailsMonthlyTarget ?? 0),
        retailMonthDev: (currentCommission.monthActual ?? 0) - (retailsMonthlyTarget ?? 0),
        retailTargetYtd: (retailsMonthlyTarget ?? 0) * months,
        retailActualYtd: currentCommission.ytdActual ?? 0,
        retailYtdRate: getPercentage(currentCommission.ytdActual ?? 0, (retailsMonthlyTarget ?? 0) * months),
        retailYtdDev: (currentCommission.ytdActual ?? 0) - (retailsMonthlyTarget ?? 0) * months,
        financeTarget: financeCommissionMonthlyTarget,
        financeActualRate,
        insuranceTarget: insuranceCommissionMonthlyTarget,
        insuranceActualRate,
    };
};

const getTestDrivePipeline = (
    dealerId: ObjectId,
    convertedPeriod: PeriodPayload,
    selectedSalesConsultantIds: ObjectId[],
    selectedVehicleModelSuiteIds: ObjectId[]
) => [
    {
        $match: {
            dealerId,
            'configuration.testDrive': true,
            'appointmentStage.status': { $in: [ApplicationStatus.TestDriveCompleted, ApplicationStatus.Completed] },
            'appointmentStage.assigneeId': { $in: selectedSalesConsultantIds },
            '_versioning.isLatest': true,
            $and: [
                { '_versioning.updatedAt': { $gte: convertedPeriod.start } },
                { '_versioning.updatedAt': { $lte: convertedPeriod.end } },
            ],
        },
    },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'vehicleId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalVariant,
                    },
                },
            ],
            as: 'variant',
        },
    },
    { $unwind: { path: '$variant', preserveNullAndEmptyArrays: false } },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'variant.modelId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalModel,
                        parentModelId: { $exists: false },
                    },
                },
            ],
            as: 'model',
        },
    },
    { $unwind: { path: '$model', preserveNullAndEmptyArrays: false } },
    { $match: { 'model._versioning.suiteId': { $in: selectedVehicleModelSuiteIds } } },
];

const getSalesOfferPipeline = (
    dealerId: ObjectId,
    convertedPeriod: PeriodPayload,
    selectedSalesConsultantIds: ObjectId[],
    selectedVehicleModelSuiteIds: ObjectId[]
) => [
    {
        $match: {
            $and: [
                { '_versioning.createdAt': { $gte: convertedPeriod.start } },
                { '_versioning.createdAt': { $lte: convertedPeriod.end } },
            ],
        },
    },
    {
        $lookup: {
            from: 'leads',
            localField: 'leadSuiteId',
            foreignField: '_versioning.suiteId',
            pipeline: [
                {
                    $match: {
                        '_versioning.isLatest': true,
                    },
                },
            ],
            as: 'lead',
        },
    },
    { $unwind: { path: '$lead', preserveNullAndEmptyArrays: false } },
    { $match: { 'lead.dealerId': dealerId, 'lead.assigneeId': { $in: selectedSalesConsultantIds } } },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'vehicle.vehicleId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalVariant,
                    },
                },
            ],
            as: 'variant',
        },
    },
    { $unwind: { path: '$variant', preserveNullAndEmptyArrays: false } },
    {
        $lookup: {
            from: 'vehicles',
            localField: 'variant.modelId',
            foreignField: '_id',
            pipeline: [
                {
                    $match: {
                        _kind: VehicleKind.LocalModel,
                        parentModelId: { $exists: false },
                    },
                },
            ],
            as: 'model',
        },
    },
    { $unwind: { path: '$model', preserveNullAndEmptyArrays: false } },
    { $match: { 'model._versioning.suiteId': { $in: selectedVehicleModelSuiteIds } } },
];

export const getLostDataType = async ({
    dealerId,
    monthOfImport,
    collections,
}: {
    dealerId: ObjectId;
    monthOfImport: string;
    collections: Collections;
}) => {
    const salesControlBoards = await (
        collections.salesControlBoards.aggregate([
            { $match: { dealerId, importDate: monthOfImport } },
            { $group: { _id: '$source' } },
        ]) as AggregationCursor<{
            _id: SalesControlBoardDataType;
        }>
    ).toArray();

    const lostDataType = [
        SalesControlBoardDataType.Leads,
        SalesControlBoardDataType.OrderIntakes,
        SalesControlBoardDataType.Retails,
    ].filter(type => !salesControlBoards.find(i => i._id === type));

    return lostDataType;
};

const retrieveSalesModelItem = (modelItems: WithId<SalesControlBoard>[], source: SalesControlBoardDataType) => {
    switch (source) {
        case SalesControlBoardDataType.Retails:
            return modelItems.find(item => item.source === SalesControlBoardDataType.Retails);

        case SalesControlBoardDataType.Leads:
            return modelItems.find(item => item.source === SalesControlBoardDataType.Leads);

        case SalesControlBoardDataType.OrderIntakes:
            return modelItems.find(item => item.source === SalesControlBoardDataType.OrderIntakes);

        default:
            throw new Error(`Unsupported source type: ${source}`);
    }
};

const getValue = (
    item: SalesControlBoardRetail | SalesControlBoardOrderIntake,
    isYTD: boolean,
    dataType: SalesControlBoardDataType,
    averageModelSetting: ModelAverageTarget,
    importDate: string
) => {
    const dataValue = () => {
        switch (dataType) {
            case SalesControlBoardDataType.Retails:
                return averageModelSetting.retailsMonthlyModelAverageTarget;

            case SalesControlBoardDataType.OrderIntakes:
                return averageModelSetting.orderIntakesMonthlyModelAverageTarget;

            default:
                throw new Error(`Unsupported data type: ${dataType}`);
        }
    };

    if (isYTD) {
        return {
            target: parseCount(dataValue() * currentMonthToThisYear(importDate)),
            actual: parseCount(item?.ytd?.actual ?? 0),
            dev: parseCount((item?.ytd?.actual ?? 0) - dataValue() * currentMonthToThisYear(importDate)),
        };
    }

    return {
        target: parseCount(dataValue()),
        actual: parseCount(item?.month?.actual ?? 0),
        dev: parseCount((item?.month?.actual ?? 0) - dataValue()),
    };
};

const getSalesOfferTestDriveValue = (
    value: number,
    averageModelSetting: ModelAverageTarget,
    isYTD: boolean,
    type: 'salesOffer' | 'testDrive',
    importDate: string
) => {
    const dataValue = () => {
        switch (type) {
            case 'salesOffer': {
                /**
                 * according https://appvantage.atlassian.net/browse/PSE-213
                 * Target = Order Intake Target x 1.1
                 */
                return { target: averageModelSetting.orderIntakesMonthlyModelAverageTarget * 1.1, actual: value };
            }

            case 'testDrive': {
                return { target: averageModelSetting.testDriveMonthlyModelAverageTarget, actual: value };
            }
            default:
                throw new Error(`Unsupported data type: ${type}`);
        }
    };

    const { actual, target } = dataValue();

    if (isYTD) {
        return {
            target: parseCount(target * currentMonthToThisYear(importDate)),
            actual: parseCount(actual),
            dev: parseCount(actual - target * currentMonthToThisYear(importDate)),
        };
    }

    return {
        target: parseCount(target),
        actual: parseCount(actual),
        dev: parseCount(actual - target),
    };
};

const getLeadValue = (
    item: SalesControlBoardLead,
    isYTD: boolean,
    averageModelSetting: ModelAverageTarget,
    importDate: string
) => {
    const calculateValue = (numerator, denominator) => {
        if (!numerator || !denominator || numerator === 0) {
            return 0;
        }

        if (numerator > 1 && denominator === 0) {
            return 1;
        }

        return numerator / (denominator || 1);
    };

    if (isYTD) {
        return {
            target: parseCount(
                averageModelSetting.orderIntakesMonthlyModelAverageTarget * 4 * currentMonthToThisYear(importDate)
            ),
            actual: parseCount(item?.ytd?.actual ?? 0),
            dev: parseCount(
                (item?.ytd?.actual ?? 0) -
                    averageModelSetting.orderIntakesMonthlyModelAverageTarget * 4 * currentMonthToThisYear(importDate)
            ),
            lio: parseCount(item?.ytd?.lio ?? 0),
            pi: parseCount(item?.ytd?.pi ?? 0),
            ulr: parseCount(calculateValue(item?.ytd?.ulr?.numerator, item?.ytd?.ulr?.denominator)),
            olr: parseCount(calculateValue(item?.ytd?.olr?.numerator, item?.ytd?.olr?.denominator)),
        };
    }

    return {
        target: parseCount(averageModelSetting.orderIntakesMonthlyModelAverageTarget * 4),
        actual: parseCount(item?.month?.actual ?? 0),
        dev: parseCount((item?.month?.actual ?? 0) - averageModelSetting.orderIntakesMonthlyModelAverageTarget * 4),
        lio: parseCount(item?.month?.lio ?? 0),
        pi: parseCount(item?.month?.pi ?? 0),
        ulr: parseCount(calculateValue(item?.month?.ulr?.numerator, item?.month?.ulr?.denominator)),
        olr: parseCount(calculateValue(item?.month?.olr?.numerator, item?.month?.olr?.denominator)),
    };
};

// Calculate total sums for each sales consultant
// Helper function to sum up performance items
const sumPerformanceItems = <T extends { [key: string]: any }>(
    grouped: Record<string, T[]>,
    nameKey: string,
    setting?: ModelAverageTargetMonthly
) => {
    const preRounded = Object.entries(grouped).map(([name, items]) => {
        const result = items.reduce(
            (acc, curr) => ({
                ...acc,
                totalLeadTarget: setting
                    ? setting.leadsMonthlyModelAverageTarget
                    : acc.totalLeadTarget + (curr.lead?.target ?? 0),
                totalLeadActual: acc.totalLeadActual + (curr.lead?.actual ?? 0),
                totalLeadDev: acc.totalLeadDev + (curr.lead?.dev ?? 0),
                totalOrderIntakeTarget: setting
                    ? setting.orderIntakesMonthlyModelAverageTarget
                    : acc.totalOrderIntakeTarget + (curr.orderIntake?.target ?? 0),
                totalOrderIntakeActual: acc.totalOrderIntakeActual + (curr.orderIntake?.actual ?? 0),
                totalOrderIntakeDev: acc.totalOrderIntakeDev + (curr.orderIntake?.dev ?? 0),
                totalRetailTarget: setting
                    ? setting.retailsMonthlyModelAverageTarget
                    : acc.totalRetailTarget + (curr.retail?.target ?? 0),
                totalRetailActual: acc.totalRetailActual + (curr.retail?.actual ?? 0),
                totalRetailDev: acc.totalRetailDev + (curr.retail?.dev ?? 0),
                totalSalesOfferTarget: setting
                    ? setting.salesOfferMonthlyModelAverageTarget
                    : acc.totalSalesOfferTarget + (curr.salesOffer?.target ?? 0),
                totalSalesOfferActual: acc.totalSalesOfferActual + (curr.salesOffer?.actual ?? 0),
                totalSalesOfferDev: acc.totalSalesOfferDev + (curr.salesOffer?.dev ?? 0),
                totalTestDriveTarget: setting
                    ? setting.testDriveMonthlyModelAverageTarget
                    : acc.totalTestDriveTarget + (curr.testDrive?.target ?? 0),
                totalTestDriveActual: acc.totalTestDriveActual + (curr.testDrive?.actual ?? 0),
                totalTestDriveDev: acc.totalTestDriveDev + (curr.testDrive?.dev ?? 0),
                totalLio: acc.totalLio + (curr.lead?.lio ?? 0),
                totalPi: acc.totalPi + (curr.lead?.pi ?? 0),
                totalUlr: acc.totalUlr + (curr.lead?.ulr ?? 0),
                totalOlr: acc.totalOlr + (curr.lead?.olr ?? 0),
            }),
            {
                totalLeadTarget: 0,
                totalLeadActual: 0,
                totalLeadDev: 0,
                totalOrderIntakeTarget: 0,
                totalOrderIntakeActual: 0,
                totalOrderIntakeDev: 0,
                totalRetailTarget: 0,
                totalRetailActual: 0,
                totalRetailDev: 0,
                totalSalesOfferTarget: 0,
                totalSalesOfferActual: 0,
                totalSalesOfferDev: 0,
                totalTestDriveTarget: 0,
                totalTestDriveActual: 0,
                totalTestDriveDev: 0,
                totalLio: 0,
                totalPi: 0,
                totalUlr: 0,
                totalOlr: 0,
            }
        );

        // Set the correct property for the name
        return {
            ...result,
            [nameKey]: name,
        };
    });

    // do math.round of each number property of the object in the array
    const rounded = preRounded.map(item => {
        const roundedItem: Record<string, number | string> = {};
        Object.entries(item).forEach(([key, value]) => {
            if (typeof value === 'number') {
                roundedItem[key] = Math.round(value);
            } else {
                roundedItem[key] = value; // keep string values as is
            }
        });

        return roundedItem;
    });

    return rounded;
};

const retrieveFeatureCountBySalesConsultantAndModel = (
    type: 'salesOffer' | 'testDrive',
    dealerId: ObjectId,
    convertedPeriod: PeriodPayload,
    selectedSalesConsultantIds: ObjectId[],
    selectedVehicleModelSuiteIds: ObjectId[]
) => {
    switch (type) {
        case 'salesOffer':
            return [
                ...getSalesOfferPipeline(
                    dealerId,
                    convertedPeriod,
                    selectedSalesConsultantIds,
                    selectedVehicleModelSuiteIds
                ),
                {
                    $group: {
                        _id: {
                            salesConsultantId: '$lead.assigneeId',
                            vehicleModelSuiteId: '$model._versioning.suiteId',
                        },
                        count: { $sum: 1 },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        salesConsultantId: '$_id.salesConsultantId',
                        vehicleModelSuiteId: '$_id.vehicleModelSuiteId',
                        count: 1,
                    },
                },
            ];

        case 'testDrive': {
            return [
                ...getTestDrivePipeline(
                    dealerId,
                    convertedPeriod,
                    selectedSalesConsultantIds,
                    selectedVehicleModelSuiteIds
                ),
                {
                    $group: {
                        _id: {
                            salesConsultantId: '$appointmentStage.assigneeId',
                            vehicleModelSuiteId: '$model._versioning.suiteId',
                        },
                        count: { $sum: 1 },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        salesConsultantId: '$_id.salesConsultantId',
                        vehicleModelSuiteId: '$_id.vehicleModelSuiteId',
                        count: 1,
                    },
                },
            ];
        }

        default:
            throw new Error(`Unsupported type: ${type}`);
    }
};

const getFeatureCountValueBySalesConsultantAndModel = (
    type: 'salesOffer' | 'testDrive',
    documents: AggregatedFeatureCount[],
    salesConsultant: ObjectId,
    modelId: ObjectId
) => {
    const found = documents.find(
        doc => doc.salesConsultantId.equals(salesConsultant) && doc.vehicleModelSuiteId.equals(modelId)
    );

    if (!found) {
        return 0;
    }

    return found.count;
};

type AggregatedFeatureCount = {
    salesConsultantId: ObjectId;
    vehicleModelSuiteId: ObjectId;
    count: number;
};

const retrieveEmptyVehicleModelsRecords = (
    averageModelSetting: ModelAverageTarget,
    value: number,
    importDate: string,
    isYTD: boolean,
    modelName: string,
    salesConsultantName: string,
    salesOfferCount: number,
    testDriveCount: number,
    modelId: ObjectId
) => {
    const dataValue = (isYTD: boolean, setting: number) => {
        if (isYTD) {
            return {
                target: parseCount(setting * currentMonthToThisYear(importDate)),
                actual: parseCount(value),
                dev: parseCount((value - setting) * currentMonthToThisYear(importDate)),
            };
        }

        return {
            target: parseCount(setting),
            actual: parseCount(value),
            dev: parseCount(value - setting),
        };
    };

    // Concatenate sales offer and test drive values
    const salesOffer = getSalesOfferTestDriveValue(
        salesOfferCount,
        averageModelSetting,
        isYTD,
        'salesOffer',
        importDate
    );
    const testDrive = getSalesOfferTestDriveValue(testDriveCount, averageModelSetting, isYTD, 'testDrive', importDate);

    return {
        lead: {
            ...dataValue(isYTD, averageModelSetting.orderIntakesMonthlyModelAverageTarget * 4),
            lio: 0,
            pi: 0,
            ulr: 0,
            olr: 0,
        },
        retail: dataValue(isYTD, averageModelSetting.retailsMonthlyModelAverageTarget),
        orderIntake: dataValue(isYTD, averageModelSetting.orderIntakesMonthlyModelAverageTarget),
        modelId,
        modelName,
        salesConsultantName,
        salesOffer,
        testDrive,
    };
};

type SalesControlBoardAggregated = GraphQLSalesControlBoardItemPerformance & { modelId: ObjectId };
export const getPerformance = async ({
    dealerId,
    salesControlBoardSetting,
    selectedSalesConsultantIds,
    selectedVehicleModelIds,
    selectedVehicleModelSuiteIds,
    modelSuiteIdById,
    collections,
    monthOfImport,
    hasManagerPermission,
    isYTD,
    convertedPeriod,
    loaders,
    vehicleModels,
    modelAverageTargetSetting,
}: {
    dealerId: ObjectId;
    salesControlBoardSetting: SalesControlBoardSetting;
    selectedSalesConsultantIds: ObjectId[];
    selectedVehicleModelIds: ObjectId[];
    selectedVehicleModelSuiteIds: ObjectId[];
    modelSuiteIdById: Record<string, ObjectId>;
    collections: Collections;
    monthOfImport: string;
    hasManagerPermission: boolean;
    hasConsultantPermission: boolean;
    isYTD: boolean;
    convertedPeriod: PeriodPayload;
    loaders: Loaders;
    vehicleModels: LocalModel[];
    modelAverageTargetSetting: ModelAverageTargetMonthly;
}): Promise<GraphQLSalesControlBoard['performance']> => {
    const allVehicleModels = vehicleModels.map(model => ({ id: model._id, suiteId: model._versioning.suiteId }));
    const eachSalesControlBoard = await collections.salesControlBoards
        .find({
            dealerId,
            source: {
                $in: [
                    SalesControlBoardDataType.Retails,
                    SalesControlBoardDataType.Leads,
                    SalesControlBoardDataType.OrderIntakes,
                ],
            },
            importDate: monthOfImport,
            modelId: { $in: allVehicleModels.map(model => model.id) },
            salesConsultantId: { $in: selectedSalesConsultantIds },
        })
        .toArray();

    const salesOffers = (await collections.salesOffers
        .aggregate(
            retrieveFeatureCountBySalesConsultantAndModel(
                'salesOffer',
                dealerId,
                convertedPeriod,
                selectedSalesConsultantIds,
                allVehicleModels.flatMap(model => model.suiteId)
            )
        )
        .toArray()) as AggregatedFeatureCount[];

    const testDrives = (await collections.applications
        .aggregate(
            retrieveFeatureCountBySalesConsultantAndModel(
                'testDrive',
                dealerId,
                convertedPeriod,
                selectedSalesConsultantIds,
                allVehicleModels.flatMap(model => model.suiteId)
            )
        )
        .toArray()) as AggregatedFeatureCount[];

    const averageModelValue = getModelAverageTarget(vehicleModels.length, salesControlBoardSetting);

    const grouped = groupBy('salesConsultantId', eachSalesControlBoard);

    const salesControlBoards: Array<SalesControlBoardAggregated> = [];

    for await (const salesConsultantId of selectedSalesConsultantIds) {
        const items = grouped[salesConsultantId.toHexString()];
        const groupedModel = groupBy('modelId', items);
        // Find IDs that are in selectedVehicleModelIds but not in groupedModel keys
        const nonRecordsVehicleIds = selectedVehicleModelIds.filter(
            id => !Object.keys(groupedModel).some(modelId => id.equals(modelId))
        );
        const result: Array<SalesControlBoardAggregated> = await Promise.all([
            ...Object.entries(groupedModel)
                .map(async ([modelId, modelItems]) => {
                    const [model, salesConsultant] = await Promise.all([
                        loaders.vehicleById.load(new ObjectId(modelId)),
                        loaders.userById.load(salesConsultantId),
                    ]);

                    if (!allVehicleModels.find(model => model.id.equals(modelId))) {
                        return null;
                    }

                    const modelSuiteId = modelSuiteIdById[modelId];

                    // get sales offer count and test drive count for the current sales consultant and model
                    const salesOfferCount = getFeatureCountValueBySalesConsultantAndModel(
                        'salesOffer',
                        salesOffers,
                        salesConsultantId,
                        modelSuiteId
                    );

                    const testDriveCount = getFeatureCountValueBySalesConsultantAndModel(
                        'testDrive',
                        testDrives,
                        salesConsultantId,
                        modelSuiteId
                    );

                    // Concatenate sales offer and test drive values
                    const salesOffer = getSalesOfferTestDriveValue(
                        salesOfferCount,
                        averageModelValue,
                        isYTD,
                        'salesOffer',
                        monthOfImport
                    );

                    const testDrive = getSalesOfferTestDriveValue(
                        testDriveCount,
                        averageModelValue,
                        isYTD,
                        'testDrive',
                        monthOfImport
                    );

                    return {
                        lead: {
                            ...getLeadValue(
                                retrieveSalesModelItem(
                                    modelItems,
                                    SalesControlBoardDataType.Leads
                                ) as SalesControlBoardLead,
                                isYTD,
                                averageModelValue,
                                monthOfImport
                            ),
                        },
                        modelName: model.name.defaultValue,
                        salesConsultantName: salesConsultant.displayName,
                        modelId: model._id,
                        retail: getValue(
                            retrieveSalesModelItem(
                                modelItems,
                                SalesControlBoardDataType.Retails
                            ) as SalesControlBoardRetail,
                            isYTD,
                            SalesControlBoardDataType.Retails,
                            averageModelValue,
                            monthOfImport
                        ),
                        orderIntake: getValue(
                            retrieveSalesModelItem(
                                modelItems,
                                SalesControlBoardDataType.OrderIntakes
                            ) as SalesControlBoardOrderIntake,
                            isYTD,
                            SalesControlBoardDataType.OrderIntakes,
                            averageModelValue,
                            monthOfImport
                        ),
                        salesOffer,
                        testDrive,
                    };
                })
                .filter(Boolean),
            ...nonRecordsVehicleIds.map(async modelId => {
                const [model, salesConsultant] = await Promise.all([
                    loaders.vehicleById.load(modelId),
                    loaders.userById.load(salesConsultantId),
                ]);

                const modelSuiteId = modelSuiteIdById[modelId.toHexString()];

                // get sales offer count and test drive count for the current sales consultant and model
                const salesOfferCount = getFeatureCountValueBySalesConsultantAndModel(
                    'salesOffer',
                    salesOffers,
                    salesConsultantId,
                    modelSuiteId
                );

                const testDriveCount = getFeatureCountValueBySalesConsultantAndModel(
                    'testDrive',
                    testDrives,
                    salesConsultantId,
                    modelSuiteId
                );

                return retrieveEmptyVehicleModelsRecords(
                    averageModelValue,
                    0,
                    monthOfImport,
                    isYTD,
                    model.name.defaultValue,
                    salesConsultant.displayName,
                    salesOfferCount,
                    testDriveCount,
                    model._id
                );
            }),
        ]);

        salesControlBoards.push(...result);
    }

    // Group totalResult by salesConsultantName for total, and by modelName for model
    const groupedTotal = groupBy('salesConsultantName', salesControlBoards);
    const groupedModel = groupBy('modelName', salesControlBoards);

    // Ensure the correct key is used for each grouping
    const summedTotal = sumPerformanceItems(groupedTotal, 'salesConsultantName', modelAverageTargetSetting);
    const summedModel = sumPerformanceItems(groupedModel, 'modelName');

    // sort the summedModel based on FIXED_MODELS order
    const sortedModel = summedModel.sort((a, b) => {
        const modelA = vehicleModels.find(model => model.name.defaultValue === a.modelName);
        const modelB = vehicleModels.find(model => model.name.defaultValue === b.modelName);

        if (modelA && modelB) {
            // Order by FIXED_MODELS first, then by model.sortOrder if both are in FIXED_MODELS, else by name
            const indexA = FIXED_MODELS.indexOf(a.modelName as string);
            const indexB = FIXED_MODELS.indexOf(b.modelName as string);

            if (indexA !== -1 && indexB !== -1) {
                return indexA - indexB;
            }

            if (indexA !== -1) {
                return -1;
            }

            if (indexB !== -1) {
                return 1;
            }

            return (a.modelName as string).localeCompare(b.modelName as string);
        }

        return 0; // If models are not found, maintain original order
    });

    return {
        total: summedTotal as GraphQLSalesControlBoardTotalPerformance[],
        model: sortedModel as GraphQLSalesControlBoardModelTotalPerformance[],
        items: salesControlBoards.filter(item =>
            selectedVehicleModelIds.some(id => item.modelId.equals(id))
        ) as GraphQLSalesControlBoardItemPerformance[],
    };
};

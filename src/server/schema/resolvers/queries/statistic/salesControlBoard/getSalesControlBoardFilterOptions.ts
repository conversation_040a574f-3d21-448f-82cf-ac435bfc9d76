import {
    ModuleType,
    SalesControlBoardModule,
    SimpleVehicleManagementModule,
    User,
    VehicleKind,
} from '../../../../../database/documents';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { DealerPolicyAction } from '../../../../../permissions';
import ensureManyFromLoaders from '../../../../../utils/ensureManyFromLoaders';
import { InvalidPermission } from '../../../../errors';
import { requiresLoggedUser } from '../../../../middlewares';
import { GraphQLQueryResolvers } from '../../../definitions';
import { FIXED_MODELS } from './helpers';

const query: GraphQLQueryResolvers['getSalesControlBoardFilterOptions'] = async (
    root,
    { dealerId },
    { loaders, getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const dealer = await loaders.dealerById.load(dealerId);

    if (!dealer) {
        throw new Error('Dealer not found');
    }

    const hasManagerPermission = await permissionController.dealers.mayOperateOn(
        dealer,
        DealerPolicyAction.ViewSalesControlBoardManager
    );

    const hasConsultantPermission = await permissionController.dealers.mayOperateOn(
        dealer,
        DealerPolicyAction.ViewSalesControlBoardSalesConsultant
    );

    if (!hasManagerPermission && !hasConsultantPermission) {
        throw new InvalidPermission();
    }

    const salesControlBoardModule = (await collections.modules.findOne({
        companyId: dealer.companyId,
        _type: ModuleType.SalesControlBoardModule,
    })) as SalesControlBoardModule;

    if (!salesControlBoardModule) {
        throw new Error('Sales control board module not found');
    }

    const user = await getUser();
    const salesConsultants = await (async () => {
        const salesConsultantIds = salesControlBoardModule.salesConsultantsAssignments.overrides.find(
            ({ dealerId: overrideDealerId }) => overrideDealerId?.equals(dealerId)
        ).value;

        if (hasManagerPermission) {
            return loaders.userById.loadMany(salesConsultantIds).then(ensureManyFromLoaders<User>);
        }

        if (hasConsultantPermission && salesConsultantIds.find(i => i.equals(user._id))) {
            return [user];
        }

        return [];
    })();

    const vehicleManagementModule = (await collections.modules.findOne({
        companyId: dealer.companyId,
        _type: ModuleType.SimpleVehicleManagement,
    })) as SimpleVehicleManagementModule;

    const vehicleModels = await collections.vehicles
        .find({
            _kind: VehicleKind.LocalModel,
            moduleId: vehicleManagementModule._id,
            '_versioning.isLatest': true,
            isDeleted: false,
            isActive: true,
            'name.defaultValue': { $in: FIXED_MODELS },
            $or: [{ parentModelId: { $exists: false } }, { parentModelId: null }],
        })
        .toArray();

    return {
        salesConsultantOptions: salesConsultants.map(i => ({ value: i._id, label: i.displayName })),
        vehicleModelOptions: vehicleModels.map(i => ({ value: i._id, label: i.name.defaultValue })),
    };
};

export default requiresLoggedUser(query);

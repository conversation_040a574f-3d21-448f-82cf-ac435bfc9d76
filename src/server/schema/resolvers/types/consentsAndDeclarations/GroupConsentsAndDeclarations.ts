import * as permissionKind from '../../../../../shared/permissions';
import { AgreementPolicyAction } from '../../../../permissions';
import { createPermissionsResolverWithModule } from '../../../../utils/permissionResolvers';
import { GraphQLGroupConsentsAndDeclarationsResolvers, ModuleType } from '../../definitions';

const GroupConsentsAndDeclarations: GraphQLGroupConsentsAndDeclarationsResolvers = {
    id: root => root._id,
    type: root => root._type,
    versioning: root => root._versioning,
    module: async (root, args, { loaders }) => {
        if (root.moduleId) {
            const module = await loaders.moduleById.load(root.moduleId);

            if (module) {
                return module;
            }
        }

        throw new Error('Module not found');
    },
    permissions: (root, args, context) =>
        createPermissionsResolverWithModule(root, context, async (module, { getPermissionController }) => {
            if (module?._type !== ModuleType.ConsentsAndDeclarations) {
                return [];
            }

            const { agreements: controller } = await getPermissionController();

            return [
                [controller.mayOperateOn(root, AgreementPolicyAction.Update, module), permissionKind.updateAgreement],
                [controller.mayOperateOn(root, AgreementPolicyAction.Delete, module), permissionKind.deleteAgreement],
            ];
        }),
    children: async (root, args, { loaders }) => loaders.consentsAndDeclarationsByParentId.load(root._id),
};

export default GroupConsentsAndDeclarations;

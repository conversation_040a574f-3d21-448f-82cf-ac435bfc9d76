import * as permissionKind from '../../../../../shared/permissions';
import { ModulePolicyAction } from '../../../../permissions';
import { createPermissionsResolver } from '../../../../utils/permissionResolvers';
import { GraphQLSalesControlBoardModuleResolvers } from '../../definitions';

const resolver: GraphQLSalesControlBoardModuleResolvers = {
    company: async (root, args, { loaders }) => loaders.companyById.load(root.companyId),
    id: root => root._id,
    permissions: (root, args, context) =>
        createPermissionsResolver(context, async () => {
            const { modules: controller } = await context.getPermissionController();

            return [
                [controller.mayOperateOn(root, ModulePolicyAction.Update), permissionKind.updateModule],
                [controller.mayOperateOn(root, ModulePolicyAction.Delete), permissionKind.deleteModule],
            ];
        }),
    versioning: root => root._versioning,
};

export default resolver;

import type { GraphQLImportDealerResponseResolvers } from '../../definitions';
import { ImportDealerResponseType } from '../../typings';

const ImportDealerResponseGraphQL: GraphQLImportDealerResponseResolvers = {
    __resolveType: parent => {
        switch (parent._kind) {
            case ImportDealerResponseType.Success:
                return 'ImportDealerSuccess';

            case ImportDealerResponseType.Fail:
                return 'ImportDealerFail';

            default:
                throw new Error('Import dealers response type not supported');
        }
    },
};

export default ImportDealerResponseGraphQL;

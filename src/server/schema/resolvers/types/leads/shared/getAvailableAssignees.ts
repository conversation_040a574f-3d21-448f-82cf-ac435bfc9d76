import { isEmpty } from 'lodash/fp';
import { Filter } from 'mongodb';
import { User } from '../../../../../database/documents';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { getAccessibleUserIds } from '../../../../../utils/accessibleUserIds';
import { uniqueObjectIds } from '../../../../../utils/fp';
import { GraphQLLeadResolvers } from '../../../definitions';

const getAvailableAssignees: GraphQLLeadResolvers['availableAssignees'] = async (
    lead,
    args,
    { getUser, getPermissionController, loaders }
) => {
    const { moduleId, assigneeId, dealerId } = lead;
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const user = await getUser(true);

    const filter: Filter<User> = { isDeleted: false };

    const hasRootPermission = permissionController.hasRootPermission();
    if (!hasRootPermission) {
        const accessibleUserIds = await getAccessibleUserIds([user?._id].filter(Boolean));
        // extract user id array
        const userIds = !isEmpty(accessibleUserIds)
            ? uniqueObjectIds([...accessibleUserIds[`${user?._id}`], assigneeId].filter(Boolean))
            : [];
        if (userIds.length === 0) {
            return [];
        }
        filter._id = { $in: userIds };
    }

    const { companyId } = await loaders.moduleById.load(moduleId);

    const matchByCompanyAndDealerId = {
        $and: [{ 'userGroups.companyId': companyId }, { 'userGroups.dealerIds': { $in: [dealerId] } }],
    };

    return (await collections.users
        .aggregate([
            { $match: filter },
            { $lookup: { from: 'userGroups', localField: '_id', foreignField: 'userIds', as: 'userGroups' } },
            assigneeId
                ? {
                      $match: {
                          $or: [matchByCompanyAndDealerId, { _id: { $in: [assigneeId] } }],
                      },
                  }
                : {
                      $match: matchByCompanyAndDealerId,
                  },
        ])
        .toArray()) as User[];
};

export default getAvailableAssignees;

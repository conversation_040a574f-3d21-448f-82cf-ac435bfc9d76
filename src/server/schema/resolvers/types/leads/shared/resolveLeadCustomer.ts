import type { GraphQLLeadResolvers } from '../../../definitions';

/**
 * Customer resolver for all lead types which always return the latest customer data
 */
const resolveLeadCustomer: GraphQLLeadResolvers['customer'] = async (root, args, { loaders }) => {
    const customer = await loaders.customerById.load(root.customerId);

    if (!customer) {
        throw new Error('Customer not found for the lead');
    }

    if (customer._versioning.isLatest || root.mergedToLeadSuiteId) {
        // If the customer is already the latest version or the lead has been merged to another lead, return it
        return customer;
    }

    return loaders.customerByLatestSuiteId.load(customer._versioning.suiteId);
};

export default resolveLeadCustomer;

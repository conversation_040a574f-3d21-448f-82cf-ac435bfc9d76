import * as permissionKind from '../../../../../../shared/permissions';
import type { Lead } from '../../../../../database/documents/Lead';
import type { FinderApplicationEntrypoint } from '../../../../../database/documents/Router';
import { ApplicationPolicyAction } from '../../../../../permissions/types/applications';
import { LeadPolicyAction } from '../../../../../permissions/types/leads';
import { ModulePolicyAction } from '../../../../../permissions/types/modules';
import ensureManyFromLoaders from '../../../../../utils/ensureManyFromLoaders';
import type { Context } from '../../../../context';

type PermissionTuple = [boolean, string];

export default async function resolveLeadPermissions(root: Lead, context: Context): Promise<PermissionTuple[]> {
    const {
        applications: applicationController,
        leads: controller,
        modules: moduleController,
    } = await context.getPermissionController();

    const launchpadModule = await context.loaders.leadModuleByCompanyId.load(root.companyId);

    const basePermissions: PermissionTuple[] = [
        [controller.mayOperateOn(root, LeadPolicyAction.Update), permissionKind.updateLead],
        [controller.mayOperateOn(root, LeadPolicyAction.View), permissionKind.viewLeads],
        [controller.mayOperateOn(root, LeadPolicyAction.UpdateContact), permissionKind.updateContact],
        [controller.mayOperateOn(root, LeadPolicyAction.ViewContact), permissionKind.viewContact],
        [controller.mayOperateOn(root, LeadPolicyAction.CreateTestDrive), permissionKind.createTestDrive],
        [controller.mayOperateOn(root, LeadPolicyAction.CreateShowroomVisit), permissionKind.createShowroomVisit],
        [controller.mayOperateOn(root, LeadPolicyAction.CreateFollowUp), permissionKind.createFollowUp],
        [controller.mayOperateOn(root, LeadPolicyAction.CreateTradeIn), permissionKind.createTradeInApplication],
        [controller.mayOperateOn(root, LeadPolicyAction.CreateSalesOffer), permissionKind.createSalesOffer],
    ];

    if (!launchpadModule) {
        return basePermissions;
    }

    const financeAndInsuranceModule = launchpadModule.financeAndInsuranceCalculator
        ? await context.loaders.moduleById.load(launchpadModule.financeAndInsuranceCalculator)
        : null;

    const [finderRouter] = launchpadModule.finderAssignedStock
        ? await context.loaders.routerByEndpointId.load(launchpadModule.finderAssignedStock)
        : [];

    const finderEntrypoint = finderRouter?.endpoints?.find(({ _id }) =>
        _id.equals(launchpadModule.finderAssignedStock)
    ) as FinderApplicationEntrypoint;

    const finderModules = finderEntrypoint?.finderApplicationModuleIds.length
        ? await context.loaders.moduleById
              .loadMany(finderEntrypoint?.finderApplicationModuleIds)
              .then(ensureManyFromLoaders)
        : [];

    const launchpadSpecificPermissions: PermissionTuple[] = [
        [
            financeAndInsuranceModule
                ? moduleController.mayOperateOn(financeAndInsuranceModule, ModulePolicyAction.CreateApplication)
                : false,
            permissionKind.createFinanceAndInsuranceApplication,
        ],
        [
            applicationController.hasCompanyPolicyForModuleAction(
                launchpadModule,
                ApplicationPolicyAction.View,
                launchpadModule.companyId
            ),
            permissionKind.viewTradeInApplication,
        ],
        [
            applicationController.hasCompanyPolicyForModuleAction(
                launchpadModule,
                ApplicationPolicyAction.View,
                launchpadModule.companyId
            ),
            permissionKind.viewSalesOffer,
        ],
        [
            finderModules.some(module => moduleController.mayOperateOn(module, ModulePolicyAction.CreateApplication)),
            permissionKind.createFinderApplicationFromLead,
        ],
    ];

    return [...basePermissions, ...launchpadSpecificPermissions];
}

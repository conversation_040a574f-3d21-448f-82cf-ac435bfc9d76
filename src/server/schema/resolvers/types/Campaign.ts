import { manageCampaigns } from '../../../../shared/permissions';
import { CampaignPolicyAction } from '../../../permissions';
import type { GraphQLCampaignResolvers } from '../definitions';

const resolver: GraphQLCampaignResolvers = {
    id: root => root._id,
    company: async (root, args, { loaders }) => loaders.companyById.load(root.companyId),
    versioning: root => root._versioning,
    permissions: async (root, args, { getPermissionController }) => {
        const permissionController = await getPermissionController();
        const permissions: string[] = [];

        if (
            permissionController.campaigns.mayOperateOn(root, CampaignPolicyAction.Create) ||
            permissionController.campaigns.mayOperateOn(root, CampaignPolicyAction.Update)
        ) {
            permissions.push(manageCampaigns);
        }

        return permissions;
    },
};

export default resolver;

import type { ObjectId } from 'mongodb';
import {
    ConditionType,
    ModuleType,
    SettingId,
    type ConfiguratorModule,
    type Module,
    type StandardApplicationModule,
} from '../../../../../database/documents';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { getVehicleName } from '../../../../../database/helpers/vehicles';

export type ApplicationModuleKeys = Pick<
    StandardApplicationModule,
    | 'customerModuleId'
    | 'myInfoSettingId'
    | 'promoCodeModuleId'
    | 'vehicleModuleId'
    | 'capModuleId'
    | 'appointmentModuleId'
    | 'visitAppointmentModuleId'
> &
    Pick<ConfiguratorModule, 'liveChatSettingId'>;

export const validateModule = async (module: Module) => {
    const { collections } = await getDatabaseContext();

    switch (module._type) {
        case ModuleType.AdyenPaymentModule: {
            const paymentSetting = await getSetting(module._id, 'paymentModuleId');

            if (!paymentSetting) {
                return [];
            }

            const paymentApplicationModuleData = await getModulePaymentSettings(paymentSetting?._id);

            if (paymentApplicationModuleData.length > 0) {
                return paymentApplicationModuleData.map(paymentApplication => paymentApplication.displayName);
            }

            const paymentEvents = await getEvents(paymentSetting?._id, 'paymentSetting.defaultId');

            if (paymentEvents.length > 0) {
                return paymentEvents.map(paymentEvent => paymentEvent.displayName);
            }

            return [];
        }

        case ModuleType.NamirialSigningModule:
        case ModuleType.BasicSigningModule:
        case ModuleType.Docusign: {
            const banks = await getBanks(module._id);

            return banks.map(bank => bank.displayName);
        }

        case ModuleType.ConsentsAndDeclarations: {
            const consents = await getConsents(module._id);

            if (consents.length > 0) {
                return consents.map(consent => consent.displayName);
            }

            const consentApplicationJourney = await getApplicationJourneys(module._id, 'applicantAgreements.moduleId');

            if (consentApplicationJourney.length > 0) {
                return ['Application Journeys'];
            }

            return [];
        }

        case ModuleType.LocalCustomerManagement: {
            const customers = await getCustomers(module._id);

            if (customers.length > 0) {
                return ['Customers'];
            }

            const customerApplicationModules = await getApplicationModules(module._id, 'customerModuleId');

            if (customerApplicationModules.length > 0) {
                return customerApplicationModules.map(
                    customerApplicationModule => customerApplicationModule.displayName
                );
            }

            const customerApplicationJourney = await getApplicationJourneys(module._id, 'applicantKYC.moduleId');

            if (customerApplicationJourney.length > 0) {
                return ['Application Journeys'];
            }

            const giftVoucherModules = await collections.modules
                .find({
                    _type: ModuleType.GiftVoucherModule,
                    customerModuleId: module._id,
                })
                .toArray();
            if (giftVoucherModules.length > 0) {
                return giftVoucherModules.map(giftVoucherModule => giftVoucherModule.displayName);
            }

            return [];
        }

        case ModuleType.MyInfoModule: {
            const myInfoSetting = await getSetting(module._id, 'moduleId');

            if (!myInfoSetting) {
                return [];
            }

            const myInfoApplicationModuleData = await getApplicationModules(myInfoSetting?._id, 'myInfoSettingId');

            if (myInfoApplicationModuleData.length > 0) {
                return myInfoApplicationModuleData.map(myInfoApplicationModule => myInfoApplicationModule.displayName);
            }

            const myInfoEvents = await getEvents(myInfoSetting?._id, 'myInfoSetting.defaultId');

            if (myInfoEvents.length > 0) {
                return myInfoEvents.map(myInfoEvent => myInfoEvent.displayName);
            }

            return [];
        }

        case ModuleType.PromoCodeModule: {
            const promoCodeApplicationModuleData = await getApplicationModules(module._id, 'promoCodeModuleId');

            if (promoCodeApplicationModuleData.length > 0) {
                return promoCodeApplicationModuleData.map(
                    promoCodeApplicationModule => promoCodeApplicationModule.displayName
                );
            }

            const promoCodes = await getPromoCodes(module._id);

            if (promoCodes.length > 0) {
                return promoCodes.map(promoCode => promoCode.promoType.description);
            }

            return [];
        }

        case ModuleType.SimpleVehicleManagement: {
            const vehicles = await getVehicles(module._id);

            if (vehicles.length > 0) {
                return vehicles.map(vehicle => getVehicleName(vehicle));
            }

            const vehicleApplicationModules = await getApplicationModules(module._id, 'vehicleModuleId');

            if (vehicleApplicationModules.length > 0) {
                return vehicleApplicationModules.map(vehicleApplicationModule => vehicleApplicationModule.displayName);
            }

            return [];
        }

        case ModuleType.WhatsappLiveChatModule:
        case ModuleType.UserlikeChatbotModule: {
            const liveChatSetting = await getSetting(module._id, 'liveChatModuleId');

            if (!liveChatSetting) {
                return [];
            }

            const liveChatApplicationModuleData = await getApplicationModules(
                liveChatSetting?._id,
                'liveChatSettingId'
            );

            return liveChatApplicationModuleData.map(
                liveChatApplicationModule => liveChatApplicationModule.displayName
            );
        }

        case ModuleType.StandardApplicationModule: {
            return validateApplicationModule(module._id, module._type);
        }

        case ModuleType.FinderApplicationPrivateModule:
        case ModuleType.FinderApplicationPublicModule: {
            return validateApplicationModule(module._id, module._type);
        }

        case ModuleType.EventApplicationModule: {
            const validations = await validateApplicationModule(module._id, module._type);
            if (validations.length > 0) {
                return validations;
            }

            const events = await getEvents(module._id, 'moduleId');
            if (events.length > 0) {
                return events.map(event => event.displayName);
            }

            return [];
        }

        case ModuleType.ConfiguratorModule: {
            const validations = await validateApplicationModule(module._id, module._type);
            if (validations.length > 0) {
                return validations;
            }

            const configurators = await getConfigurators(module._id);
            if (configurators.length > 0) {
                return ['Configurators'];
            }

            const inventories = await getInventories(module._id);
            if (inventories.length > 0) {
                return ['Inventories'];
            }

            return [];
        }

        case ModuleType.MobilityModule: {
            return validateApplicationModule(module._id, module._type);
        }

        case ModuleType.LaunchPadModule: {
            const validations = await validateApplicationModule(module._id, module._type);
            if (validations.length > 0) {
                return validations;
            }

            const retainModules = await getRelatedRetainModules(module._id);
            if (retainModules.length > 0) {
                return retainModules.map(r => r.displayName);
            }

            return [];
        }

        case ModuleType.AutoplayModule: {
            const settingIds = await collections.settings
                .find({
                    settingId: SettingId.Autoplay,
                    moduleId: module._id,
                })
                .map(setting => setting._id)
                .toArray();
            if (!settingIds || settingIds.length === 0) {
                return [];
            }

            // Check if autoplay currently used in finder vehicle modules
            const finderVehicleModules = await collections.modules
                .find({
                    _type: ModuleType.FinderVehicleManagement,
                    autoplaySettingId: { $in: settingIds },
                })
                .toArray();
            if (finderVehicleModules.length > 0) {
                return finderVehicleModules.map(finderVehicleModule => finderVehicleModule.displayName);
            }

            return [];
        }

        case ModuleType.PorscheMasterDataModule: {
            const vehicleModules = await getVehicleModules(module?._id);
            if (vehicleModules.length > 0) {
                return vehicleModules.map(vehicleModule => vehicleModule.displayName);
            }

            return [];
        }

        case ModuleType.CapModule: {
            const capSetting = await getSetting(module._id, 'capModuleId');

            if (!capSetting) {
                return [];
            }

            const capApplicationModuleData = await getApplicationModules(module._id, 'capModuleId');

            if (capApplicationModuleData.length > 0) {
                return capApplicationModuleData.map(capApplicationModule => capApplicationModule.displayName);
            }

            const capEvents = await getEvents(module._id, 'capModuleId');

            if (capEvents.length > 0) {
                return capEvents.map(capEvent => capEvent.displayName);
            }

            return [];
        }

        case ModuleType.CtsModule: {
            const settings = await collections.settings.find({ ctsModuleId: module._id }).toArray();
            if (settings.length) {
                return settings.map(setting => setting.settingId === SettingId.Cts && setting.displayName);
            }

            return [];
        }

        case ModuleType.AppointmentModule: {
            const appointmentApplications = await getAppointmentApplications(module._id);

            if (appointmentApplications.length > 0) {
                return ['Applications'];
            }

            const appointmentApplicationModuleData = await getApplicationModules(module._id, 'appointmentModuleId');

            if (appointmentApplicationModuleData.length > 0) {
                return appointmentApplicationModuleData.map(({ displayName }) => displayName);
            }

            const appointmentRouters = await getRouters(module._id, module._type);

            if (appointmentRouters.length > 0) {
                return appointmentRouters.map(({ pathname }) => pathname);
            }

            return [];
        }

        case ModuleType.VisitAppointmentModule: {
            const visitAppointmentApplications = await getVisitAppointmentApplications(module._id);

            if (visitAppointmentApplications.length > 0) {
                return ['Applications'];
            }

            const visitAppointmentApplicationModuleData = await getApplicationModules(
                module._id,
                'visitAppointmentModuleId'
            );

            if (visitAppointmentApplicationModuleData.length > 0) {
                return visitAppointmentApplicationModuleData.map(({ displayName }) => displayName);
            }

            const visitAppointmentRouters = await getRouters(module._id, module._type);

            if (visitAppointmentRouters.length > 0) {
                return visitAppointmentRouters.map(({ pathname }) => pathname);
            }

            return [];
        }

        case ModuleType.GiftVoucherModule: {
            const mobilityModules = await collections.modules
                .find({
                    _type: ModuleType.MobilityModule,
                    giftVoucherModuleId: module._id,
                })
                .toArray();

            if (mobilityModules.length > 0) {
                return mobilityModules.map(mobilityModule => mobilityModule.displayName);
            }

            const giftVouchers = await collections.giftVouchers
                .find({
                    moduleId: module._id,
                    '_versioning.isLatest': true,
                })
                .toArray();

            if (giftVouchers.length > 0) {
                return ['Gift Vouchers'];
            }

            return [];
        }

        case ModuleType.VehicleDataWithPorscheCodeIntegrationModule: {
            const settings = await collections.settings.find({ moduleId: module._id }).toArray();

            const salesOfferModules = await collections.modules
                .find({
                    _type: ModuleType.SalesOfferModule,
                    vehicleDataWithPorscheCodeIntegrationSettingId: {
                        $in: settings.map(setting => setting._id),
                    },
                })
                .toArray();

            if (salesOfferModules.length) {
                return salesOfferModules.map(salesOfferModule => salesOfferModule.displayName);
            }

            return [];
        }

        case ModuleType.InsuranceModule: {
            const applicationModule = await getRelatedInsuranceModules(module._id);
            if (applicationModule.length > 0) {
                return applicationModule.map(r => r.displayName);
            }

            return [];
        }

        case ModuleType.SalesOfferModule: {
            const salesOffer = await collections.salesOffers.countDocuments({ moduleId: module._id });

            if (salesOffer > 0) {
                return ['Sales Offer'];
            }

            const validations = await validateApplicationModule(module._id, module._type);
            if (validations.length > 0) {
                return validations;
            }

            return [];
        }

        case ModuleType.PorscheIdModule: {
            const modules = await collections.modules
                .find({
                    _type: {
                        $in: [ModuleType.ConfiguratorModule, ModuleType.FinderApplicationPublicModule],
                    },
                    porscheIdModuleId: module._id,
                })
                .toArray();
            if (modules.length > 0) {
                return modules.map(m => m.displayName);
            }

            const events = await getEvents(module._id, 'porscheIdModuleId');
            if (events.length > 0) {
                return events.map(event => event.displayName);
            }

            return [];
        }

        case ModuleType.WebsiteModule:
        default:
            return [];
    }
};

const getRelatedRetainModules = async (launchPadModuleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const retainModules = await collections.modules
        .find({
            _type: ModuleType.PorscheRetainModule,
            launchPadModuleId,
        })
        .toArray();

    return retainModules;
};

const getRelatedInsuranceModules = async (insuranceModuleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const modules = await collections.modules
        .find({
            _type: {
                $in: [
                    ModuleType.StandardApplicationModule,
                    ModuleType.ConfiguratorModule,
                    ModuleType.SalesOfferModule,
                    ModuleType.FinderApplicationPrivateModule,
                    ModuleType.FinderApplicationPublicModule,
                ],
            },
            insuranceModuleId,
        })
        .toArray();

    return modules;
};

const getVehicleModules = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const vehicleModules = await collections.modules
        .find({
            _type: ModuleType.SimpleVehicleManagement,
            porscheMasterDataModuleId: moduleId,
        })
        .toArray();

    return vehicleModules;
};

const getVehicles = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const vehicles = await collections.vehicles
        .find({
            moduleId,
            isDeleted: false,
            '_versioning.isLatest': true,
        })
        .toArray();

    return vehicles;
};

const getApplicationModules = async (moduleId: ObjectId, key: keyof ApplicationModuleKeys) => {
    const { collections } = await getDatabaseContext();

    const applicationModules = await collections.modules
        .find({
            [key]: moduleId,
            _type: {
                $in: [
                    ModuleType.StandardApplicationModule,
                    ModuleType.ConfiguratorModule,
                    ModuleType.EventApplicationModule,
                    ModuleType.FinderApplicationPrivateModule,
                    ModuleType.FinderApplicationPublicModule,
                    ModuleType.LaunchPadModule,
                ],
            },
        })
        .toArray();

    return applicationModules;
};

const getModulePaymentSettings = async (settingId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const modules = await collections.modules
        .find({
            $or: [
                { paymentSettingsId: settingId },
                { 'paymentSetting.defaultValue': settingId },
                { 'paymentSetting.overrides.value': settingId },
            ],
            _type: {
                $in: [
                    ModuleType.StandardApplicationModule,
                    ModuleType.ConfiguratorModule,
                    ModuleType.EventApplicationModule,
                ],
            },
        })
        .toArray();

    return modules;
};

const getApplicationJourneys = async (moduleId: ObjectId, key: string) => {
    const { collections } = await getDatabaseContext();

    const applicationJourneys = await collections.applicationJourneys
        .find({
            [key]: moduleId,
        })
        .toArray();

    return applicationJourneys;
};

const getAppointmentApplications = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const appointmentApplications = await collections.applications
        .find({ 'appointmentStage.appointmentModuleId': moduleId })
        .toArray();

    return appointmentApplications;
};

const getVisitAppointmentApplications = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const visitAppointmentApplications = await collections.applications
        .find({ 'visitAppointmentStage.visitAppointmentModuleId': moduleId, '_versioning.isLatest': true })
        .toArray();

    return visitAppointmentApplications;
};

const getBanks = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const banks = await collections.banks
        .find({
            $or: [{ submissionApprovalModuleId: moduleId }, { reSubmissionApprovalModuleId: moduleId }],
            isDeleted: false,
        })
        .toArray();

    return banks;
};

const getConsents = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const consents = await collections.consentsAndDeclarations
        .find({
            moduleId,
            isDeleted: false,
            '_versioning.isLatest': true,
        })
        .toArray();

    return consents;
};

const getSetting = async (moduleId: ObjectId, key: string) => {
    const { collections } = await getDatabaseContext();

    const setting = await collections.settings.findOne({
        [key]: moduleId,
    });

    return setting;
};

const getCustomers = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const customers = await collections.customers
        .find({
            moduleId,
        })
        .toArray();

    return customers;
};

const getPromoCodes = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const promoCodes = await collections.promoCodes
        .find({
            moduleId,
            isDeleted: false,
            '_versioning.isLatest': true,
        })
        .toArray();

    return promoCodes;
};

const getEvents = async (id: ObjectId, key: string) => {
    const { collections } = await getDatabaseContext();

    const events = await collections.events
        .find({
            [key]: id,
            isDeleted: false,
        })
        .toArray();

    return events;
};

const getConfigurators = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const configurators = await collections.configurators
        .find({
            moduleId,
            isDeleted: false,
        })
        .toArray();

    return configurators;
};

const getInventories = async (moduleId: ObjectId) => {
    const { collections } = await getDatabaseContext();

    const inventories = await collections.inventories
        .find({
            moduleId,
            isDeleted: false,
        })
        .toArray();

    return inventories;
};

const getRouters = async (moduleId: ObjectId, moduleType: ModuleType) => {
    const { collections } = await getDatabaseContext();

    const conditionQuery = () => {
        switch (moduleType) {
            case ModuleType.StandardApplicationModule:
                return {
                    $or: [
                        { 'endpoints.applicationModuleId': moduleId },
                        { 'endpoints.applicationModuleIds': { $in: [moduleId] } },
                    ],
                };

            case ModuleType.EventApplicationModule:
                return {
                    $or: [
                        { 'endpoints.eventApplicationModuleId': moduleId },
                        { 'endpoints.applicationModuleIds': { $in: [moduleId] } },
                    ],
                };

            case ModuleType.ConfiguratorModule:
                return {
                    $or: [
                        { 'endpoints.configuratorApplicationModuleId': moduleId },
                        { 'endpoints.applicationModuleIds': { $in: [moduleId] } },
                    ],
                };

            case ModuleType.FinderApplicationPublicModule:
            case ModuleType.FinderApplicationPrivateModule:
                return {
                    $or: [
                        { 'endpoints.finderApplicationModuleIds': { $in: [moduleId] } },
                        { 'endpoints.applicationModuleIds': { $in: [moduleId] } },
                    ],
                };

            case ModuleType.MobilityModule:
                return {
                    $or: [
                        { 'endpoints.mobilityApplicationModuleId': moduleId },
                        { 'endpoints.applicationModuleIds': { $in: [moduleId] } },
                    ],
                };
            case ModuleType.LaunchPadModule:
                return {
                    $or: [
                        { 'endpoints.launchPadApplicationModuleId': moduleId },
                        { 'endpoints.applicationModuleIds': { $in: [moduleId] } },
                    ],
                };
            default:
                return {
                    'endpoints.applicationModuleIds': { $in: [moduleId] },
                };
        }
    };

    const routers = await collections.routers.find(conditionQuery()).toArray();

    return routers;
};

const validateApplicationModule = async (moduleId: ObjectId, moduleType: ModuleType) => {
    const { collections } = await getDatabaseContext();

    const leads = await collections.leads
        .find({
            moduleId,
            '_versioning.isLatest': true,
        })
        .toArray();

    if (leads.length > 0) {
        return ['Leads'];
    }

    const applications = await collections.applications
        .find({
            moduleId,
            '_versioning.isLatest': true,
        })
        .toArray();

    if (applications.length > 0) {
        return ['Applications'];
    }

    const routers = await getRouters(moduleId, moduleType);

    if (routers.length > 0) {
        return routers.map(router => router.pathname);
    }

    const kycPresets = await collections.modules.countDocuments({
        _type: ModuleType.LocalCustomerManagement,
        'kycPresets.conditions': {
            $elemMatch: {
                $or: [
                    {
                        type: ConditionType.IsApplicationModule,
                        moduleId,
                    },
                    {
                        children: {
                            $elemMatch: {
                                type: ConditionType.IsApplicationModule,
                                moduleId,
                            },
                        },
                    },
                ],
            },
        },
    });

    if (kycPresets > 0) {
        return ['KYC Presets'];
    }

    return [];
};

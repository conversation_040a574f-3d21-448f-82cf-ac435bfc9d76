import { ObjectId } from 'mongodb';
import { DealerSpecificItem } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { requiresLoggedUser } from '../../../middlewares';
import { EmailContentUpdateType, GraphQLMutationResolvers, ModuleType } from '../../definitions';
import { retrieveEmailContentUpdateTypePermission } from './shared';

const upsertFeature = (
    feature: DealerSpecificItem<number | ObjectId[]>,
    dealerId: ObjectId,
    value: number | ObjectId[]
) => {
    const index = feature.overrides.findIndex(({ dealerId }) => dealerId?.equals(dealerId));

    if (index > -1) {
        /* eslint-disable-next-line no-param-reassign */
        feature.overrides[index].value = value;
    } else {
        feature.overrides.push({ dealerId, value });
    }

    return feature;
};

const mutate: GraphQLMutationResolvers['updateSalesControlBoardModuleByDealer'] = async (
    root,
    {
        dealerId,
        moduleId,
        financeCommissionMonthlyTarget,
        insuranceCommissionMonthlyTarget,
        salesConsultantsAssignments,
        orderIntakesMonthlyTarget,
        retailsMonthlyTarget,
        testDriveMonthlyTarget,
    },
    { getPermissionController, getUser, loaders }
) => {
    const user = await getUser();
    const versioning = getSimpleVersioningByUserForUpdate(user._id);
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const permission = await retrieveEmailContentUpdateTypePermission(
        EmailContentUpdateType.Dealer,
        permissionController,
        loaders,
        dealerId
    );

    const module = await collections.modules.findOne({
        _id: moduleId,
    });

    if (!module || module._type !== ModuleType.SalesControlBoardModule) {
        throw new Error('Module not found');
    }

    const [
        updatedFinanceCommissionMonthlyTarget,
        updatedInsuranceCommissionMonthlyTarget,
        updatedOrderIntakesMonthlyTarget,
        updatedRetailsMonthlyTarget,
        updatedTestDriveMonthlyTarget,
        updatedSalesConsultantsAssignments,
    ] = await Promise.all([
        upsertFeature(module.financeCommissionMonthlyTarget, dealerId, financeCommissionMonthlyTarget),
        upsertFeature(module.insuranceCommissionMonthlyTarget, dealerId, insuranceCommissionMonthlyTarget),
        upsertFeature(module.orderIntakesMonthlyTarget, dealerId, orderIntakesMonthlyTarget),
        upsertFeature(module.retailsMonthlyTarget, dealerId, retailsMonthlyTarget),
        upsertFeature(module.testDriveMonthlyTarget, dealerId, testDriveMonthlyTarget),
        upsertFeature(module.salesConsultantsAssignments, dealerId, salesConsultantsAssignments),
    ]);

    const updatedModuleFields = {
        financeCommissionMonthlyTarget: updatedFinanceCommissionMonthlyTarget,
        insuranceCommissionMonthlyTarget: updatedInsuranceCommissionMonthlyTarget,
        orderIntakesMonthlyTarget: updatedOrderIntakesMonthlyTarget,
        retailsMonthlyTarget: updatedRetailsMonthlyTarget,
        testDriveMonthlyTarget: updatedTestDriveMonthlyTarget,
        salesConsultantsAssignments: updatedSalesConsultantsAssignments,
        ...versioning,
    };

    return collections.modules.findOneAndUpdate(
        {
            $and: [{ _id: moduleId, _type: ModuleType.SalesControlBoardModule }, permission],
        },
        {
            $set: updatedModuleFields,
        },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutate);

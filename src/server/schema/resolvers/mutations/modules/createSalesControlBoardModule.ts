import { ObjectId } from 'mongodb';
import { DealerSpecificItem, ModuleType, SalesControlBoardModule } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyPolicyAction, generateDealerPermissionForSalesControlBoard } from '../../../../permissions';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const defaultDealerNumber: DealerSpecificItem<number> = {
    defaultValue: 1,
    overrides: [],
};

const mutate: GraphQLMutationResolvers['createSalesControlBoardModule'] = async (
    root,
    { companyId, displayName },
    { loaders, getPermissionController, getUser }
) => {
    const user = await getUser();
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    // check the company ID validity
    const company = await collections.companies.findOne({
        $and: [
            { _id: companyId },
            permissionController.companies.getFilterQueryForAction(CompanyPolicyAction.CreateModule),
        ],
    });

    if (!company) {
        throw new InvalidInput({ companyId: 'invalid company ID' });
    }

    const simpleVersioning = getSimpleVersioningByUserForCreation(user._id);

    const module: SalesControlBoardModule = {
        _id: new ObjectId(),
        _type: ModuleType.SalesControlBoardModule,
        companyId,
        displayName,
        _versioning: simpleVersioning,
        financeCommissionMonthlyTarget: defaultDealerNumber,
        insuranceCommissionMonthlyTarget: defaultDealerNumber,
        orderIntakesMonthlyTarget: defaultDealerNumber,
        retailsMonthlyTarget: defaultDealerNumber,
        testDriveMonthlyTarget: defaultDealerNumber,
        salesConsultantsAssignments: {
            defaultValue: [],
            overrides: [],
        },
    };

    await collections.modules.insertOne(module);

    const dealers = await collections.dealers.find({ companyId }).toArray();

    // create all dealer to have sales control board permissions
    for await (const dealer of dealers) {
        const permissions = await generateDealerPermissionForSalesControlBoard(dealer, module._id);

        await collections.permissions.insertMany(
            permissions.map(permission => ({ ...permission, _id: new ObjectId() }))
        );
    }

    return module;
};

export default requiresLoggedUser(mutate);

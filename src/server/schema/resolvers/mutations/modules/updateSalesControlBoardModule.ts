import getDatabaseContext from '../../../../database/getDatabaseContext';
import { ModulePolicyAction } from '../../../../permissions';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutate: GraphQLMutationResolvers['updateSalesControlBoardModule'] = async (
    root,
    { displayName, moduleId },
    { getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();

    const user = await getUser();

    const module = await collections.modules.findOne({
        $and: [{ _id: moduleId }, permissionController.modules.getFilterQueryForAction(ModulePolicyAction.Update)],
    });

    if (!module) {
        throw new InvalidPermission();
    }

    return collections.modules.findOneAndUpdate(
        { _id: moduleId },
        {
            $set: {
                displayName,
                _versioning: { ...module._versioning, ...getSimpleVersioningByUserForUpdate(user._id) },
            },
        },
        {
            returnDocument: 'after',
        }
    );
};

export default requiresLoggedUser(mutate);

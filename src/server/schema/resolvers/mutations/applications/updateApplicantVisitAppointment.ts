import dayjs from 'dayjs';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { consumeJourneyToken } from '../../../../journeys';
import validateTimeSlot from '../../../../utils/application/validations/appointmentTimeSlot';
import { InvalidInput } from '../../../errors';
import { GraphQLMutationResolvers, ModuleType } from '../../definitions';

const resolver: GraphQLMutationResolvers['updateApplicantVisitAppointment'] = async (
    root,
    { token, bookingTimeSlot },
    context
) => {
    const { collections } = await getDatabaseContext();
    const { getTranslations, loaders } = context;
    const { t } = await getTranslations(['eventApplicantForm']);
    const message = t('eventApplicantForm:messages.invalidTimeSlot');

    const { applicationId } = await consumeJourneyToken(token);
    const application = await collections.applications.findOne({ _id: applicationId });

    if (!application) {
        throw new InvalidInput('Application is not existing.');
    }

    const applicationModule = await collections.modules.findOne({ _id: application.moduleId });
    if (
        applicationModule &&
        (applicationModule._type === ModuleType.StandardApplicationModule ||
            applicationModule._type === ModuleType.FinderApplicationPrivateModule ||
            applicationModule._type === ModuleType.FinderApplicationPublicModule ||
            applicationModule._type === ModuleType.EventApplicationModule ||
            applicationModule._type === ModuleType.ConfiguratorModule)
    ) {
        const { _id: moduleId, companyId } = await collections.modules.findOne({
            _id: applicationModule.visitAppointmentModuleId,
        });
        if (moduleId) {
            const valid = await validateTimeSlot(bookingTimeSlot.slot, moduleId, loaders);
            if (!valid) {
                throw new InvalidInput({ $root: message });
            }

            const company = await loaders.companyById.load(companyId);

            const selectedTimeSlotStr = dayjs.tz(bookingTimeSlot.slot, company.timeZone).format('YYYY-MM-DDTHH:mm:ssZ');
            const applicationTimeSlotStr = dayjs
                .tz(application.appointmentStage?.bookingTimeSlot.slot, company.timeZone)
                .format('YYYY-MM-DDTHH:mm:ssZ');

            let newTimeSlot = null;
            let newBookingLimit = 0;
            if (bookingTimeSlot.useCurrentDateTime) {
                newTimeSlot = application._versioning.createdAt;
            } else if (selectedTimeSlotStr !== applicationTimeSlotStr) {
                newTimeSlot = bookingTimeSlot.slot;
                newBookingLimit = bookingTimeSlot.bookingLimit;
            }

            if (newTimeSlot) {
                await collections.applicationJourneys.findOneAndUpdate(
                    { applicationSuiteId: application._versioning.suiteId },
                    {
                        $set: {
                            'applicantVisitAppointment.bookingTimeSlot.slot': new Date(newTimeSlot),
                            'applicantVisitAppointment.bookingTimeSlot.bookingLimit': newBookingLimit,
                        },
                    }
                );

                await collections.applications.findOneAndUpdate(
                    { _id: application._id, '_versioning.isLatest': true },
                    {
                        $set: {
                            'visitAppointmentStage.bookingTimeSlot.slot': new Date(newTimeSlot),
                            'visitAppointmentStage.bookingTimeSlot.bookingLimit': newBookingLimit,
                        },
                    }
                );
            }
        }
    }

    return true;
};

export default resolver;

import type { ObjectId } from 'bson';
import isEqual from 'fast-deep-equal';
import { get, isNil } from 'lodash/fp';
import {
    ChangeKind,
    type ApplicationFinancing,
    type ApplicationInsurancing,
    KnownChange,
} from '../../../../../database/documents';

export type AmendableField = {
    key: string;
    loader?: string;
    valueKey?: string;
    label: string;
    kind: ChangeKind;
    getLabel?: (val: unknown) => string;
};

export const createGetChangesFromAmendableFieldsFn =
    <TAmendable>(fields: AmendableField[]) =>
    async (from: TAmendable, to: TAmendable, { loaders }): Promise<KnownChange[]> => {
        const changes = await Promise.all(
            fields.map(async ({ key, loader, valueKey, label, kind }) => {
                const before = get(key, from);
                const after = get(key, to);

                if (isNil(before) && isNil(after)) {
                    return null;
                }

                if (!isEqual(before, after)) {
                    if (loader) {
                        const beforeObject = !isNil(before) ? await loaders[loader].load(before) : {};
                        const afterObject = !isNil(after) ? await loaders[loader].load(after) : {};

                        return {
                            key: `${label}`,
                            before: get(valueKey, beforeObject),
                            after: get(valueKey, afterObject),
                            kind,
                        };
                    }

                    return {
                        key: `${label}`,
                        before,
                        after,
                        kind,
                    };
                }

                return null;
            })
        );

        return changes.filter(Boolean);
    };

export const AmendableFinancingFields: AmendableField[] = [
    {
        key: 'bankId',
        loader: 'bankById',
        label: 'applicationDetails:fields.financing.bank.label',
        valueKey: 'displayName',
        kind: ChangeKind.String,
    },
    {
        key: 'vehicleId',
        loader: 'vehicleById',
        label: 'applicationDetails:fields.vehicle.name.label',
        valueKey: 'name.defaultValue',
        kind: ChangeKind.String,
    },
    {
        key: 'promoCodeId',
        loader: 'promoCodeById',
        label: 'applicationDetails:fields.financing.promoCode.label',
        valueKey: 'promoCode',
        kind: ChangeKind.String,
    },
    {
        key: 'financing.financeProductId',
        loader: 'financeProductById',
        label: 'applicationDetails:fields.financing.product.label',
        valueKey: 'displayName',
        kind: ChangeKind.String,
    },
    {
        key: 'financing.carPrice',
        label: 'calculation:labels.startingPrice',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.totalPrice',
        label: 'applicationDetails:fields.financing.totalPrice.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.financedAmount',
        label: 'applicationDetails:fields.financing.financedAmount.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.term',
        label: 'applicationDetails:fields.financing.term.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.interestRate',
        label: 'applicationDetails:fields.financing.interestRate.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.downPayment',
        label: 'applicationDetails:fields.financing.downPayment.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.loan',
        label: 'applicationDetails:fields.financing.loan.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.deposit',
        label: 'applicationDetails:fields.financing.deposit.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.paymentMode',
        label: 'financeProductDetails:fields.paymentMode.label',
        kind: ChangeKind.String,
    },
    {
        key: 'financing.monthlyInstalment',
        label: 'applicationDetails:fields.financing.monthlyInstalment.label',
        kind: ChangeKind.MonthlyInstalment,
    },
    {
        key: 'financing.coe',
        label: 'applicationDetails:fields.financing.coe.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.extraFinancedAmount',
        label: 'applicationDetails:fields.financing.extraFinancedAmount.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.extraAmount',
        label: 'applicationDetails:fields.financing.extraAmount.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.promoCodeValue',
        label: 'applicationDetails:fields.financing.promoCodeValue.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.licensePlateFee',
        label: 'applicationDetails:fields.financing.licensePlateFee.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.commission',
        label: 'applicationDetails:fields.financing.commission.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.monthlyPaymentFixedInterestRate',
        label: 'applicationDetails:fields.financing.monthlyPaymentFixedRate.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.displacement',
        label: 'applicationDetails:fields.financing.displacement.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.licenseAndFuelTax',
        label: 'applicationDetails:fields.financing.licenseAndFuelTax.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.insuranceFee',
        label: 'applicationDetails:fields.financing.insuranceFee.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.taxLoss',
        label: 'applicationDetails:fields.financing.taxLoss.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'financing.tradeInAmount',
        label: 'applicationDetails:fields.financing.tradeInAmount.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.cashAfterTradeIn',
        label: 'applicationDetails:fields.financing.priceAfterTradeIn.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.estimatedSurplusBalloon',
        label: 'applicationDetails:fields.financing.estimatedSurplusBalloon.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.assuredResaleValue',
        label: 'applicationDetails:fields.financing.assuredResaleValue.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.mileage',
        label: 'applicationDetails:fields.financing.mileage.label',
        kind: ChangeKind.Amount,
    },
    {
        key: 'financing.residualValue',
        label: 'applicationDetails:fields.financing.residualValue.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.balloonPayment',
        label: 'applicationDetails:fields.financing.balloonPayment.label',
        kind: ChangeKind.CompoundValue,
    },
    {
        key: 'financing.dealerOptions',
        label: 'applicationDetails:fields.financing.dealerOptions.label',
        kind: ChangeKind.DealerOption,
    },
    // financing changes for new zea land market
    {
        key: 'financing.totalAmountPayable',
        label: 'applicationDetails:fields.financing.totalAmountPayable.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.estFee',
        label: 'applicationDetails:fields.financing.estFee.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.ppsr',
        label: 'applicationDetails:fields.financing.ppsr.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'financing.bankEstFee',
        label: 'applicationDetails:fields.financing.singularBankEstFee.label',
        kind: ChangeKind.Currency,
    },
];
type FinancingAmendable = {
    bankId?: ObjectId;
    vehicleId?: ObjectId;
    promoCodeId?: ObjectId;
    financing: ApplicationFinancing;
};
export const getFinancingChanges = createGetChangesFromAmendableFieldsFn<FinancingAmendable>(AmendableFinancingFields);

export const AmendableInsuranceFields = [
    {
        key: 'insurancing.insurerId',
        loader: 'insurerById',
        label: 'calculators:fields.insurer.label',
        kind: ChangeKind.String,
        valueKey: 'displayName',
    },
    {
        key: 'insurancing.ncd',
        label: 'calculators:fields.noClaimDiscount.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'insurancing.insurancePremium',
        label: 'calculators:fields.insurancePremium.label',
        kind: ChangeKind.Currency,
    },
    {
        key: 'insurancing.yearsOfDriving',
        label: 'calculators:fields.yearsOfDriving.label',
        kind: ChangeKind.Number,
    },
    {
        key: 'insurancing.dateOfRegistration',
        label: 'calculators:fields.dateOfRegistration.label',
        kind: ChangeKind.Date,
    },
    {
        key: 'insurancing.dateOfBirth',
        label: 'calculators:fields.dateOfBirth.label',
        kind: ChangeKind.Date,
    },
];
type InsuranceAmendable = {
    insurancing: ApplicationInsurancing;
};
export const getInsuranceChanges = createGetChangesFromAmendableFieldsFn<InsuranceAmendable>(AmendableInsuranceFields);

import { ObjectId } from 'mongodb';
import getDatabaseContext from '../../../../../database/getDatabaseContext';

type UpdateReferencesWithReplacementParams = {
    targetEndpointId: ObjectId;
    replacementEndpointId: ObjectId;
    replacementRouterId: ObjectId;
};

type BulkReplacementPlan = {
    targetEndpointId: ObjectId;
    replacementEndpointId: ObjectId;
    replacementRouterId: ObjectId;
};

export const checkReferencesExist = async (endpointId: ObjectId): Promise<boolean> => {
    const { collections } = await getDatabaseContext();

    const [applicationCount, leadCount, externalLinkCount] = await Promise.all([
        collections.applications.countDocuments({ endpointId }, { limit: 1 }),
        collections.leads.countDocuments({ endpointId }, { limit: 1 }),
        collections.externalLinks.countDocuments({ 'data.endpointId': endpointId }, { limit: 1 }),
    ]);

    return applicationCount > 0 || leadCount > 0 || externalLinkCount > 0;
};

export const checkBulkReferencesExist = async (endpointIds: ObjectId[]): Promise<Map<string, boolean>> => {
    const { collections } = await getDatabaseContext();
    // Map of endpointId.toString() -> boolean (true if references exist, false if no references)
    const referencesMap = new Map<string, boolean>();

    // Query all collections in parallel to find documents that reference any of the endpoints
    const [applications, leads, externalLinks] = await Promise.all([
        collections.applications
            .find({ endpointId: { $in: endpointIds } }, { projection: { endpointId: 1 } })
            .toArray(),
        collections.leads.find({ endpointId: { $in: endpointIds } }, { projection: { endpointId: 1 } }).toArray(),
        collections.externalLinks
            .find({ 'data.endpointId': { $in: endpointIds } }, { projection: { 'data.endpointId': 1 } })
            .toArray(),
    ]);

    // Initialize all endpoints as having no references
    endpointIds.forEach(id => referencesMap.set(id.toString(), false));

    // Mark endpoints as having references if found in any collection
    applications.forEach(app => referencesMap.set(app.endpointId.toString(), true));
    leads.forEach(lead => referencesMap.set(lead.endpointId.toString(), true));
    externalLinks.forEach(
        link => 'endpointId' in link.data && referencesMap.set(link.data.endpointId.toString(), true)
    );

    return referencesMap;
};

/**
 * Updates references in the database for a specific endpoint.
 * @param Object containing the IDs of the target and replacement endpoints
 * @returns  Boolean indicating if references were updated
 */
export const updateReferencesWithReplacement = async ({
    targetEndpointId,
    replacementEndpointId,
    replacementRouterId,
}: UpdateReferencesWithReplacementParams): Promise<Boolean> =>
    updateBulkReferencesWithReplacement({
        replacementPlans: [
            {
                targetEndpointId,
                replacementEndpointId,
                replacementRouterId,
            },
        ],
    });

/**
 * * Updates references in the database for multiple endpoints.
 * @param Object containing an array of replacement plans
 * @returns  Boolean indicating if references were updated
 */
export const updateBulkReferencesWithReplacement = async ({
    replacementPlans,
}: {
    replacementPlans: BulkReplacementPlan[];
}): Promise<Boolean> => {
    const { collections } = await getDatabaseContext();

    const applicationBulkOps = replacementPlans.map(plan => ({
        updateMany: {
            filter: { endpointId: plan.targetEndpointId },
            update: {
                $set: {
                    endpointId: plan.replacementEndpointId,
                    routerId: plan.replacementRouterId,
                },
            },
        },
    }));

    const leadBulkOps = replacementPlans.map(plan => ({
        updateMany: {
            filter: { endpointId: plan.targetEndpointId },
            update: {
                $set: {
                    endpointId: plan.replacementEndpointId,
                    routerId: plan.replacementRouterId,
                },
            },
        },
    }));

    const externalLinkBulkOps = replacementPlans.map(plan => ({
        updateMany: {
            filter: { 'data.endpointId': plan.targetEndpointId },
            update: {
                $set: {
                    'data.endpointId': plan.replacementEndpointId,
                    'data.routerId': plan.replacementRouterId,
                },
            },
        },
    }));

    const [applicationResult, leadResult, externalLinkResult] = await Promise.all([
        applicationBulkOps.length > 0
            ? collections.applications.bulkWrite(applicationBulkOps, { ordered: false })
            : { modifiedCount: 0 },
        leadBulkOps.length > 0 ? collections.leads.bulkWrite(leadBulkOps, { ordered: false }) : { modifiedCount: 0 },
        externalLinkBulkOps.length > 0
            ? collections.externalLinks.bulkWrite(externalLinkBulkOps, { ordered: false })
            : { modifiedCount: 0 },
    ]);

    // Return true if any documents were actually modified during the replacement operation
    return applicationResult.modifiedCount > 0 || leadResult.modifiedCount > 0 || externalLinkResult.modifiedCount > 0;
};

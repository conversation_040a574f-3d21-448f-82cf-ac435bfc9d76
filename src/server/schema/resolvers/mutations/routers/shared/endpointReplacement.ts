import { ObjectId } from 'mongodb';
import { EndpointType, type Endpoint, type Router } from '../../../../../database/documents/Router';
import { equivalent } from '../../../../../utils/oid';

export const ENDPOINTS_WITH_REPLACEMENT = [
    EndpointType.StandardApplicationEntrypoint,
    EndpointType.StandardApplicationPublicAccessEntrypoint,
    EndpointType.EventApplicationEntrypoint,
    EndpointType.LaunchPadApplicationEntrypoint,
    EndpointType.ConfiguratorApplicationEntrypoint,
    EndpointType.MobilityApplicationEntrypoint,
    EndpointType.FinderApplicationEntrypoint,
    EndpointType.FinderApplicationPublicAccessEntrypoint,
];

export const getModuleIdFromEndpoint = (endpoint: Endpoint) => {
    switch (endpoint._type) {
        case EndpointType.StandardApplicationEntrypoint:
        case EndpointType.StandardApplicationPublicAccessEntrypoint:
            return endpoint.applicationModuleId;

        case EndpointType.EventApplicationEntrypoint:
            return endpoint.eventApplicationModuleId;

        case EndpointType.LaunchPadApplicationEntrypoint:
            return endpoint.launchPadApplicationModuleId;

        case EndpointType.ConfiguratorApplicationEntrypoint:
            return endpoint.configuratorApplicationModuleId;

        case EndpointType.MobilityApplicationEntrypoint:
            return endpoint.mobilityApplicationModuleId;

        case EndpointType.FinderApplicationEntrypoint:
        case EndpointType.FinderApplicationPublicAccessEntrypoint:
            return endpoint.finderApplicationModuleIds;

        default:
            return null;
    }
};

export type FindReplacementEndpointParams = {
    targetEndpoint: Endpoint;
    router: Router;
    allRouters: Router[];
};

export type ReplacementEndpoint = {
    routerId: ObjectId;
    endpointId: ObjectId;
};

export const findReplacementEndpoint = ({
    targetEndpoint,
    router,
    allRouters,
}: FindReplacementEndpointParams): ReplacementEndpoint | null => {
    const targetModuleId = getModuleIdFromEndpoint(targetEndpoint);
    if (!targetModuleId) {
        return null;
    }

    const isModuleMatch = (endpoint: Endpoint) => {
        const endpointModuleId = getModuleIdFromEndpoint(endpoint);
        if (!endpointModuleId) {
            return false;
        }

        if (Array.isArray(targetModuleId) && Array.isArray(endpointModuleId)) {
            return equivalent(targetModuleId, endpointModuleId);
        }

        if (!Array.isArray(targetModuleId) && !Array.isArray(endpointModuleId)) {
            return endpointModuleId.equals(targetModuleId);
        }

        return false;
    };

    const sameRouterReplacement = router.endpoints.find(
        endpoint =>
            !endpoint._id.equals(targetEndpoint._id) &&
            endpoint._type === targetEndpoint._type &&
            isModuleMatch(endpoint)
    );

    if (sameRouterReplacement) {
        return {
            routerId: router._id,
            endpointId: sameRouterReplacement._id,
        };
    }

    for (const otherRouter of allRouters) {
        if (otherRouter._id.equals(router._id)) {
            continue;
        }

        const otherRouterReplacement = otherRouter.endpoints.find(
            endpoint => endpoint._type === targetEndpoint._type && isModuleMatch(endpoint)
        );

        if (otherRouterReplacement) {
            return {
                routerId: otherRouter._id,
                endpointId: otherRouterReplacement._id,
            };
        }
    }

    return null;
};

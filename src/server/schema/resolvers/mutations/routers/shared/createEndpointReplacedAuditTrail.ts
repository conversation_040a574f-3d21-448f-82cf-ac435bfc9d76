import { ObjectId } from 'mongodb';
import { AuditTrailKind } from '../../../../../database/documents/AuditTrail/core';
import type { EndpointReplacedAuditTrail } from '../../../../../database/documents/AuditTrail/types/endpoint';
import type { Endpoint } from '../../../../../database/documents/Router';
import { AuthorKind } from '../../../../../database/documents/Versioning';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import type { ReplacementEndpoint } from './endpointReplacement';

type CreateAuditTrailParams = {
    companyId: ObjectId;
    deletedRouterId: ObjectId;
    deletedEndpointId: ObjectId;
    replacementEndpoint: ReplacementEndpoint;
    userId: ObjectId;
};

type BulkAuditTrailParams = {
    replacementPlans: Array<{ endpoint: Endpoint; replacementEndpoint: ReplacementEndpoint }>;
    companyId: ObjectId;
    deletedRouterId: ObjectId;
    userId: ObjectId;
};

export const createEndpointReplacedAuditTrail = async ({
    companyId,
    deletedRouterId,
    deletedEndpointId,
    replacementEndpoint,
    userId,
}: CreateAuditTrailParams) => {
    const endpoint = { _id: deletedEndpointId } as Endpoint;

    await createBulkEndpointReplacedAuditTrails({
        replacementPlans: [{ endpoint, replacementEndpoint }],
        companyId,
        deletedRouterId,
        userId,
    });
};

export const createBulkEndpointReplacedAuditTrails = async ({
    replacementPlans,
    companyId,
    deletedRouterId,
    userId,
}: BulkAuditTrailParams) => {
    const { collections } = await getDatabaseContext();

    const auditTrails = replacementPlans.map<EndpointReplacedAuditTrail>(({ endpoint, replacementEndpoint }) => ({
        _id: new ObjectId(),
        _date: new Date(),
        _kind: AuditTrailKind.EndpointReplaced,
        companyId,
        deletedRouterId,
        deletedEndpointId: endpoint._id,
        replacementRouterId: replacementEndpoint.routerId,
        replacementEndpointId: replacementEndpoint.endpointId,
        author: {
            kind: AuthorKind.User,
            id: userId,
        },
    }));

    if (auditTrails.length > 0) {
        await collections.auditTrails.insertMany(auditTrails);
    }

    return auditTrails.length;
};

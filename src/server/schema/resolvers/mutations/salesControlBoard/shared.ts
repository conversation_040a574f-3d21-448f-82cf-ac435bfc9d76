import { Stream } from 'stream';
import dayjs from 'dayjs';
import { Dictionary } from 'lodash';
import { groupBy, isEmpty, isNil, uniq } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import type { Collections } from '../../../../database/collections';
import {
    LeadRatioTuple,
    ModuleType,
    SalesControlBoard,
    SalesControlBoardLead,
    SalesControlBoardOrderIntake,
    SalesControlBoardRetail,
    SalesControlBoardWeeklyCount,
    User,
    VehicleKind,
} from '../../../../database/documents';
import { getMissingHeaders } from '../../../../utils/excel/importExcel';
import { ExcelOptions, StringMap } from '../../../../utils/excel/types';
import { getTableData, getWorkbook } from '../../../../utils/excel/utils';
import { SalesControlBoardDataType } from '../../enums';
import type { ConsolidatedFeatureData, FeatureDataActualCount, ModelFlatten } from './typing';

export const getWeeksInCurrentMonth = (monthOfImport: string): Array<SalesControlBoardWeeklyCount> => {
    if (!monthOfImport) {
        return [];
    }
    const firstDay = dayjs(monthOfImport).startOf('month');
    const lastDay = dayjs(monthOfImport).endOf('month');

    let current = firstDay.startOf('week').add(1, 'day');

    if (current.isBefore(firstDay, 'day')) {
        current = current.add(7, 'day');
    }

    const weeks: Array<SalesControlBoardWeeklyCount> = [];
    let weekNum = 1;

    if (current.isAfter(firstDay)) {
        weeks.push({
            startDate: firstDay.toDate(),
            endDate: current.subtract(1, 'day').toDate(),
            numberOfWeek: weekNum,
            count: 0, // initial count is 0, will be updated later
        });
        weekNum++;
    }

    while (current.isBefore(lastDay) || current.isSame(lastDay, 'day')) {
        const weekEnd = current.add(6, 'day');

        const startOfWeek = current.isAfter(firstDay) ? current : firstDay;
        const endOfWeek = weekEnd.isBefore(lastDay) ? weekEnd : lastDay;

        weeks.push({
            startDate: startOfWeek.toDate(),
            endDate: endOfWeek.toDate(),
            numberOfWeek: weekNum,
            count: 0, // initial count is 0, will be updated later
        });

        current = current.add(7, 'day');
        weekNum++;
    }

    return weeks;
};

const headersByDataType = {
    [SalesControlBoardDataType.Retails]: {
        model: 'Model Description',
        salesConsultant: 'Sales Consultant ?',
        date: 'Date of Reg',
    },
    [SalesControlBoardDataType.Leads]: {
        model: 'Primary Interest',
        salesConsultant: 'Responsible Employee',
        date: 'Posting Date',
    },
    [SalesControlBoardDataType.OrderIntakes]: {
        model: 'Model Type',
        salesConsultant: 'Sales Person',
        date: 'Order Intake Date',
    },
};

export const getModelHeader = (dataType: SalesControlBoardDataType) => {
    if (!headersByDataType[dataType].model) {
        throw new Error(`Unsupported data type: ${dataType}`);
    }

    return headersByDataType[dataType].model;
};

const getDateHeader = (dataType: SalesControlBoardDataType) => {
    if (!headersByDataType[dataType].date) {
        throw new Error(`Unsupported data type: ${dataType}`);
    }

    return headersByDataType[dataType].date;
};

export const remapModel = (input: string) => {
    if (isEmpty(input) || isNil(input)) {
        return null;
    }

    if (input.includes('911') || input.includes('992')) {
        return '911';
    }

    if (input.includes('Boxster') || input.includes('Cayman') || input.includes('Spyder')) {
        return '718';
    }

    // H2 Macan
    return input;
};

const searchVehicleHandler = (flattenModels: ModelFlatten, modelDescription: string) => {
    const mappedModel = remapModel(modelDescription.toString());

    if (!mappedModel) {
        return null;
    }

    const model = flattenModels.find(item => mappedModel.toLowerCase().includes(item.modelDescription.toLowerCase()));

    if (!model) {
        return null;
    }

    return model.modelId;
};

const searchSalesConsultantUser = (salesConsultants: User[], displayName: string): ObjectId => {
    const user = salesConsultants.find(item => item.displayName === displayName);

    if (!user) {
        return null;
    }

    return user._id;
};

export const fetchLocalModels = async (collections: Collections, dealerId: ObjectId): Promise<ModelFlatten> => {
    const dealer = await collections.dealers.findOne({ _id: dealerId });
    const vehicleModule = await collections.modules.findOne({
        companyId: dealer.companyId,
        _type: ModuleType.SimpleVehicleManagement,
    });

    const models = await collections.vehicles
        .find({
            _kind: VehicleKind.LocalModel,
            moduleId: vehicleModule._id,
            '_versioning.isLatest': true,
            isDeleted: false,
            isActive: true,
            $or: [{ parentModelId: { $exists: false } }, { parentModelId: null }],
        })
        .toArray();

    return models.flatMap(model => ({
        modelId: model._id,
        modelDescription: model.name.defaultValue,
    }));
};

export type BoardContext = {
    dealerId: ObjectId;
    dataType: SalesControlBoardDataType;
    reportingDate: string;
    salesConsultants: User[];
    flattenModels: ModelFlatten;
};

/**
 * ==============================
 * ::::::: BASE COMPUTE :::::::::
 * ==============================
 */
const retrieveFinanceInsuranceSalesControlBoard = (
    collections: Collections,
    data: Dictionary<StringMap[]>,
    { dealerId, salesConsultants, flattenModels }: BoardContext
): Dictionary<Dictionary<{ appliedFinance: number; appliedInsurance: number }>> => {
    const result: Dictionary<Dictionary<{ appliedFinance: number; appliedInsurance: number }>> = {};
    const keys = Object.keys(data);

    for (const key of keys) {
        const groupedModels = groupBy('Model Description', data[key]);

        const models = Object.entries(groupedModels).map(([modelDescription, items]) => {
            const modelId = searchVehicleHandler(flattenModels, modelDescription);
            if (modelId) {
                return [modelId.toHexString(), items];
            }

            // Optionally skip models not found, or keep original description
            return null;
        });

        // Enhance: concatenate items for duplicate keys
        const concatModels: Record<string, any[]> = {};
        for (const entry of models) {
            if (!entry) {
                continue;
            }
            const [modelId, items] = entry;
            const modelIdStr = String(modelId);
            if (!concatModels[modelIdStr]) {
                concatModels[modelIdStr] = [];
            }
            concatModels[modelIdStr] = concatModels[modelIdStr].concat(items);
        }
        for (const [modelId, items] of Object.entries(concatModels)) {
            const uniqueLoan = items.filter(item => item['Finance Type'] === 'LOAN');
            const uniqueInsurance = items.filter(item => item['Insurance Co.'] === 'In-House');
            // validate the sales consultant in our system
            const userId = searchSalesConsultantUser(salesConsultants, key);
            // if dont have userId, should skip that row computation
            if (!userId) {
                continue; // skip if user not found
            }

            if (!result[userId.toHexString()]) {
                result[userId.toHexString()] = {};
            }
            result[userId.toHexString()][modelId] = {
                appliedFinance: uniqueLoan.length,
                appliedInsurance: uniqueInsurance.length,
            };
        }
    }

    return result;
};

const convertingExcelModelByUser = (
    collections: Collections,
    data: Dictionary<StringMap[]>,
    { dataType, flattenModels, salesConsultants, reportingDate }: BoardContext,
    collectWeeklyCount?: boolean
) => {
    const result: FeatureDataActualCount = {};
    const keys = Object.keys(data);
    const modelHeader = getModelHeader(dataType);
    const dateHeader = getDateHeader(dataType);
    const weeks = collectWeeklyCount ? getWeeksInCurrentMonth(reportingDate) : [];

    // for each sales consultant
    for (const key of keys) {
        const vehicleDictionary: FeatureDataActualCount[string] = {};
        const uniqueModels = data[key].map(
            item => [searchVehicleHandler(flattenModels, item[modelHeader]), item] as const
        );

        // validate the sales consultant in our system
        const userId = searchSalesConsultantUser(salesConsultants, key);
        // if dont have userId, should skip that row computation
        if (!userId) {
            continue; // skip if user not found
        }

        const uniqueModelSet = uniqueModels.filter(([found]) => !isNil(found));

        // loop through all entries with valid model
        for (const [modelId] of uniqueModelSet) {
            const id = modelId.toHexString();
            if (!vehicleDictionary[id]) {
                vehicleDictionary[id] = { value: 0 };

                if (collectWeeklyCount) {
                    vehicleDictionary[id].weeks = weeks.map(week => {
                        const filtered = uniqueModelSet.filter(
                            ([currentModelId, row]) =>
                                currentModelId.equals(modelId) &&
                                dayjs(row[dateHeader]).isBetween(
                                    dayjs(week.startDate),
                                    dayjs(week.endDate),
                                    'day',
                                    '[]'
                                )
                        );

                        return {
                            ...week,
                            count: filtered.length,
                        };
                    });
                }
            }

            vehicleDictionary[id].value += 1;
        }

        result[userId.toHexString()] = vehicleDictionary;
    }

    return result;
};

/**
 * get unique model descriptions based on sales consultants
 * @param data data retrieved from the excel sheet
 * @returns [salesConsultant] : [modelDescription]
 */
export const getGroupByFeature = (
    collections: Collections,
    data: StringMap[],
    boardContext: BoardContext,
    collectWeeklyCount?: boolean,
    piData?: StringMap[],
    validData?: StringMap[]
): ConsolidatedFeatureData => {
    const { dataType } = boardContext;
    const header = headersByDataType[dataType].salesConsultant;

    switch (dataType) {
        case SalesControlBoardDataType.Retails: {
            const uniqueModelBySalesConsultant = groupBy(header, data);

            const result = convertingExcelModelByUser(
                collections,
                uniqueModelBySalesConsultant,
                boardContext,
                collectWeeklyCount
            );
            const inHouseFinanceInsurance = retrieveFinanceInsuranceSalesControlBoard(
                collections,
                uniqueModelBySalesConsultant,
                boardContext
            );

            return { actualCount: result, inHouseFinanceInsurance };
        }

        case SalesControlBoardDataType.Leads: {
            const { models, salesConsultants } = uniqSalesConsultantAndModel(collections, validData, boardContext);

            const uniqueModelBySalesConsultant = groupBy(header, data);
            const uniqueModelBasedOnPIBySalesConsultant = groupBy(header, piData);
            const actualCount = convertingExcelModelByUser(
                collections,
                uniqueModelBySalesConsultant,
                boardContext,
                collectWeeklyCount
            );

            const { leadCalculation: leadRatios, piDictionary } = calculateLeadFeatureActual(
                collections,
                uniqueModelBySalesConsultant,
                boardContext,
                uniqueModelBasedOnPIBySalesConsultant,
                salesConsultants,
                models
            );

            return { actualCount, leadRatios, piDictionary };
        }

        case SalesControlBoardDataType.OrderIntakes: {
            const uniqueModelBySalesConsultant = groupBy(header, data);

            const result = convertingExcelModelByUser(
                collections,
                uniqueModelBySalesConsultant,
                boardContext,
                collectWeeklyCount
            );

            return { actualCount: result };
        }

        default:
            throw new Error(`Unsupported SalesControlBoardDataType: ${dataType}`);
    }
};

const uniqSalesConsultantAndModel = (
    collections: Collections,
    data: StringMap[],
    { dataType, flattenModels, salesConsultants: availableSalesConsultants }: BoardContext
) => {
    if (!headersByDataType[dataType]) {
        throw new Error(`Unsupported SalesControlBoardDataType: ${dataType}`);
    }

    const models = uniq(
        Object.keys(groupBy(headersByDataType[dataType].model, data)).map(model =>
            searchVehicleHandler(flattenModels, model)
        )
    );
    const salesConsultants = Object.keys(groupBy(headersByDataType[dataType].salesConsultant, data)).map(
        salesConsultant => searchSalesConsultantUser(availableSalesConsultants, salesConsultant)
    );

    return {
        models: models.filter(Boolean),
        salesConsultants: salesConsultants.filter(Boolean),
    };
};

/**
 * setting up the headers of columns need to be imported
 * @param dataType Type of Sales Control Board Data
 * @returns headers of the Sales Control Board Data Type
 */
export const getHeadersSalesControlBoardDataType = (dataType: SalesControlBoardDataType) => {
    switch (dataType) {
        case SalesControlBoardDataType.Retails:
            return {
                'Comm. No': 'Comm. No',
                'Model Description': 'Model Description',
                'Sales Consultant ?': 'Sales Consultant ?',
                'Date of Reg': 'Date of Reg',
                'Finance Type': 'Finance Type',
                'Insurance Co.': 'Insurance Co.',
            };

        case SalesControlBoardDataType.Leads: {
            return {
                'Purchase Intention': 'Purchase Intention',
                'Responsible Employee': 'Responsible Employee',
                'Lead ID': 'Lead ID',
                'Lead Status': 'Lead Status',
                'Unattended Lead': 'Unattended Lead',
                'Primary Interest': 'Primary Interest',
                'Posting Date': 'Posting Date',
            };
        }

        case SalesControlBoardDataType.OrderIntakes: {
            return {
                'Sales Person': 'Sales Person',
                'Model Type': 'Model Type',
                'Order Intake Date': 'Order Intake Date',
            };
        }

        default:
            throw new Error(`Unsupported SalesControlBoardDataType: ${dataType}`);
    }
};

/**
 * fetching all data of the Sales Control Board
 * @param stream excel FormData stream
 * @param excelOptions Excel options containing headers and start row
 * @param sheetNumber the sheet number to retrieve data from
 *
 * @returns data in the excel sheet as an array of StringMap objects
 */
export const retrieveFeatureExcelData = async (stream: Stream, excelOptions: ExcelOptions, extension: string) => {
    const workbook = await getWorkbook(stream, extension);

    const worksheet = workbook.getWorksheet(1);
    const excelHeaders = Object.values(excelOptions.headers);
    const missingHeaders = getMissingHeaders(worksheet, excelOptions.start, excelHeaders);

    // return error if there's header missing
    if (missingHeaders.length > 0) {
        return [null, `Missing Header/s: ${missingHeaders.join(', ')}. Please use template and re-upload.`];
    }

    const { data } = getTableData(worksheet, excelOptions.headers);

    return data;
};

export const retrieveStartDateOfPeriod = (reportingDate: string, period: 'month' | 'year' | '3months') => {
    switch (period) {
        case 'month':
            return dayjs(reportingDate).startOf('month');

        case 'year':
            return dayjs(reportingDate).startOf('year');

        case '3months':
            return dayjs(reportingDate).subtract(2, 'month').startOf('month');

        default:
            throw new Error(`Unsupported period: ${period}`);
    }
};

export const getFeatureBasedOnDataType = (
    dataType: SalesControlBoardDataType,
    data: StringMap[],
    reportingDate: string,
    period: 'month' | 'year' | '3months',
    forcedHeader?: string
) => {
    if (forcedHeader) {
        return data.filter(row => {
            const startDate = retrieveStartDateOfPeriod(reportingDate, period);
            const currentDate = dayjs(reportingDate).endOf('month');

            return dayjs(row[forcedHeader]).isBetween(startDate, currentDate, 'day', '[]');
        });
    }

    const header = getDateHeader(dataType);

    return data.filter(row => {
        const startDate = retrieveStartDateOfPeriod(reportingDate, period);
        const currentDate = dayjs(reportingDate).endOf('month');

        return dayjs(row[header]).isBetween(startDate, currentDate, 'day', '[]');
    });
};

const hasPreparedDataEntries = (
    dataType: SalesControlBoardDataType,
    monthData: ConsolidatedFeatureData,
    ytdData: ConsolidatedFeatureData,
    salesConsultant: ObjectId,
    modelId: ObjectId
) =>
    monthData.actualCount?.[salesConsultant.toHexString()]?.[modelId.toHexString()] !== undefined ||
    ytdData.actualCount?.[salesConsultant.toHexString()]?.[modelId.toHexString()] !== undefined;

type BoardData = {
    monthData: ConsolidatedFeatureData;
    ytdData: ConsolidatedFeatureData;
    threeMonthsData?: ConsolidatedFeatureData;
};

export const prepareSalesControlBoardDocument = async (
    collections: Collections,
    rawData: StringMap[],
    boardContext: BoardContext,
    { monthData, ytdData, threeMonthsData }: BoardData
): Promise<SalesControlBoard[]> => {
    const { dealerId, dataType, reportingDate } = boardContext;

    const documents: SalesControlBoard[] = [];
    const fallbackWeeksCount = getWeeksInCurrentMonth(reportingDate);

    const { models, salesConsultants } = uniqSalesConsultantAndModel(collections, rawData, boardContext);

    for (const salesConsultant of salesConsultants) {
        for (const modelDescription of models) {
            // if there is no data for the sales consultant and model description, skip this iteration
            if (!hasPreparedDataEntries(dataType, monthData, ytdData, salesConsultant, modelDescription)) {
                continue;
            }

            // skip if document already exists
            if (
                documents.find(
                    doc =>
                        doc.salesConsultantId.equals(salesConsultant) &&
                        doc.modelId.equals(modelDescription) &&
                        doc.dealerId.equals(dealerId) &&
                        doc.importDate === reportingDate &&
                        doc.source === dataType
                )
            ) {
                continue;
            }

            switch (dataType) {
                case SalesControlBoardDataType.Retails: {
                    // create the SalesControlBoardCore object
                    const salesControlBoard: SalesControlBoardRetail = {
                        _id: new ObjectId(),
                        source: SalesControlBoardDataType.Retails,
                        dealerId,
                        importDate: reportingDate,
                        modelId: modelDescription,
                        salesConsultantId: salesConsultant,
                        ytd: {
                            actual:
                                ytdData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                    ?.value || 0,
                            ...ytdData.inHouseFinanceInsurance?.[salesConsultant.toHexString()]?.[
                                modelDescription.toHexString()
                            ],
                        },
                        month: {
                            actual:
                                monthData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                    ?.value || 0,
                            ...monthData.inHouseFinanceInsurance?.[salesConsultant.toHexString()]?.[
                                modelDescription.toHexString()
                            ],
                        },
                        threeMonths: {
                            actual:
                                threeMonthsData.actualCount?.[salesConsultant.toHexString()]?.[
                                    modelDescription.toHexString()
                                ]?.value || 0,
                            ...threeMonthsData?.inHouseFinanceInsurance?.[salesConsultant.toHexString()]?.[
                                modelDescription.toHexString()
                            ],
                        },
                        weeks:
                            monthData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                ?.weeks ?? fallbackWeeksCount,
                    };

                    documents.push(salesControlBoard);
                    break;
                }

                case SalesControlBoardDataType.Leads: {
                    // create the SalesControlBoardCore object
                    const salesControlBoard: SalesControlBoardLead = {
                        _id: new ObjectId(),
                        source: SalesControlBoardDataType.Leads,
                        dealerId,
                        importDate: reportingDate,
                        modelId: modelDescription,
                        salesConsultantId: salesConsultant,
                        ytd: {
                            actual:
                                ytdData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                    ?.value || 0,
                            ...ytdData.piDictionary?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()],
                            ...ytdData.leadRatios?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()],
                        },
                        month: {
                            actual:
                                monthData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                    ?.value || 0,
                            ...monthData.piDictionary?.[salesConsultant.toHexString()]?.[
                                modelDescription.toHexString()
                            ],
                            ...monthData.leadRatios?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()],
                        },

                        weeks:
                            monthData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                ?.weeks ?? fallbackWeeksCount,
                    };

                    documents.push(salesControlBoard);

                    break;
                }

                case SalesControlBoardDataType.OrderIntakes: {
                    const salesControlBoard: SalesControlBoardOrderIntake = {
                        _id: new ObjectId(),
                        source: SalesControlBoardDataType.OrderIntakes,
                        dealerId,
                        importDate: reportingDate,
                        modelId: modelDescription,
                        salesConsultantId: salesConsultant,
                        ytd: {
                            actual:
                                ytdData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                    ?.value || 0,
                        },
                        month: {
                            actual:
                                monthData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                    ?.value || 0,
                        },
                        weeks:
                            monthData.actualCount?.[salesConsultant.toHexString()]?.[modelDescription.toHexString()]
                                ?.weeks ?? fallbackWeeksCount,
                    };

                    documents.push(salesControlBoard);
                    break;
                }

                default:
                    throw new Error(`Unsupported SalesControlBoardDataType: ${dataType}`);
            }
        }
    }

    return documents;
};

/**
 * ===============================
 * LEADS SECTION
 * ===============================
 *
 * All functions and logic below are related to processing Sales Control Board data for Leads.
 * This includes data validation, grouping, and document preparation for lead-type imports.
 */
/**
 *
 * @param collections database collections
 * @param data grouped sales consultants
 * @returns return feature actual count
 */
const calculateLeadFeatureActual = (
    collections: Collections,
    data: Dictionary<StringMap[]>,
    boardContext: BoardContext,
    piData: Dictionary<StringMap[]>,
    allSalesConsultantIds: ObjectId[],
    allModels: ObjectId[]
) => {
    const { flattenModels, reportingDate, salesConsultants } = boardContext;
    const result: Dictionary<Dictionary<{ lio: number; ulr: LeadRatioTuple; olr: LeadRatioTuple }>> = {};
    const piDictionary: Dictionary<Dictionary<{ pi: number }>> = {};
    const keys = Object.keys(data);

    if (!isNil(piData)) {
        const piKeys = Object.keys(piData);

        for (const key of piKeys) {
            // validate the sales consultant in our system
            const userId = searchSalesConsultantUser(salesConsultants, key);
            if (!userId) {
                continue; // skip if user not found
            }

            const uniqueModelBasedOnPIBySalesConsultant = groupBy('Primary Interest', piData[key]);

            const piComputation = Object.entries(uniqueModelBasedOnPIBySalesConsultant).reduce(
                (acc, [modelDescription, items]) => {
                    const modelId = searchVehicleHandler(flattenModels, modelDescription.toString());

                    if (!modelId) {
                        return acc; // skip if model not found
                    }

                    return {
                        ...acc,
                        [modelId.toHexString()]: {
                            // we need accumulate it due to we dont merge the modelID for 718 (Cayman, Boxster, ...)
                            // without accumulate, some 718 value will be lost
                            // in this setting up, it will not affect other 5 models
                            pi: (acc[modelId.toHexString()]?.pi || 0) + items.length,
                        },
                    };
                },
                {} as Dictionary<{
                    pi: number;
                }>
            );

            piDictionary[userId.toHexString()] = piComputation;
        }
    }

    for (const key of keys) {
        // validate the sales consultant in our system
        const userId = searchSalesConsultantUser(salesConsultants, key);

        // if dont have userId, should skip that row computation
        if (!userId) {
            continue; // skip if user not found
        }

        const groupedModels = groupBy('Primary Interest', data[key]);
        // compute each sales consultant and model of number of pi based on `convertedModels`

        const leadCalculation = Object.entries(groupedModels).reduce(
            (acc, [modelDescription, items]) => {
                const modelId = searchVehicleHandler(flattenModels, modelDescription.toString());

                if (!modelId) {
                    return acc; // skip if model not found
                }

                // Lead Status must be ‘In Process’ or ‘Open’
                const lio: number =
                    items.filter(item => item['Lead Status'] === 'In Process' || item['Lead Status'] === 'Open')
                        .length || 0;

                // No. of unattended leads (column BM with value = x) divided by
                // No. of leads with Lead Status (K) = “In process” or “Open”
                const ulr: LeadRatioTuple = {
                    numerator: items.filter(item => item['Unattended Lead'] === 'x').length || 0,
                    denominator: lio || 1, // avoid division by zero
                };

                const olr: LeadRatioTuple = {
                    numerator: items.filter(
                        item =>
                            (item['Lead Status'] === 'In Process' || item['Lead Status'] === 'Open') &&
                            dayjs(item['Purchase Intention']).isBefore(dayjs(reportingDate), 'month')
                    ).length,
                    denominator: lio || 1, // avoid division by zero
                };

                return {
                    ...acc,
                    [modelId.toHexString()]: {
                        // we need accumulate it due to we dont merge the modelID for 718 (Cayman, Boxster, ...)
                        // without accumulate, some 718 value will be lost
                        // in this setting up, it will not affect other 5 models
                        lio: (acc[modelId.toHexString()]?.lio || 0) + lio,
                        ulr: {
                            numerator: (acc[modelId.toHexString()]?.ulr?.numerator || 0) + ulr.numerator,
                            denominator: (acc[modelId.toHexString()]?.ulr?.denominator || 0) + ulr.denominator,
                        },
                        olr: {
                            numerator: (acc[modelId.toHexString()]?.olr?.numerator || 0) + olr.numerator,
                            denominator: (acc[modelId.toHexString()]?.olr?.denominator || 0) + olr.denominator,
                        },
                    },
                };
            },
            {} as Dictionary<{
                lio: number;
                ulr: LeadRatioTuple;
                olr: LeadRatioTuple;
            }>
        );

        result[userId.toHexString()] = leadCalculation;
    }

    return { leadCalculation: result, piDictionary };
};

import { ObjectId } from 'mongodb';
import type { Condition } from '../../../../database/documents/Conditions';
import type {
    GroupConsentsAndDeclarations,
    ConsentsAndDeclarations,
} from '../../../../database/documents/ConsentsAndDeclarations';
import { DataField } from '../../../../database/documents/ConsentsAndDeclarations';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { AgreementPolicyAction } from '../../../../permissions';
import {
    getAdvancedVersioningByUserForCreation,
    getAdvancedVersioningByUserForUpdate,
    getSimpleVersioningByUserForUpdate,
} from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { ConsentsAndDeclarationsType, type GraphQLMutationResolvers } from '../../definitions';
import { checkAllConditionSettings } from './checkConditionSettings';
import generateConditions from './generateConditions';
import { getConsentAndModuleFromSuiteId, createChildConsent, updateChildConsent } from './shared';

const mutation: GraphQLMutationResolvers['updateGroupConsentsAndDeclarations'] = async (
    root,
    { suiteId, settings, conditionSettings },
    { getUser, getPermissionController, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const userData = await getUser();
    const versioning = getSimpleVersioningByUserForUpdate(userData._id);
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['errors']);

    const { consent: groupConsent, consentModule } = await getConsentAndModuleFromSuiteId(suiteId);

    if (groupConsent._type !== ConsentsAndDeclarationsType.Group) {
        throw new InvalidInput({ suiteId: t('errors:consentsAndDeclarations.notGroupConsent') });
    }

    if (!permissionController.agreements.mayOperateOn(groupConsent, AgreementPolicyAction.Update, consentModule)) {
        throw new InvalidPermission();
    }

    let conditions: Condition[] = [];
    if (conditionSettings?.length > 0) {
        const isConditionSettingsValid = checkAllConditionSettings(conditionSettings);

        if (!isConditionSettingsValid) {
            throw new InvalidInput({ conditionSettings: t('errors:consentsAndDeclarations.invalidConditionSettings') });
        } else {
            conditions = conditionSettings.map(setting => generateConditions(setting));
        }
    }

    const { children, ...groupSettings } = settings;

    const result = await collections.consentsAndDeclarations.findOneAndUpdate(
        { '_versioning.suiteId': suiteId, '_versioning.isLatest': true },
        { $set: { '_versioning.isLatest': false, ...versioning } },
        { returnDocument: 'after' }
    );

    if (!result) {
        return null;
    }

    const user = await getUser();
    const createVersioning = getAdvancedVersioningByUserForCreation(user._id, result._versioning.suiteId);

    const updatedGroupDocument: GroupConsentsAndDeclarations = {
        ...result,
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Group,
        ...groupSettings,
        conditions,
        _versioning: createVersioning,
    };

    await collections.consentsAndDeclarations.insertOne(updatedGroupDocument);

    // Get existing child consents
    const existingChildren = await collections.consentsAndDeclarations
        .find({
            parentId: result._id,
            isDeleted: false,
            '_versioning.isLatest': true,
        })
        .toArray();

    // Process child consents
    const processedChildIds = new Set<string>();
    const updatePromises: Promise<ConsentsAndDeclarations>[] = [];
    const createPromises: Promise<ConsentsAndDeclarations>[] = [];

    // Separate updates and creates
    for (const child of children) {
        if (child.id) {
            // Update existing child
            const existingChild = existingChildren.find(c => c._id.equals(child.id!));
            if (existingChild) {
                updatePromises.push(
                    updateChildConsent({
                        child,
                        existingConsent: existingChild,
                        user,
                        parentConditions: conditions,
                    })
                );
                processedChildIds.add(child.id.toString());
            }
        } else {
            // Prepare new child for creation
            createPromises.push(
                createChildConsent({
                    child,
                    moduleId: updatedGroupDocument.moduleId,
                    parentId: updatedGroupDocument._id,
                    user,
                    featurePurpose: updatedGroupDocument.featurePurpose,
                    parentConditions: conditions,
                })
            );
        }
    }

    // Execute all updates and creates in parallel
    const [updatedChildDocuments, newChildDocuments] = await Promise.all([
        Promise.all(updatePromises),
        Promise.all(createPromises),
    ]);

    // Insert new children
    if (newChildDocuments.length > 0) {
        await collections.consentsAndDeclarations.insertMany(newChildDocuments);
    }

    if (updatedChildDocuments.length > 0) {
        // Update parentId for all updated child documents to point to new group document
        const updatedChildrenWithNewParentId = updatedChildDocuments.map(child => ({
            ...child,
            parentId: updatedGroupDocument._id,
        }));

        await collections.consentsAndDeclarations.insertMany(updatedChildrenWithNewParentId);
    }

    // Mark unprocessed children as deleted (following versioning pattern)
    const childrenToDelete = existingChildren.filter(
        existingChild => !processedChildIds.has(existingChild._id.toString())
    );

    if (childrenToDelete.length > 0) {
        await Promise.all(
            childrenToDelete.map(async existingChild => {
                // Mark current document as not latest
                const result = await collections.consentsAndDeclarations.findOneAndUpdate(
                    { '_versioning.suiteId': existingChild._versioning.suiteId, '_versioning.isLatest': true },
                    { $set: { '_versioning.isLatest': false, ...getAdvancedVersioningByUserForUpdate(user._id) } },
                    { returnDocument: 'after' }
                );

                if (result) {
                    // Create new document with same suiteId but marked as deleted
                    const createVersioning = getAdvancedVersioningByUserForCreation(
                        user._id,
                        result._versioning.suiteId
                    );

                    const deletedDocument: ConsentsAndDeclarations = {
                        ...result,
                        _id: new ObjectId(),
                        parentId: updatedGroupDocument._id,
                        isDeleted: true,
                        _versioning: createVersioning,
                    };

                    await collections.consentsAndDeclarations.insertOne(deletedDocument);
                }
            })
        );
    }

    return updatedGroupDocument;
};

export default requiresLoggedUser(mutation);

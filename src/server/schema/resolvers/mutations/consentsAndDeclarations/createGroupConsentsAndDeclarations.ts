import { isNil } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import type { Condition } from '../../../../database/documents/Conditions';
import type {
    GroupConsentsAndDeclarations,
    ConsentFeaturePurpose,
} from '../../../../database/documents/ConsentsAndDeclarations';
import { ConsentFeatureType, DataField } from '../../../../database/documents/ConsentsAndDeclarations';
import { ModuleType } from '../../../../database/documents/modules';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { ModulePolicyAction } from '../../../../permissions';
import { getAdvancedVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { buildRateLimiterMiddleware, requiresLoggedUser } from '../../../middlewares';
import { ConsentsAndDeclarationsType, GraphQLMutationResolvers } from '../../definitions';
import { checkAllConditionSettings } from './checkConditionSettings';
import generateConditions from './generateConditions';
import { createChildConsent } from './shared';

const mutation: GraphQLMutationResolvers['createGroupConsentsAndDeclarations'] = async (
    root,
    { moduleId, settings, conditionSettings, eventId },
    { getPermissionController, getUser, getTranslations }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const { t } = await getTranslations(['errors']);

    const module = await collections.modules.findOne({ _id: moduleId });

    if (!(module?._type === ModuleType.ConsentsAndDeclarations)) {
        throw new InvalidInput({ moduleId: t('errors:consentsAndDeclarations.invalidModuleId') });
    }

    if (!permissionController.modules.mayOperateOn(module, ModulePolicyAction.CreateAgreement)) {
        throw new InvalidPermission();
    }

    const { children, ...groupSettings } = settings;

    // Create conditions for the group
    let conditions: Condition[] = [];

    if (conditionSettings?.length > 0) {
        const isConditionSettingsValid = checkAllConditionSettings(conditionSettings);
        if (!isConditionSettingsValid) {
            throw new InvalidInput({ conditionSettings: t('errors:consentsAndDeclarations.invalidConditionSettings') });
        }
        conditions = conditionSettings.map(setting => generateConditions(setting));
    }

    const featurePurpose: ConsentFeaturePurpose = !isNil(eventId)
        ? { type: ConsentFeatureType.Event, featureId: eventId }
        : { type: ConsentFeatureType.Module, featureId: moduleId };

    const user = await getUser();

    // Create the group document
    const groupDocument: GroupConsentsAndDeclarations = {
        _id: new ObjectId(),
        _type: ConsentsAndDeclarationsType.Group,
        moduleId,
        ...groupSettings,
        dataField: DataField.None, // Groups don't have their own dataField, it's handled by children
        isDeleted: false,
        conditions,
        _versioning: getAdvancedVersioningByUserForCreation(user._id),
        featurePurpose,
    };

    // Insert the group first
    await collections.consentsAndDeclarations.insertOne(groupDocument);

    // Create child consents with parent conditions
    const childDocuments = await Promise.all(
        children.map(child =>
            createChildConsent({
                child,
                moduleId,
                parentId: groupDocument._id,
                user,
                featurePurpose,
                parentConditions: conditions,
            })
        )
    );

    // Insert all child documents
    if (childDocuments.length > 0) {
        await collections.consentsAndDeclarations.insertMany(childDocuments);
    }

    return groupDocument;
};

export default buildRateLimiterMiddleware({ operation: 'createGroupConsentsAndDeclarations' })(
    requiresLoggedUser(mutation)
);

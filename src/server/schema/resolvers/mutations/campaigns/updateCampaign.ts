import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['updateCampaign'] = async (
    root,
    { id, settings },
    { getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const user = await getUser();

    const versioning = getSimpleVersioningByUserForUpdate(user._id);

    const updatedCampaign = await collections.campaigns.findOneAndUpdate(
        {
            $and: [
                { _id: id, isDeleted: false },
                permissionController.campaigns.getFilterQueryForAction(CampaignPolicyAction.Update),
            ],
        },
        {
            $set: {
                ...settings,
                ...versioning,
            },
        },
        { returnDocument: 'after' }
    );

    if (!updatedCampaign) {
        throw new InvalidPermission();
    }

    return updatedCampaign;
};

export default requiresLoggedUser(mutation);

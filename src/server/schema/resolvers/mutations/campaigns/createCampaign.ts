import { ObjectId } from 'mongodb';
import type { Campaign } from '../../../../database/documents/Campaign';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CampaignPolicyAction } from '../../../../permissions/types/campaigns';
import { getSimpleVersioningByUserForCreation } from '../../../../utils/versioning';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const mutation: GraphQLMutationResolvers['createCampaign'] = async (
    root,
    { companyId, settings },
    { getPermissionController, getUser }
) => {
    const { collections } = await getDatabaseContext();
    const permissionController = await getPermissionController();
    const user = await getUser();

    if (!permissionController.campaigns.hasCompanyPolicyForAction(CampaignPolicyAction.Create, companyId)) {
        throw new InvalidPermission();
    }

    const versioning = getSimpleVersioningByUserForCreation(user._id);

    const campaign: Campaign = {
        _id: new ObjectId(),
        companyId,
        ...settings,
        isDeleted: false,
        _versioning: versioning,
    };

    await collections.campaigns.insertOne(campaign);

    return campaign;
};

export default requiresLoggedUser(mutation);

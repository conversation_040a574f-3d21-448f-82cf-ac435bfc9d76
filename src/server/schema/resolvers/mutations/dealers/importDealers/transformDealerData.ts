import { type CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';
import { isEmpty } from 'lodash/fp';
import { ObjectId } from 'mongodb';
import type { Dealer } from '../../../../../database/documents/Dealer';
import type { TranslatedString } from '../../../../../database/documents/LanguagePack';
import type { SimpleVersioning } from '../../../../../database/documents/Versioning';
import type { DealerExcelRow } from './types';

const getTranslatedStringData = (fieldName: string, row: DealerExcelRow): TranslatedString => {
    const defaultValue = (row[fieldName] as string) || '';
    const fieldPrefix = `${fieldName}.`;

    const translations = Object.entries(row)
        .filter(([key]) => key.startsWith(fieldPrefix))
        .map(([key, value]) => {
            const languagePackId = key.slice(fieldPrefix.length);

            if (!languagePackId || !ObjectId.isValid(languagePackId) || isEmpty(value)) {
                return null;
            }

            return {
                languagePackId: new ObjectId(languagePackId),
                value: String(value).trim(),
            };
        })
        .filter(Boolean);

    return {
        defaultValue: defaultValue.trim(),
        overrides: translations,
    };
};

const transformPhoneNumber = (telephone: string, countryCode: CountryCode): Dealer['contact']['telephone'] => {
    if (!telephone || telephone.trim() === '') {
        return undefined;
    }

    try {
        const phoneNumber = parsePhoneNumberFromString(telephone.trim(), countryCode);

        if (phoneNumber && phoneNumber.isValid()) {
            return {
                value: phoneNumber.formatNational(),
                prefix: Number(phoneNumber.countryCallingCode),
            };
        }

        return { value: telephone.trim(), prefix: undefined };
    } catch {
        return { value: telephone.trim(), prefix: undefined };
    }
};

type TransformDealerDataParams = {
    row: DealerExcelRow;
    companyId: ObjectId;
    versioning: SimpleVersioning;
    countryCode: string;
};

const transformExcelRowToDealerData = ({
    row,
    companyId,
    versioning,
    countryCode,
}: TransformDealerDataParams): Dealer => {
    const { displayName, limitFeatures, isActive, telephone, email, latitude, longitude, dealerCode } = row;

    return {
        _id: new ObjectId(),
        companyId,
        displayName: displayName?.trim() || '',
        legalName: getTranslatedStringData('legalName', row),
        isActive: isActive?.toUpperCase() === 'YES' || false,
        coe: 0,
        ppsr: 0,
        estFee: 0,
        location: {
            type: 'Point',
            coordinates: [Number(longitude) || 0, Number(latitude) || 0],
        },
        contact: {
            telephone: telephone ? transformPhoneNumber(String(telephone), countryCode as CountryCode) : undefined,
            email: email?.trim() || '',
            address: getTranslatedStringData('address', row),
            additionalInfo: getTranslatedStringData('additionalInformation', row),
            socialMedia: [],
        },
        integrationDetails: {
            dealerCode: dealerCode ? String(dealerCode) : undefined,
            partnerNumber: '',
            assortment: '',
            additionalParameter: [{ purpose: '', value: '' }],
        },
        _versioning: versioning,
        isDeleted: false,
        limitFeature: limitFeatures?.toUpperCase() === 'YES' || false,
    };
};

export { transformExcelRowToDealerData, getTranslatedStringData };

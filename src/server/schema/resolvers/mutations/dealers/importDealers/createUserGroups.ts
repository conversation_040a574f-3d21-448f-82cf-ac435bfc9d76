import { ObjectId } from 'mongodb';
import type { Company } from '../../../../../database/documents/Company';
import type { Dealer } from '../../../../../database/documents/Dealer';
import type { UserGroup } from '../../../../../database/documents/UserGroup';
import type { SimpleVersioning } from '../../../../../database/documents/Versioning';
import getDatabaseContext from '../../../../../database/getDatabaseContext';

type Params = {
    newDealers: Dealer[];
    company: Company;
    simpleVersioning: SimpleVersioning;
};

/**
 * Finds an existing Market Admin user group that contains all pre-existing dealers (excluding new ones).
 * A Market Admin user group is identified by having all existing dealers assigned to it.
 */
const getCompanyExistingMarketAdminUserGroup = async ({
    company,
    newDealers,
}: Pick<Params, 'company' | 'newDealers'>): Promise<UserGroup | null> => {
    const { collections } = await getDatabaseContext();

    const newDealerIds = newDealers.map(dealer => dealer._id);

    const existingDealers = await collections.dealers
        .find({ companyId: company._id, isDeleted: false, _id: { $nin: newDealerIds } }, { projection: { _id: 1 } })
        .toArray();

    if (!existingDealers.length) {
        return null;
    }

    const existingDealerIds = existingDealers.map(dealer => dealer._id);

    const marketAdminUserGroup = await collections.userGroups.findOne<UserGroup>({
        companyId: company._id,
        dealerIds: { $all: existingDealerIds },
    });

    return marketAdminUserGroup;
};

/**
 * Creates a new Market Admin user group or updates an existing one with new dealers.
 * The Market Admin user group contains all dealers in the company and has parallel structure.
 */
const createMarketAdminUserGroup = async (params: Params): Promise<{ count: number; userGroupId: ObjectId | null }> => {
    const { newDealers, company, simpleVersioning } = params;
    const { collections } = await getDatabaseContext();

    const existingMarketAdminUserGroup = await getCompanyExistingMarketAdminUserGroup(params);

    const newDealerIds = newDealers.map(dealer => dealer._id);

    if (existingMarketAdminUserGroup) {
        await collections.userGroups.updateOne(
            { _id: existingMarketAdminUserGroup._id },
            { $addToSet: { dealerIds: { $each: newDealerIds } } }
        );

        return { count: 0, userGroupId: existingMarketAdminUserGroup._id };
    }

    const allDealers = await collections.dealers
        .find({ companyId: company._id, isDeleted: false }, { projection: { _id: 1 } })
        .toArray();

    const allDealerIds = allDealers.map(dealer => dealer._id);

    const newUserGroup: UserGroup = {
        _id: new ObjectId(),
        companyId: company._id,
        displayName: `${company.countryCode} Administrator`,
        description: `${company.countryCode} Administrator`,
        isActive: true,
        isParallel: true,
        userIds: [],
        dealerIds: allDealerIds,
        superiorUserGroupIds: [],
        _versioning: simpleVersioning,
    };

    await collections.userGroups.insertOne(newUserGroup);

    return { count: 1, userGroupId: newUserGroup._id };
};

/**
 * Creates Dealer Manager user groups for new dealers.
 * Each dealer gets its own Dealer Manager user group with the Market Admin group as parent.
 */
const createDealerManagerUserGroups = async (
    params: Params,
    marketAdminUserGroupId: ObjectId | null
): Promise<{ count: number; dealerManagerUserGroupIds: Map<string, ObjectId> }> => {
    const { newDealers, company, simpleVersioning } = params;
    const { collections } = await getDatabaseContext();

    const superiorUserGroupIds = marketAdminUserGroupId ? [marketAdminUserGroupId] : [];

    const dealerManagerUserGroups: UserGroup[] = newDealers.map(dealer => ({
        _id: new ObjectId(),
        companyId: company._id,
        displayName: `${company.countryCode} Dealer Manager - ${dealer.displayName}`,
        description: `${company.countryCode} Dealer Manager - ${dealer.displayName}`,
        isActive: true,
        isParallel: true,
        userIds: [],
        dealerIds: [dealer._id],
        superiorUserGroupIds,
        _versioning: simpleVersioning,
    }));

    const dealerManagerUserGroupIds = new Map<string, ObjectId>();

    newDealers.forEach((dealer, index) => {
        dealerManagerUserGroupIds.set(dealer._id.toString(), dealerManagerUserGroups[index]._id);
    });

    if (dealerManagerUserGroups.length > 0) {
        await collections.userGroups.insertMany(dealerManagerUserGroups);

        return { count: dealerManagerUserGroups.length, dealerManagerUserGroupIds };
    }

    return { count: 0, dealerManagerUserGroupIds };
};

/**
 * Creates Sales Consultant user groups for new dealers.
 * Each dealer gets its own Sales Consultant user group with the respective Dealer Manager group as parent.
 * The Sales Consultant groups have a non-parallel structure.
 */
const createSalesConsultantUserGroups = async (
    params: Params,
    dealerManagerUserGroupIds: Map<string, ObjectId>
): Promise<number> => {
    const { newDealers, company, simpleVersioning } = params;
    const { collections } = await getDatabaseContext();

    const salesConsultantUserGroups: UserGroup[] = newDealers.map(dealer => {
        const dealerManagerUserGroupId = dealerManagerUserGroupIds.get(dealer._id.toString());
        const superiorUserGroupIds = dealerManagerUserGroupId ? [dealerManagerUserGroupId] : [];

        return {
            _id: new ObjectId(),
            companyId: company._id,
            displayName: `${company.countryCode} Sales Consultant - ${dealer.displayName}`,
            description: `${company.countryCode} Sales Consultant - ${dealer.displayName}`,
            isActive: true,
            isParallel: false,
            userIds: [],
            dealerIds: [dealer._id],
            superiorUserGroupIds,
            _versioning: simpleVersioning,
        };
    });

    if (salesConsultantUserGroups.length > 0) {
        await collections.userGroups.insertMany(salesConsultantUserGroups);

        return salesConsultantUserGroups.length;
    }

    return 0;
};

const createDealerUserGroups = async (params: Params): Promise<number> => {
    const marketAdminResult = await createMarketAdminUserGroup(params);
    const dealerManagerResult = await createDealerManagerUserGroups(params, marketAdminResult.userGroupId);
    const salesConsultantUserGroupsCount = await createSalesConsultantUserGroups(
        params,
        dealerManagerResult.dealerManagerUserGroupIds
    );

    return marketAdminResult.count + dealerManagerResult.count + salesConsultantUserGroupsCount;
};

export default createDealerUserGroups;

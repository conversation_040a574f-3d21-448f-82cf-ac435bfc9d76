import validators from '../../../../../utils/validators';

const yesNoValidator = (field: string, message?: string) =>
    validators.custom(field, (value, values, errors, context) => {
        if (!value) {
            return message || `${field} is required`;
        }

        const upperValue = String(value).toUpperCase();

        if (!['YES', 'NO'].includes(upperValue)) {
            return message || `${field} must be Yes or No`;
        }

        return null;
    });

const dealerRowDataValidator = validators.compose(
    validators.requiredString('displayName', 'Display Name is required'),
    validators.requiredString('legalName', 'Legal Name is required'),
    yesNoValidator('limitFeatures', 'Limit Features must be Yes or No'),
    yesNoValidator('isActive', 'Active status must be Yes or No'),
    validators.validEmail('email'),
    validators.requiredNumber('latitude', 'Latitude is required'),
    validators.validNumberRange('latitude', -90, 90, 'Latitude must be between -90 and 90'),
    validators.requiredNumber('longitude', 'Longitude is required'),
    validators.validNumberRange('longitude', -180, 180, 'Longitude must be between -180 and 180')
);

const validateDealerData = (data: Record<string, unknown>[]) => {
    const errors: Record<string, string[]> = {};

    data.forEach((row, index) => {
        const rowErrors = dealerRowDataValidator.validate(row);
        if (!rowErrors || Object.keys(rowErrors).length === 0) {
            return;
        }

        errors[`Row ${index + 1}`] = Object.values(rowErrors);
    });

    return errors;
};

export default validateDealerData;

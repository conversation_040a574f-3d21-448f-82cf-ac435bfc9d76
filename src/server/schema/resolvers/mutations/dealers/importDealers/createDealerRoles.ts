import { ObjectId } from 'mongodb';
import type { Company } from '../../../../../database/documents/Company';
import type { Dealer } from '../../../../../database/documents/Dealer';
import type { Role } from '../../../../../database/documents/Permissions';
import type { SimpleVersioning } from '../../../../../database/documents/Versioning';
import { ModuleType } from '../../../../../database/documents/modules';
import getDatabaseContext from '../../../../../database/getDatabaseContext';
import { getDealerSystemName } from '../../../../../permissions/dealer';
import { getCompanySystemName, getModuleSystemName } from '../../../../../permissions/shared';

type Params = {
    newDealers: Dealer[];
    company: Company;
    versioning: SimpleVersioning;
};

const getPermissionIdsForRoleType = async (
    company: Company,
    roleType: 'marketAdmin' | 'dealerManager' | 'salesConsultant'
): Promise<ObjectId[]> => {
    const { collections } = await getDatabaseContext();

    const companySystemName = getCompanySystemName(company._id);

    const systemNamePatterns: string[] = [];

    if (roleType === 'marketAdmin' || roleType === 'dealerManager') {
        systemNamePatterns.push(
            `${companySystemName}:company/manage-users`,
            `${companySystemName}:company/view-roles`,
            `${companySystemName}:company/view-user-groups`
        );
    }

    const relevantModuleTypes = [
        ModuleType.ConsentsAndDeclarations,
        ModuleType.SimpleVehicleManagement,
        ModuleType.LocalCustomerManagement,
        ModuleType.LaunchPadModule,
        ModuleType.AppointmentModule,
        ModuleType.VisitAppointmentModule,
        ModuleType.EventApplicationModule,
    ];

    const modules = await collections.modules
        .find(
            { companyId: company._id, _type: { $in: relevantModuleTypes } },
            { projection: { _id: 1, _type: 1, companyId: 1 } }
        )
        .toArray();

    const modulePermissions = modules.flatMap(module => {
        const moduleSystemName = getModuleSystemName(company._id, module._id);

        switch (module._type) {
            case ModuleType.ConsentsAndDeclarations:
                return roleType === 'marketAdmin' ? [`${moduleSystemName}/agreement/full`] : [];

            case ModuleType.SimpleVehicleManagement:
                return roleType === 'marketAdmin' ? [`${moduleSystemName}/vehicle/full`] : [];

            case ModuleType.LocalCustomerManagement:
                if (roleType === 'marketAdmin' || roleType === 'dealerManager') {
                    return [`${moduleSystemName}/customerUpdate`];
                }

                if (roleType === 'salesConsultant') {
                    return [`${moduleSystemName}/customerView`];
                }

                return [];

            case ModuleType.LaunchPadModule:
                if (roleType === 'marketAdmin') {
                    return [
                        `${moduleSystemName}/lead/manageAllLead`,
                        `${moduleSystemName}/applications/manageAllRequest`,
                        `${moduleSystemName}/lead/manageContact`,
                        `${moduleSystemName}/lead/createContact`,
                        `${moduleSystemName}/lead/createLead`,
                        `${moduleSystemName}/lead/createFollowUp`,
                        `${moduleSystemName}/lead/createShowroomVisit`,
                        `${moduleSystemName}/lead/createTestDrive`,
                    ];
                }

                if (roleType === 'dealerManager') {
                    return [
                        `${moduleSystemName}/applications/manageAllRequestFromHierarchy`,
                        `${moduleSystemName}/lead/manageLeadFromHierarchy`,
                        `${moduleSystemName}/requestManageFromHierarchy`,
                        `${moduleSystemName}/lead/manageContactFromHierarchy`,
                        `${moduleSystemName}/lead/createContact`,
                        `${moduleSystemName}/lead/createLead`,
                        `${moduleSystemName}/lead/createFollowUp`,
                        `${moduleSystemName}/lead/createShowroomVisit`,
                        `${moduleSystemName}/lead/createTestDrive`,
                    ];
                }

                if (roleType === 'salesConsultant') {
                    return [
                        `${moduleSystemName}/applications/manageAllRequestFromHierarchy`,
                        `${moduleSystemName}/lead/manageOwnLead`,
                        `${moduleSystemName}/lead/manageOwnContact`,
                        `${moduleSystemName}/lead/createContact`,
                        `${moduleSystemName}/lead/createLead`,
                        `${moduleSystemName}/lead/createFollowUp`,
                        `${moduleSystemName}/lead/createShowroomVisit`,
                        `${moduleSystemName}/lead/createTestDrive`,
                    ];
                }

                return [];

            case ModuleType.AppointmentModule:
                if (roleType === 'marketAdmin') {
                    return [`${moduleSystemName}/applications/manageAllAppointment`];
                }

                return [`${moduleSystemName}/applications/manageAppointmentFromHierarchy`];

            case ModuleType.VisitAppointmentModule:
                if (roleType === 'marketAdmin') {
                    return [`${moduleSystemName}/applications/manageAllAppointment`];
                }

                return [`${moduleSystemName}/applications/manageAppointmentFromHierarchy`];

            case ModuleType.EventApplicationModule: {
                const basePermissions = [`${moduleSystemName}/createEventApplication`];

                if (roleType === 'marketAdmin') {
                    return [
                        ...basePermissions,
                        `${moduleSystemName}/applications/manageAll`,
                        `${moduleSystemName}/eventFull`,
                    ];
                }

                if (roleType === 'dealerManager' || roleType === 'salesConsultant') {
                    return [
                        ...basePermissions,
                        `${moduleSystemName}/applications/manageAllFromHierarchy`,
                        `${moduleSystemName}/eventView`,
                    ];
                }

                return basePermissions;
            }

            default:
                return [];
        }
    });

    systemNamePatterns.push(...modulePermissions);

    const permissions = await collections.permissions
        .find({ systemName: { $in: systemNamePatterns } }, { projection: { _id: 1 } })
        .toArray();

    return permissions.map(permission => permission._id);
};

/**
 * Finds an existing Market Admin role that contains permissions for all pre-existing dealers.
 * A Market Admin role is identified by having dealer update permissions for all existing dealers.
 */
const getCompanyExistingMarketAdminRole = async ({
    company,
    newDealers,
}: Pick<Params, 'company' | 'newDealers'>): Promise<Role | null> => {
    const { collections } = await getDatabaseContext();

    const newDealerIds = newDealers.map(dealer => dealer._id);

    const existingDealers = await collections.dealers
        .find({ companyId: company._id, isDeleted: false, _id: { $nin: newDealerIds } })
        .toArray();

    if (!existingDealers.length) {
        return null;
    }

    const existingDealerSystemNames = existingDealers.map(dealer => `${getDealerSystemName(dealer._id)}/update`);

    const requiredPermissions = await collections.permissions
        .find({ systemName: { $in: existingDealerSystemNames } }, { projection: { _id: 1 } })
        .toArray();

    if (!requiredPermissions.length) {
        return null;
    }

    const marketAdminRole = await collections.roles.findOne<Role>({
        companyId: company._id,
        isDeleted: false,
        permissionIds: { $all: requiredPermissions.map(permission => permission._id) },
    });

    return marketAdminRole;
};

/**
 * Creates a new Market Admin role or updates an existing one with permissions for new dealers.
 * The Market Admin role has comprehensive permissions across all company modules and dealers.
 */
const createMarketAdminRole = async (params: Params): Promise<number> => {
    const { newDealers, company, versioning } = params;
    const { collections } = await getDatabaseContext();

    const existingMarketAdminRole = await getCompanyExistingMarketAdminRole(params);

    const newDealerIds = newDealers.map(dealer => dealer._id);
    const expectedSystemNames = newDealerIds.map(dealerId => `${getDealerSystemName(dealerId)}/update`);

    const newDealerUpdatePermissions = await collections.permissions
        .find({ systemName: { $in: expectedSystemNames } })
        .toArray();

    const newPermissionIds = newDealerUpdatePermissions.map(permission => permission._id);

    if (existingMarketAdminRole) {
        await collections.roles.updateOne(
            { _id: existingMarketAdminRole._id },
            { $addToSet: { permissionIds: { $each: newPermissionIds } } }
        );

        return 0;
    }

    const basePermissionIds = await getPermissionIdsForRoleType(company, 'marketAdmin');
    const allPermissionIds = [...basePermissionIds, ...newPermissionIds];

    const newRole: Role = {
        _id: new ObjectId(),
        companyId: company._id,
        displayName: `${company.countryCode} Administrator`,
        description: `${company.countryCode} Administrator`,
        isDeleted: false,
        isActive: true,
        userIds: [],
        permissionIds: allPermissionIds,
        _versioning: versioning,
    };

    await collections.roles.insertOne(newRole);

    return 1;
};

/**
 * Creates a Sales Consultant role for the company if one doesn't already exist.
 * The Sales Consultant role has limited permissions and excludes dealer update permissions.
 */
const createSalesConsultantRole = async (params: Params): Promise<number> => {
    const { company, versioning } = params;
    const { collections } = await getDatabaseContext();

    const allDealers = await collections.dealers
        .find({ companyId: company._id, isDeleted: false }, { projection: { _id: 1 } })
        .toArray();

    const dealerUpdateSystemNames = allDealers.map(dealer => `${getDealerSystemName(dealer._id)}/update`);

    const dealerUpdatePermissions = await collections.permissions
        .find({ systemName: { $in: dealerUpdateSystemNames } }, { projection: { _id: 1 } })
        .toArray();

    const dealerUpdatePermissionIds = dealerUpdatePermissions.map(permission => permission._id);

    const existingSCRole = await collections.roles.findOne({
        companyId: company._id,
        isDeleted: false,
        permissionIds: { $not: { $elemMatch: { $in: dealerUpdatePermissionIds } } },
    });

    if (existingSCRole) {
        return 0;
    }

    const scPermissionIds = await getPermissionIdsForRoleType(company, 'salesConsultant');

    const scRole: Role = {
        _id: new ObjectId(),
        companyId: company._id,
        displayName: `${company.countryCode} Sales Consultant`,
        description: `${company.countryCode} Sales Consultant`,
        isDeleted: false,
        isActive: true,
        userIds: [],
        permissionIds: scPermissionIds,
        _versioning: versioning,
    };

    await collections.roles.insertOne(scRole);

    return 1;
};

/**
 * Creates Dealer Manager roles for new dealers.
 * Each dealer gets its own Dealer Manager role with hierarchical permissions and dealer-specific update rights.
 */
const createDealerManagerRoles = async (params: Params): Promise<number> => {
    const { newDealers, company, versioning } = params;
    const { collections } = await getDatabaseContext();

    const baseDealerManagerPermissionIds = await getPermissionIdsForRoleType(company, 'dealerManager');

    const dealerSystemNames = newDealers.map(dealer => `${getDealerSystemName(dealer._id)}/update`);
    const dealerUpdatePermissions = await collections.permissions
        .find({ systemName: { $in: dealerSystemNames } }, { projection: { _id: 1, systemName: 1 } })
        .toArray();

    const permissionMap = new Map(dealerUpdatePermissions.map(permission => [permission.systemName, permission._id]));

    const dealerManagerRoles: Role[] = newDealers.map(dealer => {
        const dealerSystemName = `${getDealerSystemName(dealer._id)}/update`;
        const dealerPermissionId = permissionMap.get(dealerSystemName);

        const allPermissionIds = dealerPermissionId
            ? [...baseDealerManagerPermissionIds, dealerPermissionId]
            : baseDealerManagerPermissionIds;

        return {
            _id: new ObjectId(),
            companyId: company._id,
            displayName: `${company.countryCode} Dealer Manager - ${dealer.displayName}`,
            description: `${company.countryCode} Dealer Manager - ${dealer.displayName}`,
            isDeleted: false,
            isActive: true,
            userIds: [],
            permissionIds: allPermissionIds,
            _versioning: versioning,
        };
    });

    if (dealerManagerRoles.length > 0) {
        await collections.roles.insertMany(dealerManagerRoles);

        return dealerManagerRoles.length;
    }

    return 0;
};

const createDealersRoles = async (params: Params): Promise<number> => {
    const marketAdminRolesCount = await createMarketAdminRole(params);
    const salesConsultantRolesCount = await createSalesConsultantRole(params);
    const dealerManagerRolesCount = await createDealerManagerRoles(params);

    return marketAdminRolesCount + salesConsultantRolesCount + dealerManagerRolesCount;
};

export default createDealersRoles;

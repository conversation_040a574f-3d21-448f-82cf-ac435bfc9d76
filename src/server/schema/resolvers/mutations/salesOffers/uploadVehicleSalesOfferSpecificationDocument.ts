import { extname } from 'path';
import { handleFileUpload } from '../../../../core/storage';
import { BucketType, SalesOfferFeatureKind, SalesOfferFeatureStatus } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { allowedExtensions } from '../../../../utils/extensions';
import { generatePdfPreview } from '../../../../utils/preview';
import validateFile from '../../../../utils/validateFile';
import { getSimpleVersioningByUserForUpdate } from '../../../../utils/versioning';
import { InvalidInput } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getImpactedUpdates } from './helpers';

const mutate: GraphQLMutationResolvers['uploadVehicleSalesOfferSpecificationDocument'] = async (
    root,
    { file, id },
    { getUser }
) => {
    const { collections } = await getDatabaseContext();

    const user = await getUser();
    const acceptedExtensions = [allowedExtensions.pdf];
    const dirName = `salesOffer/${id}/specificationDocument`;
    const fileUploaded = await file;
    const extension = extname(fileUploaded.filename).toLowerCase();

    if (!acceptedExtensions.includes(extension)) {
        throw new Error('Unsupported extension');
    }

    const existedSalesOffer = await collections.salesOffers.findOne({ _id: id });
    if (!existedSalesOffer) {
        throw new InvalidInput({
            id: 'Sales offer not found',
        });
    }

    const isFileValid = await validateFile(fileUploaded.createReadStream(), fileUploaded.filename, acceptedExtensions);

    if (!isFileValid) {
        throw new InvalidInput({
            specificationDocument: 'Invalid File Format',
        });
    }

    const uploadedFile = await handleFileUpload(BucketType.Public, dirName, fileUploaded);
    const preview = await generatePdfPreview(uploadedFile);
    const impactedUpdates = await getImpactedUpdates(
        existedSalesOffer,
        [SalesOfferFeatureKind.Vehicle],
        collections,
        user
    );

    const salesOffer = await collections.salesOffers.findOneAndUpdate(
        { _id: id },
        {
            $set: {
                'vehicle.specificationDocument': { ...uploadedFile, preview },
                'vehicle.status': SalesOfferFeatureStatus.Updated,
                'vehicle.lastUpdatedAt': new Date(),
                _versioning: {
                    ...existedSalesOffer._versioning,
                    ...getSimpleVersioningByUserForUpdate(user._id),
                },
                'signings.specifications': null,
                ...impactedUpdates,
            },
        },
        { returnDocument: 'after' }
    );

    return salesOffer;
};

export default requiresLoggedUser(mutate);

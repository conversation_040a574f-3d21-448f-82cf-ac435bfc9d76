import { omit } from 'lodash/fp';
import { deleteUploadedFiles } from '../../../../core/storage';
import { UploadedFile } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { InvalidInput } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

export const deleteSalesOfferDocument = async original => {
    const filesToDelete = [omit(['kind', 'status', 'lastUpdatedAt', 'createdAt'], original)];

    if ('preview' in original && original.preview) {
        filesToDelete.push(original.preview);
    }

    await deleteUploadedFiles(filesToDelete as UploadedFile[]);
};

const mutate: GraphQLMutationResolvers['deleteSalesOfferDocument'] = async (root, { salesOfferId, fileId }) => {
    const { collections } = await getDatabaseContext();

    const salesOffer = await collections.salesOffers.findOne({
        _id: salesOfferId,
    });

    if (!salesOffer) {
        throw new InvalidInput({
            id: 'Sales offer not found',
        });
    }

    const original = salesOffer.otherDocuments.find(i => i._id.equals(fileId));

    if (!original) {
        return salesOffer;
    }

    await deleteSalesOfferDocument(original);

    return collections.salesOffers.findOneAndUpdate(
        { _id: salesOfferId },
        { $pull: { otherDocuments: { _id: fileId } } },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutate);

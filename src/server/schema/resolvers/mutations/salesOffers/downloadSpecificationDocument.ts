import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../../../core/config';
import { ExternalLinkKind, SalesOfferSpecificationDocumentDownloadLink } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutate: GraphQLMutationResolvers['downloadSpecificationDocument'] = async (root, { id }, { loaders }) => {
    const { collections } = await getDatabaseContext();
    const salesOffer = await collections.salesOffers.findOne({
        _id: id,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    if (!salesOffer.vehicle?.specificationDocument) {
        throw new Error('Specification document not found');
    }

    const link: SalesOfferSpecificationDocumentDownloadLink = {
        _id: new ObjectId(),
        _kind: ExternalLinkKind.SalesOfferSpecificationDocumentDownload,
        secret: nanoid(),
        data: {
            salesOfferId: salesOffer._id,
        },
        expiresAt: dayjs().add(5, 'minute').toDate(),
        deleteOnFetch: true,
    };
    await collections.externalLinks.insertOne(link);

    return urljoin(config.applicationEndpoint, 'api', 'downloads', 'salesOffers', 'specificationDocument', link.secret);
};

export default requiresLoggedUser(mutate);

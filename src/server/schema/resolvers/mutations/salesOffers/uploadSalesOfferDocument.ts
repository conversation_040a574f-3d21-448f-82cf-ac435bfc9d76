import { extname } from 'path';
import { pull } from 'lodash/fp';
import { handleFileUpload } from '../../../../core/storage';
import { BucketType, SalesOfferDocumentKind, SalesOfferDocumentStatus } from '../../../../database';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { allowedExtensions } from '../../../../utils/extensions';
import validateFile from '../../../../utils/validateFile';
import { InvalidInput } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

const mutate: GraphQLMutationResolvers['uploadSalesOfferDocument'] = async (
    root,
    { file, salesOfferId },
    { getUser }
) => {
    const { collections } = await getDatabaseContext();

    const acceptedExtensions = [...pull('.svg', allowedExtensions.image), allowedExtensions.pdf];
    const dirName = `salesOffer/${salesOfferId.toString()}/documents`;
    const fileUploaded = await file;
    const extension = extname(fileUploaded.filename).toLowerCase();

    if (!acceptedExtensions.includes(extension)) {
        throw new Error('Unsupported extension');
    }

    const isFileValid = await validateFile(fileUploaded.createReadStream(), fileUploaded.filename, acceptedExtensions);

    if (!isFileValid) {
        throw new InvalidInput({
            specificationDocument: 'Invalid File Format',
        });
    }

    const existedSalesOffer = await collections.salesOffers.findOne({ _id: salesOfferId });
    if (!existedSalesOffer) {
        throw new InvalidInput({
            id: 'Sales offer not found',
        });
    }

    const uploadedFile = await handleFileUpload(BucketType.Private, dirName, fileUploaded);

    return collections.salesOffers.findOneAndUpdate(
        { _id: salesOfferId },
        {
            $push: {
                otherDocuments: {
                    ...uploadedFile,
                    kind: SalesOfferDocumentKind.Others,
                    status: SalesOfferDocumentStatus.Signed,
                    lastUpdatedAt: new Date(),
                    createdAt: new Date(),
                },
            },
        },
        { returnDocument: 'after' }
    );
};

export default requiresLoggedUser(mutate);

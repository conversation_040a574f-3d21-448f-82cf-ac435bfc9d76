import getDatabaseContext from '../../../../database/getDatabaseContext';
import { mainQueue } from '../../../../queues';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';
import { getSalesOfferDocuments } from './downloadSalesOfferDocument';

const mutate: GraphQLMutationResolvers['shareSalesOfferDocument'] = async (root, { params }, { loaders }) => {
    const { salesOfferId, fileId, kind } = params;
    const { collections } = await getDatabaseContext();
    const salesOffer = await collections.salesOffers.findOne({
        _id: salesOfferId,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const document = getSalesOfferDocuments(kind, salesOffer);

    if (!document.find(i => i._id.equals(fileId))) {
        throw new Error('Document not found');
    }

    await mainQueue.add({
        type: 'onShareSalesOfferDocument',
        ...params,
    });

    return salesOffer;
};

export default requiresLoggedUser(mutate);

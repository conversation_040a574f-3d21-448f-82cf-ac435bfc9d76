import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import urljoin from 'url-join';
import config from '../../../../core/config';
import {
    ExternalLinkKind,
    SalesOfferDocumentDownloadLink,
    SalesOfferDocumentKind,
    SalesOffer,
} from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLMutationResolvers } from '../../definitions';

export const getSalesOfferDocuments = (kind: SalesOfferDocumentKind, salesOffer: SalesOffer) => {
    switch (kind) {
        case SalesOfferDocumentKind.VSA:
            return salesOffer.vsa.documents;

        case SalesOfferDocumentKind.MainDetails:
            return salesOffer.mainDetails.documents;

        case SalesOfferDocumentKind.Vehicle:
            return salesOffer.vehicle.documents;

        case SalesOfferDocumentKind.Deposit:
            return salesOffer.deposit.documents;

        case SalesOfferDocumentKind.Finance:
            return salesOffer.finance.documents;

        case SalesOfferDocumentKind.Insurance:
            return salesOffer.insurance.documents;

        case SalesOfferDocumentKind.Others:
            return salesOffer.otherDocuments;

        default:
            return [];
    }
};

const mutate: GraphQLMutationResolvers['downloadSalesOfferDocument'] = async (
    root,
    { salesOfferId, fileId, kind },
    { loaders }
) => {
    const { collections } = await getDatabaseContext();
    const salesOffer = await collections.salesOffers.findOne({
        _id: salesOfferId,
    });

    if (!salesOffer) {
        throw new Error('Sales offer not found');
    }

    const document = getSalesOfferDocuments(kind, salesOffer);

    if (!document.find(i => i._id.equals(fileId))) {
        throw new Error('Document not found');
    }

    const link: SalesOfferDocumentDownloadLink = {
        _id: new ObjectId(),
        _kind: ExternalLinkKind.SalesOfferDocumentDownload,
        secret: nanoid(),
        data: {
            salesOfferId,
            fileId,
            kind,
        },
        expiresAt: dayjs().add(5, 'minute').toDate(),
        deleteOnFetch: true,
    };
    await collections.externalLinks.insertOne(link);

    return urljoin(config.applicationEndpoint, 'api', 'downloads', 'salesOffers', 'documents', link.secret);
};

export default requiresLoggedUser(mutate);

import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyImportService } from '../../../../services/CompanyImportService';
import { InvalidPermission } from '../../../errors';
import { requiresLoggedUser } from '../../../middlewares';
import type { GraphQLMutationResolvers } from '../../definitions';

const importCompanyConfiguration: GraphQLMutationResolvers['importCompanyConfiguration'] = async (
    _,
    { upload, options = {} },
    { getUser, getPermissionController }
) => {
    const user = await getUser(false);
    const permissionController = await getPermissionController();

    if (!permissionController.hasRootPermission()) {
        throw new InvalidPermission();
    }

    const databaseContext = await getDatabaseContext();
    const importService = new CompanyImportService(databaseContext);

    try {
        const uploadedFile = await upload;
        const fileStream = uploadedFile.createReadStream();

        const result = await importService.importCompanyFromStream(fileStream, uploadedFile.filename, user._id, {
            skipFiles: options.skipFiles ?? false,
            dryRun: options.dryRun ?? false,
        });

        return {
            success: result.success,
            message: result.message,
            companyId: result.companyId || null,
            errors: result.errors || [],
        };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

        return {
            success: false,
            message: `Import failed: ${errorMessage}`,
            companyId: null,
            errors: [errorMessage],
        };
    }
};

export default requiresLoggedUser(importCompanyConfiguration);

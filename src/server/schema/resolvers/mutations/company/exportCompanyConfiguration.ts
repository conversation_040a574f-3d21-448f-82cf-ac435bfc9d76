import { uploadFile, getUrlForUpload } from '../../../../core/storage';
import { BucketType } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import { CompanyExportService } from '../../../../services/CompanyExportService';
import { InvalidInput, InvalidPermission } from '../../../errors';
import { GraphQLMutationResolvers } from '../../definitions';

const exportCompanyConfiguration: GraphQLMutationResolvers['exportCompanyConfiguration'] = async (
    _,
    { companyId, options = {} },
    context
) => {
    // Get user and permission controller
    await context.getUser(false); // Ensure user is authenticated
    const permissionController = await context.getPermissionController();

    // Check permissions - only super admin can export company configurations
    if (!permissionController.hasRootPermission()) {
        throw new InvalidPermission();
    }

    // Verify company exists
    const { collections } = await getDatabaseContext();
    const company = await collections.companies.findOne({
        _id: companyId,
        isDeleted: false,
    });

    if (!company) {
        throw new InvalidInput(`Company with ID ${companyId} not found`);
    }

    try {
        // Create export service
        const exportService = new CompanyExportService(collections);

        // Export company configuration
        const { stream, filename, contentType, statistics } = await exportService.exportCompanyToStream(companyId, {
            includeFiles: options.includeFiles ?? true,
            compress: options.compress ?? true,
            format: (options.format as 'EJSON' | 'JSON') ?? 'EJSON',
        });

        // Upload to storage for download
        const uploadedFile = await uploadFile(BucketType.Private, 'exports/companies', filename, stream, {
            'Content-Type': contentType,
            'Content-Disposition': `attachment; filename="${filename}"`,
        });

        const finalStatistics = {
            ...statistics,
            totalSize: uploadedFile.size, // Use actual uploaded file size
        };

        return {
            success: true,
            downloadUrl: await getUrlForUpload(uploadedFile, 3600),
            filename,
            statistics: finalStatistics,
            error: null,
        };
    } catch (error) {
        console.error('Export failed:', error);

        return {
            success: false,
            downloadUrl: null,
            filename: null,
            statistics: null,
            error: error?.message || 'Export failed due to an unexpected error',
        };
    }
};

export default exportCompanyConfiguration;

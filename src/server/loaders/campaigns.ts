import DataLoader from 'dataloader';
import type { ObjectId } from 'mongodb';
import type { Campaign } from '../database/documents/Campaign';
import getDatabaseContext from '../database/getDatabaseContext';
import { buildOneToOneLoader, buildManyToManyLoader } from './helpers';

type CampaignLoaders = {
    campaignById: DataLoader<ObjectId, Campaign | null>;
    activeCampaignsByCompanyId: DataLoader<ObjectId, Campaign[]>;
};

const createCampaignLoaders = (): CampaignLoaders => {
    const campaignById = buildOneToOneLoader<Campaign>(keys =>
        getDatabaseContext().then(({ collections }) => collections.campaigns.find({ _id: { $in: keys } }).toArray())
    );

    const activeCampaignsByCompanyId = buildManyToManyLoader<Campaign>(
        keys =>
            getDatabaseContext().then(({ collections }) =>
                collections.campaigns.find({ companyId: { $in: keys }, isActive: true }).toArray()
            ),
        document => [document.companyId.toHexString()]
    );

    return {
        campaignById,
        activeCampaignsByCompanyId,
    };
};

export default createCampaignLoaders;

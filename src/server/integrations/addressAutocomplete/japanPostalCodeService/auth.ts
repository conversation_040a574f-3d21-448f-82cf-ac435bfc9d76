import fetch, { Response } from 'node-fetch';
import config from '../../../core/config';
import getRedisInstance from '../../../core/redis';
import handleInvalidResponse from './handleInvalidResponse';

// in seconds
const MIN_TOKEN_LIFESPAN = 60;
const CACHE_KEY = `japan-postal-code:token`;

type AuthResponse = {
    token?: string;
    expires_in?: number;
    request_id?: string;
    error_code?: string;
    message?: string;
};

const getAuthToken = async (): Promise<string> => {
    const redis = await getRedisInstance();

    // Check if token is cached
    const cached = await redis.get(CACHE_KEY);
    if (cached) {
        return cached;
    }

    // If not cached, fetch a new token
    const { japanPostalCodeApi } = config;

    const endpoint = `${japanPostalCodeApi.url}/api/v1/j/token`;

    const response: Response = await fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            grant_type: 'client_credentials',
            client_id: japanPostalCodeApi.clientId,
            secret_key: japanPostalCodeApi.secretKey,
        }),
    });

    if (!response.ok) {
        await handleInvalidResponse(response);
    }

    const data: AuthResponse = await response.json();

    if (data.token && data.expires_in && data.expires_in > MIN_TOKEN_LIFESPAN) {
        // Cache the token
        redis.set(CACHE_KEY, data.token, 'EX', data.expires_in - MIN_TOKEN_LIFESPAN);
    }

    return data.token;
};

export default getAuthToken;

import * as Sentry from '@sentry/node';
import { Response } from 'node-fetch';

/* handle invalid response from Japan Postal Code Api */
const handleInvalidResponse = async (response: Response): Promise<void> => {
    const errorMessage = 'Invalid response from Japan Postal Code Api';
    const responseBody = await response.text();

    console.error(errorMessage, responseBody);

    Sentry.withScope(scope => {
        scope.setContext('japanPostalCodeResponse', { response: responseBody });
        Sentry.captureMessage(errorMessage);
    });

    throw new Error(`${errorMessage} - ${response.status} ${response.statusText}`);
};

export default handleInvalidResponse;

import * as Sentry from '@sentry/node';
import fetch, { Response } from 'node-fetch';
import config from '../../../core/config';
import {
    type AddressAutocompleteService,
    type AddressAutocompleteInput,
    type AddressAutocompleteResult,
    type AddressComponent,
    AddressComponentType,
} from '../types';
import getAuthToken from './auth';
import handleInvalidResponse from './handleInvalidResponse';

type Address = {
    pref_name: string; // Region
    pref_code: string; // Region code

    city_name: string; // City
    city_code: string; // City code

    zip_code: string; // Postal code

    town_name: string; // Address
};

type SearchPostalCodeResponse = {
    searchtype: string;
    addresses: Address[];
};

class JapanPostalCodeAddressAutocompleteService implements AddressAutocompleteService {
    async searchAddresses(input: AddressAutocompleteInput): Promise<AddressAutocompleteResult[]> {
        try {
            const { query } = input;

            if (query?.length !== 7) {
                return []; // Invalid postal code format for Japan
            }

            const accessToken = await getAuthToken();
            if (!accessToken) {
                return []; // No access token available
            }

            const { japanPostalCodeApi } = config;

            const endpoint = `${japanPostalCodeApi.url}/api/v1/searchcode/${encodeURIComponent(query)}`;

            const response: Response = await fetch(endpoint, {
                method: 'GET',
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                await handleInvalidResponse(response);
            }

            const data: SearchPostalCodeResponse = await response.json();

            return data.addresses?.map(address => this.mapAddressToResult(address));
        } catch (error) {
            console.error('Error in Japan Postal Code address autocomplete:', error);

            Sentry.withScope(scope => {
                scope.setContext('japanPostalCodeAddressAutocomplete', input);
                Sentry.captureException(error);
            });

            if (error instanceof Error) {
                throw error;
            }

            throw new Error('Failed to fetch Japan Postal Code address suggestions');
        }
    }

    private mapAddressToResult(address: Address): AddressAutocompleteResult {
        const components = this.extractComponents(address);

        return {
            id: address.zip_code,
            address: address.town_name,
            components,
        };
    }

    private extractComponents(responseAddress: Address): AddressComponent[] {
        const components: AddressComponent[] = [];

        components.push({
            type: this.japanPostalTypeToComponentType('pref_name'),
            longName: responseAddress.pref_name,
            shortName: responseAddress.pref_code,
        });

        components.push({
            type: this.japanPostalTypeToComponentType('city_name'),
            longName: responseAddress.city_name,
            shortName: responseAddress.city_code,
        });

        components.push({
            type: this.japanPostalTypeToComponentType('zip_code'),
            longName: responseAddress.zip_code,
            shortName: responseAddress.zip_code,
        });

        components.push({
            type: this.japanPostalTypeToComponentType('town_name'),
            longName: responseAddress.town_name,
            shortName: responseAddress.town_name,
        });

        return components;
    }

    private japanPostalTypeToComponentType(japanPostalType: string): AddressComponentType {
        const typeMap: Record<string, AddressComponentType> = {
            pref_name: AddressComponentType.REGION,
            city_name: AddressComponentType.PLACE,
            zip_code: AddressComponentType.POSTCODE,
            town_name: AddressComponentType.ADDRESS,
        };

        return typeMap[japanPostalType];
    }
}

export default JapanPostalCodeAddressAutocompleteService;

import { URLSearchParams } from 'url';
import * as Sentry from '@sentry/node';
import fetch from 'node-fetch';
import urljoin from 'url-join';
import { GenericCapRequestParams } from '../utils/types';
import { BPComm, BpResults } from './types';

type GetBusinessPartnerParams = GenericCapRequestParams & {
    businessPartnerId: string;
};

const getBusinessPartnerComm = async (bpParams: GetBusinessPartnerParams): Promise<BpResults<BPComm>> => {
    try {
        const { secrets, capRegion, auth, capGroup, capBaseUrl, capEndpointEnv, businessPartnerId } = bpParams;

        if (secrets?.clientId && secrets?.clientSecret && capRegion && auth && businessPartnerId) {
            const endpoint = urljoin(
                `https://${capBaseUrl}`,
                capGroup,
                capEndpointEnv,
                'crm/businesspartner',
                `BusinessPartnerSet('${businessPartnerId}')`,
                'BpData_To_BpComm'
            );

            const filterField = [`BusinessPartnerId eq '${businessPartnerId}'`];

            const paramsDetails = {
                $format: 'json',
                $filter: filterField.join(' and '),
            };

            const params = new URLSearchParams();
            const paramsKeys = Object.keys(paramsDetails);
            paramsKeys.forEach(key => {
                params.append(key, paramsDetails[key]);
            });

            const headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'application/json',
                Authorization: `Bearer ${auth}`,
                'X-Porsche-Client-Id': secrets?.clientId,
                'X-Porsche-Client-Secret': secrets?.clientSecret,
                region: capRegion,
            };

            const response = await fetch(`${endpoint}?${params.toString()}`, {
                headers,
                method: 'GET',
            });

            const responseData = await response?.json();

            if (response?.status === 200) {
                return responseData;
            }

            const responseErrorMessage = responseData?.error?.message?.value || responseData?.moreInformation;

            Sentry.withScope(scope => {
                scope.clearBreadcrumbs();
                scope.setTag('api', 'c@p');
                scope.setContext('payload', {
                    endpoint: 'GET BpData_To_BpComm',
                    data: { endpoint: `${endpoint}?${params.toString()}`, headers },
                });
                Sentry.captureException(JSON.stringify({ status: response?.status, responseData }, null, 4));
            });

            return { error: responseErrorMessage };
        }

        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('api', 'c@p');
            scope.setContext('payload', { endpoint: 'GET BpData_To_BpComm', data: bpParams });
            Sentry.captureException('Required value for getting the BP comm is missing');
        });

        return { error: 'Required value for getting the BP comm is missing' };
    } catch (error) {
        Sentry.withScope(scope => {
            scope.clearBreadcrumbs();
            scope.setTag('api', 'c@p');
            scope.setContext('payload', { endpoint: 'GET BpData_To_BpComm', data: bpParams });
            Sentry.captureException(error);
        });

        console.error(`Error found in getBusinessPartnerComm: ${error}`);

        return { error: `Error found in getBusinessPartnerComm: ${error}` };
    }
};

export default getBusinessPartnerComm;

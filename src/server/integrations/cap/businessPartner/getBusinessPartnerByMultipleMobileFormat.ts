import { chunk, flatten, uniqBy } from 'lodash/fp';
import { getCountryCodeByPhonePrefix, getPhonePrefixByCountryCode } from '../utils/getCountryCode';
import { type CapRequestParamsWithTrails, type GenericGetResult } from '../utils/types';
import getBusinessPartner from './getBusinessPartner';
import { type BusinessPartnerResData } from './types';

type PrefixType = string | number;
const sanitizePhoneNumber = (initialPhone: string, initialPrefix: PrefixType): string => {
    // Remove all non-digit characters
    const prefix = String(initialPrefix).replace(/\D/g, '');
    const phone = initialPhone.replace(/\D/g, '');

    if (phone.startsWith('0')) {
        return phone.slice(1); // Remove leading zero if present
    }

    if (phone.startsWith(prefix)) {
        return phone.slice(prefix.length); // Remove leading country code if present
    }

    return phone;
};

/**
 * Generates possible variations of a phone number to support matching between local and international formats.
 *
 * Context:
 * - Many users enter local phone numbers with a leading '0', while international formats use a country
 *   prefix (e.g., +62, +60).
 * - The system should treat numbers with and without a leading '0' as equivalent for lookup and duplicate checks.
 * - This function helps ensure users can retrieve C@P information regardless of the format they enter.
 * Example:
 *   Input: '0818879879' → ['0818879879', '818879879']
 *   Input: '+62818879879' → ['0818879879', '818879879']
 *   Input: '818879879' → ['0818879879', '818879879']
 */
const getGeneralMobileFormat = (initialPhone: string, prefix: PrefixType): string[] => {
    const phone = sanitizePhoneNumber(initialPhone, prefix);

    return [`0${phone}`, phone];
};

/**
 * Generates all PKO mobile number permutations for Korea, supporting dynamic lengths.
 * Permutations:
 * 1. XX-XXXX-XXXX (or XX-XXXX-XXX, etc. depending on length)
 * 2. 0XX-XXXX-XXXX (or 0XX-XXXX-XXX, etc. depending on length)
 * 3. 0XXXXXXXXXX...
 * 4. XXXXXXXXXX...
 *
 * Ensures that any search for the digits (with or without dashes, with or without leading zero)
 * will match the BP record.
 */
const getKoreaMobileFormats = (initialPhone: string): string[] => {
    const KOREA_MOBILE_PREFIX = '82'; // South Korea's country code
    const phone = sanitizePhoneNumber(initialPhone, KOREA_MOBILE_PREFIX);

    // Format 3: 0XXXXXXXXXX... & Format 4: XXXXXXXXXX...
    const result: string[] = getGeneralMobileFormat(phone, KOREA_MOBILE_PREFIX);
    const len = phone.length;

    if (len >= 6) {
        // Format 1: XX-XXXX-XXXX (dynamic last segment)
        // First 2, next 4, rest
        result.push(`${phone.slice(0, 2)}-${phone.slice(2, 6)}-${phone.slice(6)}`);

        // Format 2: 0XX-XXXX-XXXX (dynamic last segment)
        // First 2 after 0, next 4, rest
        result.push(`0${phone.slice(0, 3)}-${phone.slice(3, 7)}-${phone.slice(7)}`);
    }

    return result;
};

export const getFormattedPhoneNumbers = ({
    phone,
    prefix: initialPrefix,
    companyCountryCode,
}: {
    phone: string;
    prefix: PrefixType;
    companyCountryCode: string;
}): string[] => {
    // If the initial prefix is provided, use it to determine the country code
    // Otherwise, derive the prefix from the company country code
    const countryCode = initialPrefix
        ? getCountryCodeByPhonePrefix(parseInt(String(initialPrefix).replace(/\D/g, ''), 10))
        : companyCountryCode;
    const prefix = initialPrefix ?? getPhonePrefixByCountryCode(companyCountryCode) ?? '';

    return countryCode === 'KR' ? getKoreaMobileFormats(phone) : getGeneralMobileFormat(phone, prefix);
};

type PhoneFormatParams = CapRequestParamsWithTrails & {
    phone: string;
    companyCountryCode: string;
    prefix?: PrefixType;
    dealer?: string;
    size?: number;
};

const getBusinessPartnerByMultipleMobileFormat = async ({
    phone,
    companyCountryCode,
    prefix,
    lead,
    t,
    auth,
    dealer,
    size = 50, // Default size if not provided
    ...capSetting
}: PhoneFormatParams): Promise<GenericGetResult<BusinessPartnerResData>> => {
    const modifiedPhoneNumbers = getFormattedPhoneNumbers({
        phone,
        prefix,
        companyCountryCode,
    });

    const BATCH_SIZE = 2;
    // Use lodash/fp chunk to group into arrays of 2, last array will have 1 if odd
    const groupedPhoneNumbers: string[][] = chunk(BATCH_SIZE, modifiedPhoneNumbers);

    // Sequential batch processing, parallel inside each batch
    const batchSearchBusinessPartnerByMobile = groupedPhoneNumbers.map(batch =>
        Promise.all(
            batch.map(mobileNumber =>
                getBusinessPartner({
                    ...capSetting,
                    lead,
                    t,
                    auth,
                    query: { mobileNumber, dealer, size },
                })
            )
        )
    );

    const flattenedResults = flatten(await Promise.all(batchSearchBusinessPartnerByMobile));

    const errors: string[] = [];
    const results: BusinessPartnerResData[] = [];

    flattenedResults.forEach(result => {
        if (result.error) {
            errors.push(result.error);
        } else if (result.d?.results) {
            results.push(...result.d.results);
        }
    });

    const uniqueBusinessPartnerResults = uniqBy('BusinessPartnerGuid', results);

    return {
        error: !uniqueBusinessPartnerResults.length ? errors.join('\n') : null,
        d: {
            results: uniqueBusinessPartnerResults,
            __count: uniqueBusinessPartnerResults.length.toString(),
        },
    };
};
export default getBusinessPartnerByMultipleMobileFormat;

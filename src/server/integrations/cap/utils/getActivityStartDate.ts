import dayjs, { Dayjs } from 'dayjs';
import { CapAvailableApplicationType } from '../../../database';
import { ApplicationStage } from '../../../database/documents/Applications';
import { ApplicationScenario } from '../../../database/documents/moduleShared';

// Referring to AN-2508
// VF-973: JP
// VF-1228: KR
const getActivityStartDate = (countryCode: string, application?: CapAvailableApplicationType): Dayjs => {
    if (application) {
        if (
            application.scenarios.includes(ApplicationScenario.Appointment) &&
            application.stages.includes(ApplicationStage.Appointment)
        ) {
            if (application.appointmentStage?.bookingTimeSlot.slot) {
                return dayjs(application.appointmentStage.bookingTimeSlot.slot);
            }
        }

        if (application.scenarios.includes(ApplicationScenario.Payment)) {
            return dayjs().add(2, 'day');
        }
    }

    return countryCode === 'JP' || countryCode === 'KR' ? dayjs().add(14, 'day') : dayjs().add(7, 'day');
};

export default getActivityStartDate;

import type { Queue } from 'bull';
import { Redis, Cluster } from 'ioredis';
import getDatabaseContext from '../database/getDatabaseContext';
import getPubSub from './pubSub';
import getRedisInstance from './redis';

const validateRedis = (redis: Queue['clients'][number] | Redis | Cluster, name: string) => {
    if (redis.status !== 'ready') {
        throw new Error(`${name} is not ready (${redis.status})`);
    }
};

export const runHealthChecks = async () => {
    // check the main redis first
    validateRedis(await getRedisInstance(), 'Redis client');

    // check pub-sub
    const pubSub = getPubSub();
    validateRedis(pubSub.getPublisher(), 'Redis client for publisher');
    validateRedis(pubSub.getSubscriber(), 'Redis client for subscriber');

    // check the mongo database
    const { regular, encrypted } = await getDatabaseContext();
    await regular.db.command({ ping: 1 });

    if (regular.client !== encrypted.client) {
        // also ping on encrypted client
        await encrypted.db.command({ ping: 1 });
    }
};

// Add a function to validate queues separately
export const validateQueues = (queues: any[]) => {
    Object.values(queues).map(queue =>
        queue.clients.map(client => validateRedis(client, `Redis client for ${queue.queueName}`))
    );
};

import type React from 'react';
import VisitAppointmentSubmitConfirmation from './VisitAppointmentSubmitConfirmation';
import type { VisitAppointmentSubmitConfirmationProps } from './VisitAppointmentSubmitConfirmation';

export type VisitAppointmentSalespersonBookingConfirmationTemplateData = VisitAppointmentSubmitConfirmationProps;

const VisitAppointmentSalespersonBookingConfirmation = (
    props: VisitAppointmentSalespersonBookingConfirmationTemplateData
): React.ReactElement => <VisitAppointmentSubmitConfirmation {...props} />;

export default VisitAppointmentSalespersonBookingConfirmation;

import type React from 'react';
import AppointmentSubmitConfirmation from './AppointmentSubmitConfirmation';
import type { AppointmentSubmitConfirmationProps } from './AppointmentSubmitConfirmation';

export type AppointmentSalespersonBookingConfirmationTemplateData = AppointmentSubmitConfirmationProps;

const AppointmentSalespersonBookingConfirmation = (
    props: AppointmentSalespersonBookingConfirmationTemplateData
): React.ReactElement => <AppointmentSubmitConfirmation {...props} />;

export default AppointmentSalespersonBookingConfirmation;

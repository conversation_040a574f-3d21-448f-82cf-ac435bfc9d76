import { MjmlColumn, MjmlSection, MjmlWrapper } from '@faire/mjml-react';
import { ReactNode, useMemo } from 'react';
import { Text } from '../TextSection';
import IntroImage from './IntroImage';
import { SectionSpacer } from './ui';

type BodyProps = {
    introTitle?: string | ReactNode;
    introLevel?: 'h1' | 'h2' | 'h3' | 'h4' | 'small' | 'p';
    titleMarked?: boolean;
    children: ReactNode;
    introImageUrl?: string;
    isTextWrapper?: boolean;
    contentMarked?: boolean;
};

const SummaryContent = ({
    introTitle,
    introLevel,
    titleMarked,
    introImageUrl,
    isTextWrapper,
    contentMarked,
    children,
}: BodyProps) => {
    const titlePaddingBottom = useMemo(() => {
        if (contentMarked && titleMarked) {
            return '8px';
        }

        if (titleMarked || contentMarked) {
            return '21px';
        }

        return '34px';
    }, [contentMarked, titleMarked]);

    return (
        <>
            <IntroImage introImageUrl={introImageUrl} />
            <SectionSpacer height={titleMarked ? '35px' : '48px'} />
            <MjmlWrapper css-class="content-text-link" padding={isTextWrapper ? '0 7px' : '0 32px'}>
                {introTitle && introLevel === 'h2' && (
                    <MjmlSection paddingBottom={titlePaddingBottom} paddingTop="0px">
                        <MjmlColumn padding={0}>
                            <Text align="left" level="h2" paddingBottom={0} paddingTop={0}>
                                {introTitle}
                            </Text>
                        </MjmlColumn>
                    </MjmlSection>
                )}

                {introTitle && introLevel !== 'h2' && (
                    <MjmlSection paddingBottom={titlePaddingBottom} paddingTop="0px">
                        <MjmlColumn>
                            <Text align="left" level={!introLevel ? 'h1' : introLevel} paddingTop={0}>
                                <span style={{ whiteSpace: 'pre-wrap' }}>{introTitle}</span>
                            </Text>
                        </MjmlColumn>
                    </MjmlSection>
                )}
                {children}
            </MjmlWrapper>
        </>
    );
};

export default SummaryContent;

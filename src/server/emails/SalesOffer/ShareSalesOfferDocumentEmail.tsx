import GeneralEDMLayout from '../components/GeneralEDMLayout';
import { DealerInformationSection } from '../components/common';
import SalesOfferContentText from './components/SalesOfferContentText';
import SalesOfferIntroTitle from './components/SalesOfferIntroTitle';
import type { ShareSalesOfferDocumentProps } from './shared';

const ShareSalesOfferDocumentEmail = ({
    contentText,
    emailContext,
    introTitle,
    dealerEmailContext,
}: ShareSalesOfferDocumentProps) => (
    <GeneralEDMLayout emailContext={emailContext}>
        <SalesOfferIntroTitle introTitle={introTitle} />
        <SalesOfferContentText contentText={contentText} />
        <DealerInformationSection
            contactUsText="emails:salesOffer.contactUs"
            contentAlign="left"
            customPadding="48px 0px 0px 0px"
            dealerEmailContext={dealerEmailContext}
        />
    </GeneralEDMLayout>
);

export default ShareSalesOfferDocumentEmail;

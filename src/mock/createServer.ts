import express from 'express';
import createApiEndpoints from './api';

const createWebServer = () => {
    console.info('Starting mock server...');

    const expressServer = express();
    const PORT = 4001;

    expressServer.use(express.json({ limit: '10mb' }));
    expressServer.use(express.urlencoded({ extended: true, limit: '10mb' }));

    expressServer.use((req, res, next) => {
        const timestamp = new Date().toISOString();
        console.info(`[${timestamp}] ${req.method} ${req.path}`);

        // Log auth headers (first 20 chars for security)
        if (req.headers.authorization) {
            console.info(`  🔐 Auth: ${req.headers.authorization.substring(0, 20)}...`);
        }

        // Log request body in development
        if (Object.keys(req.body || {}).length > 0) {
            console.info(`  📦 Body:`, JSON.stringify(req.body, null, 2));
        }

        next();
    });

    expressServer.use('/api', createApiEndpoints());

    const server = expressServer.listen(PORT, () => {
        console.info(`Mock server is running at http://localhost:${PORT}`);
    });

    return { httpServer: server, url: `http://localhost:${PORT}` };
};

export default createWebServer;

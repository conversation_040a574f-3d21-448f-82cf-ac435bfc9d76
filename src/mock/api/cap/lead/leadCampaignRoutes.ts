import { Router, Response } from 'express';
import { AuthenticatedRequest } from '../../ppn/tokenValidation';

const mockLeadCampaigns = [
    {
        __metadata: {
            id: "https://ASIA.CRM-TEST.PORSCHE.COM:443/crm/v2/lead/LeadCampaignSet('00505685965C1EEFA1882FC11D816909')",
            uri: "https://ASIA.CRM-TEST.PORSCHE.COM:443/crm/v2/lead/LeadCampaignSet('00505685965C1EEFA1882FC11D816909')",
            type: 'YCRMA5627_API_COMMUNITY_SRV.LeadCampaign',
        },
        campaignGuid: '00505685965C1EEFA1882FC11D816909',
        campaignId: 'PJ-BOUNCE-EMAIL-HANDLING',
        description: 'PJ Bounce Email Handling',
        statusId: 'I1004',
        isFavourite: false,
        responsibleDealerGuid: '00000000000000000000000000000000',
    },
    {
        __metadata: {
            id: "https://ASIA.CRM-TEST.PORSCHE.COM:443/crm/v2/lead/LeadCampaignSet('00505685965C1EEFA1882FC11D816910')",
            uri: "https://ASIA.CRM-TEST.PORSCHE.COM:443/crm/v2/lead/LeadCampaignSet('00505685965C1EEFA1882FC11D816910')",
            type: 'YCRMA5627_API_COMMUNITY_SRV.LeadCampaign',
        },
        campaignGuid: '00505685965C1EEFA1882FC11D816910',
        campaignId: 'PJ-NEWSLETTER-SIGNUP',
        description: 'PJ Newsletter Signup Campaign',
        statusId: 'I1005',
        isFavourite: true,
        responsibleDealerGuid: '00000000000000000000000000000001',
    },
    {
        __metadata: {
            id: "https://ASIA.CRM-TEST.PORSCHE.COM:443/crm/v2/lead/LeadCampaignSet('00505685965C1EEFA1882FC11D816911')",
            uri: "https://ASIA.CRM-TEST.PORSCHE.COM:443/crm/v2/lead/LeadCampaignSet('00505685965C1EEFA1882FC11D816911')",
            type: 'YCRMA5627_API_COMMUNITY_SRV.LeadCampaign',
        },
        campaignGuid: '00505685965C1EEFA1882FC11D816911',
        campaignId: 'PJ-PRODUCT-LAUNCH',
        description: 'PJ Product Launch Campaign',
        statusId: 'I1006',
        isFavourite: false,
        responsibleDealerGuid: '00000000000000000000000000000002',
    },
];

// Helper function to parse OData filter
const parseODataFilter = (filter: string) => {
    if (!filter) {
        return {};
    }

    // Simple parser for campaignId eq 'value'
    const campaignIdMatch = filter.match(/campaignId\s+eq\s+'([^']+)'/i);
    if (campaignIdMatch) {
        return { campaignId: campaignIdMatch[1] };
    }

    // Add more filter parsing as needed
    return {};
};

const applyFilters = (campaigns: any[], filters: any) => {
    let filteredCampaigns = [...campaigns];

    if (filters.campaignId) {
        filteredCampaigns = filteredCampaigns.filter(campaign => campaign.campaignId === filters.campaignId);
    }

    return filteredCampaigns;
};

const router = Router();

/**
 * GET /v2/lead/LeadCampaignSet
 * Get LeadCampaign data with OData filtering support
 */
router.get('/LeadCampaignSet', (req: AuthenticatedRequest, res: Response) => {
    try {
        // Parse query parameters
        const filter = req.query.$filter as string;
        const format = req.query.$format as string;
        const top = req.query.$top ? parseInt(req.query.$top as string, 10) : undefined;
        const skip = req.query.$skip ? parseInt(req.query.$skip as string, 10) : 0;

        console.info(`[LEAD CAMPAIGN API] Filter: ${filter}, Format: ${format}`);

        // Parse and apply filters
        const filters = parseODataFilter(filter);
        let filteredCampaigns = applyFilters(mockLeadCampaigns, filters);

        // Apply pagination
        if (skip > 0) {
            filteredCampaigns = filteredCampaigns.slice(skip);
        }
        if (top) {
            filteredCampaigns = filteredCampaigns.slice(0, top);
        }

        // Return in OData format
        const response: any = {
            d: {
                results: filteredCampaigns,
            },
        };

        // Add metadata if requested
        if (format === 'json') {
            response.d.__metadata = {
                type: 'Collection(YCRMA5627_API_COMMUNITY_SRV.LeadCampaign)',
            };
        }

        return res.json(response);
    } catch (error) {
        console.error('[LEAD CAMPAIGN API ERROR]', error);

        return res.status(500).json({
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'An error occurred while processing the request',
            },
        });
    }
});

/**
 * GET /v2/lead/LeadCampaignSet('guid')
 * Get specific LeadCampaign by GUID
 */
router.get('/LeadCampaignSet\\(:guid\\)', (req: AuthenticatedRequest, res: Response) => {
    try {
        const guid = req.params.guid?.replace(/['"]/g, ''); // Remove quotes if present

        console.info(`[LEAD CAMPAIGN API] Getting campaign by GUID: ${guid}`);

        const campaign = mockLeadCampaigns.find(c => c.campaignGuid === guid);

        if (!campaign) {
            return res.status(404).json({
                error: {
                    code: 'CAMPAIGN_NOT_FOUND',
                    message: `LeadCampaign with GUID '${guid}' not found`,
                },
            });
        }

        const response = {
            d: campaign,
        };

        return res.json(response);
    } catch (error) {
        console.error('[LEAD CAMPAIGN API ERROR]', error);

        return res.status(500).json({
            error: {
                code: 'INTERNAL_SERVER_ERROR',
                message: 'An error occurred while processing the request',
            },
        });
    }
});

export default router;

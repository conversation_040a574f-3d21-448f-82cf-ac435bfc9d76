import { Router } from 'express';
import { validateBearerToken } from '../../ppn/tokenValidation';
import { validatePorscheHeaders } from '../shared';
import leadCampaignRoutes from './leadCampaignRoutes';

const createLeadEndpoints = () => {
    const router = Router();

    router.use('/', [validateBearerToken, validatePorscheHeaders], leadCampaignRoutes);

    return router;
};

const leadRouter = createLeadEndpoints();

export default leadRouter;

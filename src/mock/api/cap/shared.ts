import { AuthenticatedRequest } from '../ppn/tokenValidation';

// Validate client credentials (mock validation)
const validClients = {
    'porsche-client-123': 'porsche-secret-456',
    'test-client': 'test-secret',
    'e2e-client': 'e2e-secret',
};

export const validatePorscheHeaders = (req: AuthenticatedRequest, res, next) => {
    const clientId = req.headers['x-porsche-client-id'];
    const clientSecret = req.headers['x-porsche-client-secret'];
    const { region } = req.headers;

    // Check required headers
    // TODO: Update validation message with the correct error messages
    if (!clientId) {
        return res.status(400).json({
            error: {
                code: 'MISSING_HEADER',
                message: 'Missing required header: X-Porsche-Client-Id',
            },
        });
    }

    if (!clientSecret) {
        return res.status(400).json({
            error: {
                code: 'MISSING_HEADER',
                message: 'Missing required header: X-Porsche-Client-Secret',
            },
        });
    }

    if (!region) {
        return res.status(400).json({
            error: {
                code: 'MISSING_HEADER',
                message: 'Missing required header: region',
            },
        });
    }

    if (!validClients[clientId as string] || validClients[clientId as string] !== clientSecret) {
        return res.status(401).json({
            error: {
                code: 'INVALID_CLIENT_CREDENTIALS',
                message: 'Invalid X-Porsche-Client-Id or X-Porsche-Client-Secret',
            },
        });
    }

    // Validate region
    const validRegions = ['asia'];
    if (!validRegions.includes(region as string)) {
        return res.status(400).json({
            error: {
                code: 'INVALID_REGION',
                message: `Invalid region. Valid regions: ${validRegions.join(', ')}`,
            },
        });
    }

    // Log the request for debugging
    console.info(`Client: ${clientId}, Region: ${region}`);

    return next();
};

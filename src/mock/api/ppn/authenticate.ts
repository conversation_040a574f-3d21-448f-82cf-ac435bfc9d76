/* eslint-disable camelcase */
import crypto from 'crypto';
import { EJSON } from 'bson';
import { RequestHandler, Request } from 'express';
import jwt from 'jsonwebtoken';

const mockClients = {
    e2e: {
        id: 'e2e',
        secret: 'e2e123',
        name: 'E2E Test Client',
    },
};

interface AuthenticatedRequest extends Request {
    clientCredentials?: {
        clientId: string;
        clientSecret: string;
    };
    client?: {
        id: string;
        secret: string;
        name: string;
    };
}

export const parseBasicAuth: RequestHandler = (req: AuthenticatedRequest, res, next) => {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Basic ')) {
        return res.status(401).json({
            error: 'invalid_client',
            error_description: 'Missing or invalid Authorization header. Basic auth required.',
        });
    }

    try {
        // Decode base64 credentials
        const base64Credentials = authHeader.split(' ')[1];
        const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
        const [clientId, clientSecret] = credentials.split(':');

        if (!clientId || !clientSecret) {
            return res.status(401).json({
                error: 'invalid_client',
                error_description: 'Invalid Basic auth format. Expected clientId:clientSecret',
            });
        }

        req.clientCredentials = { clientId, clientSecret };

        return next();
    } catch (error) {
        return res.status(401).json({
            error: 'invalid_client',
            error_description: 'Invalid Basic auth encoding',
        });
    }
};

export const validateClient = (req, res, next) => {
    if (!req.clientCredentials) {
        return res.status(401).json({
            error: 'invalid_client',
            error_description: 'Client credentials not found',
        });
    }

    const { clientId, clientSecret } = req.clientCredentials;
    const client = mockClients[clientId];

    if (!client || client.secret !== clientSecret) {
        return res.status(401).json({
            error: 'invalid_client',
            error_description: 'Invalid client credentials',
        });
    }

    req.client = client;

    return next();
};

// Middleware to validate request body
export const validateAuthRequest: RequestHandler<{}, any, any, any, { grant_type: string; ppn_resource: string }> = (
    req,
    res,
    next
) => {
    const { grant_type, ppn_resource } = req.body;

    // Validate grant_type
    if (!grant_type) {
        return res.status(400).json({
            error: 'invalid_request',
            error_description: 'Missing grant_type parameter',
        });
    }

    // Validate supported grant types
    const supportedGrantTypes = ['client_credentials', 'authorization_code', 'refresh_token'];
    if (!supportedGrantTypes.includes(grant_type)) {
        return res.status(400).json({
            error: 'unsupported_grant_type',
            error_description: `Grant type '${grant_type}' is not supported. Supported types: ${supportedGrantTypes.join(', ')}`,
        });
    }

    // Validate ppn_resource
    if (!ppn_resource) {
        return res.status(400).json({
            error: 'invalid_request',
            error_description: 'Missing ppn_resource parameter',
        });
    }

    return next();
};

const generateMockToken = (client, grantType, resource) => {
    const payload = {
        client_id: client.id,
        client_name: client.name,
        grant_type: grantType,
        resource,
        scope: ['read', 'write'],
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour expiry
        jti: crypto.randomUUID(),
    };

    return jwt.sign(EJSON.serialize(payload), 'abcdefghijkl');
};

const authenticate: RequestHandler = (req: AuthenticatedRequest, res, next) => {
    const { grant_type, ppn_resource } = req.body;
    const { client } = req;

    try {
        // Generate tokens
        const accessToken = generateMockToken(client, grant_type, ppn_resource);

        // Mock response based on grant type
        const response = {
            access_token: accessToken,
            token_type: 'Bearer',
            expires_in: 3600,
            scope: 'read write',
            resource: ppn_resource,
        };

        // Log successful authentication (in real app, use proper logging)
        console.info(`[AUTH SUCCESS] Client: ${client.id}, Grant: ${grant_type}, Resource: ${ppn_resource}`);

        res.json(response);
    } catch (error) {
        console.error('[AUTH ERROR]', error);
        res.status(500).json({
            error: 'server_error',
            error_description: 'Internal server error during token generation',
        });
    }
};

export default authenticate;

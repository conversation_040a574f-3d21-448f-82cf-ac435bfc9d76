import { Router } from 'express';
import authenticate, { parseBasicAuth, validateAuthRequest, validateClient } from './authenticate';
import { protectRoute } from './tokenValidation';

const createPPNEndpoints = () => {
    const router = Router();

    router.post('/as/token.oauth2', [parseBasicAuth, validateClient, validateAuthRequest], authenticate);
    router.get('/validate', protectRoute.e2e, (req, res) => {
        res.json({
            status: 'healthy',
            user: req.user,
            timestamp: new Date().toISOString(),
            authenticated: true,
        });
    });

    return router;
};

const ppnRouter = createPPNEndpoints();

export default ppnRouter;

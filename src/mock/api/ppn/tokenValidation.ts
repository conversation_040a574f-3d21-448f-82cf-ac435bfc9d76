import { <PERSON><PERSON><PERSON><PERSON> } from 'bson';
import { Request, RequestH<PERSON>ler } from 'express';
import jwt from 'jsonwebtoken';

// Token payload interface
export type TokenPayload = {
    client_id: string;
    client_name: string;
    grant_type: string;
    resource: string;
    scope: string[];
    iat: number;
    exp: number;
    jti: string;
};

// Authenticated request interface
export interface AuthenticatedRequest extends Request {
    tokenPayload?: TokenPayload;
    user?: {
        clientId: string;
        clientName: string;
        resource: string;
        scope: string[];
    };
}

// JWT secret (should match the one in authenticate.ts)
const JWT_SECRET = 'abcdefghijkl';

/**
 * Validate JWT token and extract payload
 */
export const validateToken = (token: string): { valid: boolean; payload?: TokenPayload; error?: string } => {
    if (!token) {
        return {
            valid: false,
            error: 'Token is required',
        };
    }

    try {
        // Verify and decode JWT token
        const decoded = jwt.verify(token, JWT_SECRET) as any;

        // Deserialize the payload (since we used EJSON.serialize when creating the token)
        const payload = EJSON.deserialize(decoded) as TokenPayload;

        // Additional validation checks
        if (!payload.client_id || !payload.resource || !payload.scope) {
            return {
                valid: false,
                error: 'Invalid token payload',
            };
        }

        return {
            valid: true,
            payload,
        };
    } catch (error) {
        if (error instanceof jwt.TokenExpiredError) {
            return {
                valid: false,
                error: 'Token has expired',
            };
        }
        if (error instanceof jwt.JsonWebTokenError) {
            return {
                valid: false,
                error: 'Invalid token',
            };
        }

        return {
            valid: false,
            error: 'Token validation failed',
        };
    }
};

/**
 * Core middleware to validate Bearer token and attach user info to request
 */
export const validateBearerToken: RequestHandler = (req: AuthenticatedRequest, res, next) => {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
            error: 'unauthorized',
            error_description: 'Missing or invalid Authorization header. Bearer token required.',
        });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    const validationResult = validateToken(token);

    if (!validationResult.valid) {
        return res.status(401).json({
            error: 'invalid_token',
            error_description: validationResult.error,
        });
    }

    // Attach token payload and user info to request
    req.tokenPayload = validationResult.payload;
    req.user = {
        clientId: validationResult.payload!.client_id,
        clientName: validationResult.payload!.client_name,
        resource: validationResult.payload!.resource,
        scope: validationResult.payload!.scope,
    };

    return next();
};

/**
 * Middleware to check if token was issued by specific client
 */
export const requireClient =
    (clientId: string): RequestHandler =>
    (req: AuthenticatedRequest, res, next) => {
        if (!req.tokenPayload) {
            return res.status(401).json({
                error: 'unauthorized',
                error_description: 'Token validation required',
            });
        }

        if (req.tokenPayload.client_id !== clientId) {
            return res.status(403).json({
                error: 'access_denied',
                error_description: `Access denied. Token not issued for client '${clientId}'`,
            });
        }

        return next();
    };

/**
 * Pre-built protection combinations
 */
export const protectRoute = {
    // Basic protection - just requires valid token
    basic: validateBearerToken,

    // E2E testing - requires valid token + e2e client
    e2e: [validateBearerToken, requireClient('e2e')],
};

import { ObjectId } from 'mongodb';
import {
    ConditionType,
    ConsentsAndDeclarations,
    ConsentsAndDeclarationsPurpose,
    ConsentsAndDeclarationsType,
    DataField,
} from '../../server/database/documents';
import { consentModule } from './modules';
import { advancedVersioning } from './shared';

const consentsAndDeclarations: ConsentsAndDeclarations[] = [
    {
        _id: new ObjectId(),
        displayName: 'Test consents and declarations',
        isActive: true,
        isDeleted: false,
        _type: ConsentsAndDeclarationsType.Checkbox,
        _versioning: advancedVersioning,
        conditions: [{ type: ConditionType.IsApplicant }],
        dataField: DataField.DataProcessing,
        featurePurpose: null,
        isMandatory: true,
        legalMarkup: { defaultValue: '', overrides: [] },
        moduleId: consentModule._id,
        orderNumber: 0,
        purpose: [ConsentsAndDeclarationsPurpose.KYC],
        description: {
            defaultValue: 'Used for test',
            overrides: [],
        },
    },
];

export default consentsAndDeclarations;

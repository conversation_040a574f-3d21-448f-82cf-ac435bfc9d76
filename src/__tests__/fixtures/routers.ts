import { ObjectId } from 'mongodb';
import { AuthorKind, EndpointType, Router, LayoutType } from '../../server/database/documents';
import companies from './companies';
import launchPadModule from './modules/launchPadModule';
import { languagePacks } from './shared';

export const adminPermissionId = new ObjectId('601f7ae763d6cfc2554f6b68');
export const suiteId = new ObjectId('601f7ae763d6cfc2554f6b68');

export const routers: Router[] = [
    {
        _id: new ObjectId(),
        _versioning: {
            createdBy: { kind: AuthorKind.System },
            createdAt: new Date(),
            updatedBy: { kind: AuthorKind.System },
            updatedAt: new Date(),
        },
        companyId: companies[0]._id,
        endpoints: [
            {
                _id: new ObjectId(),
                _type: EndpointType.LaunchPadApplicationEntrypoint,
                displayName: 'Test Launchpad',
                launchPadApplicationModuleId: launchPadModule._id,
                pathname: '/',
            },
        ],
        hostname: 'localhost',
        languages: [languagePacks[0]._id],
        layout: {
            _type: LayoutType.PorscheV3,
        },
        menuItems: [],
        pathname: '/launchpad-test',
        pathScripts: [],
        withAdmin: true,
    },
];

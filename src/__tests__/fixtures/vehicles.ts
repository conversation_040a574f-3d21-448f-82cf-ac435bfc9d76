import { ObjectId } from 'mongodb';
import {
    LocalVariant,
    VehicleKind,
    BodyType,
    EngineType,
    LocalMake,
    LocalModel,
} from '../../server/database/documents';
import { simpleVehicleManagementModule } from './modules/shared';
import { advancedVersioning } from './shared';

export const localMakes: LocalMake[] = [
    {
        _id: new ObjectId(),
        _kind: VehicleKind.LocalMake,
        _versioning: advancedVersioning,
        identifier: 'POR',
        isActive: true,
        isDeleted: false,
        moduleId: simpleVehicleManagementModule._id,
        name: { defaultValue: 'Porsche', overrides: [] },
        order: 0,
    },
];

export const localModels: LocalModel[] = [
    {
        _id: new ObjectId(),
        _kind: VehicleKind.LocalModel,
        _versioning: advancedVersioning,
        identifier: '777',
        bodyType: BodyType.Coupe,
        isActive: true,
        isDeleted: false,
        moduleId: simpleVehicleManagementModule._id,
        name: { defaultValue: '777', overrides: [] },
        makeId: localMakes[0]._id,
        order: 0,
    },
];

export const localVariants: LocalVariant[] = [
    {
        _id: new ObjectId(),
        _versioning: advancedVersioning,
        _kind: VehicleKind.LocalVariant,
        bodyType: BodyType.Coupe,
        description: { defaultValue: '', overrides: [] },
        engine: { defaultValue: 'ICE', overrides: [] },
        engineType: EngineType.Petrol,
        features: { defaultValue: '', overrides: [] },
        identifier: '99999',
        isActive: true,
        isDeleted: false,
        modelId: localModels[0]._id,
        name: { defaultValue: '911 S/T', overrides: [] },
        moduleId: simpleVehicleManagementModule._id,
        order: 0,
        vehiclePrice: 100000,
    },
];

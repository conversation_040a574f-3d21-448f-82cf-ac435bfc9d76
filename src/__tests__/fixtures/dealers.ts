import { ObjectId } from 'mongodb';
import { AuthorKind, Dealer } from '../../server/database/documents';
import companies from './companies';

const dealers: Dealer[] = [
    {
        _id: new ObjectId(),
        displayName: 'Test Dealer',
        legalName: {
            defaultValue: 'Test Dealer',
            overrides: [],
        },
        coe: 0,
        ppsr: 0,
        estFee: 0,
        _versioning: {
            createdBy: { kind: AuthorKind.System },
            createdAt: new Date(),
            updatedBy: { kind: AuthorKind.System },
            updatedAt: new Date(),
        },
        isActive: true,
        isDeleted: false,
        companyId: companies[0]._id,
        contact: { socialMedia: [] },
        integrationDetails: { additionalParameter: [] },
        limitFeature: false,
        location: null,
    },
    {
        _id: new ObjectId(),
        displayName: 'Test Dealer 1',
        legalName: {
            defaultValue: 'Test Dealer 1',
            overrides: [],
        },
        coe: 0,
        ppsr: 0,
        estFee: 0,
        _versioning: {
            createdBy: { kind: AuthorKind.System },
            createdAt: new Date(),
            updatedBy: { kind: AuthorKind.System },
            updatedAt: new Date(),
        },
        isActive: true,
        isDeleted: false,
        companyId: companies[1]._id,
        contact: { socialMedia: [] },
        integrationDetails: { additionalParameter: [] },
        limitFeature: false,
        location: null,
    },
];

export default dealers;

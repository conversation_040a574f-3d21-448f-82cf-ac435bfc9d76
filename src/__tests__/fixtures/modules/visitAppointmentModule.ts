import { ObjectId } from 'mongodb';
import { ModuleType, TimeSlotEnum, VisitAppointmentModule } from '../../../server/database/documents';
import companies from '../companies';
import { commonVersioning } from '../shared';

const visitAppointmentModule: VisitAppointmentModule = {
    _id: new ObjectId(),
    _type: ModuleType.VisitAppointmentModule,
    companyId: companies[0]._id,
    displayName: 'Test Visit Appointment Module',
    advancedBookingLimit: 1,
    bookingTimeSlot: [{ bookingLimit: 1, slot: new Date(2080, 6, 1, 9, 0, 0, 0), _type: TimeSlotEnum.Appointment }],
    emailContents: {
        customer: {
            bookingAmendment: null,
            bookingComplete: null,
            bookingCancellation: {
                subject: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
                introTitle: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
                contentText: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
                introImage: null,
                isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
            },
            bookingConfirmation: null,
            submitConfirmation: null,
        },
        salesPerson: {
            submitConfirmation: null,
            bookingAmendment: {
                subject: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                introImage: undefined,
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                isSummaryVehicleVisible: {
                    defaultValue: false,
                    overrides: [],
                },
            },
            bookingConfirmation: {
                subject: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                introImage: undefined,
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                isSummaryVehicleVisible: {
                    defaultValue: false,
                    overrides: [],
                },
            },
            bookingCancellation: {
                subject: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
                introTitle: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
                contentText: { defaultValue: { defaultValue: null, overrides: [] }, overrides: [] },
                introImage: null,
                isSummaryVehicleVisible: { defaultValue: false, overrides: [] },
            },
        },
    },
    unavailableDayOfWeek: [],
    bookingInformation: { defaultValue: '', overrides: [] },
    maxAdvancedBookingLimit: 2,
    _versioning: commonVersioning,
};

export default visitAppointmentModule;

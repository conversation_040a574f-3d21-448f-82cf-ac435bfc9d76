import { ObjectId } from 'mongodb';
import { AppointmentModule, ModuleType, TimeSlotEnum } from '../../../server/database/documents';
import companies from '../companies';
import { commonVersioning } from '../shared';

const appointmentModule: AppointmentModule = {
    _id: new ObjectId(),
    _type: ModuleType.AppointmentModule,
    companyId: companies[0]._id,
    displayName: 'Test Visit Appointment Module',
    advancedBookingLimit: 1,
    bookingTimeSlot: [{ bookingLimit: 1, slot: new Date(2080, 6, 1, 9, 0, 0, 0), _type: TimeSlotEnum.Appointment }],
    unavailableDayOfWeek: [],
    bookingInformation: { defaultValue: '', overrides: [] },
    maxAdvancedBookingLimit: 2,
    _versioning: commonVersioning,
    hasTestDriveProcess: true,
    showRemoteFlowButtonInKYCPage: false,
    hasTestDriveSigning: false,
    isReminderTimeEnabled: false,
    emailContents: null,
};

export default appointmentModule;

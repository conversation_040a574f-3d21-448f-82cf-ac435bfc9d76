import { ObjectId } from 'mongodb';
import {
    Customer,
    CustomerKind,
    LocalCustomerFieldKey,
    LocalCustomerFieldSource,
} from '../../server/database/documents/Customer';
import { localCustomerManagementModule } from './modules/shared';
import { advancedVersioning } from './shared';

const customers: Customer[] = [
    {
        _id: new ObjectId(),
        _kind: CustomerKind.Local,
        moduleId: localCustomerManagementModule._id,
        _versioning: advancedVersioning,
        isDeleted: false,
        kycPresetIds: [],
        fields: [
            {
                key: LocalCustomerFieldKey.Title,
                source: LocalCustomerFieldSource.UserInput,
                value: 'Mr',
            },
            {
                key: LocalCustomerFieldKey.FirstName,
                source: LocalCustomerFieldSource.UserInput,
                deterministicString: 'FirstName',
            },
            {
                key: LocalCustomerFieldKey.LastName,
                source: LocalCustomerFieldSource.UserInput,
                deterministicString: 'LastName',
            },
        ],
    },
];

export default customers;

import { ObjectId } from 'mongodb';
import {
    LocalCustomerFieldKey as GraphQlLocalCustomerFieldKey,
    LocalCustomerFieldSettings,
    LocalCustomerFieldSource,
} from '../../app/api/types';
import { ApplicationKind, Lead, LeadStatus } from '../../server/database/documents';
import { LocalCustomerFieldSource as DocumentLocalCustomerFieldSource } from '../../server/database/documents/Customer';
import companies from './companies';
import customers from './customers';
import dealers from './dealers';
import launchPadModule from './modules/launchPadModule';
import { consentModule } from './modules/shared';
import { routers } from './routers';
import { adminPermissionId, advancedVersioning, languagePacks } from './shared';
import { localVariants } from './vehicles';

export const leads: Lead[] = [
    {
        _id: new ObjectId(),
        kind: ApplicationKind.Launchpad,
        customerId: customers[0]._id,
        documents: [],
        identifier: 'L250500099',
        isDraft: false,
        isLead: true,
        moduleId: launchPadModule._id,
        dealerId: dealers[0]._id,
        status: LeadStatus.SubmittedToCap,
        tradeInVehicle: [{ isSelected: true, source: DocumentLocalCustomerFieldSource.UserInput }],
        assigneeId: adminPermissionId,
        campaignValues: {},
        capValues: {
            submissionSucceeded: [],
            competitorVehicleGuids: [],
            businessPartnerGuid: 'E31095D41F321FD08DC26A598AD8DF68',
            businessPartnerId: '7000084031',
        },
        customerAgreements: { moduleId: consentModule._id, agreements: [] },
        intentType: null,
        endpointId: routers[0].endpoints[0]._id,
        languageId: languagePacks[0]._id,
        _versioning: advancedVersioning,
        companyId: companies[0]._id,
    },
    {
        _id: new ObjectId(),
        kind: ApplicationKind.Launchpad,
        customerId: customers[0]._id,
        documents: [],
        identifier: 'L250500100',
        isDraft: false,
        isLead: false,
        moduleId: launchPadModule._id,
        dealerId: dealers[0]._id,
        status: LeadStatus.PendingQualify,
        tradeInVehicle: [{ isSelected: true, source: DocumentLocalCustomerFieldSource.UserInput }],
        assigneeId: adminPermissionId,
        campaignValues: {},
        capValues: null,
        customerAgreements: { moduleId: consentModule._id, agreements: [] },
        intentType: null,
        endpointId: routers[0].endpoints[0]._id,
        languageId: languagePacks[0]._id,
        _versioning: advancedVersioning,
        companyId: companies[0]._id,
    },
    {
        _id: new ObjectId(),
        kind: ApplicationKind.Standard,
        customerId: customers[0]._id,
        documents: [],
        identifier: 'L250500101',
        isDraft: false,
        isLead: false,
        moduleId: launchPadModule._id,
        dealerId: dealers[0]._id,
        status: LeadStatus.PendingQualify,
        tradeInVehicle: [{ isSelected: true, source: DocumentLocalCustomerFieldSource.UserInput }],
        assigneeId: adminPermissionId,
        campaignValues: {},
        capValues: null,
        customerAgreements: { moduleId: consentModule._id, agreements: [] },
        endpointId: routers[0].endpoints[0]._id,
        languageId: languagePacks[0]._id,
        _versioning: advancedVersioning,
        vehicleId: localVariants[0]._id,
        companyId: companies[0]._id,
    },
];

export const contactData: LocalCustomerFieldSettings[] = [
    {
        key: GraphQlLocalCustomerFieldKey.Title,
        source: LocalCustomerFieldSource.UserInput,
        stringValue: 'Mr',
    },
    {
        key: GraphQlLocalCustomerFieldKey.FirstName,
        source: LocalCustomerFieldSource.UserInput,
        stringValue: 'FirstName',
    },
    {
        key: GraphQlLocalCustomerFieldKey.LastName,
        source: LocalCustomerFieldSource.UserInput,
        stringValue: 'LastName',
    },
    {
        key: GraphQlLocalCustomerFieldKey.Email,
        source: LocalCustomerFieldSource.UserInput,
        stringValue: '<EMAIL>',
    },
    {
        key: GraphQlLocalCustomerFieldKey.Phone,
        source: LocalCustomerFieldSource.UserInput,
        phoneValue: {
            prefix: 65,
            value: '11111111111',
        },
    },
    {
        key: GraphQlLocalCustomerFieldKey.Birthday,
        source: LocalCustomerFieldSource.UserInput,
        dateValue: '1990-11-10T17:00:00.000Z',
    },
];

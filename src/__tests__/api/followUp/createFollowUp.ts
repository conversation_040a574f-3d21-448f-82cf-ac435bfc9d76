import { gql } from '@apollo/client';
import companies from '../../fixtures/companies';
import consentsAndDeclarations from '../../fixtures/consentsAndDeclarations';
import customers from '../../fixtures/customers';
import dealers from '../../fixtures/dealers';
import { leads } from '../../fixtures/leads';
import launchPadModule from '../../fixtures/modules/launchPadModule';
import {
    consentModule,
    localCustomerManagementModule,
    simpleVehicleManagementModule,
} from '../../fixtures/modules/shared';
import permissions from '../../fixtures/permissions';
import users, { defaultAdminUserId } from '../../fixtures/users';
import { getApolloClient } from '../../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../../helpers/database';
import { setupWebServer } from '../../helpers/server';

const mutation = gql`
    mutation createLeadFollowUp($leadId: ObjectID!, $followUpValues: CreateLeadFollowUpInput!) {
        result: createLeadFollowUp(leadId: $leadId, followUpValues: $followUpValues)
    }
`;

const webServer = setupWebServer();

describe('Follow Up Create Tests', () => {
    let client: ReturnType<typeof getApolloClient>['client'];
    let originalToken: string;
    let csrf: string;

    beforeAll(async () => {
        await webServer.initialize();

        await setupDatabase();

        const modules = [consentModule, localCustomerManagementModule, launchPadModule, simpleVehicleManagementModule];

        await loadFixtures('companies', companies);
        await loadFixtures('dealers', dealers);
        await loadFixtures('consentsAndDeclarations', consentsAndDeclarations);
        await loadFixtures('modules', modules);
        await loadFixtures('permissions', permissions);
        await loadFixtures('users', users);
        await loadFixtures('customers', customers);
        await loadFixtures('leads', leads);

        const session = await createSessionForUser(defaultAdminUserId);
        originalToken = session.token;
        csrf = session.csrf;
        client = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf }).client;
    });

    afterAll(async () => {
        await cleanDatabase();
        await webServer.cleanUp();
    });

    test('Create follow up successfully with valid input', async () => {
        const { data } = await client.mutate({
            mutation,
            variables: {
                leadId: leads[0]._id,
                followUpValues: {
                    remarks: 'Test',
                    scheduledDate: '2080-06-01T16:06:00.200Z',
                },
            },
        });
        expect(data).not.toBeNull();
        expect(data.result).toBeTruthy();
    });

    test('Create follow up with missing required fields', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    followUpValues: {
                        remarks: 'Test',
                        scheduledDate: '2080-06-01T16:06:00.200Z',
                    },
                },
            })
        ).rejects.toMatchObject({
            message: 'Response not successful: Received status code 400',
        });
    });

    test('Create follow up with invalid lead which not submitted to C@p yet', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    leadId: leads[1]._id,
                    followUpValues: {
                        remarks: 'Test',
                        scheduledDate: '2080-06-01T16:06:00.200Z',
                    },
                },
            })
        ).rejects.toMatchObject({
            message: 'Lead with current status cannot be followed up.',
        });
    });

    test('Create follow up without required permission', async () => {
        const { token: originalToken, csrf } = await createSessionForUser(users[1]._id);
        const { client: nonAuthorizedClient } = getApolloClient(webServer.url, {
            authorizationToken: originalToken,
            csrf,
        });

        await expect(
            nonAuthorizedClient.mutate({
                mutation,
                variables: {
                    leadId: leads[0]._id,
                    followUpValues: {
                        remarks: 'Test',
                        scheduledDate: '2080-06-01T16:06:00.200Z',
                    },
                },
            })
        ).rejects.toMatchObject({
            message: 'forbidden',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'FORBIDDEN',
                    },
                },
            ],
        });
    });
});

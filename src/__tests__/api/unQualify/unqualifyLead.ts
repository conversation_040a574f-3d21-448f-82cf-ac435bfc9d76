import { gql } from '@apollo/client';
import consentsAndDeclarations from '../../fixtures/consentsAndDeclarations';
import { leads } from '../../fixtures/leads';
import permissions from '../../fixtures/permissions';
import users, { defaultAdminUserId } from '../../fixtures/users';
import { getApolloClient } from '../../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../../helpers/database';
import { setupWebServer } from '../../helpers/server';

const mutation = gql`
    mutation unqualifyLead($leadId: ObjectID!) {
        result: unqualifyLead(leadId: $leadId)
    }
`;

const webServer = setupWebServer();

describe('Unqualify Create Tests', () => {
    let client: ReturnType<typeof getApolloClient>['client'];
    let originalToken: string;
    let csrf: string;

    beforeAll(async () => {
        await webServer.initialize();

        await setupDatabase();

        await loadFixtures('permissions', permissions);
        await loadFixtures('consentsAndDeclarations', consentsAndDeclarations);
        await loadFixtures('users', users);
        await loadFixtures('leads', leads);

        const session = await createSessionForUser(defaultAdminUserId);
        originalToken = session.token;
        csrf = session.csrf;
        client = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf }).client;
    });

    afterAll(async () => {
        await cleanDatabase();

        await webServer.cleanUp();
    });

    test('Unqualify Lead/Contact successfully with valid input', async () => {
        const { data } = await client.mutate({
            mutation,
            variables: {
                leadId: leads[1]._id,
            },
        });
        expect(data).not.toBeNull();
        expect(data.result).toBeTruthy();
    });

    test('Unqualify Lead/Contact successfully with invalid lead', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    leadId: leads[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
        });
    });

    test('Unqualify Lead/Contact with missing required fields', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {},
            })
        ).rejects.toMatchObject({
            message: 'Response not successful: Received status code 400',
        });
    });

    test('Unqualify Lead/Contact without required permission', async () => {
        const { token: originalToken, csrf } = await createSessionForUser(users[1]._id);
        const { client: nonAuthorizedClient } = getApolloClient(webServer.url, {
            authorizationToken: originalToken,
            csrf,
        });
        await expect(
            nonAuthorizedClient.mutate({
                mutation,
                variables: {
                    leadId: leads[1]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'forbidden',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'FORBIDDEN',
                    },
                },
            ],
        });
    });
});

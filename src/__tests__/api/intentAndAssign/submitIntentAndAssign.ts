import { gql } from '@apollo/client';
import companies from '../../fixtures/companies';
import consentsAndDeclarations from '../../fixtures/consentsAndDeclarations';
import customers from '../../fixtures/customers';
import { leads } from '../../fixtures/leads';
import {
    consentModule,
    eventModule,
    localCustomerManagementModule,
    simpleVehicleManagementModule,
} from '../../fixtures/modules';
import launchPadModule from '../../fixtures/modules/launchPadModule';
import permissions from '../../fixtures/permissions';
import { languagePacks } from '../../fixtures/shared';
import users, { defaultAdminUserId } from '../../fixtures/users';
import { localVariants } from '../../fixtures/vehicles';
import { getApolloClient } from '../../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../../helpers/database';
import { setupWebServer } from '../../helpers/server';

const mutation = gql`
    mutation submitIntentAndAssign($leadId: ObjectID!, $submitIntentAndAssignInput: SubmitIntentAndAssignInput!) {
        result: submitIntentAndAssign(leadId: $leadId, input: $submitIntentAndAssignInput)
    }
`;

const webServer = setupWebServer();

describe('Intent And Assign Create Tests', () => {
    let client: ReturnType<typeof getApolloClient>['client'];
    let originalToken: string;
    let csrf: string;

    beforeAll(async () => {
        await webServer.initialize();

        await setupDatabase();

        const modules = [
            consentModule,
            launchPadModule,
            localCustomerManagementModule,
            simpleVehicleManagementModule,
            eventModule,
        ];

        await loadFixtures('companies', companies);
        await loadFixtures('modules', modules);
        await loadFixtures('consentsAndDeclarations', consentsAndDeclarations);
        await loadFixtures('permissions', permissions);
        await loadFixtures('users', users);
        await loadFixtures('customers', customers);
        await loadFixtures('leads', leads);

        const session = await createSessionForUser(defaultAdminUserId);
        originalToken = session.token;
        csrf = session.csrf;
        client = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf }).client;
    });

    afterAll(async () => {
        // use setTimeout to fix "Jest did not exit one second after the test run has completed." issue
        setTimeout(async () => {
            await cleanDatabase();
            await webServer.cleanUp();
        });
    });

    test('Submit intent&assign successfully with valid input', async () => {
        const { data } = await client.mutate({
            mutation,
            variables: {
                leadId: leads[0]._id,
                submitIntentAndAssignInput: {
                    vehicleId: localVariants[0]._id,
                    assigneeId: defaultAdminUserId,
                    vehicleCondition: 'New',
                    campaignId: 'PAP7900001-M-CP001-24-TEST',
                    purchaseIntention: '2025-06-02T03:55:53.282Z',
                    intentType: 'ScheduledAppointment',
                    purposeOfVisit: 'NewVehicleEnquiryPurchase',
                    languageId: languagePacks[0]._id,
                },
            },
        });

        expect(data).not.toBeNull();
        expect(data.result).toBeFalsy();
    });

    test('Submit intent&assign with missing required fields', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    leadId: leads[0]._id,
                    submitIntentAndAssignInput: {
                        vehicleId: localVariants[0]._id,
                        assigneeId: defaultAdminUserId,
                        vehicleCondition: 'New',
                        purchaseIntention: '2025-06-02T03:55:53.282Z',
                        intentType: 'ScheduledAppointment',
                        purposeOfVisit: 'NewVehicleEnquiryPurchase',
                        languageId: languagePacks[0]._id,
                    },
                },
            })
        ).rejects.toMatchObject({
            message: 'Response not successful: Received status code 400',
        });
    });

    test('Submit intent&assign with invalid lead', async () => {
        const { data } = await client.mutate({
            mutation,
            variables: {
                leadId: leads[2]._id,
                submitIntentAndAssignInput: {
                    vehicleId: localVariants[0]._id,
                    assigneeId: defaultAdminUserId,
                    vehicleCondition: 'New',
                    campaignId: 'PAP7900001-M-CP001-24-TEST',
                    purchaseIntention: '2025-06-02T03:55:53.282Z',
                    intentType: 'ScheduledAppointment',
                    purposeOfVisit: 'NewVehicleEnquiryPurchase',
                    languageId: languagePacks[0]._id,
                },
            },
        });

        expect(data).not.toBeNull();
        expect(data.result).toBeFalsy();
    });

    test('Submit intent&assign without required permission', async () => {
        const { token: originalToken, csrf } = await createSessionForUser(users[1]._id);
        const { client: nonAuthorizedClient } = getApolloClient(webServer.url, {
            authorizationToken: originalToken,
            csrf,
        });
        await expect(
            nonAuthorizedClient.mutate({
                mutation,
                variables: {
                    leadId: leads[0]._id,
                    submitIntentAndAssignInput: {
                        vehicleId: localVariants[0]._id,
                        assigneeId: defaultAdminUserId,
                        vehicleCondition: 'New',
                        campaignId: 'PAP7900001-M-CP001-24-TEST',
                        purchaseIntention: '2025-06-02T03:55:53.282Z',
                        intentType: 'ScheduledAppointment',
                        purposeOfVisit: 'NewVehicleEnquiryPurchase',
                        languageId: languagePacks[0]._id,
                    },
                },
            })
        ).rejects.toMatchObject({
            message: 'User not found',
        });
    });
});

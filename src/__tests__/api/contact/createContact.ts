import { gql } from '@apollo/client';
import { ObjectId } from 'mongodb';
import { LocalCustomerFieldSource } from '../../../app/api/types';
import { EventApplicationModule, LaunchPadModule, Lead } from '../../../server/database';
import getDatabaseContext from '../../../server/database/getDatabaseContext';
import companies from '../../fixtures/companies';
import consentsAndDeclarations from '../../fixtures/consentsAndDeclarations';
import dealers from '../../fixtures/dealers';
import { contactData } from '../../fixtures/leads';
import {
    consentModule,
    eventModule,
    localCustomerManagementModule,
    simpleVehicleManagementModule,
} from '../../fixtures/modules';
import launchPadModule from '../../fixtures/modules/launchPadModule';
import permissions from '../../fixtures/permissions';
import { routers } from '../../fixtures/routers';
import { languagePacks } from '../../fixtures/shared';
import users, { defaultAdminUserId } from '../../fixtures/users';
import { getApolloClient } from '../../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../../helpers/database';
import { setupWebServer } from '../../helpers/server';

const mutation = gql`
    mutation createNewContactFromBp(
        $applicationModuleId: ObjectID!
        $dealerId: ObjectID!
        $capValues: CapValuesInput!
        $contactData: [LocalCustomerFieldSettings!]!
        $contactDocuments: [ApplicationDocumentPayload!]!
        $tradeInVehicle: [TradeInVehiclePayload!]!
        $customerAgreements: [ApplicantAgreementSettings!]!
        $endpointId: ObjectID!
        $languageId: ObjectID!
    ) {
        result: createNewContactFromBp(
            applicationModuleId: $applicationModuleId
            dealerId: $dealerId
            capValues: $capValues
            contactData: $contactData
            contactDocuments: $contactDocuments
            tradeInVehicle: $tradeInVehicle
            customerAgreements: $customerAgreements
            endpointId: $endpointId
            languageId: $languageId
        ) {
            id
            identifier
            customerId
            status
            capValues {
                businessPartnerGuid
                businessPartnerId
                __typename
            }
            versioning {
                ...AdvancedVersioningData
                __typename
            }
            __typename
        }
    }
    fragment AdvancedVersioningData on AdvancedVersioning {
        suiteId
        isLatest
        createdBy {
            ...AuthorData
            __typename
        }
        createdAt
        updatedBy {
            ...AuthorData
            __typename
        }
        updatedAt
        __typename
    }

    fragment AuthorData on Author {
        ... on User {
            id
            email
            displayName
            __typename
        }
        ... on LocalCustomer {
            id
            fullName
            email
            __typename
        }
        __typename
    }
`;

const webServer = setupWebServer();

describe('Contact Create Tests', () => {
    let client: ReturnType<typeof getApolloClient>['client'];
    let originalToken: string;
    let csrf: string;

    beforeAll(async () => {
        await webServer.initialize();

        await setupDatabase();

        const modules = [
            consentModule,
            launchPadModule,
            localCustomerManagementModule,
            simpleVehicleManagementModule,
            eventModule,
        ];

        await loadFixtures('companies', companies);
        await loadFixtures('modules', modules);
        await loadFixtures('languagePacks', languagePacks);
        await loadFixtures('routers', routers);
        await loadFixtures('dealers', dealers);
        await loadFixtures('consentsAndDeclarations', consentsAndDeclarations);
        await loadFixtures('permissions', permissions);
        await loadFixtures('users', users);

        const session = await createSessionForUser(defaultAdminUserId);
        originalToken = session.token;
        csrf = session.csrf;
        client = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf }).client;
    });

    afterAll(async () => {
        await cleanDatabase();

        await webServer.cleanUp();
    });

    test('Create contact successfully with valid input', async () => {
        const { collections } = await getDatabaseContext();
        const module = await collections.modules.findOne<LaunchPadModule>({ _id: launchPadModule._id });

        const { data } = await client.mutate({
            mutation,
            variables: {
                applicationModuleId: module._id,
                dealerId: dealers[0]._id,
                capValues: {},
                contactData,
                contactDocuments: [],
                tradeInVehicle: [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }],
                customerAgreements: [{ id: consentModule._id }],
                endpointId: routers[0].endpoints[0]._id,
                languageId: languagePacks[0]._id,
            },
        });

        const contact = await collections.leads.findOne<Lead>({ _id: new ObjectId(data.result.id) });

        expect(contact).not.toBeNull();
        expect(contact._id.toString()).toBe(data.result.id);
        expect(contact.moduleId).toEqual(module._id);
        expect(contact.isLead).toBeFalsy();
    });

    test('Create contact with missing required fields', async () => {
        const { collections } = await getDatabaseContext();
        const module = await collections.modules.findOne<LaunchPadModule>({ _id: launchPadModule._id });
        await expect(
            client.mutate({
                mutation,
                variables: {
                    applicationModuleId: module._id,
                    capValues: {},
                    contactData,
                    contactDocuments: [],
                    tradeInVehicle: [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }],
                    customerAgreements: [{ id: consentModule._id }],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'Response not successful: Received status code 400',
        });
    });

    test('Create contact with invalid application module', async () => {
        const { collections } = await getDatabaseContext();
        const module = await collections.modules.findOne<EventApplicationModule>({ _id: eventModule._id });
        await expect(
            client.mutate({
                mutation,
                variables: {
                    applicationModuleId: module._id,
                    dealerId: dealers[0]._id,
                    capValues: {},
                    contactData,
                    contactDocuments: [],
                    tradeInVehicle: [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }],
                    customerAgreements: [{ id: consentModule._id }],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
        });
    });

    test('Create contact with dealer of diffrent company', async () => {
        const { collections } = await getDatabaseContext();
        const module = await collections.modules.findOne<LaunchPadModule>({ _id: launchPadModule._id });
        await expect(
            client.mutate({
                mutation,
                variables: {
                    applicationModuleId: module._id,
                    dealerId: dealers[1]._id,
                    capValues: {},
                    contactData,
                    contactDocuments: [],
                    tradeInVehicle: [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }],
                    customerAgreements: [{ id: consentModule._id }],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'bad request',
        });
    });

    test('Create contact without required permission', async () => {
        const { token: originalToken, csrf } = await createSessionForUser(users[1]._id);
        const { client: nonAuthorizedClient } = getApolloClient(webServer.url, {
            authorizationToken: originalToken,
            csrf,
        });

        const { collections } = await getDatabaseContext();
        const module = await collections.modules.findOne<LaunchPadModule>({ _id: launchPadModule._id });

        await expect(
            nonAuthorizedClient.mutate({
                mutation,
                variables: {
                    applicationModuleId: module._id,
                    dealerId: dealers[0]._id,
                    capValues: {},
                    contactData,
                    contactDocuments: [],
                    tradeInVehicle: [{ isSelected: true, source: LocalCustomerFieldSource.UserInput }],
                    customerAgreements: [{ id: consentModule._id }],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'forbidden',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'FORBIDDEN',
                    },
                },
            ],
        });
    });
});

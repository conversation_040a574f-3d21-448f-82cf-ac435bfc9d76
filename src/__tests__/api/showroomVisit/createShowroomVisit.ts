import { gql } from '@apollo/client';
import { ObjectId } from 'mongodb';
import { Application, LaunchPadModule } from '../../../server/database';
import getDatabaseContext from '../../../server/database/getDatabaseContext';
import companies from '../../fixtures/companies';
import consentsAndDeclarations from '../../fixtures/consentsAndDeclarations';
import { leads } from '../../fixtures/leads';
import {
    consentModule,
    eventModule,
    localCustomerManagementModule,
    simpleVehicleManagementModule,
} from '../../fixtures/modules';
import launchPadModule from '../../fixtures/modules/launchPadModule';
import permissions from '../../fixtures/permissions';
import { routers } from '../../fixtures/routers';
import { languagePacks } from '../../fixtures/shared';
import users, { defaultAdminUserId } from '../../fixtures/users';
import { getApolloClient } from '../../helpers/apollo';
import { cleanDatabase, createSessionForUser, loadFixtures, setupDatabase } from '../../helpers/database';
import { setupWebServer } from '../../helpers/server';

const mutation = gql`
    mutation createShowroomVisitAppointmentFromLead(
        $leadId: ObjectID!
        $bookingTimeSlot: SubmitAppointmentBookingTimeSlot!
        $agreedConsents: [ApplicantAgreementSettings!]!
        $endpointId: ObjectID
        $languageId: ObjectID
    ) {
        result: createShowroomVisitAppointmentFromLead(
            leadId: $leadId
            bookingTimeSlot: $bookingTimeSlot
            agreedConsents: $agreedConsents
            endpointId: $endpointId
            languageId: $languageId
        ) {
            ... on Application {
                id
                versioning {
                    ...AdvancedVersioningData
                    __typename
                }
                __typename
            }
            __typename
        }
    }

    fragment AdvancedVersioningData on AdvancedVersioning {
        suiteId
        isLatest
        createdBy {
            ...AuthorData
            __typename
        }
        createdAt
        updatedBy {
            ...AuthorData
            __typename
        }
        updatedAt
        __typename
    }

    fragment AuthorData on Author {
        ... on User {
            id
            email
            displayName
            __typename
        }
        ... on LocalCustomer {
            id
            fullName
            email
            __typename
        }
        __typename
    }
`;

const webServer = setupWebServer();

describe('Showroom Visit Create Tests', () => {
    let client: ReturnType<typeof getApolloClient>['client'];
    let originalToken: string;
    let csrf: string;

    beforeAll(async () => {
        await webServer.initialize();

        await setupDatabase();

        const modules = [
            consentModule,
            launchPadModule,
            localCustomerManagementModule,
            simpleVehicleManagementModule,
            eventModule,
        ];

        await loadFixtures('companies', companies);
        await loadFixtures('modules', modules);
        await loadFixtures('languagePacks', languagePacks);
        await loadFixtures('routers', routers);
        await loadFixtures('consentsAndDeclarations', consentsAndDeclarations);
        await loadFixtures('permissions', permissions);
        await loadFixtures('users', users);
        await loadFixtures('leads', leads);

        const session = await createSessionForUser(defaultAdminUserId);
        originalToken = session.token;
        csrf = session.csrf;
        client = getApolloClient(webServer.url, { authorizationToken: originalToken, csrf }).client;
    });

    afterAll(async () => {
        await cleanDatabase();

        await webServer.cleanUp();
    });

    test('Create showroom visit successfully with valid input', async () => {
        const { collections } = await getDatabaseContext();
        const module = await collections.modules.findOne<LaunchPadModule>({ _id: launchPadModule._id });
        const { data } = await client.mutate({
            mutation,
            variables: {
                leadId: leads[0]._id,
                bookingTimeSlot: {
                    bookingLimit: 0,
                    slot: new Date(2080, 6, 1, 9, 0, 0, 0),
                    useCurrentDateTime: false,
                },
                agreedConsents: [],
                endpointId: routers[0].endpoints[0]._id,
                languageId: languagePacks[0]._id,
            },
        });

        const application = await collections.applications.findOne<Application>({ _id: new ObjectId(data.result.id) });

        expect(application).not.toBeNull();
        expect(application._id.toString()).toBe(data.result.id);
        expect(application.moduleId).toEqual(module._id);
        expect(application.leadId).toEqual(leads[0]._id);
    });

    test('Create showroom visit with missing required fields', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    bookingTimeSlot: {
                        bookingLimit: 0,
                        slot: new Date(2080, 6, 1, 9, 0, 0, 0),
                        useCurrentDateTime: false,
                    },
                    agreedConsents: [],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'Response not successful: Received status code 400',
        });
    });

    test('Create showroom visit with invalid lead', async () => {
        await expect(
            client.mutate({
                mutation,
                variables: {
                    leadId: leads[1]._id,
                    bookingTimeSlot: {
                        bookingLimit: 0,
                        slot: new Date(2080, 6, 1, 9, 0, 0, 0),
                        useCurrentDateTime: false,
                    },
                    agreedConsents: [],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'forbidden',
        });
    });

    test('Create showroom visit without required permission', async () => {
        const { token: originalToken, csrf } = await createSessionForUser(users[1]._id);
        const { client: nonAuthorizedClient } = getApolloClient(webServer.url, {
            authorizationToken: originalToken,
            csrf,
        });
        await expect(
            nonAuthorizedClient.mutate({
                mutation,
                variables: {
                    leadId: leads[0]._id,
                    bookingTimeSlot: {
                        bookingLimit: 0,
                        slot: new Date(2080, 6, 1, 9, 0, 0, 0),
                        useCurrentDateTime: false,
                    },
                    agreedConsents: [],
                    endpointId: routers[0].endpoints[0]._id,
                    languageId: languagePacks[0]._id,
                },
            })
        ).rejects.toMatchObject({
            message: 'forbidden',
            graphQLErrors: [
                {
                    extensions: {
                        code: 'FORBIDDEN',
                    },
                },
            ],
        });
    });
});

/**
 * Unit tests for appointmentTimeSlot validation functions
 * Tests all functions including edge cases and error scenarios
 */

import { ObjectId } from 'bson';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { BookingPeriodType } from '../../server/database/documents/Event';
import { TimeSlotEnum } from '../../server/database/documents/moduleShared';
import { DayOfWeek } from '../../server/database/documents/shared';
import { ModuleType } from '../../server/schema/resolvers/definitions';
import mockGetBlockedAppointmentTimeSlotsModule from '../../server/utils/application/getBlockedAppointmentTimeSlots';
import validateTimeSlot from '../../server/utils/application/validations/appointmentTimeSlot';

// Setup dayjs plugins
dayjs.extend(utc);
dayjs.extend(timezone);

// Mock dependencies
jest.mock('../../server/utils/application/getBlockedAppointmentTimeSlots');

const mockGetBlockedAppointmentTimeSlots = mockGetBlockedAppointmentTimeSlotsModule as jest.MockedFunction<
    typeof mockGetBlockedAppointmentTimeSlotsModule
>;

describe('appointmentTimeSlot validation functions', () => {
    // Test data setup
    const moduleId = new ObjectId();
    const eventId = new ObjectId();
    const companyId = new ObjectId();
    const timeZone = 'Asia/Singapore';

    const mockCompany = {
        _id: companyId,
        timeZone,
        sessionTimeout: 30,
    };

    const mockModule = {
        _id: moduleId,
        _type: ModuleType.AppointmentModule,
        companyId,
        bookingTimeSlot: [
            {
                slot: new Date('2025-06-25T10:00:00Z'),
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 2,
            },
            {
                slot: new Date('2025-06-25T14:00:00Z'),
                _type: TimeSlotEnum.Appointment,
                bookingLimit: 1,
            },
        ],
    };

    const mockVisitModule = {
        ...mockModule,
        _type: ModuleType.VisitAppointmentModule,
    };

    const mockEvent = {
        _id: eventId,
        customTestDriveBookingSlots: {
            isEnabled: true,
            bookingPeriodType: BookingPeriodType.FixedPeriod,
            fixedPeriods: [
                {
                    _id: new ObjectId(),
                    startDate: new Date('2025-06-25T00:00:00Z'),
                    endDate: new Date('2025-06-30T23:59:59Z'),
                    advancedBookingLimit: 0,
                    bookingTimeSlot: [
                        {
                            slot: new Date('2025-06-25T10:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 3,
                        },
                    ],
                },
            ],
        },
    };

    const mockEventBookingWindow = {
        _id: eventId,
        customTestDriveBookingSlots: {
            isEnabled: true,
            bookingPeriodType: BookingPeriodType.BookingWindow,
            bookingWindowSettings: {
                unavailableDayOfWeek: [DayOfWeek.Sunday],
                advancedBookingLimit: 1,
                maxAdvancedBookingLimit: 30,
                bookingTimeSlot: [
                    {
                        slot: new Date('2025-06-25T10:00:00Z'),
                        _type: TimeSlotEnum.Appointment,
                        bookingLimit: 2,
                    },
                ],
            },
        },
    };

    const mockLoaders = {
        moduleById: {
            load: jest.fn(),
        },
        companyById: {
            load: jest.fn(),
        },
        eventById: {
            load: jest.fn(),
        },
    } as any; // Use 'as any' to bypass type checking for the mock

    beforeEach(() => {
        jest.clearAllMocks();
        mockLoaders.moduleById.load.mockResolvedValue(mockModule);
        mockLoaders.companyById.load.mockResolvedValue(mockCompany);
        mockLoaders.eventById.load.mockResolvedValue(mockEvent);
        mockGetBlockedAppointmentTimeSlots.mockResolvedValue([]);
    });

    describe('validateTimeSlot', () => {
        const selectedTimeSlot = new Date('2025-06-25T10:00:00Z');

        it('should return false when loaders is undefined', async () => {
            const result = await validateTimeSlot(selectedTimeSlot, moduleId);
            expect(result).toBe(false);
        });

        it('should return false for unsupported module types', async () => {
            mockLoaders.moduleById.load.mockResolvedValue({
                ...mockModule,
                _type: 'UnsupportedModule',
            });

            const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(false);
        });

        it('should return false when company is not found', async () => {
            mockLoaders.companyById.load.mockResolvedValue(null);

            const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(false);
        });

        it('should return false when selected time is in the past', async () => {
            const pastTimeSlot = new Date('2023-01-01T10:00:00Z');

            const result = await validateTimeSlot(pastTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(false);
        });

        it('should return false when no matching booking time slot is found', async () => {
            const unmatchedTimeSlot = new Date('2025-06-25T15:00:00Z');

            const result = await validateTimeSlot(unmatchedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(false);
        });

        it('should return true when slot is available with standard module', async () => {
            const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(true);
        });

        it('should return false when slot is fully booked', async () => {
            // Mock that 2 slots are already booked (matching the booking limit)
            const blockedSlots = [new Date('2025-06-25T10:00:00Z'), new Date('2025-06-25T10:00:00Z')];
            mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

            const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(false);
        });

        it('should return true when booking limit is 0 (unlimited)', async () => {
            mockLoaders.moduleById.load.mockResolvedValue({
                ...mockModule,
                bookingTimeSlot: [
                    {
                        slot: new Date('2025-06-25T10:00:00Z'),
                        _type: TimeSlotEnum.Appointment,
                        bookingLimit: 0, // Unlimited
                    },
                ],
            });

            const blockedSlots = [
                new Date('2025-06-25T10:00:00Z'),
                new Date('2025-06-25T10:00:00Z'),
                new Date('2025-06-25T10:00:00Z'),
            ];
            mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

            const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(true);
        });

        it('should work with VisitAppointmentModule', async () => {
            mockLoaders.moduleById.load.mockResolvedValue(mockVisitModule);

            const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
            expect(result).toBe(true);
        });

        describe('Event custom slots - Fixed Period', () => {
            beforeEach(() => {
                mockLoaders.eventById.load.mockResolvedValue(mockEvent);
            });

            it('should use Event custom slots when eventId is provided and enabled', async () => {
                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true);
                expect(mockLoaders.eventById.load).toHaveBeenCalledWith(eventId);
            });

            it('should return false when selected date is outside fixed period', async () => {
                // Use a time that doesn't exist in module slots (15:00) and is outside fixed period
                const outsidePeriodTimeSlot = new Date('2025-07-01T15:00:00Z');

                const result = await validateTimeSlot(outsidePeriodTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(false);
            });

            it('should return false when no time slot matches in fixed period', async () => {
                const unmatchedTimeSlot = new Date('2025-06-25T15:00:00Z');

                const result = await validateTimeSlot(unmatchedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(false);
            });

            it('should fallback to module slots when custom slots are disabled', async () => {
                const eventWithDisabledSlots = {
                    ...mockEvent,
                    customTestDriveBookingSlots: {
                        ...mockEvent.customTestDriveBookingSlots,
                        isEnabled: false,
                    },
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithDisabledSlots);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true);
            });
        });

        describe('Event custom slots - Booking Window', () => {
            beforeEach(() => {
                mockLoaders.eventById.load.mockResolvedValue(mockEventBookingWindow);
            });

            it('should use booking window settings when configured', async () => {
                const thursdayTimeSlot = new Date('2025-06-26T10:00:00Z');

                const result = await validateTimeSlot(thursdayTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true);
            });

            it('should work when within booking window limits', async () => {
                const validFutureTimeSlot = new Date('2025-06-27T10:00:00Z');

                const result = await validateTimeSlot(validFutureTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true);
            });

            it('should handle no max advanced booking limit', async () => {
                const eventWithNoMaxLimit = {
                    ...mockEventBookingWindow,
                    customTestDriveBookingSlots: {
                        ...mockEventBookingWindow.customTestDriveBookingSlots,
                        bookingWindowSettings: {
                            ...mockEventBookingWindow.customTestDriveBookingSlots.bookingWindowSettings,
                            maxAdvancedBookingLimit: null,
                        },
                    },
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithNoMaxLimit);

                const farFutureTimeSlot = new Date('2025-08-15T10:00:00Z');

                const result = await validateTimeSlot(farFutureTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true);
            });

            it('should handle multiple booking time slots in booking window', async () => {
                const eventWithMultipleTimeSlots = {
                    ...mockEventBookingWindow,
                    customTestDriveBookingSlots: {
                        ...mockEventBookingWindow.customTestDriveBookingSlots,
                        bookingWindowSettings: {
                            ...mockEventBookingWindow.customTestDriveBookingSlots.bookingWindowSettings,
                            bookingTimeSlot: [
                                {
                                    slot: new Date('2025-06-25T09:00:00Z'),
                                    _type: TimeSlotEnum.Appointment,
                                    bookingLimit: 1,
                                },
                                {
                                    slot: new Date('2025-06-25T10:00:00Z'),
                                    _type: TimeSlotEnum.Appointment,
                                    bookingLimit: 2,
                                },
                                {
                                    slot: new Date('2025-06-25T14:00:00Z'),
                                    _type: TimeSlotEnum.Appointment,
                                    bookingLimit: 3,
                                },
                                {
                                    slot: new Date('2025-06-25T16:00:00Z'),
                                    _type: TimeSlotEnum.Appointment,
                                    bookingLimit: 1,
                                },
                            ],
                        },
                    },
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithMultipleTimeSlots);

                // Test first time slot (09:00)
                const timeSlot1 = new Date('2025-06-27T09:00:00Z');
                const result1 = await validateTimeSlot(timeSlot1, moduleId, mockLoaders, eventId);
                expect(result1).toBe(true);

                // Test second time slot (10:00)
                const timeSlot2 = new Date('2025-06-27T10:00:00Z');
                const result2 = await validateTimeSlot(timeSlot2, moduleId, mockLoaders, eventId);
                expect(result2).toBe(true);

                // Test third time slot (14:00)
                const timeSlot3 = new Date('2025-06-27T14:00:00Z');
                const result3 = await validateTimeSlot(timeSlot3, moduleId, mockLoaders, eventId);
                expect(result3).toBe(true);

                // Test fourth time slot (16:00)
                const timeSlot4 = new Date('2025-06-27T16:00:00Z');
                const result4 = await validateTimeSlot(timeSlot4, moduleId, mockLoaders, eventId);
                expect(result4).toBe(true);

                // Test non-existing time slot (12:00)
                const nonExistingTimeSlot = new Date('2025-06-27T12:00:00Z');
                const result6 = await validateTimeSlot(nonExistingTimeSlot, moduleId, mockLoaders, eventId);
                expect(result6).toBe(false);
            });

            it('should respect booking limits for multiple time slots in booking window', async () => {
                const eventWithMultipleTimeSlots = {
                    ...mockEventBookingWindow,
                    customTestDriveBookingSlots: {
                        ...mockEventBookingWindow.customTestDriveBookingSlots,
                        bookingWindowSettings: {
                            ...mockEventBookingWindow.customTestDriveBookingSlots.bookingWindowSettings,
                            bookingTimeSlot: [
                                {
                                    slot: new Date('2025-06-25T10:00:00Z'),
                                    _type: TimeSlotEnum.Appointment,
                                    bookingLimit: 1, // Low limit for testing
                                },
                                {
                                    slot: new Date('2025-06-25T14:00:00Z'),
                                    _type: TimeSlotEnum.Appointment,
                                    bookingLimit: 2,
                                },
                            ],
                        },
                    },
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithMultipleTimeSlots);

                // Mock that the 10:00 slot is fully booked (1 booking = limit reached)
                const blockedSlots = [new Date('2025-06-27T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

                // Test fully booked 10:00 slot
                const fullyBookedSlot = new Date('2025-06-27T10:00:00Z'); // Friday
                const result1 = await validateTimeSlot(fullyBookedSlot, moduleId, mockLoaders, eventId);
                expect(result1).toBe(false);

                // Test available 14:00 slot (no bookings yet, limit is 2)
                const availableSlot = new Date('2025-06-27T14:00:00Z'); // Friday
                const result2 = await validateTimeSlot(availableSlot, moduleId, mockLoaders, eventId);
                expect(result2).toBe(true);
            });
        });

        describe('Multiple Fixed Periods', () => {
            it('should validate time slots from multiple fixed periods', async () => {
                const eventWithMultiplePeriods = {
                    ...mockEvent,
                    customTestDriveBookingSlots: {
                        ...mockEvent.customTestDriveBookingSlots,
                        fixedPeriods: [
                            {
                                _id: new ObjectId(),
                                startDate: new Date('2025-06-25T00:00:00Z'),
                                endDate: new Date('2025-06-27T23:59:59Z'),
                                advancedBookingLimit: 0,
                                bookingTimeSlot: [
                                    {
                                        slot: new Date('2025-06-25T10:00:00Z'),
                                        _type: TimeSlotEnum.Appointment,
                                        bookingLimit: 3,
                                    },
                                ],
                            },
                            {
                                _id: new ObjectId(),
                                startDate: new Date('2025-06-28T00:00:00Z'),
                                endDate: new Date('2025-06-30T23:59:59Z'),
                                advancedBookingLimit: 0,
                                bookingTimeSlot: [
                                    {
                                        slot: new Date('2025-06-28T10:00:00Z'), // Use 10:00 instead of 14:00
                                        _type: TimeSlotEnum.Appointment,
                                        bookingLimit: 2,
                                    },
                                ],
                            },
                        ],
                    },
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithMultiplePeriods);

                const firstPeriodSlot = new Date('2025-06-25T10:00:00Z');
                const result1 = await validateTimeSlot(firstPeriodSlot, moduleId, mockLoaders, eventId);
                expect(result1).toBe(true);

                const secondPeriodSlot = new Date('2025-06-28T10:00:00Z');
                const result2 = await validateTimeSlot(secondPeriodSlot, moduleId, mockLoaders, eventId);
                expect(result2).toBe(true);

                const differentTimeSlot = new Date('2025-07-01T14:00:00Z');
                const result3 = await validateTimeSlot(differentTimeSlot, moduleId, mockLoaders, eventId);
                expect(result3).toBe(false);
            });
        });

        describe('Edge cases', () => {
            it('should handle timezone conversions correctly', async () => {
                const mockCompanyUTC = {
                    ...mockCompany,
                    timeZone: 'UTC',
                };
                mockLoaders.companyById.load.mockResolvedValue(mockCompanyUTC);

                const utcTimeSlot = new Date('2025-06-25T10:00:00Z');
                const result = await validateTimeSlot(utcTimeSlot, moduleId, mockLoaders);
                expect(result).toBe(true);
            });

            it('should handle missing event when eventId provided', async () => {
                mockLoaders.eventById.load.mockResolvedValue(null);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true); // Should fallback to module slots
            });

            it('should handle event without custom test drive booking slots', async () => {
                const eventWithoutSlots = {
                    _id: eventId,
                    customTestDriveBookingSlots: undefined,
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithoutSlots);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true); // Should fallback to module slots
            });

            it('should handle empty booking time slots in module', async () => {
                mockLoaders.moduleById.load.mockResolvedValue({
                    ...mockModule,
                    bookingTimeSlot: [],
                });

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result).toBe(false);
            });

            it('should handle blocked time slots array with different formats', async () => {
                const blockedSlots = [new Date('2025-06-25T10:00:00Z'), new Date('2025-06-25T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result).toBe(false);
            });

            it('should handle non-AppointmentModule with eventId', async () => {
                mockLoaders.moduleById.load.mockResolvedValue(mockVisitModule);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result).toBe(true);
                expect(mockLoaders.eventById.load).not.toHaveBeenCalled();
            });
        });

        describe('Booking Limit Edge Cases', () => {
            it('should handle bookingLimit exactly at the boundary', async () => {
                // Test when booked count equals booking limit exactly
                mockLoaders.moduleById.load.mockResolvedValue({
                    ...mockModule,
                    bookingTimeSlot: [
                        {
                            slot: new Date('2025-06-25T10:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 2,
                        },
                    ],
                });

                // Mock exactly 2 bookings (equals the limit)
                const blockedSlots = [new Date('2025-06-25T10:00:00Z'), new Date('2025-06-25T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result).toBe(false); // Should be false as limit is reached
            });

            it('should handle bookingLimit just under the boundary', async () => {
                // Test when booked count is one less than booking limit
                mockLoaders.moduleById.load.mockResolvedValue({
                    ...mockModule,
                    bookingTimeSlot: [
                        {
                            slot: new Date('2025-06-25T10:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 3,
                        },
                    ],
                });

                // Mock 2 bookings (one less than limit of 3)
                const blockedSlots = [new Date('2025-06-25T10:00:00Z'), new Date('2025-06-25T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result).toBe(true); // Should be true as still under limit
            });

            it('should handle bookingLimit of 1 (single booking)', async () => {
                mockLoaders.moduleById.load.mockResolvedValue({
                    ...mockModule,
                    bookingTimeSlot: [
                        {
                            slot: new Date('2025-06-25T10:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 1,
                        },
                    ],
                });

                // Test with no existing bookings
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue([]);
                const result1 = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result1).toBe(true);

                // Test with one existing booking (should reach limit)
                const blockedSlots = [new Date('2025-06-25T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);
                const result2 = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result2).toBe(false);
            });

            it('should handle large bookingLimit values', async () => {
                mockLoaders.moduleById.load.mockResolvedValue({
                    ...mockModule,
                    bookingTimeSlot: [
                        {
                            slot: new Date('2025-06-25T10:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 1000, // Large limit
                        },
                    ],
                });

                // Create many blocked slots but still under the large limit
                const blockedSlots = Array.from({ length: 999 }, () => new Date('2025-06-25T10:00:00Z'));
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

                const result = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders);
                expect(result).toBe(true); // Should still be available (999 < 1000)
            });

            it('should handle mixed time slots with different booking limits', async () => {
                mockLoaders.moduleById.load.mockResolvedValue({
                    ...mockModule,
                    bookingTimeSlot: [
                        {
                            slot: new Date('2025-06-25T10:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 1, // Very limited
                        },
                        {
                            slot: new Date('2025-06-25T14:00:00Z'),
                            _type: TimeSlotEnum.Appointment,
                            bookingLimit: 5, // More available
                        },
                    ],
                });

                // Mock the 10:00 slot as fully booked
                const blockedSlots = [new Date('2025-06-25T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);

                // Test the fully booked 10:00 slot
                const slot1 = new Date('2025-06-25T10:00:00Z');
                const result1 = await validateTimeSlot(slot1, moduleId, mockLoaders);
                expect(result1).toBe(false);

                // Test the available 14:00 slot
                const slot2 = new Date('2025-06-25T14:00:00Z');
                const result2 = await validateTimeSlot(slot2, moduleId, mockLoaders);
                expect(result2).toBe(true);
            });

            it('should handle bookingLimit with event custom slots', async () => {
                const eventWithLimitedSlots = {
                    ...mockEvent,
                    customTestDriveBookingSlots: {
                        ...mockEvent.customTestDriveBookingSlots,
                        fixedPeriods: [
                            {
                                _id: new ObjectId(),
                                startDate: new Date('2025-06-25T00:00:00Z'),
                                endDate: new Date('2025-06-30T23:59:59Z'),
                                advancedBookingLimit: 0,
                                bookingTimeSlot: [
                                    {
                                        slot: new Date('2025-06-25T10:00:00Z'),
                                        _type: TimeSlotEnum.Appointment,
                                        bookingLimit: 2,
                                    },
                                ],
                            },
                        ],
                    },
                };
                mockLoaders.eventById.load.mockResolvedValue(eventWithLimitedSlots);

                // Test with no bookings
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue([]);
                const result1 = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result1).toBe(true);

                // Test with bookings at limit
                const blockedSlots = [new Date('2025-06-25T10:00:00Z'), new Date('2025-06-25T10:00:00Z')];
                mockGetBlockedAppointmentTimeSlots.mockResolvedValue(blockedSlots);
                const result2 = await validateTimeSlot(selectedTimeSlot, moduleId, mockLoaders, eventId);
                expect(result2).toBe(false);
            });
        });
    });
});

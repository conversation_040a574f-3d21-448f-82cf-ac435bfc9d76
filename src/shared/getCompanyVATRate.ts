import dayjs from 'dayjs';
import type { Company, VATRateSettingItem } from '../server/database/documents';

export const findMostRecentVATRate = (vatRateTable: VATRateSettingItem[]): VATRateSettingItem | null => {
    const filteredAndSorted = vatRateTable
        .filter(item => dayjs(item.startDate).startOf('day').isSameOrBefore(dayjs().startOf('day')))
        .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime());

    return filteredAndSorted[0] || null;
};

export const getVATRate = (company: Company): number | null => {
    if (company?.vatRateSettings?.vatRateTable?.length > 0) {
        const found = findMostRecentVATRate(company?.vatRateSettings?.vatRateTable);

        return found ? Number(found.value) / 100 : null;
    }

    return null;
};
